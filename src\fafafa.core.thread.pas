unit fafafa.core.thread;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base,
  fafafa.core.thread.types,
  fafafa.core.thread.future,
  fafafa.core.thread.pool;

// 重新导出核心类型和接口
type
  // 基础类型
  TThreadProc = fafafa.core.thread.types.TThreadProc;
  TThreadMethod = fafafa.core.thread.types.TThreadMethod;
  TExceptionHandler = fafafa.core.thread.types.TExceptionHandler;
  
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  TThreadRefProc = fafafa.core.thread.types.TThreadRefProc;
  generic TFunc<T> = fafafa.core.thread.types.specialize TFunc<T>;
  generic TFunc<T, TResult> = fafafa.core.thread.types.specialize TFunc<T, TResult>;
  TAction = fafafa.core.thread.types.TAction;
  generic TAction<T> = fafafa.core.thread.types.specialize TAction<T>;
  TExceptionRefHandler = fafafa.core.thread.types.TExceptionRefHandler;
  {$ENDIF}
  
  // 枚举类型
  TThreadPoolState = fafafa.core.thread.types.TThreadPoolState;
  TFutureState = fafafa.core.thread.types.TFutureState;
  
  // 接口类型
  IWorkItem = fafafa.core.thread.types.IWorkItem;
  generic IFuture<T> = fafafa.core.thread.types.specialize IFuture<T>;
  generic IPromise<T> = fafafa.core.thread.types.specialize IPromise<T>;
  IThreadPool = fafafa.core.thread.types.IThreadPool;
  generic IThreadSafeQueue<T> = fafafa.core.thread.types.specialize IThreadSafeQueue<T>;
  
  // 异常类型
  EThreadPool = fafafa.core.thread.types.EThreadPool;
  EFuture = fafafa.core.thread.types.EFuture;
  EFutureCancelled = fafafa.core.thread.types.EFutureCancelled;
  EFutureTimeout = fafafa.core.thread.types.EFutureTimeout;

// 工厂函数
function CreateThreadPool(aCoreThreads: Integer = 4; aMaxThreads: Integer = 16; 
  aKeepAliveTimeMs: Cardinal = 60000): IThreadPool;

generic function CreateFuture<T>: specialize IFuture<T>;
generic function CreatePromise<T>: specialize IPromise<T>;
generic function CreateThreadSafeQueue<T>: specialize IThreadSafeQueue<T>;

// 便利函数
function GetDefaultThreadPool: IThreadPool;
procedure SetDefaultThreadPool(aPool: IThreadPool);

// 全局异步执行函数
procedure ExecuteAsync(const aProc: TThreadProc); overload;
procedure ExecuteAsync(const aMethod: TThreadMethod); overload;
{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure ExecuteAsync(const aRefProc: TThreadRefProc); overload;
function SubmitAsync<T>(const aFunc: specialize TFunc<T>): specialize IFuture<T>;
{$ENDIF}

implementation

var
  GDefaultThreadPool: IThreadPool = nil;

// 工厂函数实现

function CreateThreadPool(aCoreThreads: Integer; aMaxThreads: Integer; 
  aKeepAliveTimeMs: Cardinal): IThreadPool;
begin
  Result := fafafa.core.thread.pool.CreateThreadPool(aCoreThreads, aMaxThreads, aKeepAliveTimeMs);
end;

generic function CreateFuture<T>: specialize IFuture<T>;
begin
  Result := fafafa.core.thread.future.specialize CreateFuture<T>;
end;

generic function CreatePromise<T>: specialize IPromise<T>;
begin
  Result := fafafa.core.thread.future.specialize CreatePromise<T>;
end;

generic function CreateThreadSafeQueue<T>: specialize IThreadSafeQueue<T>;
begin
  Result := fafafa.core.thread.future.specialize CreateThreadSafeQueue<T>;
end;

// 默认线程池管理

function GetDefaultThreadPool: IThreadPool;
begin
  if GDefaultThreadPool = nil then
  begin
    GDefaultThreadPool := CreateThreadPool(4, 16, 60000);
    GDefaultThreadPool.Start;
  end;
  Result := GDefaultThreadPool;
end;

procedure SetDefaultThreadPool(aPool: IThreadPool);
begin
  GDefaultThreadPool := aPool;
end;

// 全局异步执行函数

procedure ExecuteAsync(const aProc: TThreadProc);
begin
  GetDefaultThreadPool.Execute(aProc);
end;

procedure ExecuteAsync(const aMethod: TThreadMethod);
begin
  GetDefaultThreadPool.Execute(aMethod);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure ExecuteAsync(const aRefProc: TThreadRefProc);
begin
  GetDefaultThreadPool.Execute(aRefProc);
end;

function SubmitAsync<T>(const aFunc: specialize TFunc<T>): specialize IFuture<T>;
begin
  Result := GetDefaultThreadPool.Submit<T>(aFunc);
end;
{$ENDIF}

// 单元初始化和清理

initialization
  // 单元初始化时不创建默认线程池，延迟到第一次使用时创建

finalization
  // 程序结束时关闭默认线程池
  if Assigned(GDefaultThreadPool) then
  begin
    GDefaultThreadPool.Shutdown(True);
    GDefaultThreadPool := nil;
  end;

end.
