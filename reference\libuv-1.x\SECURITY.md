# Security Policy

## Supported Versions

Currently, we are providing security updates for the latest release in the v1.x series:

| Version | Supported          |
| ------- | ------------------ |
| Latest v1.x  | :white_check_mark: |

## Reporting a Vulnerability

If you believe you have found a security vulnerability in `libuv`, please use the [GitHub's private vulnerability reporting feature](https://docs.github.com/en/code-security/security-advisories/guidance-on-reporting-and-writing-information-about-vulnerabilities/privately-reporting-a-security-vulnerability#privately-reporting-a-security-vulnerability) in the [libuv repository](https://github.com/libuv/libuv) to report it to us.

This will allow us to assess the risk, and make a fix available before we add a bug report to the GitHub repository.

Please do:

* Provide as much information as you can about the vulnerability.
* Provide details about your configuration and environment, if applicable.

Please do not:

* Post any information about the vulnerability in public places.
* Attempt to exploit the vulnerability yourself.

We take all security bugs seriously. Thank you for improving the security of `libuv`. We appreciate your efforts and responsible disclosure and will make every effort to acknowledge your contributions.