unit test_math;

{$MODE OBJFPC}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  fafafa.core.math,
  Math; // Use standard Math unit for baseline comparison

type

  { TMathTest }

  TMathTest = class(TTestCase)
  published
    procedure TestMin;
    procedure TestMax;
    procedure TestCeil;
    procedure TestCeil64;
  end;

implementation

procedure TMathTest.TestMin;
begin
  CheckEquals(Math.Min(1, 2), fafafa.core.math.Min(1, 2), 'Min(1, 2)');
  CheckEquals(Math.Min(2, 1), fafafa.core.math.Min(2, 1), 'Min(2, 1)');
  CheckEquals(Math.Min(-1, -2), fafafa.core.math.Min(-1, -2), 'Min(-1, -2)');
  CheckEquals(Math.Min(-2, -1), fafafa.core.math.Min(-2, -1), 'Min(-2, -1)');
  CheckEquals(Math.Min(0, 5), fafafa.core.math.Min(0, 5), 'Min(0, 5)');
  CheckEquals(Math.Min(-5, 0), fafafa.core.math.Min(-5, 0), 'Min(-5, 0)');
  CheckEquals(Math.Min(5, 5), fafafa.core.math.Min(5, 5), 'Min(5, 5)');
end;

procedure TMathTest.TestMax;
begin
  CheckEquals(Math.Max(1, 2), fafafa.core.math.Max(1, 2), 'Max(1, 2)');
  CheckEquals(Math.Max(2, 1), fafafa.core.math.Max(2, 1), 'Max(2, 1)');
  CheckEquals(Math.Max(-1, -2), fafafa.core.math.Max(-1, -2), 'Max(-1, -2)');
  CheckEquals(Math.Max(-2, -1), fafafa.core.math.Max(-2, -1), 'Max(-2, -1)');
  CheckEquals(Math.Max(0, 5), fafafa.core.math.Max(0, 5), 'Max(0, 5)');
  CheckEquals(Math.Max(-5, 0), fafafa.core.math.Max(-5, 0), 'Max(-5, 0)');
  CheckEquals(Math.Max(5, 5), fafafa.core.math.Max(5, 5), 'Max(5, 5)');
end;

procedure TMathTest.TestCeil;
begin
  CheckEquals(Math.Ceil(3.14), fafafa.core.math.Ceil(3.14), 'Ceil(3.14)');
  CheckEquals(Math.Ceil(3.0), fafafa.core.math.Ceil(3.0), 'Ceil(3.0)');
  CheckEquals(Math.Ceil(3.99), fafafa.core.math.Ceil(3.99), 'Ceil(3.99)');
  CheckEquals(Math.Ceil(-3.14), fafafa.core.math.Ceil(-3.14), 'Ceil(-3.14)');
  CheckEquals(Math.Ceil(-3.0), fafafa.core.math.Ceil(-3.0), 'Ceil(-3.0)');
  CheckEquals(Math.Ceil(-3.99), fafafa.core.math.Ceil(-3.99), 'Ceil(-3.99)');
  CheckEquals(Math.Ceil(0.0), fafafa.core.math.Ceil(0.0), 'Ceil(0.0)');
end;

procedure TMathTest.TestCeil64;
begin
  CheckEquals(Int64(Math.Ceil(3.14)), fafafa.core.math.Ceil64(3.14), 'Ceil64(3.14)');
  CheckEquals(Int64(Math.Ceil(3.0)), fafafa.core.math.Ceil64(3.0), 'Ceil64(3.0)');
  CheckEquals(Int64(Math.Ceil(3.99)), fafafa.core.math.Ceil64(3.99), 'Ceil64(3.99)');
  CheckEquals(Int64(Math.Ceil(-3.14)), fafafa.core.math.Ceil64(-3.14), 'Ceil64(-3.14)');
  CheckEquals(Int64(Math.Ceil(-3.0)), fafafa.core.math.Ceil64(-3.0), 'Ceil64(-3.0)');
  CheckEquals(Int64(Math.Ceil(-3.99)), fafafa.core.math.Ceil64(-3.99), 'Ceil64(-3.99)');
  CheckEquals(Int64(Math.Ceil(0.0)), fafafa.core.math.Ceil64(0.0), 'Ceil64(0.0)');
end;

initialization
  RegisterTest(TMathTest);
end.
