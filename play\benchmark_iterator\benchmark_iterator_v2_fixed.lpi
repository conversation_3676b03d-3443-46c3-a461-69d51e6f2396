<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <PathDelim Value="\"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
        <UseDefaultCompilerOptions Value="True"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="benchmark_iterator_v2_fixed"/>
      <UseAppBundle Value="False"/>
      <ResourceType Value="res"/>
    </General>
    <BuildModes>
      <Item Name="Default" Default="True"/>
      <Item Name="Debug">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="benchmark_iterator_v2_fixed"/>
          </Target>
          <SearchPaths>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <Parsing>
            <SyntaxOptions>
              <IncludeAssertionCode Value="True"/>
            </SyntaxOptions>
          </Parsing>
          <CodeGeneration>
            <Checks>
              <IOChecks Value="True"/>
              <RangeChecks Value="True"/>
              <OverflowChecks Value="True"/>
              <StackChecks Value="True"/>
            </Checks>
            <VerifyObjMethodCallValidity Value="True"/>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <DebugInfoType Value="dsDwarf3"/>
              <UseHeaptrc Value="True"/>
              <TrashVariables Value="True"/>
            </Debugging>
          </Linking>
        </CompilerOptions>
      </Item>
      <Item Name="Release">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="benchmark_iterator_v2_fixed"/>
          </Target>
          <SearchPaths>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <CodeGeneration>
            <SmartLinkUnit Value="True"/>
            <Optimizations>
              <OptimizationLevel Value="3"/>
            </Optimizations>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <GenerateDebugInfo Value="False"/>
              <RunWithoutDebug Value="True"/>
              <StripSymbols Value="True"/>
            </Debugging>
            <LinkSmart Value="True"/>
          </Linking>
        </CompilerOptions>
      </Item>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
      <UseFileFilters Value="True"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <Units>
      <Unit>
        <Filename Value="benchmark_iterator_v2_fixed.lpr"/>
        <IsPartOfProject Value="True"/>
      </Unit>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="benchmark_iterator_v2_fixed"/>
    </Target>
    <SearchPaths>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
  </CompilerOptions>
  <Debugging>
    <Exceptions>
      <Item>
        <Name Value="EAbort"/>
      </Item>
      <Item>
        <Name Value="ECodetoolError"/>
      </Item>
      <Item>
        <Name Value="EFOpenError"/>
      </Item>
    </Exceptions>
  </Debugging>
</CONFIG>
