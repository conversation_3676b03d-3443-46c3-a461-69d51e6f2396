unit fafafa.core;

{$MODE OBJFPC}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  classes,
  SysUtils,
  { fafafa.core }
  fafafa.core.mem.utils,
  fafafa.core.mem.allocator,
  fafafa.core.collections.elementManager
  ;

  ///
  /// 异常
  ///

  type

    ECore            = fafafa.core.base.ECore;
    EWow             = fafafa.core.base.EWow;
    EArgumentNil     = fafafa.core.base.EArgumentNil;
    EEmptyCollection = fafafa.core.base.EEmptyCollection;
    EInvalidArgument = fafafa.core.base.EInvalidArgument;
    EOutOfRange      = fafafa.core.base.EOutOfRange;
    ENotSupported    = fafafa.core.base.ENotSupported;
    EOutOfMemory     = fafafa.core.base.EOutOfMemory;


  ///
  /// Allocator 分配器
  ///

    type

    IAllocator                 = fafafa.core.mem.allocator.IAllocator;
    TAllocator                 = fafafa.core.mem.allocator.TAllocator;
    TRtlAllocator              = fafafa.core.mem.allocator.TRtlAllocator;
    TCrtAllocator              = fafafa.core.mem.allocator.TCrtAllocator;
    TCallbackAllocatorCallback = fafafa.core.mem.allocator.TCallbackAllocatorCallback;


  function GetRtlAllocator: TRtlAllocator; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
  {$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
  function GetCrtAllocator: TCrtAllocator; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
  {$ENDIF}
  function GetDefaultAllocator: TAllocator; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  function CreateCallbackAllocator(aGetMem: TGetMemCallback; 
                                   aAllocMem: TAllocMemCallback; 
                                  aReallocMem: TReallocMemCallback; 
                                  aFreeMem: TFreeMemCallback): TCallbackAllocator; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  function MakeCallbackAllocator(aGetMem: TGetMemCallback; 
                                 aAllocMem: TAllocMemCallback; 
                                 aReallocMem: TReallocMemCallback; 
                                 aFreeMem: TFreeMemCallback): IAllocator; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  ///
  /// 容器
  ///

  type

    IElementManager = fafafa.core.collections.elementManager.IElementManager;
    TElementManager = fafafa.core.collections.elementManager.TElementManager;


  ///
  /// MemPool 内存池
  ///




  ///
  /// Thread 线程
  ///


implementation

function GetRtlAllocator: TAllocator;
begin
  Result := fafafa.core.mem.allocator.GetRtlAllocator();
end;

{$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
function GetCrtAllocator: TAllocator;
begin
  Result := fafafa.core.mem.allocator.GetCrtAllocator();
end;
{$ENDIF}

function GetDefaultAllocator: TAllocator;
begin
  Result := GetRtlAllocator();
end;

function CreateCallbackAllocator(aGetMem: TGetMemCallback; 
  aAllocMem: TAllocMemCallback; aReallocMem: TReallocMemCallback; 
  aFreeMem: TFreeMemCallback): TCallbackAllocator;
begin
  Result := fafafa.core.mem.allocator.CreateCallbackAllocator(aGetMem, aAllocMem, aReallocMem, aFreeMem);
end;

function MakeCallbackAllocator(aGetMem: TGetMemCallback; 
  aAllocMem: TAllocMemCallback; aReallocMem: TReallocMemCallback; 
  aFreeMem: TFreeMemCallback): IAllocator;
begin
  Result := CreateCallbackAllocator(aGetMem, aAllocMem, aReallocMem, aFreeMem);
end;

end.