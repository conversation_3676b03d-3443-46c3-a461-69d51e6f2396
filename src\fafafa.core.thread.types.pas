unit fafafa.core.thread.types;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base;

type
  // 前向声明
  IThreadPool = interface;
  generic IFuture<T> = interface;
  generic IPromise<T> = interface;

  // 线程相关的基础类型
  TThreadProc = procedure;
  TThreadMethod = procedure of object;
  
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  TThreadRefProc = reference to procedure;
  generic TFunc<T> = reference to function: T;
  generic TFunc<T, TResult> = reference to function(const aValue: T): TResult;
  TAction = reference to procedure;
  generic TAction<T> = reference to procedure(const aValue: T);
  {$ENDIF}

  // 异常处理类型
  TExceptionHandler = procedure(aException: Exception) of object;
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  TExceptionRefHandler = reference to procedure(aException: Exception);
  {$ENDIF}

  // 线程池状态
  TThreadPoolState = (
    tpsCreated,    // 已创建，未启动
    tpsRunning,    // 正在运行
    tpsShutdown,   // 正在关闭
    tpsClosed      // 已关闭
  );

  // Future状态
  TFutureState = (
    fsWaiting,     // 等待中
    fsCompleted,   // 已完成
    fsCancelled,   // 已取消
    fsFailed       // 执行失败
  );

  // 工作项接口
  IWorkItem = interface(IInterface)
  ['{WORK-ITEM-INTERFACE-GUID}']
    procedure Execute;
    function IsCancelled: Boolean;
    procedure Cancel;
  end;

  // Future接口 - 表示异步操作的未来结果
  generic IFuture<T> = interface(IInterface)
  ['{FUTURE-INTERFACE-GUID}']
    // 状态查询
    function GetState: TFutureState;
    function IsDone: Boolean;
    function IsCancelled: Boolean;
    function IsCompleted: Boolean;
    function IsFailed: Boolean;
    
    // 结果获取
    function GetValue(aTimeoutMs: Cardinal = INFINITE): T;
    function TryGetValue(out aValue: T; aTimeoutMs: Cardinal = 0): Boolean;
    function GetException: Exception;
    
    // 取消操作
    procedure Cancel;
    
    // 链式调用支持
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Then<TResult>(const aFunc: specialize TFunc<T, TResult>): specialize IFuture<TResult>;
    function Catch(const aHandler: TExceptionRefHandler): specialize IFuture<T>;
    function Finally(const aAction: TAction): specialize IFuture<T>;
    {$ENDIF}
    
    // 属性
    property State: TFutureState read GetState;
    property Done: Boolean read IsDone;
    property Cancelled: Boolean read IsCancelled;
    property Completed: Boolean read IsCompleted;
    property Failed: Boolean read IsFailed;
  end;

  // Promise接口 - 用于设置Future的结果
  generic IPromise<T> = interface(IInterface)
  ['{PROMISE-INTERFACE-GUID}']
    procedure SetValue(const aValue: T);
    procedure SetException(aException: Exception);
    function GetFuture: specialize IFuture<T>;
    function IsFulfilled: Boolean;
  end;

  // 线程池接口
  IThreadPool = interface(IInterface)
  ['{THREAD-POOL-INTERFACE-GUID}']
    // 基本操作
    procedure Start;
    procedure Shutdown(aWaitForCompletion: Boolean = True);
    
    // 提交任务
    procedure Execute(const aProc: TThreadProc); overload;
    procedure Execute(const aMethod: TThreadMethod); overload;
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Execute(const aRefProc: TThreadRefProc); overload;
    function Submit<T>(const aFunc: specialize TFunc<T>): specialize IFuture<T>;
    {$ENDIF}
    
    // 状态查询
    function GetState: TThreadPoolState;
    function GetActiveThreadCount: Integer;
    function GetQueuedTaskCount: Integer;
    function GetCoreThreadCount: Integer;
    function GetMaxThreadCount: Integer;
    
    // 配置
    procedure SetCoreThreadCount(aCount: Integer);
    procedure SetMaxThreadCount(aCount: Integer);
    procedure SetKeepAliveTime(aTimeMs: Cardinal);
    
    // 属性
    property State: TThreadPoolState read GetState;
    property ActiveThreads: Integer read GetActiveThreadCount;
    property QueuedTasks: Integer read GetQueuedTaskCount;
    property CoreThreads: Integer read GetCoreThreadCount write SetCoreThreadCount;
    property MaxThreads: Integer read GetMaxThreadCount write SetMaxThreadCount;
  end;

  // 线程安全队列接口
  generic IThreadSafeQueue<T> = interface(IInterface)
  ['{THREAD-SAFE-QUEUE-GUID}']
    procedure Enqueue(const aItem: T);
    function TryDequeue(out aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function GetCount: Integer;
    function IsEmpty: Boolean;
    procedure Clear;
    
    property Count: Integer read GetCount;
    property Empty: Boolean read IsEmpty;
  end;

  // 线程相关异常
  EThreadPool = class(ECore)
  public
    constructor Create(const aMessage: string);
  end;

  EFuture = class(ECore)
  public
    constructor Create(const aMessage: string);
  end;

  EFutureCancelled = class(EFuture)
  public
    constructor Create;
  end;

  EFutureTimeout = class(EFuture)
  public
    constructor Create;
  end;

// 工厂函数声明
function CreateThreadPool(aCoreThreads: Integer = 4; aMaxThreads: Integer = 16; 
  aKeepAliveTimeMs: Cardinal = 60000): IThreadPool;

generic function CreateFuture<T>: specialize IFuture<T>;
generic function CreatePromise<T>: specialize IPromise<T>;

implementation

// 异常类实现
constructor EThreadPool.Create(const aMessage: string);
begin
  inherited Create(aMessage);
end;

constructor EFuture.Create(const aMessage: string);
begin
  inherited Create(aMessage);
end;

constructor EFutureCancelled.Create;
begin
  inherited Create('Future operation was cancelled');
end;

constructor EFutureTimeout.Create;
begin
  inherited Create('Future operation timed out');
end;

// 工厂函数的实现将在具体的实现单元中提供
function CreateThreadPool(aCoreThreads: Integer; aMaxThreads: Integer; 
  aKeepAliveTimeMs: Cardinal): IThreadPool;
begin
  // 实现将在 fafafa.core.thread.pool.pas 中提供
  Result := nil;
  raise ENotSupported.Create('CreateThreadPool implementation not loaded');
end;

generic function CreateFuture<T>: specialize IFuture<T>;
begin
  // 实现将在 fafafa.core.thread.future.pas 中提供
  Result := nil;
  raise ENotSupported.Create('CreateFuture implementation not loaded');
end;

generic function CreatePromise<T>: specialize IPromise<T>;
begin
  // 实现将在 fafafa.core.thread.future.pas 中提供
  Result := nil;
  raise ENotSupported.Create('CreatePromise implementation not loaded');
end;

end.
