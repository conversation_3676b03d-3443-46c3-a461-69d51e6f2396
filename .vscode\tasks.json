{"version": "2.0.0", "runner": "terminal", "tasks": [{"command": ".vscode/CompileOmniPascalServerProject.bat", "args": ["build"], "type": "shell", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "label": "build", "problemMatcher": {"owner": "external", "fileLocation": ["absolute"], "pattern": {"regexp": "(([A-Za-z]):\\\\(?:[^\\/:*?\"<>|\\r\\n]+\\\\)*[^\\/\\s\\(:*?\"<>|\\r\\n]*)\\((\\d+),(\\d+)\\)\\s.*(Fatal|Error|Warning|Hint|Note):\\s\\((\\d+)\\)\\s(.*)$", "file": 1, "line": 3, "column": 4, "severity": 5, "code": 6, "message": 7}, "severity": "info"}, "group": {"kind": "build", "isDefault": true}}, {"command": ".vscode/CompileOmniPascalServerProject.bat", "args": ["test"], "type": "shell", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "label": "test", "problemMatcher": {"owner": "external", "fileLocation": ["absolute"], "pattern": {"regexp": "(([A-Za-z]):\\\\(?:[^\\/:*?\"<>|\\r\\n]+\\\\)*[^\\/\\s\\(:*?\"<>|\\r\\n]*)\\((\\d+),(\\d+)\\)\\s.*(Fatal|Error|Warning|Hint|Note):\\s\\((\\d+)\\)\\s(.*)$", "file": 1, "line": 3, "column": 4, "severity": 5, "code": 6, "message": 7}, "severity": "info"}, "group": {"kind": "test", "isDefault": true}}]}