program example.arrays;

{$mode objfpc}{$H+}
{$ModeSwitch advancedrecords}
uses
  {$IFDEF UNIX}
  cthreads,
  {$ENDIF}
  Classes
  { you can add units after this };
TYPE
  { TIter<T> 泛型迭代器. }
  generic TIter<T> = record
  public
    function  GetStarted: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetCurrent: T; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  MoveNext: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Reset; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  Equal(const aIter: TIter): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  end;

function TIter.GetStarted: Boolean;
begin

end;

function TIter.GetCurrent: T;
begin

end;

function TIter.MoveNext: Boolean;
begin

end;

procedure TIter.Reset;
begin

end;

function TIter.Equal(const aIter: TIter): Boolean;
begin

end;

begin

end.

