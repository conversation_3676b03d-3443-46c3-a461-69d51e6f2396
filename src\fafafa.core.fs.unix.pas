{**
 * fafafa.core.fs.unix.pas
 *
 * @desc Unix 平台文件系统实现 - 严格移植版本
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024
 *}

unit fafafa.core.fs.unix;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  BaseUnix, Unix, SysUtils, fafafa.core.fs;

type
  { Unix 平台文件系统实现 }
  TUnixFileSystem = class(TPlatformFileSystem)
  public
    // 基础文件操作
    class function OpenFile(const aPath: string; aFlags: Integer; aMode: Integer): Integer; override;
    class function CloseFile(aHandle: TFileHandle): Integer; override;
    class function ReadFile(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; override;
    class function WriteFile(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; override;
    
    // 文件系统操作
    class function DeleteFile(const aPath: string): Integer; override;
    class function CreateDirectory(const aPath: string; aMode: Integer): Integer; override;
    class function RemoveDirectory(const aPath: string): Integer; override;
    class function RenameFile(const aOldPath, aNewPath: string): Integer; override;
    class function CopyFile(const aSrc, aDest: string; aFlags: Integer): Integer; override;
    
    // 文件信息操作
    class function GetFileStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function GetFileStatByHandle(aHandle: TFileHandle; out aStat: TFileStat): Integer; override;
    class function GetLinkStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function CheckAccess(const aPath: string; aMode: Integer): Integer; override;
    
    // 权限和时间操作
    class function ChangeMode(const aPath: string; aMode: Integer): Integer; override;
    class function ChangeModeByHandle(aHandle: TFileHandle; aMode: Integer): Integer; override;
    class function SetFileTime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer; override;
    class function SetFileTimeByHandle(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer; override;
    
    // 符号链接操作
    class function CreateSymlink(const aTarget, aPath: string; aFlags: Integer): Integer; override;
    class function ReadSymlink(const aPath: string; out aTarget: string): Integer; override;
    
    // 目录操作
    class function ReadDirectory(const aPath: string; out aEntries: TArray<string>): Integer; override;
    
    // 新增的 libuv 功能
    class function FileSync(aHandle: TFileHandle): Integer; override;
    class function FileDataSync(aHandle: TFileHandle): Integer; override;
    class function FileTruncate(aHandle: TFileHandle; aOffset: Int64): Integer; override;
    class function SendFile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer; override;
    class function RealPath(const aPath: string; out aRealPath: string): Integer; override;
    class function ChangeOwner(const aPath: string; aUID, aGID: Integer): Integer; override;
    class function ChangeOwnerByHandle(aHandle: TFileHandle; aUID, aGID: Integer): Integer; override;
    class function ChangeOwnerLink(const aPath: string; aUID, aGID: Integer): Integer; override;
    class function GetFileSystemStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function ScanDirectory(const aPath: string; out aEntries: TArray<string>): Integer; override;
    class function MakeTempDirectory(const aTemplate: string; out aPath: string): Integer; override;
    class function MakeTempFile(const aTemplate: string; out aPath: string): Integer; override;
  private
    class function GetFileOpenFlags(aFlags: Integer): Integer;
    class function GetFileMode(aFlags: Integer): Integer;
    class function DateTimeToTimeVal(aDateTime: TDateTime): TTimeVal;
    class function TimeValToDateTime(aTimeVal: TTimeVal): TDateTime;
    class function StatToFileStat(const aStat: TStat): TFileStat;
    class function GetLastErrorCode: Integer;
  end;

implementation

{ TUnixFileSystem }

class function TUnixFileSystem.OpenFile(const aPath: string; aFlags: Integer; aMode: Integer): Integer;
var
  LHandle: Integer;
  LFlags: Integer;
  LMode: Integer;
begin
  try
    LFlags := GetFileOpenFlags(aFlags);
    LMode := GetFileMode(aMode);

    LHandle := FpOpen(PChar(aPath), LFlags, LMode);

    if LHandle = -1 then
    begin
      Result := -GetLastErrorCode; // 返回负的错误码，符合 libuv 规范
    end
    else
    begin
      Result := LHandle; // 返回文件句柄
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.CloseFile(aHandle: TFileHandle): Integer;
begin
  try
    if FpClose(aHandle) = 0 then
      Result := 0 // 成功返回 0
    else
      Result := -GetLastErrorCode; // 失败返回负的错误码
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ReadFile(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
var
  LBytesRead: SizeUInt;
begin
  try
    if aOffset >= 0 then
    begin
      // 使用偏移量读取
      LBytesRead := FpPRead(aHandle, aBuffer, aSize, aOffset);
    end
    else
    begin
      // 从当前位置读取
      LBytesRead := FpRead(aHandle, aBuffer, aSize);
    end;

    if LBytesRead = SizeUInt(-1) then
      Result := -GetLastErrorCode
    else
      Result := Integer(LBytesRead); // 返回读取的字节数
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.WriteFile(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
var
  LBytesWritten: SizeUInt;
begin
  try
    if aOffset >= 0 then
    begin
      // 使用偏移量写入
      LBytesWritten := FpPWrite(aHandle, aBuffer, aSize, aOffset);
    end
    else
    begin
      // 从当前位置写入
      LBytesWritten := FpWrite(aHandle, aBuffer, aSize);
    end;

    if LBytesWritten = SizeUInt(-1) then
      Result := -GetLastErrorCode
    else
      Result := Integer(LBytesWritten); // 返回写入的字节数
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.DeleteFile(const aPath: string): Integer;
begin
  try
    if FpUnlink(PChar(aPath)) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.CreateDirectory(const aPath: string; aMode: Integer): Integer;
begin
  try
    if FpMkDir(PChar(aPath), aMode) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.RemoveDirectory(const aPath: string): Integer;
begin
  try
    if FpRmDir(PChar(aPath)) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.RenameFile(const aOldPath, aNewPath: string): Integer;
begin
  try
    if FpRename(PChar(aOldPath), PChar(aNewPath)) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.CopyFile(const aSrc, aDest: string; aFlags: Integer): Integer;
var
  LSrcHandle, LDestHandle: Integer;
  LBuffer: array[0..8191] of Byte;
  LBytesRead, LBytesWritten: SizeUInt;
begin
  try
    LSrcHandle := FpOpen(PChar(aSrc), O_RDONLY);
    if LSrcHandle = -1 then
    begin
      Result := -GetLastErrorCode;
      Exit;
    end;

    try
      LDestHandle := FpOpen(PChar(aDest), O_WRONLY or O_CREAT or O_TRUNC, &666);
      if LDestHandle = -1 then
      begin
        Result := -GetLastErrorCode;
        Exit;
      end;

      try
        repeat
          LBytesRead := FpRead(LSrcHandle, LBuffer, SizeOf(LBuffer));
          if LBytesRead > 0 then
          begin
            LBytesWritten := FpWrite(LDestHandle, LBuffer, LBytesRead);
            if LBytesWritten <> LBytesRead then
            begin
              Result := -GetLastErrorCode;
              Exit;
            end;
          end;
        until LBytesRead = 0;

        Result := 0;
      finally
        FpClose(LDestHandle);
      end;
    finally
      FpClose(LSrcHandle);
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.GetFileStat(const aPath: string; out aStat: TFileStat): Integer;
var
  LUnixStat: TStat;
begin
  try
    if FpStat(PChar(aPath), LUnixStat) = 0 then
    begin
      aStat := StatToFileStat(LUnixStat);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.GetFileStatByHandle(aHandle: TFileHandle; out aStat: TFileStat): Integer;
var
  LUnixStat: TStat;
begin
  try
    if FpFStat(aHandle, LUnixStat) = 0 then
    begin
      aStat := StatToFileStat(LUnixStat);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.GetLinkStat(const aPath: string; out aStat: TFileStat): Integer;
var
  LUnixStat: TStat;
begin
  try
    if FpLStat(PChar(aPath), LUnixStat) = 0 then
    begin
      aStat := StatToFileStat(LUnixStat);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.CheckAccess(const aPath: string; aMode: Integer): Integer;
begin
  try
    if FpAccess(PChar(aPath), aMode) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ChangeMode(const aPath: string; aMode: Integer): Integer;
begin
  try
    if FpChmod(PChar(aPath), aMode) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ChangeModeByHandle(aHandle: TFileHandle; aMode: Integer): Integer;
begin
  try
    if FpFChmod(aHandle, aMode) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.SetFileTime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer;
var
  LAccessTime, LModificationTime: TTimeVal;
  LTimeArray: array[0..1] of TTimeVal;
begin
  try
    LAccessTime := DateTimeToTimeVal(aAccessTime);
    LModificationTime := DateTimeToTimeVal(aModificationTime);
    LTimeArray[0] := LAccessTime;
    LTimeArray[1] := LModificationTime;

    if FpUtime(PChar(aPath), @LTimeArray) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.SetFileTimeByHandle(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer;
var
  LAccessTime, LModificationTime: TTimeVal;
  LTimeArray: array[0..1] of TTimeVal;
begin
  try
    LAccessTime := DateTimeToTimeVal(aAccessTime);
    LModificationTime := DateTimeToTimeVal(aModificationTime);
    LTimeArray[0] := LAccessTime;
    LTimeArray[1] := LModificationTime;

    if FpFutime(aHandle, @LTimeArray) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.CreateSymlink(const aTarget, aPath: string; aFlags: Integer): Integer;
begin
  try
    if FpSymlink(PChar(aTarget), PChar(aPath)) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ReadSymlink(const aPath: string; out aTarget: string): Integer;
var
  LBuffer: array[0..1023] of Char;
  LBytesRead: Integer;
begin
  try
    LBytesRead := FpReadlink(PChar(aPath), LBuffer, SizeOf(LBuffer) - 1);
    if LBytesRead > 0 then
    begin
      SetLength(aTarget, LBytesRead);
      Move(LBuffer, aTarget[1], LBytesRead);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ReadDirectory(const aPath: string; out aEntries: TArray<string>): Integer;
var
  LDir: PDir;
  LDirEntry: PDirEnt;
  LCount: Integer;
begin
  try
    LCount := 0;
    SetLength(aEntries, 0);

    LDir := FpOpendir(PChar(aPath));
    if LDir = nil then
    begin
      Result := -GetLastErrorCode;
      Exit;
    end;

    try
      repeat
        LDirEntry := FpReaddir(LDir);
        if LDirEntry <> nil then
        begin
          if (LDirEntry^.d_name <> '.') and (LDirEntry^.d_name <> '..') then
          begin
            SetLength(aEntries, LCount + 1);
            aEntries[LCount] := LDirEntry^.d_name;
            Inc(LCount);
          end;
        end;
      until LDirEntry = nil;

      Result := LCount; // 返回条目数量
    finally
      FpClosedir(LDir);
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 新增的 libuv 功能实现 }

class function TUnixFileSystem.FileSync(aHandle: TFileHandle): Integer;
begin
  try
    if FpFsync(aHandle) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.FileDataSync(aHandle: TFileHandle): Integer;
begin
  try
    if FpFdatasync(aHandle) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.FileTruncate(aHandle: TFileHandle; aOffset: Int64): Integer;
begin
  try
    if FpFtruncate(aHandle, aOffset) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.SendFile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer;
var
  LBytesSent: SizeUInt;
begin
  try
    LBytesSent := FpSendfile(aOutHandle, aInHandle, aInOffset, aLength);
    if LBytesSent = SizeUInt(-1) then
      Result := -GetLastErrorCode
    else
      Result := Integer(LBytesSent);
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.RealPath(const aPath: string; out aRealPath: string): Integer;
var
  LBuffer: array[0..1023] of Char;
  LResult: PChar;
begin
  try
    LResult := FpRealpath(PChar(aPath), LBuffer);
    if LResult <> nil then
    begin
      aRealPath := string(LResult);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ChangeOwner(const aPath: string; aUID, aGID: Integer): Integer;
begin
  try
    if FpChown(PChar(aPath), aUID, aGID) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ChangeOwnerByHandle(aHandle: TFileHandle; aUID, aGID: Integer): Integer;
begin
  try
    if FpFchown(aHandle, aUID, aGID) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ChangeOwnerLink(const aPath: string; aUID, aGID: Integer): Integer;
begin
  try
    if FpLchown(PChar(aPath), aUID, aGID) = 0 then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.GetFileSystemStat(const aPath: string; out aStat: TFileStat): Integer;
var
  LStatFS: TStatFS;
begin
  try
    if FpStatFS(PChar(aPath), LStatFS) = 0 then
    begin
      // 填充文件系统统计信息
      aStat.Device := LStatFS.f_fsid;
      aStat.Inode := 0;
      aStat.Mode := 0;
      aStat.NLink := 0;
      aStat.UID := 0;
      aStat.GID := 0;
      aStat.RDev := 0;
      aStat.Size := Int64(LStatFS.f_blocks) * LStatFS.f_bsize;
      aStat.BlockSize := LStatFS.f_bsize;
      aStat.Blocks := LStatFS.f_bavail;
      aStat.AccessTime := Now;
      aStat.ModificationTime := Now;
      aStat.CreationTime := Now;
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.ScanDirectory(const aPath: string; out aEntries: TArray<string>): Integer;
begin
  // Unix 上 scandir 与 readdir 相同
  Result := ReadDirectory(aPath, aEntries);
end;

class function TUnixFileSystem.MakeTempDirectory(const aTemplate: string; out aPath: string): Integer;
var
  LTemplate: string;
  LResult: PChar;
begin
  try
    LTemplate := aTemplate;
    LResult := FpMkdtemp(PChar(LTemplate));
    if LResult <> nil then
    begin
      aPath := string(LResult);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TUnixFileSystem.MakeTempFile(const aTemplate: string; out aPath: string): Integer;
var
  LTemplate: string;
  LHandle: Integer;
begin
  try
    LTemplate := aTemplate;
    LHandle := FpMkstemp(PChar(LTemplate));
    if LHandle <> -1 then
    begin
      aPath := string(LTemplate);
      FpClose(LHandle);
      Result := LHandle;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 私有辅助方法 }

class function TUnixFileSystem.GetFileOpenFlags(aFlags: Integer): Integer;
begin
  case aFlags and 3 of
    0: Result := O_RDONLY;  // O_RDONLY
    1: Result := O_WRONLY;  // O_WRONLY
    2: Result := O_RDWR;    // O_RDWR
    else
      Result := O_RDONLY;
  end;

  if (aFlags and 64) <> 0 then // O_CREAT
  begin
    if (aFlags and 128) <> 0 then // O_EXCL
      Result := Result or O_EXCL
    else
      Result := Result or O_CREAT;
  end;

  if (aFlags and 512) <> 0 then // O_TRUNC
    Result := Result or O_TRUNC;

  if (aFlags and 8) <> 0 then // O_APPEND
    Result := Result or O_APPEND;
end;

class function TUnixFileSystem.GetFileMode(aFlags: Integer): Integer;
begin
  Result := &666; // 默认权限 rw-rw-rw-
end;

class function TUnixFileSystem.DateTimeToTimeVal(aDateTime: TDateTime): TTimeVal;
var
  LUnixTime: Int64;
begin
  LUnixTime := DateTimeToUnix(aDateTime);
  Result.tv_sec := LUnixTime;
  Result.tv_usec := 0;
end;

class function TUnixFileSystem.TimeValToDateTime(aTimeVal: TTimeVal): TDateTime;
begin
  Result := UnixToDateTime(aTimeVal.tv_sec);
end;

class function TUnixFileSystem.StatToFileStat(const aStat: TStat): TFileStat;
begin
  Result.Device := aStat.st_dev;
  Result.Inode := aStat.st_ino;
  Result.Mode := aStat.st_mode;
  Result.NLink := aStat.st_nlink;
  Result.UID := aStat.st_uid;
  Result.GID := aStat.st_gid;
  Result.RDev := aStat.st_rdev;
  Result.Size := aStat.st_size;
  Result.BlockSize := aStat.st_blksize;
  Result.Blocks := aStat.st_blocks;
  Result.AccessTime := UnixToDateTime(aStat.st_atime);
  Result.ModificationTime := UnixToDateTime(aStat.st_mtime);
  Result.CreationTime := UnixToDateTime(aStat.st_ctime);
end;

class function TUnixFileSystem.GetLastErrorCode: Integer;
begin
  Result := fpGetErrno;
end;

end. 