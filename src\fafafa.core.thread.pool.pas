unit fafafa.core.thread.pool;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes, SyncObjs,
  fafafa.core.base,
  fafafa.core.thread.types,
  fafafa.core.thread.future;

type
  // 工作项实现
  TWorkItem = class(TInterfacedObject, IWorkItem)
  private
    FProc: TThreadProc;
    FMethod: TThreadMethod;
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    FRefProc: TThreadRefProc;
    {$ENDIF}
    FCancelled: Boolean;
    FLock: TCriticalSection;
  public
    constructor Create(const aProc: TThreadProc); overload;
    constructor Create(const aMethod: TThreadMethod); overload;
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    constructor Create(const aRefProc: TThreadRefProc); overload;
    {$ENDIF}
    destructor Destroy; override;
    
    procedure Execute;
    function IsCancelled: Boolean;
    procedure Cancel;
  end;

  // 带返回值的工作项
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  generic TFutureWorkItem<T> = class(TInterfacedObject, IWorkItem)
  private
    FFunc: specialize TFunc<T>;
    FPromise: specialize IPromise<T>;
    FCancelled: Boolean;
    FLock: TCriticalSection;
  public
    constructor Create(const aFunc: specialize TFunc<T>; const aPromise: specialize IPromise<T>);
    destructor Destroy; override;
    
    procedure Execute;
    function IsCancelled: Boolean;
    procedure Cancel;
  end;
  {$ENDIF}

  // 工作线程
  TWorkerThread = class(TThread)
  private
    FThreadPool: TObject; // 避免循环引用，使用TObject
    FWorkQueue: specialize IThreadSafeQueue<IWorkItem>;
    FKeepAliveTime: Cardinal;
    FLastWorkTime: QWord;
    FActive: Boolean;
  public
    constructor Create(aThreadPool: TObject; aWorkQueue: specialize IThreadSafeQueue<IWorkItem>; 
      aKeepAliveTime: Cardinal);
    destructor Destroy; override;
    
    procedure Execute; override;
    procedure Shutdown;
    
    property Active: Boolean read FActive;
    property LastWorkTime: QWord read FLastWorkTime;
  end;

  // 线程池实现
  TThreadPool = class(TInterfacedObject, IThreadPool)
  private
    FState: TThreadPoolState;
    FCoreThreads: Integer;
    FMaxThreads: Integer;
    FKeepAliveTime: Cardinal;
    FWorkQueue: specialize IThreadSafeQueue<IWorkItem>;
    FWorkerThreads: TList;
    FLock: TCriticalSection;
    FActiveThreads: Integer;
    FQueuedTasks: Integer;
    
    procedure CreateWorkerThread;
    procedure CleanupIdleThreads;
    function GetWorkerThreadCount: Integer;
    procedure WaitForAllThreads;
  public
    constructor Create(aCoreThreads: Integer = 4; aMaxThreads: Integer = 16; 
      aKeepAliveTimeMs: Cardinal = 60000);
    destructor Destroy; override;
    
    // IThreadPool 接口实现
    procedure Start;
    procedure Shutdown(aWaitForCompletion: Boolean = True);
    
    procedure Execute(const aProc: TThreadProc); overload;
    procedure Execute(const aMethod: TThreadMethod); overload;
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Execute(const aRefProc: TThreadRefProc); overload;
    function Submit<T>(const aFunc: specialize TFunc<T>): specialize IFuture<T>;
    {$ENDIF}
    
    function GetState: TThreadPoolState;
    function GetActiveThreadCount: Integer;
    function GetQueuedTaskCount: Integer;
    function GetCoreThreadCount: Integer;
    function GetMaxThreadCount: Integer;
    
    procedure SetCoreThreadCount(aCount: Integer);
    procedure SetMaxThreadCount(aCount: Integer);
    procedure SetKeepAliveTime(aTimeMs: Cardinal);
  end;

// 工厂函数实现
function CreateThreadPool(aCoreThreads: Integer = 4; aMaxThreads: Integer = 16; 
  aKeepAliveTimeMs: Cardinal = 60000): IThreadPool;

implementation

// TWorkItem 实现

constructor TWorkItem.Create(const aProc: TThreadProc);
begin
  inherited Create;
  FProc := aProc;
  FMethod := nil;
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  FRefProc := nil;
  {$ENDIF}
  FCancelled := False;
  FLock := TCriticalSection.Create;
end;

constructor TWorkItem.Create(const aMethod: TThreadMethod);
begin
  inherited Create;
  FProc := nil;
  FMethod := aMethod;
  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  FRefProc := nil;
  {$ENDIF}
  FCancelled := False;
  FLock := TCriticalSection.Create;
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
constructor TWorkItem.Create(const aRefProc: TThreadRefProc);
begin
  inherited Create;
  FProc := nil;
  FMethod := nil;
  FRefProc := aRefProc;
  FCancelled := False;
  FLock := TCriticalSection.Create;
end;
{$ENDIF}

destructor TWorkItem.Destroy;
begin
  FreeAndNil(FLock);
  inherited Destroy;
end;

procedure TWorkItem.Execute;
begin
  FLock.Enter;
  try
    if FCancelled then
      Exit;
  finally
    FLock.Leave;
  end;
  
  try
    if Assigned(FProc) then
      FProc()
    else if Assigned(FMethod) then
      FMethod()
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    else if Assigned(FRefProc) then
      FRefProc();
    {$ENDIF}
  except
    // 工作项执行异常应该被捕获，避免影响工作线程
    on E: Exception do
    begin
      // 可以在这里记录日志
    end;
  end;
end;

function TWorkItem.IsCancelled: Boolean;
begin
  FLock.Enter;
  try
    Result := FCancelled;
  finally
    FLock.Leave;
  end;
end;

procedure TWorkItem.Cancel;
begin
  FLock.Enter;
  try
    FCancelled := True;
  finally
    FLock.Leave;
  end;
end;

// TFutureWorkItem<T> 实现

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
constructor TFutureWorkItem.Create(const aFunc: specialize TFunc<T>; const aPromise: specialize IPromise<T>);
begin
  inherited Create;
  FFunc := aFunc;
  FPromise := aPromise;
  FCancelled := False;
  FLock := TCriticalSection.Create;
end;

destructor TFutureWorkItem.Destroy;
begin
  FreeAndNil(FLock);
  inherited Destroy;
end;

procedure TFutureWorkItem.Execute;
var
  Result: T;
begin
  FLock.Enter;
  try
    if FCancelled then
    begin
      FPromise.SetException(EFutureCancelled.Create);
      Exit;
    end;
  finally
    FLock.Leave;
  end;
  
  try
    Result := FFunc();
    FPromise.SetValue(Result);
  except
    on E: Exception do
      FPromise.SetException(E);
  end;
end;

function TFutureWorkItem.IsCancelled: Boolean;
begin
  FLock.Enter;
  try
    Result := FCancelled;
  finally
    FLock.Leave;
  end;
end;

procedure TFutureWorkItem.Cancel;
begin
  FLock.Enter;
  try
    FCancelled := True;
  finally
    FLock.Leave;
  end;
end;
{$ENDIF}

// TWorkerThread 实现

constructor TWorkerThread.Create(aThreadPool: TObject; aWorkQueue: specialize IThreadSafeQueue<IWorkItem>; 
  aKeepAliveTime: Cardinal);
begin
  inherited Create(False);
  FThreadPool := aThreadPool;
  FWorkQueue := aWorkQueue;
  FKeepAliveTime := aKeepAliveTime;
  FLastWorkTime := GetTickCount64;
  FActive := True;
  FreeOnTerminate := False;
end;

destructor TWorkerThread.Destroy;
begin
  inherited Destroy;
end;

procedure TWorkerThread.Execute;
var
  WorkItem: IWorkItem;
begin
  while not Terminated and FActive do
  begin
    if FWorkQueue.TryDequeue(WorkItem, 1000) then // 1秒超时
    begin
      FLastWorkTime := GetTickCount64;
      if Assigned(WorkItem) and not WorkItem.IsCancelled then
      begin
        WorkItem.Execute;
      end;
      WorkItem := nil;
    end
    else
    begin
      // 检查是否应该因为空闲时间过长而退出
      if (FKeepAliveTime > 0) and (GetTickCount64 - FLastWorkTime > FKeepAliveTime) then
      begin
        FActive := False;
        Break;
      end;
    end;
  end;
end;

procedure TWorkerThread.Shutdown;
begin
  FActive := False;
  Terminate;
end;

// TThreadPool 实现

constructor TThreadPool.Create(aCoreThreads: Integer; aMaxThreads: Integer;
  aKeepAliveTimeMs: Cardinal);
begin
  inherited Create;
  FState := tpsCreated;
  FCoreThreads := aCoreThreads;
  FMaxThreads := aMaxThreads;
  FKeepAliveTime := aKeepAliveTimeMs;
  FWorkQueue := specialize CreateThreadSafeQueue<IWorkItem>;
  FWorkerThreads := TList.Create;
  FLock := TCriticalSection.Create;
  FActiveThreads := 0;
  FQueuedTasks := 0;
end;

destructor TThreadPool.Destroy;
begin
  if FState = tpsRunning then
    Shutdown(True);
  FreeAndNil(FWorkerThreads);
  FreeAndNil(FLock);
  inherited Destroy;
end;

procedure TThreadPool.Start;
var
  i: Integer;
begin
  FLock.Enter;
  try
    if FState <> tpsCreated then
      raise EThreadPool.Create('ThreadPool already started or shutdown');

    FState := tpsRunning;

    // 创建核心线程
    for i := 0 to FCoreThreads - 1 do
      CreateWorkerThread;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.Shutdown(aWaitForCompletion: Boolean);
var
  i: Integer;
  Thread: TWorkerThread;
begin
  FLock.Enter;
  try
    if FState <> tpsRunning then
      Exit;

    FState := tpsShutdown;

    // 关闭所有工作线程
    for i := 0 to FWorkerThreads.Count - 1 do
    begin
      Thread := TWorkerThread(FWorkerThreads[i]);
      Thread.Shutdown;
    end;

    // 关闭工作队列
    specialize TThreadSafeQueue<IWorkItem>(FWorkQueue).Shutdown;
  finally
    FLock.Leave;
  end;

  if aWaitForCompletion then
    WaitForAllThreads;

  FLock.Enter;
  try
    FState := tpsClosed;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.Execute(const aProc: TThreadProc);
var
  WorkItem: IWorkItem;
begin
  if FState <> tpsRunning then
    raise EThreadPool.Create('ThreadPool is not running');

  WorkItem := TWorkItem.Create(aProc);
  FWorkQueue.Enqueue(WorkItem);

  FLock.Enter;
  try
    Inc(FQueuedTasks);

    // 如果需要，创建新的工作线程
    if (GetWorkerThreadCount < FMaxThreads) and (FQueuedTasks > FActiveThreads) then
      CreateWorkerThread;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.Execute(const aMethod: TThreadMethod);
var
  WorkItem: IWorkItem;
begin
  if FState <> tpsRunning then
    raise EThreadPool.Create('ThreadPool is not running');

  WorkItem := TWorkItem.Create(aMethod);
  FWorkQueue.Enqueue(WorkItem);

  FLock.Enter;
  try
    Inc(FQueuedTasks);

    if (GetWorkerThreadCount < FMaxThreads) and (FQueuedTasks > FActiveThreads) then
      CreateWorkerThread;
  finally
    FLock.Leave;
  end;
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TThreadPool.Execute(const aRefProc: TThreadRefProc);
var
  WorkItem: IWorkItem;
begin
  if FState <> tpsRunning then
    raise EThreadPool.Create('ThreadPool is not running');

  WorkItem := TWorkItem.Create(aRefProc);
  FWorkQueue.Enqueue(WorkItem);

  FLock.Enter;
  try
    Inc(FQueuedTasks);

    if (GetWorkerThreadCount < FMaxThreads) and (FQueuedTasks > FActiveThreads) then
      CreateWorkerThread;
  finally
    FLock.Leave;
  end;
end;

function TThreadPool.Submit<T>(const aFunc: specialize TFunc<T>): specialize IFuture<T>;
var
  Promise: specialize IPromise<T>;
  WorkItem: IWorkItem;
begin
  if FState <> tpsRunning then
    raise EThreadPool.Create('ThreadPool is not running');

  Promise := specialize CreatePromise<T>;
  Result := Promise.GetFuture;

  WorkItem := specialize TFutureWorkItem<T>.Create(aFunc, Promise);
  FWorkQueue.Enqueue(WorkItem);

  FLock.Enter;
  try
    Inc(FQueuedTasks);

    if (GetWorkerThreadCount < FMaxThreads) and (FQueuedTasks > FActiveThreads) then
      CreateWorkerThread;
  finally
    FLock.Leave;
  end;
end;
{$ENDIF}

function TThreadPool.GetState: TThreadPoolState;
begin
  FLock.Enter;
  try
    Result := FState;
  finally
    FLock.Leave;
  end;
end;

function TThreadPool.GetActiveThreadCount: Integer;
begin
  FLock.Enter;
  try
    Result := FActiveThreads;
  finally
    FLock.Leave;
  end;
end;

function TThreadPool.GetQueuedTaskCount: Integer;
begin
  Result := FWorkQueue.Count;
end;

function TThreadPool.GetCoreThreadCount: Integer;
begin
  FLock.Enter;
  try
    Result := FCoreThreads;
  finally
    FLock.Leave;
  end;
end;

function TThreadPool.GetMaxThreadCount: Integer;
begin
  FLock.Enter;
  try
    Result := FMaxThreads;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.SetCoreThreadCount(aCount: Integer);
begin
  FLock.Enter;
  try
    if aCount > 0 then
      FCoreThreads := aCount;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.SetMaxThreadCount(aCount: Integer);
begin
  FLock.Enter;
  try
    if aCount >= FCoreThreads then
      FMaxThreads := aCount;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.SetKeepAliveTime(aTimeMs: Cardinal);
begin
  FLock.Enter;
  try
    FKeepAliveTime := aTimeMs;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadPool.CreateWorkerThread;
var
  Thread: TWorkerThread;
begin
  Thread := TWorkerThread.Create(Self, FWorkQueue, FKeepAliveTime);
  FWorkerThreads.Add(Thread);
  Inc(FActiveThreads);
end;

procedure TThreadPool.CleanupIdleThreads;
var
  i: Integer;
  Thread: TWorkerThread;
begin
  FLock.Enter;
  try
    for i := FWorkerThreads.Count - 1 downto 0 do
    begin
      Thread := TWorkerThread(FWorkerThreads[i]);
      if not Thread.Active then
      begin
        FWorkerThreads.Delete(i);
        Thread.WaitFor;
        Thread.Free;
        Dec(FActiveThreads);
      end;
    end;
  finally
    FLock.Leave;
  end;
end;

function TThreadPool.GetWorkerThreadCount: Integer;
begin
  Result := FWorkerThreads.Count;
end;

procedure TThreadPool.WaitForAllThreads;
var
  i: Integer;
  Thread: TWorkerThread;
begin
  FLock.Enter;
  try
    for i := 0 to FWorkerThreads.Count - 1 do
    begin
      Thread := TWorkerThread(FWorkerThreads[i]);
      Thread.WaitFor;
      Thread.Free;
    end;
    FWorkerThreads.Clear;
    FActiveThreads := 0;
  finally
    FLock.Leave;
  end;
end;

// 工厂函数实现

function CreateThreadPool(aCoreThreads: Integer; aMaxThreads: Integer;
  aKeepAliveTimeMs: Cardinal): IThreadPool;
begin
  Result := TThreadPool.Create(aCoreThreads, aMaxThreads, aKeepAliveTimeMs);
end;

end.
