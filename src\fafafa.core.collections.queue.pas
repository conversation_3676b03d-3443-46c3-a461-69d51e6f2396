unit fafafa.core.collections.queue;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  fafafa.core.collections.base;


type

  { IQueue 泛型队列接口 }
  generic IQueue<T> = interface(specialize IGenericCollection<T>)
  ['{8D2A4A2F-3C7C-4E94-A763-6E2E7D6C5D37}']
    
    {**
     * Enqueue
     *
     * @desc 将元素添加到队列尾部 (入队)
     * 
     * @params
     *   aElement 要入队的元素
     *
     * @remark 该方法将一个元素添加到队列尾部。
     *}
    procedure Enqueue(const aElement: T);

    {**
     * Push (别名)
     *
     * @desc 别名: 等同于 Enqueue
     *}
    procedure Push(const aElement: T);

    {**
     * Dequeue
     *
     * @desc 移除并返回队列头部的元素 (出队)
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法移除队列头部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function  Dequeue: T; overload;

    {**
     * Pop (别名)
     *
     * @desc 别名: 等同于 Dequeue
     *}
    function  Pop: T; overload;

    {**
     * Dequeue
     *
     * @desc 尝试移除队列头部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试移除队列头部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function  Dequeue(var aElement: T): Boolean; overload;

    {**
     * Pop (别名)
     *
     * @desc 别名: 等同于 Dequeue
     *}
    function  Pop(var aElement: T): Boolean; overload; inline;

    {**
     * Peek
     *
     * @desc 获取队列头部元素但不移除
     *
     * @return 返回队列头部元素
     *
     * @remark
     *   该方法返回队列头部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function  Peek: T; overload;

    {**
     * Peek
     *
     * @desc 尝试获取队列头部元素但不移除
     *
     * @params
     *   aElement 用于存储队列头部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列头部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function Peek(var aElement: T): Boolean; overload;

  end;




implementation

end.