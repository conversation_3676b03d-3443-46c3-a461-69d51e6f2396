name: Sanitizer checks

on:
  pull_request:
    paths:
      - '**'
      - '!docs/**'
      - '!.**'
      - '.github/workflows/sanitizer.yml'
  push:
    branches:
      - v[0-9].*
      - master

jobs:
  sanitizers-linux:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        config:
          - name: ASAN
            flags: -DASAN=ON -DCMAKE_BUILD_TYPE=Debug
          - name: MSAN
            flags: -DMSAN=ON -DCMAKE_BUILD_TYPE=Debug -DCMAKE_C_COMPILER=clang
          - name: TSAN
            flags: -DTSAN=ON -DCMAKE_BUILD_TYPE=Release
          - name: UBSAN
            flags: -DUBSAN=ON -DCMAKE_BUILD_TYPE=Debug -DCMAKE_C_COMPILER=clang
    steps:
      - uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt-get install ninja-build
      - name: Envinfo
        run: npx envinfo

      # [AM]SAN fail on newer kernels due to a bigger PIE slide
      - name: Disable ASLR
        run: |
          sudo sysctl -w kernel.randomize_va_space=0

      - name: Build ${{ matrix.config.name }}
        run: |
          cmake -B build -G Ninja -DBUILD_TESTING=ON ${{ matrix.config.flags }}
          cmake --build build

      - name: Test ${{ matrix.config.name }}
        run: |
          # Note: path must be absolute because some tests chdir.
          # TSan exits with an error when it can't find the file.
          if [ "${{ matrix.config.name }}" = "TSAN" ]; then
            env TSAN_OPTIONS="suppressions=$PWD/tsansupp.txt" ./build/uv_run_tests_a
          else
            ./build/uv_run_tests_a
          fi

  sanitizers-macos:
    runs-on: macos-13
    strategy:
      matrix:
        config:
          - name: ASAN
            flags: -DASAN=ON -DCMAKE_BUILD_TYPE=Debug
          - name: TSAN
            flags: -DTSAN=ON -DCMAKE_BUILD_TYPE=Release
          - name: UBSAN
            flags: -DUBSAN=ON -DCMAKE_BUILD_TYPE=Debug
    steps:
      - uses: actions/checkout@v4

      - name: Envinfo
        run: npx envinfo

      - name: Build ${{ matrix.config.name }}
        run: |
          cmake -B build -DBUILD_TESTING=ON ${{ matrix.config.flags }}
          cmake --build build
      - name: Test ${{ matrix.config.name }}
        run: ./build/uv_run_tests_a

  sanitizers-windows:
    runs-on: windows-2022
    steps:
      - uses: actions/checkout@v4
      - name: Setup
        run: |
          choco install ninja

      # Note: clang shipped with VS2022 has an issue where the UBSAN runtime doesn't link.
      - name: Install LLVM and Clang
        uses: KyleMayes/install-llvm-action@v2
        with:
          version: "17"

      - name: Envinfo
        run: npx envinfo

      - name: UBSAN Build
        run: |
          cmake -B build-ubsan -G Ninja -DBUILD_TESTING=ON -DUBSAN=ON -DCMAKE_BUILD_TYPE=Debug -DCMAKE_C_COMPILER=clang
          cmake --build build-ubsan
      - name: UBSAN Test
        run: |
          ./build-ubsan/uv_run_tests_a
