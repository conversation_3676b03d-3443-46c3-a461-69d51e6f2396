unit test_collections.elementManager;

{$MODE OBJFPC}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  fafafa.core.base,
  fafafa.core.collections.elementManager,
  fafafa.core.mem.allocator;

type

  { TMathTest }

  { TTestCase_ElementManager }

  TTestCase_ElementManager = class(TTestCase)
  published
    procedure Test_Create;
    procedure Test_Create_Allocator;
    procedure Test_GetElementSize;
    procedure Test_GetIsManagedType;
    procedure Test_GetElementTypeInfo;
    procedure Test_GetAllocator;
    procedure Test_AllocElements;
    procedure Test_AllocElement;
    procedure Test_ReallocElements;
    procedure Test_FreeElements;
    procedure Test_FreeElement;
    procedure Test_CopyElements;
    procedure Test_CopyElementsNonOverlap;
    procedure Test_FillElements;
    procedure Test_ZeroElements;
    procedure Test_IsOverlap;
    
   end;

implementation


procedure TTestCase_ElementManager.Test_Create;
var
  LManager: specialize IElementManager<Integer>;
begin
  LManager := specialize TElementManager<Integer>.Create;
  AssertNotNull('ElementManager should be created successfully', LManager);
end;

procedure TTestCase_ElementManager.Test_Create_Allocator;
var
  LManager: specialize IElementManager<Integer>;
begin
  LManager := specialize TElementManager<Integer>.Create(GetRtlAllocator);
  AssertNotNull('ElementManager should be created successfully', LManager);
end;

procedure TTestCase_ElementManager.Test_GetElementSize;
var
  LInt8Manager:  specialize IElementManager<Int8>;
  LInt16Manager: specialize IElementManager<Int16>;
  LInt32Manager: specialize IElementManager<Int32>;
  LInt64Manager: specialize IElementManager<Int64>;
begin
  LInt8Manager  := specialize TElementManager<Int8>.Create;
  LInt16Manager := specialize TElementManager<Int16>.Create;
  LInt32Manager := specialize TElementManager<Int32>.Create;
  LInt64Manager := specialize TElementManager<Int64>.Create;

  AssertEquals('Element size should be equal to 1', 1, LInt8Manager.ElementSize);
  AssertEquals('Element size should be equal to 2', 2, LInt16Manager.ElementSize);
  AssertEquals('Element size should be equal to 4', 4, LInt32Manager.ElementSize);
  AssertEquals('Element size should be equal to 8', 8, LInt64Manager.ElementSize);
end;

procedure TTestCase_ElementManager.Test_GetIsManagedType;
var
  LInt8Manager:   specialize IElementManager<Int8>;
  LInt16Manager:  specialize IElementManager<Int16>;
  LInt32Manager:  specialize IElementManager<Int32>;
  LInt64Manager:  specialize IElementManager<Int64>;
  LFloatManager:  specialize IElementManager<Single>;
  LDoubleManager: specialize IElementManager<Double>;
  LStringManager: specialize IElementManager<String>;
begin
  LInt8Manager   := specialize TElementManager<Int8>.Create;
  LInt16Manager  := specialize TElementManager<Int16>.Create;
  LInt32Manager  := specialize TElementManager<Int32>.Create;
  LInt64Manager  := specialize TElementManager<Int64>.Create;
  LFloatManager  := specialize TElementManager<Single>.Create;
  LDoubleManager := specialize TElementManager<Double>.Create;
  LStringManager := specialize TElementManager<String>.Create;

  AssertFalse('Int8 is not managed type', LInt8Manager.IsManagedType);
  AssertFalse('Int16 is not managed type', LInt16Manager.IsManagedType);
  AssertFalse('Int32 is not managed type', LInt32Manager.IsManagedType);
  AssertFalse('Int64 is not managed type', LInt64Manager.IsManagedType);
  AssertFalse('Float is not managed type', LFloatManager.IsManagedType);
  AssertFalse('Double is not managed type', LDoubleManager.IsManagedType);
  AssertTrue('String is managed type', LStringManager.IsManagedType);
end;


procedure TTestCase_ElementManager.Test_GetElementTypeInfo;
var
  LIntManager: specialize IElementManager<Integer>;
  LStrManager: specialize IElementManager<String>;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  AssertNotNull('Type info should not be nil', LIntManager.ElementTypeInfo);
  AssertTrue('Type info should be equal to system.typeinfo(Integer)', system.typeinfo(Integer) = LIntManager.ElementTypeInfo);

  LStrManager := specialize TElementManager<String>.Create;
  AssertNotNull('Type info should not be nil', LStrManager.ElementTypeInfo);
  AssertTrue('Type info should be equal to system.typeinfo(String)', system.typeinfo(String) = LStrManager.ElementTypeInfo);
end;

function getMem_cb(aSize: PtrUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function allocMem_cb(aSize: PtrUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function reallocMem_cb(aPtr: Pointer; aSize: PtrUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure freeMem_cb(aPtr: Pointer);
begin
  Freemem(aPtr);
end;


function createAllocator: TAllocator;
begin
  Result := TCallbackAllocator.Create(@getMem_cb, @allocMem_cb, @reallocMem_cb, @freeMem_cb);
end;

procedure TTestCase_ElementManager.Test_GetAllocator;
var
  LIntManager: specialize IElementManager<Integer>;
  LMemAllocator: TAllocator;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  AssertNotNull('Mem allocator should not be nil', LIntManager.Allocator);
  AssertTrue('Mem allocator should be equal to RtlMemAllocator', LIntManager.Allocator = GetRtlAllocator);

  LMemAllocator := createAllocator();
  try
    LIntManager := specialize TElementManager<Integer>.Create(LMemAllocator);
    AssertNotNull('Mem allocator should not be nil', LIntManager.Allocator);
    AssertTrue('Mem allocator should be equal to RtlMemAllocator', LIntManager.Allocator = LMemAllocator);
  finally
    LMemAllocator.Free;
  end;
end;

procedure TTestCase_ElementManager.Test_AllocElements;
var
  LIntManager: specialize IElementManager<Integer>;
  LP:          PInteger;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  AssertNotNull('Allocator should be created successfully', LIntManager);
  AssertEquals('Allocated memory should be 4 bytes', 4, LIntManager.ElementSize);

  LP := LIntManager.AllocElements(1);
  AssertNotNull('Allocated memory should not be nil', LP);
  LP^ := 123;
  AssertEquals('Allocated memory should be 123', 123, LP^);
  LIntManager.FreeElements(LP, 1);

  LP := LIntManager.AllocElements(4);
  AssertNotNull('Allocated memory should not be nil', LP);
  LP[0] := 123;
  LP[1] := 456;
  LP[2] := 789;
  LP[3] := 100;
  AssertEquals('Allocated memory should be 123', 123, LP[0]);
  AssertEquals('Allocated memory should be 456', 456, LP[1]);
  AssertEquals('Allocated memory should be 789', 789, LP[2]);
  AssertEquals('Allocated memory should be 100', 100, LP[3]);

  // 空操作原则测试: 0
  LIntManager.InitializeElements(LP, 0);
  AssertEquals('Allocated memory should be 123', 123, LP[0]);
  AssertEquals('Allocated memory should be 456', 456, LP[1]);
  AssertEquals('Allocated memory should be 789', 789, LP[2]);
  AssertEquals('Allocated memory should be 100', 100, LP[3]);

  LIntManager.FreeElements(LP, 4);
  

  {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  { 异常测试: InitializeElements with nil pointer }
  AssertException(
    'InitializeElements should raise ENil for nil pointer', 
    EArgumentNil,
    procedure
    begin
      LIntManager.InitializeElements(nil, 1);
    end
  );

  { 异常测试: FinalizeManagedElements with nil pointer }
  AssertException('FinalizeManagedElements should raise EArgumentNil for nil pointer', EArgumentNil,
    procedure
    begin
      LIntManager.FinalizeManagedElements(nil, 1);
    end
  );
  {$ENDIF}
end;

procedure TTestCase_ElementManager.Test_AllocElement;
var
  LIntManager:    specialize IElementManager<Integer>;
  LPInt:            PInteger;
  LInt64Manager:  specialize IElementManager<Int64>;
  LPInt64:          PInt64;
  LStringManager: specialize IElementManager<String>;
  LPString:         PString;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt := LIntManager.AllocElement;
  AssertNotNull('Allocated memory should not be nil', LPInt);
  LPInt^ := MaxInt;
  AssertEquals('Allocated memory should be maxint', MaxInt, LPInt^);
  LIntManager.FreeElement(LPInt);

  LInt64Manager := specialize TElementManager<Int64>.Create;
  LPInt64 := LInt64Manager.AllocElement;
  AssertNotNull('Allocated memory should not be nil', LPInt64);
  LPInt64^ := high(Int64);
  AssertEquals('Allocated memory should be maxint64', high(Int64), LPInt64^);
  LInt64Manager.FreeElement(LPInt64);

  LStringManager := specialize TElementManager<String>.Create;
  LPString := LStringManager.AllocElement;
  AssertNotNull('Allocated memory should not be nil', LPString);
  LPString^ := 'Hello, World!';
  AssertEquals('Allocated memory should be "Hello, World!"', 'Hello, World!', LPString^);
  LStringManager.FreeElement(LPString);
end;

procedure TTestCase_ElementManager.Test_ReallocElements;
var
  LIntManager: specialize IElementManager<Integer>;
  LPInt: PInteger;
  LStringManager: specialize IElementManager<String>;
  LPString: PString;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt         := LIntManager.AllocElements(1);
  AssertNotNull('Reallocated memory should not be nil', LPInt);
  LPInt^ := 123;

  { 扩容到2个元素 }
  LPInt := LIntManager.ReallocElements(LPInt, 1, 2);
  AssertNotNull('Reallocated memory should not be nil', LPInt);
  AssertEquals('Reallocated memory should be 123', 123, LPInt[0]);
  LPInt[1] := 456;
  AssertEquals('Reallocated memory should be 456', 456, LPInt[1]);

  { 扩容到4个元素 }
  LPInt := LIntManager.ReallocElements(LPInt, 2, 4);
  AssertNotNull('Reallocated memory should not be nil', LPInt);
  AssertEquals('Reallocated memory should be 123', 123, LPInt[0]);
  AssertEquals('Reallocated memory should be 456', 456, LPInt[1]);
  LPInt[2] := 789;
  LPInt[3] := 100;
  AssertEquals('Reallocated memory should be 789', 789, LPInt[2]);
  AssertEquals('Reallocated memory should be 100', 100, LPInt[3]);

  { 缩容到2个元素 }
  LPInt := LIntManager.ReallocElements(LPInt, 4, 2);
  AssertNotNull('Reallocated memory should not be nil', LPInt);
  AssertEquals('Reallocated memory should be 123', 123, LPInt[0]);
  AssertEquals('Reallocated memory should be 456', 456, LPInt[1]);

  { 缩容到1个元素 }
  LPInt := LIntManager.ReallocElements(LPInt, 2, 1);
  AssertNotNull('Reallocated memory should not be nil', LPInt);
  AssertEquals('Reallocated memory should be 123', 123, LPInt[0]);

  { 缩容到0个元素(释放) }
  LPInt := LIntManager.ReallocElements(LPInt, 1, 0);
  AssertNull('Reallocated memory should be nil', LPInt);

  { 调整nil(分配) }
  LPInt := LIntManager.ReallocElements(nil, 0, 8);
  AssertNotNull('Reallocated memory should not be nil', LPInt);

  { 缩容到0个元素(释放)}
  LPInt := LIntManager.ReallocElements(LPInt, 8, 0);
  AssertNull('Reallocated memory should be nil', LPInt);

  { 托管元素 }

  LStringManager := specialize TElementManager<String>.Create;
  LPString := LStringManager.AllocElements(1);
  AssertNotNull('Reallocated memory should not be nil', LPString);
  LPString[0] := 'Hello, World!';

  { 扩容到2个元素 }
  LPString := LStringManager.ReallocElements(LPString, 1, 2);
  AssertNotNull('Reallocated memory should not be nil', LPString);
  AssertEquals('Reallocated memory should be "Hello, World!"', 'Hello, World!', LPString[0]);
  LPString[1] := 'what?';
  AssertEquals('Reallocated memory should be "what?"', 'what?', LPString[1]);

  { 扩容到4个元素 }
  LPString := LStringManager.ReallocElements(LPString, 2, 4);
  AssertNotNull('Reallocated memory should not be nil', LPString);
  AssertEquals('Reallocated memory should be "Hello, World!"', 'Hello, World!', LPString[0]);
  AssertEquals('Reallocated memory should be "what?"', 'what?', LPString[1]);
  LPString[2] := 'this is a test';
  LPString[3] := 'that is not fine';
  AssertEquals('Reallocated memory should be "this is a test"', 'this is a test', LPString[2]);
  AssertEquals('Reallocated memory should be "that is not fine"', 'that is not fine', LPString[3]);

  { 缩容到2个元素 }
  LPString := LStringManager.ReallocElements(LPString, 4, 2);
  AssertNotNull('Reallocated memory should not be nil', LPString);
  AssertEquals('Reallocated memory should be "Hello, World!"', 'Hello, World!', LPString[0]);
  AssertEquals('Reallocated memory should be "what?"', 'what?', LPString[1]);

  { 缩容到1个元素 }
  LPString := LStringManager.ReallocElements(LPString, 2, 1);
  AssertNotNull('Reallocated memory should not be nil', LPString);
  AssertEquals('Reallocated memory should be "Hello, World!"', 'Hello, World!', LPString[0]);

  { 缩容到0个元素(释放) }
  LPString := LStringManager.ReallocElements(LPString, 1, 0);
  AssertNull('Reallocated memory should be nil', LPString);

  { 调整nil(分配) }
  LPString := LStringManager.ReallocElements(nil, 0, 8);
  AssertNotNull('Reallocated memory should not be nil', LPString);

  { 缩容到0个元素(释放)}
  LPString := LStringManager.ReallocElements(LPString, 8, 0);
  AssertNull('Reallocated memory should be nil', LPString);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: aDst = nil 且 aNewElementCount = 0 }
  AssertException('Should raise EWow for nil destination and zero new count', EWow,
    procedure
    begin
      LIntManager.ReallocElements(nil, 0, 0);
    end
  );

  AssertException('Should raise EWow for nil destination and zero new count (Managed)', EWow,
    procedure
    begin
      LStringManager.ReallocElements(nil, 0, 0);
    end
  );
  {$ENDIF}
end;

procedure TTestCase_ElementManager.Test_FreeElements;
var
  LIntManager: specialize IElementManager<Integer>;
  LP: PInteger;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  LP := LIntManager.AllocElements(10);
  AssertNotNull('Pointer should not be nil before FreeElements', LP);

  // 正常释放
  LIntManager.FreeElements(LP, 10);
  // 在测试环境中，我们无法断言指针本身是否为 nil，因为 FreeMem 不会改变指针变量的值
  // 但我们确保了调用不产生异常

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: FreeElements with nil pointer }
  AssertException('FreeElements should raise ENil for nil pointer', ENil,
    procedure
    begin
      LIntManager.FreeElements(nil, 1);
    end
  );

  { 异常测试: FreeElements with zero count }
  LP := LIntManager.AllocElements(1); // 重新分配以测试
  try
    AssertException('FreeElements should raise EIsZero for zero count', EIsZero,
      procedure
      begin
        LIntManager.FreeElements(LP, 0);
      end
    );
  finally
    LIntManager.FreeElements(LP, 1); // 清理
  end;
  {$ENDIF}
end;

procedure TTestCase_ElementManager.Test_FreeElement;
var
  LIntManager: specialize IElementManager<Integer>;
  LP: PInteger;
begin
  LIntManager := specialize TElementManager<Integer>.Create;
  LP := LIntManager.AllocElement;
  AssertNotNull('Pointer should not be nil before FreeElement', LP);

  // 正常释放
  LIntManager.FreeElement(LP);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: FreeElement with nil pointer }
  AssertException('FreeElement should raise ENil for nil pointer', ENil,
    procedure
    begin
      LIntManager.FreeElement(nil);
    end
  );
  {$ENDIF}

end;

procedure TTestCase_ElementManager.Test_CopyElements;
const
  COUNT_BIG = 1024;
var
  LIntManager:    specialize IElementManager<Integer>;
  LPInt:            PInteger;
  LStringManager: specialize IElementManager<String>;
  LPString:         PString;
  i:                Integer;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 自身复制 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt         := LIntManager.AllocElements(4);
  LPInt[0]      := 0;
  LPInt[1]      := 1;
  LPInt[2]      := 2;
  LPInt[3]      := 3;
  
  AssertException(
   'exception should be raised: ESame',
   ESame,
   procedure
   begin
     LIntManager.CopyElements(LPInt, LPInt, 4);
   end);
  
  LIntManager.FreeElements(LPInt, 4);
  
  { 异常测试: 自身复制 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString         := LStringManager.AllocElements(4);
  LPString[0]      := '0';
  LPString[1]      := '1';
  LPString[2]      := '2';
  LPString[3]      := '3';
  
  AssertException(
   'exception should be raised: ESame',
   ESame,
   procedure
   begin
     LStringManager.CopyElements(LPString, LPString, 4);
   end);
  
  LStringManager.FreeElements(LPString, 4);
  {$ENDIF}

  { 正向无重叠 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt         := LIntManager.AllocElements(8);
  LPInt[0]      := 0;
  LPInt[1]      := 1;
  LPInt[2]      := 2;
  LPInt[3]      := 3;
  LPInt[4]      := 4;
  LPInt[5]      := 5;
  LPInt[6]      := 6;
  LPInt[7]      := 7;

  LIntManager.CopyElements(@LPInt[0], @LPInt[4], 4);
  AssertEquals('CopyElements should be 01234567', 0, LPInt[0]);
  AssertEquals('CopyElements should be 01234567', 1, LPInt[1]);
  AssertEquals('CopyElements should be 01234567', 2, LPInt[2]);
  AssertEquals('CopyElements should be 01234567', 3, LPInt[3]);
  AssertEquals('CopyElements should be 01234567', 0, LPInt[4]);
  AssertEquals('CopyElements should be 01234567', 1, LPInt[5]);
  AssertEquals('CopyElements should be 01234567', 2, LPInt[6]);
  AssertEquals('CopyElements should be 01234567', 3, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 正向无重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString         := LStringManager.AllocElements(8);
  LPString[0]      := '0';
  LPString[1]      := '1';
  LPString[2]      := '2';
  LPString[3]      := '3';
  LPString[4]      := '4';
  LPString[5]      := '5';
  LPString[6]      := '6';
  LPString[7]      := '7';

  LStringManager.CopyElements(@LPString[0], @LPString[4], 4);
  AssertEquals('CopyElements should be 01234567', '0', LPString[0]);
  AssertEquals('CopyElements should be 01234567', '1', LPString[1]);
  AssertEquals('CopyElements should be 01234567', '2', LPString[2]);
  AssertEquals('CopyElements should be 01234567', '3', LPString[3]);
  AssertEquals('CopyElements should be 01234567', '0', LPString[4]);
  AssertEquals('CopyElements should be 01234567', '1', LPString[5]);
  AssertEquals('CopyElements should be 01234567', '2', LPString[6]);
  AssertEquals('CopyElements should be 01234567', '3', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 正向无重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPInt[i] := i;

  LIntManager.CopyElements(@LPInt[0], @LPInt[COUNT_BIG div 2], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals(IntToStr(i) + ' CopyElements should be ' + IntToStr(i), i, LPInt[i]);

  for i := COUNT_BIG div 2 to pred(COUNT_BIG) do
    AssertEquals(IntToStr(i) + ' CopyElements should be ' + IntToStr(i - COUNT_BIG div 2), i - COUNT_BIG div 2, LPInt[i]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 正向无重叠 大缓冲区 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  LStringManager.CopyElements(@LPString[0], @LPString[COUNT_BIG div 2], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals(IntToStr(i) + ' CopyElements should be ' + IntToStr(i), IntToStr(i), LPString[i]);

  for i := COUNT_BIG div 2 to pred(COUNT_BIG) do
    AssertEquals(IntToStr(i) + ' CopyElements should be ' + IntToStr(i - COUNT_BIG div 2), IntToStr(i - COUNT_BIG div 2), LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  {
    正向大重叠 L=6
    01234567  01123456
     SD
  }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElements(@LPInt[1], @LPInt[2], 6);
  AssertEquals('CopyElements should be 01123456', 0, LPInt[0]);
  AssertEquals('CopyElements should be 01123456', 1, LPInt[1]);
  AssertEquals('CopyElements should be 01123456', 1, LPInt[2]);
  AssertEquals('CopyElements should be 01123456', 2, LPInt[3]);
  AssertEquals('CopyElements should be 01123456', 3, LPInt[4]);
  AssertEquals('CopyElements should be 01123456', 4, LPInt[5]);
  AssertEquals('CopyElements should be 01123456', 5, LPInt[6]);
  AssertEquals('CopyElements should be 01123456', 6, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 正向大重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElements(@LPString[1], @LPString[2], 6);
  AssertEquals('CopyElements should be 01123456', '0', LPString[0]);
  AssertEquals('CopyElements should be 01123456', '1', LPString[1]);
  AssertEquals('CopyElements should be 01123456', '1', LPString[2]);
  AssertEquals('CopyElements should be 01123456', '2', LPString[3]);
  AssertEquals('CopyElements should be 01123456', '3', LPString[4]);
  AssertEquals('CopyElements should be 01123456', '4', LPString[5]);
  AssertEquals('CopyElements should be 01123456', '5', LPString[6]);
  AssertEquals('CopyElements should be 01123456', '6', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 正向大重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt         := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to COUNT_BIG - 1 do
    LPInt[i] := i;

  AssertEquals('CopyElements should be 0', 0, LPInt[0]);
  AssertEquals('CopyElements should be '+ intToStr(COUNT_BIG - 1), COUNT_BIG - 1, LPInt[COUNT_BIG - 1]);

  LIntManager.CopyElements(@LPInt[1], @LPInt[2], COUNT_BIG - 2);
  AssertEquals('CopyElements should be 0', 0, LPInt[0]);
  AssertEquals('CopyElements should be 1', 1, LPInt[1]);
  AssertEquals('CopyElements should be 1', 1, LPInt[2]);
  AssertEquals('CopyElements should be '+ IntToStr(i - 2), i - 2, LPInt[i - 1]);

  for i := 2 to pred(COUNT_BIG) - 1 do
    AssertEquals(IntToStr(i) + ' CopyElements should be ' + IntToStr(i - 1), i - 1, LPInt[i]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 正向大重叠 大缓冲区 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString         := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  AssertEquals('CopyElements should be 0', '0', LPString[0]);
  AssertEquals('CopyElements should be '+ intToStr(COUNT_BIG - 1), IntToStr(COUNT_BIG - 1), LPString[COUNT_BIG - 1]);

  //0,1,2..1023 -> 0,1,1,2..1022
  LStringManager.CopyElements(@LPString[1], @LPString[2], COUNT_BIG - 2);
  AssertEquals('CopyElements should be 0', '0', LPString[0]);
  AssertEquals('CopyElements should be 1', '1', LPString[1]);
  AssertEquals('CopyElements should be 1', '1', LPString[2]);
  AssertEquals('CopyElements should be '+ IntToStr(i - 2), IntToStr(i - 2), LPString[i - 1]);

  for i := 2 to pred(COUNT_BIG) do
    AssertEquals('CopyElements should be ' + IntToStr(i - 1), IntToStr(i - 1), LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 
    正向小重叠 L=4
    01234567  01231234
     S  D
  }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt         := LIntManager.AllocElements(8);
  LPInt[0]      := 0;
  LPInt[1]      := 1;
  LPInt[2]      := 2;
  LPInt[3]      := 3;
  LPInt[4]      := 4;
  LPInt[5]      := 5;
  LPInt[6]      := 6;
  LPInt[7]      := 7;

  LIntManager.CopyElements(@LPInt[1], @LPInt[4], 4);
  AssertEquals('CopyElements should be 01231234', 0, LPInt[0]);
  AssertEquals('CopyElements should be 01231234', 1, LPInt[1]);
  AssertEquals('CopyElements should be 01231234', 2, LPInt[2]);
  AssertEquals('CopyElements should be 01231234', 3, LPInt[3]);
  AssertEquals('CopyElements should be 01231234', 1, LPInt[4]);
  AssertEquals('CopyElements should be 01231234', 2, LPInt[5]);
  AssertEquals('CopyElements should be 01231234', 3, LPInt[6]);
  AssertEquals('CopyElements should be 01231234', 4, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 正向小重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString         := LStringManager.AllocElements(8);
  LPString[0]      := '0';
  LPString[1]      := '1';
  LPString[2]      := '2';
  LPString[3]      := '3';
  LPString[4]      := '4';
  LPString[5]      := '5';
  LPString[6]      := '6';
  LPString[7]      := '7';

  LStringManager.CopyElements(@LPString[1], @LPString[4], 4);
  AssertEquals('CopyElements should be 01231234', '0', LPString[0]);
  AssertEquals('CopyElements should be 01231234', '1', LPString[1]);
  AssertEquals('CopyElements should be 01231234', '2', LPString[2]);
  AssertEquals('CopyElements should be 01231234', '3', LPString[3]);
  AssertEquals('CopyElements should be 01231234', '1', LPString[4]);
  AssertEquals('CopyElements should be 01231234', '2', LPString[5]);
  AssertEquals('CopyElements should be 01231234', '3', LPString[6]);
  AssertEquals('CopyElements should be 01231234', '4', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 正向小重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPInt[i] := i;

  LIntManager.CopyElements(@LPInt[0], @LPInt[COUNT_BIG div 4], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 4) do
    AssertEquals('CopyElements int (before overlap)', i, LPInt[i]);

  for i := COUNT_BIG div 4 to pred(COUNT_BIG div 2) do
    AssertEquals(IntToStr(i) + ' CopyElements (overlap) should be ' + IntToStr(i - COUNT_BIG div 4), i - COUNT_BIG div 4, LPInt[i]);
    
  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 正向小重叠 大缓冲区 托管元素 }

  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  LStringManager.CopyElements(@LPString[0], @LPString[COUNT_BIG div 4], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 4) do
    AssertEquals('CopyElements string (before overlap)', IntToStr(i), LPString[i]);

  for i := COUNT_BIG div 4 to pred(COUNT_BIG div 2) do
    AssertEquals('CopyElements string (overlap)', IntToStr(i - COUNT_BIG div 4), LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 反向无重叠 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElements(@LPInt[4], @LPInt[0], 4);
  AssertEquals('CopyElements should be 4', 4, LPInt[0]);
  AssertEquals('CopyElements should be 5', 5, LPInt[1]);
  AssertEquals('CopyElements should be 6', 6, LPInt[2]);
  AssertEquals('CopyElements should be 7', 7, LPInt[3]);
  AssertEquals('CopyElements should be 4', 4, LPInt[4]);
  AssertEquals('CopyElements should be 5', 5, LPInt[5]);
  AssertEquals('CopyElements should be 6', 6, LPInt[6]);
  AssertEquals('CopyElements should be 7', 7, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 反向无重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElements(@LPString[4], @LPString[0], 4);
  AssertEquals('CopyElements should be 4', '4', LPString[0]);
  AssertEquals('CopyElements should be 5', '5', LPString[1]);
  AssertEquals('CopyElements should be 6', '6', LPString[2]);
  AssertEquals('CopyElements should be 7', '7', LPString[3]);
  AssertEquals('CopyElements should be 4', '4', LPString[4]);
  AssertEquals('CopyElements should be 5', '5', LPString[5]);
  AssertEquals('CopyElements should be 6', '6', LPString[6]);
  AssertEquals('CopyElements should be 7', '7', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 反向无重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPInt[i] := i;

  LIntManager.CopyElements(@LPInt[COUNT_BIG div 2], @LPInt[0], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals('CopyElements int (reverse, non-overlap)', i + COUNT_BIG div 2, LPInt[i]);

  for i := COUNT_BIG div 2 to pred(COUNT_BIG) do
    AssertEquals('CopyElements int (original)', i, LPInt[i]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 反向无重叠 大缓冲区 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  LStringManager.CopyElements(@LPString[COUNT_BIG div 2], @LPString[0], COUNT_BIG div 2);

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals('CopyElements string (reverse, non-overlap)', IntToStr(i + COUNT_BIG div 2), LPString[i]);

  for i := COUNT_BIG div 2 to pred(COUNT_BIG) do
    AssertEquals('CopyElements string (original)', IntToStr(i), LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 反向大重叠}
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElements(@LPInt[4], @LPInt[1], 4);
  AssertEquals('CopyElements should be 0', 0, LPInt[0]);
  AssertEquals('CopyElements should be 4', 4, LPInt[1]);
  AssertEquals('CopyElements should be 5', 5, LPInt[2]);
  AssertEquals('CopyElements should be 6', 6, LPInt[3]);
  AssertEquals('CopyElements should be 7', 7, LPInt[4]);
  AssertEquals('CopyElements should be 5', 5, LPInt[5]);
  AssertEquals('CopyElements should be 6', 6, LPInt[6]);
  AssertEquals('CopyElements should be 7', 7, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 反向大重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElements(@LPString[4], @LPString[1], 4);
  AssertEquals('CopyElements should be 0', '0', LPString[0]);
  AssertEquals('CopyElements should be 4', '4', LPString[1]);
  AssertEquals('CopyElements should be 5', '5', LPString[2]);
  AssertEquals('CopyElements should be 6', '6', LPString[3]);
  AssertEquals('CopyElements should be 7', '7', LPString[4]);
  AssertEquals('CopyElements should be 5', '5', LPString[5]);
  AssertEquals('CopyElements should be 6', '6', LPString[6]);
  AssertEquals('CopyElements should be 7', '7', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

 { 反向大重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPInt[i] := i;

  LIntManager.CopyElements(@LPInt[COUNT_BIG div 2], @LPInt[1], (COUNT_BIG div 2) - 1);
  AssertEquals('CopyElements should be 0', 0, LPInt[0]);

  for i := 0 to pred(COUNT_BIG div 2) - 1 do
    AssertEquals('CopyElements should be '+ IntToStr((COUNT_BIG div 2) + i), (COUNT_BIG div 2) + i, LPInt[i + 1]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 反向大重叠 大缓冲区 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  LStringManager.CopyElements(@LPString[COUNT_BIG div 2], @LPString[1], (COUNT_BIG div 2) - 1);
  AssertEquals('CopyElements should be 0', '0', LPString[0]);

  for i := 0 to pred(COUNT_BIG div 2) - 1 do
    AssertEquals('CopyElements should be '+ IntToStr((COUNT_BIG div 2) + i), IntToStr((COUNT_BIG div 2) + i), LPString[i + 1]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 反向小重叠 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElements(@LPInt[4], @LPInt[2], 4);

  AssertEquals('CopyElements should be 0', 0, LPInt[0]);
  AssertEquals('CopyElements should be 1', 1, LPInt[1]);
  AssertEquals('CopyElements should be 4', 4, LPInt[2]);
  AssertEquals('CopyElements should be 5', 5, LPInt[3]);
  AssertEquals('CopyElements should be 6', 6, LPInt[4]);
  AssertEquals('CopyElements should be 7', 7, LPInt[5]);
  AssertEquals('CopyElements should be 6', 6, LPInt[6]);
  AssertEquals('CopyElements should be 7', 7, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 反向小重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElements(@LPString[4], @LPString[2], 4);
  AssertEquals('CopyElements should be 0', '0', LPString[0]);
  AssertEquals('CopyElements should be 1', '1', LPString[1]);
  AssertEquals('CopyElements should be 4', '4', LPString[2]);
  AssertEquals('CopyElements should be 5', '5', LPString[3]);
  AssertEquals('CopyElements should be 6', '6', LPString[4]);
  AssertEquals('CopyElements should be 7', '7', LPString[5]);
  AssertEquals('CopyElements should be 6', '6', LPString[6]);
  AssertEquals('CopyElements should be 7', '7', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 反向小重叠 大缓冲区 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPInt[i] := i;

  LIntManager.CopyElements(@LPInt[COUNT_BIG div 2], @LPInt[(COUNT_BIG div 2) div 2], (COUNT_BIG div 2));

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals('CopyElements should be '+ IntToStr(((COUNT_BIG div 2) div 2) + i), ((COUNT_BIG div 2)) + i, LPInt[i + ((COUNT_BIG div 2) div 2)]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 反向小重叠 大缓冲区 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    LPString[i] := IntToStr(i);

  LStringManager.CopyElements(@LPString[COUNT_BIG div 2], @LPString[(COUNT_BIG div 2) div 2], (COUNT_BIG div 2));

  for i := 0 to pred(COUNT_BIG div 2) do
    AssertEquals('CopyElements should be '+ IntToStr(((COUNT_BIG div 2) div 2) + i), IntToStr(((COUNT_BIG div 2)) + i), LPString[i + ((COUNT_BIG div 2) div 2)]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 增加例子: 大重叠 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(14);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '8';
  LPString[7]    := '9';
  LPString[8]    := '10';
  LPString[9]    := '11';

  LStringManager.CopyElements(@LPString[0], @LPString[4], 10);
  LPString[0] := '12';
  LPString[1] := '13';
  LPString[2] := '14';
  LPString[3] := '15';

  AssertEquals('CopyElements should be 12', '12', LPString[0]);
  AssertEquals('CopyElements should be 13', '13', LPString[1]);
  AssertEquals('CopyElements should be 14', '14', LPString[2]);
  AssertEquals('CopyElements should be 15', '15', LPString[3]);
  AssertEquals('CopyElements should be 4',  '0',  LPString[4]);
  AssertEquals('CopyElements should be 5',  '1',  LPString[5]);
  AssertEquals('CopyElements should be 2',  '2',  LPString[6]);
  AssertEquals('CopyElements should be 3',  '3',  LPString[7]);
  AssertEquals('CopyElements should be 4',  '4',  LPString[8]);
  AssertEquals('CopyElements should be 5',  '5',  LPString[9]);
  AssertEquals('CopyElements should be 8',  '8',  LPString[10]);
  AssertEquals('CopyElements should be 9',  '9',  LPString[11]);
  AssertEquals('CopyElements should be 10', '10', LPString[12]);
  AssertEquals('CopyElements should be 11', '11', LPString[13]);

  LStringManager.FreeElements(LPString, 14);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: aSrc is nil }
  AssertException('CopyElements should raise ENil for nil source', ENil,
    procedure
    begin
      LIntManager.CopyElements(nil, @LPInt[1], 1);
    end
  );

  { 异常测试: aDst is nil }
  AssertException('CopyElements should raise ENil for nil destination', ENil,
    procedure
    begin
      LIntManager.CopyElements(@LPInt[0], nil, 1);
    end
  );

  { 异常测试: aElementCount is zero }
  AssertException('CopyElements should raise EIsZero for zero count', EIsZero,
    procedure
    begin
      LIntManager.CopyElements(@LPInt[0], @LPInt[1], 0);
    end
  );

  { 异常测试: aSrc = aDst }
  AssertException('CopyElements should raise ESame for same source and destination', ESame,
    procedure
    begin
      LIntManager.CopyElements(LPInt, LPInt, 1);
    end
  );
  {$ENDIF}
end;

procedure TTestCase_ElementManager.Test_CopyElementsNonOverlap;
var
  LIntManager: specialize IElementManager<Integer>;
  LPInt: PInteger;
  LStringManager: specialize IElementManager<String>;
  LPString: PString;
begin
  { 正向无重叠 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElementsNonOverlap(@LPInt[0], @LPInt[4], 4);

  AssertEquals('CopyElements should be 0', 0, LPInt[0]);
  AssertEquals('CopyElements should be 1', 1, LPInt[1]);
  AssertEquals('CopyElements should be 2', 2, LPInt[2]);
  AssertEquals('CopyElements should be 3', 3, LPInt[3]);
  AssertEquals('CopyElements should be 0', 0, LPInt[4]);
  AssertEquals('CopyElements should be 1', 1, LPInt[5]);
  AssertEquals('CopyElements should be 2', 2, LPInt[6]);
  AssertEquals('CopyElements should be 3', 3, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 正向无重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElementsNonOverlap(@LPString[0], @LPString[4], 4);

  AssertEquals('CopyElements should be 0', '0', LPString[0]);
  AssertEquals('CopyElements should be 1', '1', LPString[1]);
  AssertEquals('CopyElements should be 2', '2', LPString[2]);
  AssertEquals('CopyElements should be 3', '3', LPString[3]);
  AssertEquals('CopyElements should be 0', '0', LPString[4]);
  AssertEquals('CopyElements should be 1', '1', LPString[5]);
  AssertEquals('CopyElements should be 2', '2', LPString[6]);
  AssertEquals('CopyElements should be 3', '3', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  { 反向无重叠 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LPInt[0]    := 0;
  LPInt[1]    := 1;
  LPInt[2]    := 2;
  LPInt[3]    := 3;
  LPInt[4]    := 4;
  LPInt[5]    := 5;
  LPInt[6]    := 6;
  LPInt[7]    := 7;

  LIntManager.CopyElementsNonOverlap(@LPInt[4], @LPInt[0], 4);

  AssertEquals('CopyElements should be 4', 4, LPInt[0]);
  AssertEquals('CopyElements should be 5', 5, LPInt[1]);
  AssertEquals('CopyElements should be 6', 6, LPInt[2]);
  AssertEquals('CopyElements should be 7', 7, LPInt[3]);
  AssertEquals('CopyElements should be 4', 4, LPInt[4]);
  AssertEquals('CopyElements should be 5', 5, LPInt[5]);
  AssertEquals('CopyElements should be 6', 6, LPInt[6]);
  AssertEquals('CopyElements should be 7', 7, LPInt[7]);

  LIntManager.FreeElements(LPInt, 8);

  { 反向无重叠 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LPString[0]    := '0';
  LPString[1]    := '1';
  LPString[2]    := '2';
  LPString[3]    := '3';
  LPString[4]    := '4';
  LPString[5]    := '5';
  LPString[6]    := '6';
  LPString[7]    := '7';

  LStringManager.CopyElementsNonOverlap(@LPString[4], @LPString[0], 4);

  AssertEquals('CopyElements should be 4', '4', LPString[0]);
  AssertEquals('CopyElements should be 5', '5', LPString[1]);
  AssertEquals('CopyElements should be 6', '6', LPString[2]);
  AssertEquals('CopyElements should be 7', '7', LPString[3]);
  AssertEquals('CopyElements should be 4', '4', LPString[4]);
  AssertEquals('CopyElements should be 5', '5', LPString[5]);
  AssertEquals('CopyElements should be 6', '6', LPString[6]);
  AssertEquals('CopyElements should be 7', '7', LPString[7]);

  LStringManager.FreeElements(LPString, 8);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: aSrc is nil }
  AssertException('CopyElementsNonOverlap should raise ENil for nil source', ENil,
    procedure
    begin
      LIntManager.CopyElementsNonOverlap(nil, @LPInt[1], 1);
    end
  );

  { 异常测试: aDst is nil }
  AssertException('CopyElementsNonOverlap should raise ENil for nil destination', ENil,
    procedure
    begin
      LIntManager.CopyElementsNonOverlap(@LPInt[0], nil, 1);
    end
  );

  { 异常测试: aElementCount is zero }
  AssertException('CopyElementsNonOverlap should raise EIsZero for zero count', EIsZero,
    procedure
    begin
      LIntManager.CopyElementsNonOverlap(@LPInt[0], @LPInt[1], 0);
    end
  );

  { 异常测试: aSrc = aDst }
  AssertException('CopyElementsNonOverlap should raise ESame for same source and destination', ESame,
    procedure
    begin
      LIntManager.CopyElementsNonOverlap(LPInt, LPInt, 1);
    end
  );
  {$ENDIF}
end;

procedure TTestCase_ElementManager.Test_FillElements;
const
  COUNT_BIG = 1024;
var
  LIntManager: specialize IElementManager<Integer>;
  LPInt: PInteger;
  LStringManager: specialize IElementManager<String>;
  LPString: PString;
  i: Integer;
begin

  { 小填充 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LIntManager.FillElements(LPInt, 888, 8);

  for i := 0 to pred(8) do
    AssertEquals('FillElements should be 888', 888, LPInt[i]);

  LIntManager.FreeElements(LPInt, 8);

  { 小填充 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LStringManager.FillElements(LPString, '888', 8);

  for i := 0 to pred(8) do
    AssertEquals('FillElements should be 888', '888', LPString[i]);

  LStringManager.FreeElements(LPString, 8);

  { 大填充 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);
  LIntManager.FillElements(LPInt, 888, COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    AssertEquals('FillElements should be 888', 888, LPInt[i]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 大填充 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);
  LStringManager.FillElements(LPString, '888', COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    AssertEquals('FillElements should be 888', '888', LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);
end;


type

  TTest128 = record
    a: Integer;
    b: Integer;
    c: Integer;
    d: Integer;
  end;

  PTest128 = ^TTest128;

procedure TTestCase_ElementManager.Test_ZeroElements;
const
  COUNT_BIG = 1024;
var
  LIntManager: specialize IElementManager<Integer>;
  LPInt: PInteger;
  LStringManager: specialize IElementManager<String>;
  LPString: PString;
  i: Integer;
  LTest128Manager: specialize IElementManager<TTest128>;
  LPTest128: PTest128;
begin
  { 小写零 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(8);
  LIntManager.ZeroElements(LPInt, 8);

  for i := 0 to pred(8) do
    AssertEquals('ZeroElements should be 0', 0, LPInt[i]);

  LIntManager.FreeElements(LPInt, 8);

  { 小写零 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(8);
  LStringManager.ZeroElements(LPString, 8);

  for i := 0 to pred(8) do
    AssertEquals('ZeroElements should be 0', '', LPString[i]);

  LStringManager.FreeElements(LPString, 8);

  { 大写零 }
  LIntManager := specialize TElementManager<Integer>.Create;
  LPInt       := LIntManager.AllocElements(COUNT_BIG);
  LIntManager.ZeroElements(LPInt, COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    AssertEquals('ZeroElements should be 0', 0, LPInt[i]);

  LIntManager.FreeElements(LPInt, COUNT_BIG);

  { 大写零 托管元素 }
  LStringManager := specialize TElementManager<String>.Create;
  LPString       := LStringManager.AllocElements(COUNT_BIG);
  LStringManager.ZeroElements(LPString, COUNT_BIG);

  for i := 0 to pred(COUNT_BIG) do
    AssertEquals('ZeroElements should be 0', '', LPString[i]);

  LStringManager.FreeElements(LPString, COUNT_BIG);

  { 小写零 结构体 }
  LTest128Manager := specialize TElementManager<TTest128>.Create;
  LPTest128       := LTest128Manager.AllocElements(8);
  LTest128Manager.ZeroElements(LPTest128, 8);

  for i := 0 to pred(8) do
  begin
    AssertEquals('ZeroElements should be 0', 0, LPTest128[i].a);
    AssertEquals('ZeroElements should be 0', 0, LPTest128[i].b);
    AssertEquals('ZeroElements should be 0', 0, LPTest128[i].c);
    AssertEquals('ZeroElements should be 0', 0, LPTest128[i].d);
  end;

  LTest128Manager.FreeElements(LPTest128, 8);
end;

procedure TTestCase_ElementManager.Test_IsOverlap;
var
  LManager: specialize IElementManager<Integer>;
  LPtr:     PInteger;
begin
  LManager := specialize TElementManager<Integer>.Create;
  LPtr     := LManager.AllocElements(10);
  try
    // Test case 1: No overlap, block2 is after block1
    AssertFalse('Should not overlap (B after A)',
      LManager.IsOverlap(LPtr, @LPtr[5], 5));

    // Test case 2: No overlap, block1 is after block2
    AssertFalse('Should not overlap (A after B)',
      LManager.IsOverlap(@LPtr[5], LPtr, 5));

    // Test case 3: Adjacent blocks, no overlap
    AssertFalse('Should not overlap (adjacent)',
      LManager.IsOverlap(LPtr, @LPtr[3], 3));

    // Test case 4: Partial overlap, one element
    AssertTrue('Should overlap by one element',
      LManager.IsOverlap(LPtr, @LPtr[2], 3));

    // Test case 5: Partial overlap, multiple elements
    AssertTrue('Should overlap by multiple elements',
      LManager.IsOverlap(LPtr, @LPtr[3], 5));

    // Test case 6: Total overlap (same pointers)
    AssertTrue('Should overlap (identical blocks)',
      LManager.IsOverlap(LPtr, LPtr, 5));

    // --- Boundary and Nil Pointer Tests ---

    // Test case 7: Edge case: zero element count should never overlap
    AssertFalse('Should not overlap (zero count A)',
      LManager.IsOverlap(LPtr, @LPtr[1], 0));
    AssertFalse('Should not overlap (zero count B)',
      LManager.IsOverlap(@LPtr[1], LPtr, 0));
    AssertFalse('Should not overlap (zero count both)',
      LManager.IsOverlap(LPtr, LPtr, 0));

    // Test case 8: Edge case: nil pointers should never overlap
    AssertFalse('Should not overlap (nil src)',
      LManager.IsOverlap(nil, LPtr, 5));
    AssertFalse('Should not overlap (nil dst)',
      LManager.IsOverlap(LPtr, nil, 5));
    AssertFalse('Should not overlap (both nil)',
      LManager.IsOverlap(nil, nil, 5));
    AssertFalse('Should not overlap (nil with zero count)',
      LManager.IsOverlap(nil, nil, 0));

  finally
    LManager.FreeElements(LPtr, 10);
  end;
end;

initialization
  RegisterTest(TTestCase_ElementManager);
end.
