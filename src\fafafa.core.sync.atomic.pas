unit fafafa.core.sync.atomic;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils,
  fafafa.core.base,
  fafafa.core.sync.types;

type
  // 原子整数类型
  TAtomicInt32 = record
  private
    FValue: LongInt;
  public
    // 基础操作
    function Load: LongInt; inline;
    function Store(aValue: LongInt): LongInt; inline;
    function Exchange(aValue: LongInt): LongInt; inline;
    function CompareExchange(aExpected, aDesired: LongInt): Boolean; inline;
    
    // 算术操作
    function Increment: LongInt; inline;
    function Decrement: LongInt; inline;
    function Add(aValue: LongInt): LongInt; inline;
    function Subtract(aValue: LongInt): LongInt; inline;
    
    // 位操作
    function BitwiseAnd(aValue: LongInt): LongInt; inline;
    function BitwiseOr(aValue: LongInt): LongInt; inline;
    function BitwiseXor(aValue: LongInt): LongInt; inline;
    
    // 属性访问
    property Value: LongInt read Load write Store;
    
    // 操作符重载
    class operator Implicit(const aAtomic: TAtomicInt32): LongInt;
    class operator Implicit(aValue: LongInt): TAtomicInt32;
    class operator Add(const aAtomic: TAtomicInt32; aValue: LongInt): LongInt;
    class operator Subtract(const aAtomic: TAtomicInt32; aValue: LongInt): LongInt;
  end;

  // 原子无符号整数类型
  TAtomicUInt32 = record
  private
    FValue: Cardinal;
  public
    function Load: Cardinal; inline;
    function Store(aValue: Cardinal): Cardinal; inline;
    function Exchange(aValue: Cardinal): Cardinal; inline;
    function CompareExchange(aExpected, aDesired: Cardinal): Boolean; inline;
    
    function Increment: Cardinal; inline;
    function Decrement: Cardinal; inline;
    function Add(aValue: Cardinal): Cardinal; inline;
    function Subtract(aValue: Cardinal): Cardinal; inline;
    
    property Value: Cardinal read Load write Store;
    
    class operator Implicit(const aAtomic: TAtomicUInt32): Cardinal;
    class operator Implicit(aValue: Cardinal): TAtomicUInt32;
  end;

  // 原子指针类型
  TAtomicPointer = record
  private
    FValue: Pointer;
  public
    function Load: Pointer; inline;
    function Store(aValue: Pointer): Pointer; inline;
    function Exchange(aValue: Pointer): Pointer; inline;
    function CompareExchange(aExpected, aDesired: Pointer): Boolean; inline;
    
    property Value: Pointer read Load write Store;
    
    class operator Implicit(const aAtomic: TAtomicPointer): Pointer;
    class operator Implicit(aValue: Pointer): TAtomicPointer;
  end;

  // 原子布尔类型
  TAtomicBoolean = record
  private
    FValue: LongInt; // 使用整数存储布尔值
  public
    function Load: Boolean; inline;
    function Store(aValue: Boolean): Boolean; inline;
    function Exchange(aValue: Boolean): Boolean; inline;
    function CompareExchange(aExpected, aDesired: Boolean): Boolean; inline;
    
    // 逻辑操作
    function SetTrue: Boolean; inline;  // 设置为True，返回旧值
    function SetFalse: Boolean; inline; // 设置为False，返回旧值
    function Toggle: Boolean; inline;   // 切换值，返回新值
    
    property Value: Boolean read Load write Store;
    
    class operator Implicit(const aAtomic: TAtomicBoolean): Boolean;
    class operator Implicit(aValue: Boolean): TAtomicBoolean;
  end;

  // 原子引用计数器
  TAtomicRefCount = record
  private
    FValue: LongInt;
  public
    function AddRef: LongInt; inline;
    function Release: LongInt; inline;
    function GetCount: LongInt; inline;
    
    property Count: LongInt read GetCount;
    
    class operator Initialize(var aRefCount: TAtomicRefCount);
  end;

// 全局原子操作函数（C风格API）
function atomic_load_int32(var aTarget: LongInt): LongInt; inline;
function atomic_store_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;
function atomic_exchange_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;
function atomic_cas_int32(var aTarget: LongInt; aExpected, aDesired: LongInt): Boolean; inline;
function atomic_add_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;

function atomic_load_uint32(var aTarget: Cardinal): Cardinal; inline;
function atomic_store_uint32(var aTarget: Cardinal; aValue: Cardinal): Cardinal; inline;
function atomic_cas_uint32(var aTarget: Cardinal; aExpected, aDesired: Cardinal): Boolean; inline;

function atomic_load_pointer(var aTarget: Pointer): Pointer; inline;
function atomic_store_pointer(var aTarget: Pointer; aValue: Pointer): Pointer; inline;
function atomic_cas_pointer(var aTarget: Pointer; aExpected, aDesired: Pointer): Boolean; inline;

implementation

// TAtomicInt32 实现

function TAtomicInt32.Load: LongInt;
begin
  // 在x86/x64上，对齐的32位读取是原子的
  Result := FValue;
end;

function TAtomicInt32.Store(aValue: LongInt): LongInt;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicInt32.Exchange(aValue: LongInt): LongInt;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicInt32.CompareExchange(aExpected, aDesired: LongInt): Boolean;
begin
  Result := InterlockedCompareExchange(FValue, aDesired, aExpected) = aExpected;
end;

function TAtomicInt32.Increment: LongInt;
begin
  Result := InterlockedIncrement(FValue);
end;

function TAtomicInt32.Decrement: LongInt;
begin
  Result := InterlockedDecrement(FValue);
end;

function TAtomicInt32.Add(aValue: LongInt): LongInt;
begin
  Result := InterlockedExchangeAdd(FValue, aValue) + aValue;
end;

function TAtomicInt32.Subtract(aValue: LongInt): LongInt;
begin
  Result := InterlockedExchangeAdd(FValue, -aValue) - aValue;
end;

function TAtomicInt32.BitwiseAnd(aValue: LongInt): LongInt;
var
  Current, NewValue: LongInt;
begin
  repeat
    Current := FValue;
    NewValue := Current and aValue;
  until InterlockedCompareExchange(FValue, NewValue, Current) = Current;
  Result := NewValue;
end;

function TAtomicInt32.BitwiseOr(aValue: LongInt): LongInt;
var
  Current, NewValue: LongInt;
begin
  repeat
    Current := FValue;
    NewValue := Current or aValue;
  until InterlockedCompareExchange(FValue, NewValue, Current) = Current;
  Result := NewValue;
end;

function TAtomicInt32.BitwiseXor(aValue: LongInt): LongInt;
var
  Current, NewValue: LongInt;
begin
  repeat
    Current := FValue;
    NewValue := Current xor aValue;
  until InterlockedCompareExchange(FValue, NewValue, Current) = Current;
  Result := NewValue;
end;

class operator TAtomicInt32.Implicit(const aAtomic: TAtomicInt32): LongInt;
begin
  Result := aAtomic.Load;
end;

class operator TAtomicInt32.Implicit(aValue: LongInt): TAtomicInt32;
begin
  Result.FValue := aValue;
end;

class operator TAtomicInt32.Add(const aAtomic: TAtomicInt32; aValue: LongInt): LongInt;
begin
  Result := aAtomic.Load + aValue;
end;

class operator TAtomicInt32.Subtract(const aAtomic: TAtomicInt32; aValue: LongInt): LongInt;
begin
  Result := aAtomic.Load - aValue;
end;

// TAtomicUInt32 实现

function TAtomicUInt32.Load: Cardinal;
begin
  Result := FValue;
end;

function TAtomicUInt32.Store(aValue: Cardinal): Cardinal;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicUInt32.Exchange(aValue: Cardinal): Cardinal;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicUInt32.CompareExchange(aExpected, aDesired: Cardinal): Boolean;
begin
  Result := InterlockedCompareExchange(FValue, aDesired, aExpected) = aExpected;
end;

function TAtomicUInt32.Increment: Cardinal;
begin
  Result := InterlockedIncrement(FValue);
end;

function TAtomicUInt32.Decrement: Cardinal;
begin
  Result := InterlockedDecrement(FValue);
end;

function TAtomicUInt32.Add(aValue: Cardinal): Cardinal;
begin
  Result := InterlockedExchangeAdd(FValue, aValue) + aValue;
end;

function TAtomicUInt32.Subtract(aValue: Cardinal): Cardinal;
begin
  Result := InterlockedExchangeAdd(FValue, -LongInt(aValue)) - aValue;
end;

class operator TAtomicUInt32.Implicit(const aAtomic: TAtomicUInt32): Cardinal;
begin
  Result := aAtomic.Load;
end;

class operator TAtomicUInt32.Implicit(aValue: Cardinal): TAtomicUInt32;
begin
  Result.FValue := aValue;
end;

// TAtomicPointer 实现

function TAtomicPointer.Load: Pointer;
begin
  Result := FValue;
end;

function TAtomicPointer.Store(aValue: Pointer): Pointer;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicPointer.Exchange(aValue: Pointer): Pointer;
begin
  Result := InterlockedExchange(FValue, aValue);
end;

function TAtomicPointer.CompareExchange(aExpected, aDesired: Pointer): Boolean;
begin
  Result := InterlockedCompareExchangePointer(FValue, aDesired, aExpected) = aExpected;
end;

class operator TAtomicPointer.Implicit(const aAtomic: TAtomicPointer): Pointer;
begin
  Result := aAtomic.Load;
end;

class operator TAtomicPointer.Implicit(aValue: Pointer): TAtomicPointer;
begin
  Result.FValue := aValue;
end;

// TAtomicBoolean 实现

function TAtomicBoolean.Load: Boolean;
begin
  Result := FValue <> 0;
end;

function TAtomicBoolean.Store(aValue: Boolean): Boolean;
var
  NewValue, OldValue: LongInt;
begin
  NewValue := Ord(aValue);
  OldValue := InterlockedExchange(FValue, NewValue);
  Result := OldValue <> 0;
end;

function TAtomicBoolean.Exchange(aValue: Boolean): Boolean;
var
  NewValue, OldValue: LongInt;
begin
  NewValue := Ord(aValue);
  OldValue := InterlockedExchange(FValue, NewValue);
  Result := OldValue <> 0;
end;

function TAtomicBoolean.CompareExchange(aExpected, aDesired: Boolean): Boolean;
var
  ExpectedValue, DesiredValue: LongInt;
begin
  ExpectedValue := Ord(aExpected);
  DesiredValue := Ord(aDesired);
  Result := InterlockedCompareExchange(FValue, DesiredValue, ExpectedValue) = ExpectedValue;
end;

function TAtomicBoolean.SetTrue: Boolean;
var
  OldValue: LongInt;
begin
  OldValue := InterlockedExchange(FValue, 1);
  Result := OldValue <> 0;
end;

function TAtomicBoolean.SetFalse: Boolean;
var
  OldValue: LongInt;
begin
  OldValue := InterlockedExchange(FValue, 0);
  Result := OldValue <> 0;
end;

function TAtomicBoolean.Toggle: Boolean;
var
  Current, NewValue: LongInt;
begin
  repeat
    Current := FValue;
    NewValue := 1 - Current;
  until InterlockedCompareExchange(FValue, NewValue, Current) = Current;
  Result := NewValue <> 0;
end;

class operator TAtomicBoolean.Implicit(const aAtomic: TAtomicBoolean): Boolean;
begin
  Result := aAtomic.Load;
end;

class operator TAtomicBoolean.Implicit(aValue: Boolean): TAtomicBoolean;
begin
  Result.FValue := Ord(aValue);
end;

// TAtomicRefCount 实现

function TAtomicRefCount.AddRef: LongInt;
begin
  Result := InterlockedIncrement(FValue);
end;

function TAtomicRefCount.Release: LongInt;
begin
  Result := InterlockedDecrement(FValue);
end;

function TAtomicRefCount.GetCount: LongInt;
begin
  Result := FValue;
end;

class operator TAtomicRefCount.Initialize(var aRefCount: TAtomicRefCount);
begin
  aRefCount.FValue := 0;
end;

// 全局原子操作函数实现

function atomic_load_int32(var aTarget: LongInt): LongInt;
begin
  Result := aTarget;
end;

function atomic_store_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := InterlockedExchange(aTarget, aValue);
end;

function atomic_exchange_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := InterlockedExchange(aTarget, aValue);
end;

function atomic_cas_int32(var aTarget: LongInt; aExpected, aDesired: LongInt): Boolean;
begin
  Result := InterlockedCompareExchange(aTarget, aDesired, aExpected) = aExpected;
end;

function atomic_add_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := InterlockedExchangeAdd(aTarget, aValue) + aValue;
end;

function atomic_load_uint32(var aTarget: Cardinal): Cardinal;
begin
  Result := aTarget;
end;

function atomic_store_uint32(var aTarget: Cardinal; aValue: Cardinal): Cardinal;
begin
  Result := InterlockedExchange(aTarget, aValue);
end;

function atomic_cas_uint32(var aTarget: Cardinal; aExpected, aDesired: Cardinal): Boolean;
begin
  Result := InterlockedCompareExchange(aTarget, aDesired, aExpected) = aExpected;
end;

function atomic_load_pointer(var aTarget: Pointer): Pointer;
begin
  Result := aTarget;
end;

function atomic_store_pointer(var aTarget: Pointer; aValue: Pointer): Pointer;
begin
  Result := InterlockedExchange(aTarget, aValue);
end;

function atomic_cas_pointer(var aTarget: Pointer; aExpected, aDesired: Pointer): Boolean;
begin
  Result := InterlockedCompareExchangePointer(aTarget, aDesired, aExpected) = aExpected;
end;

end.
