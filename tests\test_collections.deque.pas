unit test_collections.deque;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testregistry, TypInfo,
  fafafa.core.base,
  fafafa.core.collections.base,
  fafafa.core.collections.deque,
  fafafa.core.collections.elementManager,
  fafafa.core.mem.allocator;

{ 全局函数声明 }
function ForEachTestFunc(const aValue: Integer; aData: Pointer): Boolean;
function EqualsTestFunc(const aValue1, aValue2: Integer; aData: Pointer): Boolean;

type

  { TTestCase_Deque - TDeque测试套件 }
  
  TTestCase_Deque = class(TTestCase)
  private
    { ForEach测试辅助字段 }
    FForEachCounter: SizeInt;
    FForEachSum: SizeInt;
    FForEachValues: array of Integer;

    { 对象方法 }
    function ForEachTestMethod(const aValue: Integer; aData: Pointer): Boolean;
    function EqualsTestMethod(const aValue1, aValue2: Integer; aData: Pointer): Boolean;
    function PredicateTestMethod(const aValue: Integer; aData: Pointer): Boolean;
  published
    { ===== 构造函数测试 ===== }
    procedure Test_Create;
    procedure Test_Create_Allocator;
    procedure Test_Create_Allocator_Data;
    procedure Test_Create_Capacity;
    procedure Test_Create_Capacity_Allocator;
    procedure Test_Create_Capacity_Allocator_Data;
    procedure Test_Create_Array;
    procedure Test_Create_Array_Allocator;
    procedure Test_Create_Array_Allocator_Data;

    { ===== 析构函数测试 ===== }
    procedure Test_Destroy;

    { ===== ICollection 接口方法测试 ===== }
    procedure Test_GetAllocator;
    procedure Test_GetCount;
    procedure Test_IsEmpty;
    procedure Test_GetData;
    procedure Test_SetData;
    procedure Test_Clear;
    procedure Test_Clone;
    procedure Test_IsCompatible;
    procedure Test_PtrIter;

    { ===== IGenericCollection<T> 接口方法测试 ===== }
    procedure Test_GetEnumerator;
    procedure Test_Iter;
    procedure Test_GetElementSize;
    procedure Test_GetIsManagedType;
    procedure Test_GetElementManager;
    procedure Test_GetElementTypeInfo;
    procedure Test_LoadFrom_Array;
    procedure Test_Append_Array;
    procedure Test_ToArray;
    procedure Test_ForEach;
    procedure Test_Contains;
    procedure Test_CountOf;
    procedure Test_CountIF;
    procedure Test_Fill;
    procedure Test_Zero;
    procedure Test_Replace;
    procedure Test_ReplaceIf;
    procedure Test_Reverse;

    { ===== IQueue<T> 接口方法测试 ===== }
    procedure Test_Enqueue;
    procedure Test_Push;
    procedure Test_Dequeue;
    procedure Test_Pop;
    procedure Test_Peek;

    { ===== IDeque<T> 接口方法测试 ===== }
    procedure Test_PushFront;
    procedure Test_PushFront_Array;
    procedure Test_PushFront_Pointer;
    procedure Test_PushBack;
    procedure Test_PushBack_Array;
    procedure Test_PushBack_Pointer;
    procedure Test_PopFront;
    procedure Test_PopBack;
    procedure Test_PeekFront;
    procedure Test_PeekBack;

    { ===== 特殊场景测试 ===== }
    procedure Test_EmptyDeque;
    procedure Test_SingleElement;
    procedure Test_CircularBuffer;
    procedure Test_GrowthStrategy;
    procedure Test_ManagedTypes;
    procedure Test_LargeData;
    procedure Test_EdgeCases;
  end;

implementation

{ 全局函数实现 }
function ForEachTestFunc(const aValue: Integer; aData: Pointer): Boolean;
var
  LSum: PInteger;
begin
  LSum := PInteger(aData);
  LSum^ := LSum^ + aValue;
  Result := True;
end;

function EqualsTestFunc(const aValue1, aValue2: Integer; aData: Pointer): Boolean;
begin
  Result := aValue1 = aValue2;
end;

{ TTestCase_Deque }

function TTestCase_Deque.ForEachTestMethod(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(FForEachCounter);
  Inc(FForEachSum, aValue);
  if FForEachCounter <= Length(FForEachValues) then
    FForEachValues[FForEachCounter - 1] := aValue;
  Result := True;
end;

function TTestCase_Deque.EqualsTestMethod(const aValue1, aValue2: Integer; aData: Pointer): Boolean;
begin
  Result := aValue1 = aValue2;
end;

function TTestCase_Deque.PredicateTestMethod(const aValue: Integer; aData: Pointer): Boolean;
var
  LThreshold: PInteger;
begin
  LThreshold := PInteger(aData);
  Result := aValue > LThreshold^;
end;

{ ===== 构造函数测试 ===== }

procedure TTestCase_Deque.Test_Create;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertTrue('Capacity should be >= 0', LDeque.Capacity >= 0);
    AssertNotNull('Allocator should not be nil', LDeque.Allocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Allocator;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
begin
  LAllocator := GetRtlAllocator;
  LDeque := specialize TDeque<Integer>.Create(LAllocator);
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertTrue('Capacity should be >= 0', LDeque.Capacity >= 0);
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Allocator_Data;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
  LData: Integer;
begin
  LAllocator := GetRtlAllocator;
  LData := 42;
  LDeque := specialize TDeque<Integer>.Create(LAllocator, @LData);
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);
    AssertSame('Data should be the same', @LData, LDeque.Data);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Capacity;
const
  CAPACITY = 32;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create(CAPACITY);
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertTrue('Capacity should be >= requested', LDeque.Capacity >= CAPACITY);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Capacity_Allocator;
const
  CAPACITY = 32;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
begin
  LAllocator := GetRtlAllocator;
  LDeque := specialize TDeque<Integer>.Create(CAPACITY, LAllocator);
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertTrue('Capacity should be >= requested', LDeque.Capacity >= CAPACITY);
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Capacity_Allocator_Data;
const
  CAPACITY = 32;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
  LData: Integer;
begin
  LAllocator := GetRtlAllocator;
  LData := 42;
  LDeque := specialize TDeque<Integer>.Create(CAPACITY, LAllocator, @LData);
  try
    AssertEquals('Count should be 0', 0, LDeque.Count);
    AssertTrue('Should be empty', LDeque.IsEmpty);
    AssertTrue('Capacity should be >= requested', LDeque.Capacity >= CAPACITY);
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);
    AssertSame('Data should be the same', @LData, LDeque.Data);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Array;
var
  LDeque: specialize TDeque<Integer>;
  LSourceArray: array[0..4] of Integer = (1, 2, 3, 4, 5);
  i: Integer;
begin
  LDeque := specialize TDeque<Integer>.Create(LSourceArray);
  try
    AssertEquals('Count should match array length', Int64(Length(LSourceArray)), Int64(LDeque.Count));
    AssertFalse('Should not be empty', LDeque.IsEmpty);
    
    { 验证元素顺序 }
    for i := 0 to High(LSourceArray) do
    begin
      AssertEquals('Element should match', LSourceArray[i], LDeque.PopFront);
    end;
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Array_Allocator;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
  LSourceArray: array[0..2] of Integer = (10, 20, 30);
  i: Integer;
begin
  LAllocator := GetRtlAllocator;
  LDeque := specialize TDeque<Integer>.Create(LSourceArray, LAllocator);
  try
    AssertEquals('Count should match array length', Int64(Length(LSourceArray)), Int64(LDeque.Count));
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);

    { 验证元素顺序 }
    for i := 0 to High(LSourceArray) do
    begin
      AssertEquals('Element should match', LSourceArray[i], LDeque.PopFront);
    end;
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Create_Array_Allocator_Data;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
  LData: Integer;
  LSourceArray: array[0..1] of Integer = (100, 200);
  i: Integer;
begin
  LAllocator := GetRtlAllocator;
  LData := 42;
  LDeque := specialize TDeque<Integer>.Create(LSourceArray, LAllocator, @LData);
  try
    AssertEquals('Count should match array length', Int64(Length(LSourceArray)), Int64(LDeque.Count));
    AssertSame('Allocator should be the same', LAllocator, LDeque.Allocator);
    AssertSame('Data should be the same', @LData, LDeque.Data);

    { 验证元素顺序 }
    for i := 0 to High(LSourceArray) do
    begin
      AssertEquals('Element should match', LSourceArray[i], LDeque.PopFront);
    end;
  finally
    LDeque.Free;
  end;
end;

{ ===== 析构函数测试 ===== }

procedure TTestCase_Deque.Test_Destroy;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  LDeque.PushBack(1);
  LDeque.PushBack(2);
  LDeque.PushBack(3);
  AssertEquals('Count should be 3', 3, LDeque.Count);

  { 析构函数会自动调用 }
  LDeque.Free;
  { 如果没有内存泄漏或访问违规，测试通过 }
end;

{ ===== ICollection 接口方法测试 ===== }

procedure TTestCase_Deque.Test_GetAllocator;
var
  LDeque: specialize TDeque<Integer>;
  LAllocator: TAllocator;
begin
  LAllocator := GetRtlAllocator;
  LDeque := specialize TDeque<Integer>.Create(LAllocator);
  try
    AssertSame('Allocator should be the same', LAllocator, LDeque.GetAllocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_GetCount;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    AssertEquals('Initial count should be 0', 0, LDeque.GetCount);

    LDeque.PushBack(1);
    AssertEquals('Count should be 1', 1, LDeque.GetCount);

    LDeque.PushFront(2);
    AssertEquals('Count should be 2', 2, LDeque.GetCount);

    LDeque.PopBack;
    AssertEquals('Count should be 1', 1, LDeque.GetCount);

    LDeque.PopFront;
    AssertEquals('Count should be 0', 0, LDeque.GetCount);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_IsEmpty;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    AssertTrue('Should be empty initially', LDeque.IsEmpty);

    LDeque.PushBack(1);
    AssertFalse('Should not be empty', LDeque.IsEmpty);

    LDeque.PopBack;
    AssertTrue('Should be empty after pop', LDeque.IsEmpty);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_GetData;
var
  LDeque: specialize TDeque<Integer>;
  LData: Integer;
begin
  LData := 42;
  LDeque := specialize TDeque<Integer>.Create(GetRtlAllocator, @LData);
  try
    AssertSame('Data should be the same', @LData, LDeque.GetData);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_SetData;
var
  LDeque: specialize TDeque<Integer>;
  LData1, LData2: Integer;
begin
  LData1 := 42;
  LData2 := 84;
  LDeque := specialize TDeque<Integer>.Create(GetRtlAllocator, @LData1);
  try
    AssertSame('Initial data should be LData1', @LData1, LDeque.Data);

    LDeque.SetData(@LData2);
    AssertSame('Data should be LData2', @LData2, LDeque.Data);

    LDeque.SetData(nil);
    AssertNull('Data should be nil', LDeque.Data);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_Clear;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    { 添加一些元素 }
    LDeque.PushBack(1);
    LDeque.PushBack(2);
    LDeque.PushFront(3);
    AssertEquals('Count should be 3', 3, LDeque.Count);
    AssertFalse('Should not be empty', LDeque.IsEmpty);

    { 清空 }
    LDeque.Clear;
    AssertEquals('Count should be 0 after clear', 0, LDeque.Count);
    AssertTrue('Should be empty after clear', LDeque.IsEmpty);

    { 清空后应该能正常添加元素 }
    LDeque.PushBack(4);
    AssertEquals('Count should be 1', 1, LDeque.Count);
    AssertEquals('Element should be 4', 4, LDeque.PopBack);
  finally
    LDeque.Free;
  end;
end;

{ 添加一些核心的双端队列测试方法 }

procedure TTestCase_Deque.Test_PushFront;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    { 测试基本的前端插入 }
    LDeque.PushFront(1);
    AssertEquals('Count should be 1', 1, LDeque.Count);
    AssertEquals('Front element should be 1', 1, LDeque.PeekFront);

    LDeque.PushFront(2);
    AssertEquals('Count should be 2', 2, LDeque.Count);
    AssertEquals('Front element should be 2', 2, LDeque.PeekFront);

    LDeque.PushFront(3);
    AssertEquals('Count should be 3', 3, LDeque.Count);
    AssertEquals('Front element should be 3', 3, LDeque.PeekFront);

    { 验证顺序：应该是 3, 2, 1 }
    AssertEquals('First pop should be 3', 3, LDeque.PopFront);
    AssertEquals('Second pop should be 2', 2, LDeque.PopFront);
    AssertEquals('Third pop should be 1', 1, LDeque.PopFront);
    AssertTrue('Should be empty', LDeque.IsEmpty);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_Deque.Test_PushBack;
var
  LDeque: specialize TDeque<Integer>;
begin
  LDeque := specialize TDeque<Integer>.Create;
  try
    { 测试基本的后端插入 }
    LDeque.PushBack(1);
    AssertEquals('Count should be 1', 1, LDeque.Count);
    AssertEquals('Back element should be 1', 1, LDeque.PeekBack);

    LDeque.PushBack(2);
    AssertEquals('Count should be 2', 2, LDeque.Count);
    AssertEquals('Back element should be 2', 2, LDeque.PeekBack);

    LDeque.PushBack(3);
    AssertEquals('Count should be 3', 3, LDeque.Count);
    AssertEquals('Back element should be 3', 3, LDeque.PeekBack);

    { 验证顺序：应该是 1, 2, 3 }
    AssertEquals('First pop should be 1', 1, LDeque.PopFront);
    AssertEquals('Second pop should be 2', 2, LDeque.PopFront);
    AssertEquals('Third pop should be 3', 3, LDeque.PopFront);
    AssertTrue('Should be empty', LDeque.IsEmpty);
  finally
    LDeque.Free;
  end;
end;

{ 存根实现 - 其他测试方法 }

procedure TTestCase_Deque.Test_Clone;
begin
  { TODO: 实现Clone测试 }
end;

procedure TTestCase_Deque.Test_IsCompatible;
begin
  { TODO: 实现IsCompatible测试 }
end;

procedure TTestCase_Deque.Test_PtrIter;
begin
  { TODO: 实现PtrIter测试 }
end;

procedure TTestCase_Deque.Test_GetEnumerator;
begin
  { TODO: 实现GetEnumerator测试 }
end;

procedure TTestCase_Deque.Test_Iter;
begin
  { TODO: 实现Iter测试 }
end;

procedure TTestCase_Deque.Test_GetElementSize;
begin
  { TODO: 实现GetElementSize测试 }
end;

procedure TTestCase_Deque.Test_GetIsManagedType;
begin
  { TODO: 实现GetIsManagedType测试 }
end;

procedure TTestCase_Deque.Test_GetElementManager;
begin
  { TODO: 实现GetElementManager测试 }
end;

procedure TTestCase_Deque.Test_GetElementTypeInfo;
begin
  { TODO: 实现GetElementTypeInfo测试 }
end;

procedure TTestCase_Deque.Test_LoadFrom_Array;
begin
  { TODO: 实现LoadFrom_Array测试 }
end;

procedure TTestCase_Deque.Test_Append_Array;
begin
  { TODO: 实现Append_Array测试 }
end;

procedure TTestCase_Deque.Test_ToArray;
begin
  { TODO: 实现ToArray测试 }
end;

procedure TTestCase_Deque.Test_ForEach;
begin
  { TODO: 实现ForEach测试 }
end;

procedure TTestCase_Deque.Test_Contains;
begin
  { TODO: 实现Contains测试 }
end;

procedure TTestCase_Deque.Test_CountOf;
begin
  { TODO: 实现CountOf测试 }
end;

procedure TTestCase_Deque.Test_CountIF;
begin
  { TODO: 实现CountIF测试 }
end;

procedure TTestCase_Deque.Test_Fill;
begin
  { TODO: 实现Fill测试 }
end;

procedure TTestCase_Deque.Test_Zero;
begin
  { TODO: 实现Zero测试 }
end;

procedure TTestCase_Deque.Test_Replace;
begin
  { TODO: 实现Replace测试 }
end;

procedure TTestCase_Deque.Test_ReplaceIf;
begin
  { TODO: 实现ReplaceIf测试 }
end;

procedure TTestCase_Deque.Test_Reverse;
begin
  { TODO: 实现Reverse测试 }
end;

procedure TTestCase_Deque.Test_Enqueue;
begin
  { TODO: 实现Enqueue测试 }
end;

procedure TTestCase_Deque.Test_Push;
begin
  { TODO: 实现Push测试 }
end;

procedure TTestCase_Deque.Test_Dequeue;
begin
  { TODO: 实现Dequeue测试 }
end;

procedure TTestCase_Deque.Test_Pop;
begin
  { TODO: 实现Pop测试 }
end;

procedure TTestCase_Deque.Test_Peek;
begin
  { TODO: 实现Peek测试 }
end;

procedure TTestCase_Deque.Test_PushFront_Array;
begin
  { TODO: 实现PushFront_Array测试 }
end;

procedure TTestCase_Deque.Test_PushFront_Pointer;
begin
  { TODO: 实现PushFront_Pointer测试 }
end;

procedure TTestCase_Deque.Test_PushBack_Array;
begin
  { TODO: 实现PushBack_Array测试 }
end;

procedure TTestCase_Deque.Test_PushBack_Pointer;
begin
  { TODO: 实现PushBack_Pointer测试 }
end;

procedure TTestCase_Deque.Test_PopFront;
begin
  { TODO: 实现PopFront测试 }
end;

procedure TTestCase_Deque.Test_PopBack;
begin
  { TODO: 实现PopBack测试 }
end;

procedure TTestCase_Deque.Test_PeekFront;
begin
  { TODO: 实现PeekFront测试 }
end;

procedure TTestCase_Deque.Test_PeekBack;
begin
  { TODO: 实现PeekBack测试 }
end;

procedure TTestCase_Deque.Test_EmptyDeque;
begin
  { TODO: 实现EmptyDeque测试 }
end;

procedure TTestCase_Deque.Test_SingleElement;
begin
  { TODO: 实现SingleElement测试 }
end;

procedure TTestCase_Deque.Test_CircularBuffer;
begin
  { TODO: 实现CircularBuffer测试 }
end;

procedure TTestCase_Deque.Test_GrowthStrategy;
begin
  { TODO: 实现GrowthStrategy测试 }
end;

procedure TTestCase_Deque.Test_ManagedTypes;
begin
  { TODO: 实现ManagedTypes测试 }
end;

procedure TTestCase_Deque.Test_LargeData;
begin
  { TODO: 实现LargeData测试 }
end;

procedure TTestCase_Deque.Test_EdgeCases;
begin
  { TODO: 实现EdgeCases测试 }
end;

{ 添加测试注册 }

initialization
  RegisterTest(TTestCase_Deque);

end.
