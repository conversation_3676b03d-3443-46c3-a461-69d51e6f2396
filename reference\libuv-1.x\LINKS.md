### Apps / VM
* [AliceO2](https://github.com/AliceO2Group/AliceO2): The framework and detector specific code for the reconstruction, calibration and simulation for the ALICE experiment at CERN.
* [Beam](https://github.com/BeamMW/beam): A scalable, confidential cryptocurrency based on the Mimblewimble protocol.
* [BIND 9](https://bind.isc.org/): DNS software system including an authoritative server, a recursive resolver and related utilities.
* [cjdns](https://github.com/cjdelisle/cjdns): Encrypted self-configuring network/VPN routing engine
* [clearskies_core](https://github.com/larroy/clearskies_core): Clearskies file synchronization program. (C++11)
* [CMake](https://cmake.org) open-source, cross-platform family of tools designed to build, test and package software
* [Cocos-Engine](https://github.com/cocos/cocos-engine): The runtime framework for Cocos Creator editor.
* [Coherence](https://github.com/liesware/coherence/): Cryptographic server for modern web apps.
* [DPS8M](https://dps8m.gitlab.io): GE ∕ Honeywell ∕ Bull DPS‑8/M and 6180/L68 mainframe simulator.
* [DPS-For-IoT](https://github.com/intel/dps-for-iot/wiki): Fully distributed publish/subscribe protocol.
* [HashLink](https://github.com/HaxeFoundation/hashlink): Haxe run-time with libuv support included.
* [Haywire](https://github.com/kellabyte/Haywire): Asynchronous HTTP server.
* [H2O](https://github.com/h2o/h2o): An optimized HTTP server with support for HTTP/1.x and HTTP/2.
* [Igropyr](https://github.com/guenchi/Igropyr): a async Scheme http server base on libuv.
* [Julia](http://julialang.org/): Scientific computing programming language
* [Kestrel](https://github.com/dotnet/aspnetcore/tree/main/src/Servers/Kestrel): web server (C# + libuv + [ASP.NET Core](http://github.com/aspnet))
* [Knot DNS Resolver](https://www.knot-resolver.cz/): A minimalistic DNS caching resolver
* [Lever](http://leverlanguage.com): runtime, libuv at the 0.9.0 release
* [libnode](https://github.com/plenluno/libnode): C++ implementation of Node.js
* [libstorj](https://github.com/Storj/libstorj): Library for interacting with Storj network
* [libuv_message_framing](https://github.com/litesync/libuv_message_framing) Message-based communication for libuv
* [luaw](https://github.com/raksoras/luaw): Lua web server backed by libuv
* [Luvit](http://luvit.io): Node.JS for the Lua Inventor
* [mo](https://github.com/wehu/mo): Scheme (guile) + libuv runtime
* [MoarVM](https://github.com/MoarVM/MoarVM): a VM for [Rakudo](http://rakudo.org/) [Raku](http://raku.org)
* [Mysocks](https://github.com/zhou0/mysocks): a cross-platform [Shadowsocks](https://shadowsocks.org) client
* [mediasoup](http://mediasoup.org): Powerful WebRTC SFU for Node.js
* [Neovim](https://neovim.io/): A major refactor of Vim.
* [node9](https://github.com/jvburnes/node9): A portable, hybrid, distributed OS based on Inferno, LuaJIT and Libuv
* [node.js](http://www.nodejs.org/): Javascript (using Google's V8) + libuv
* [node.native](https://github.com/d5/node.native): node.js-like API for C++11
* [nodeuv](https://github.com/nodeuv): An organization with several c++ wrappers for libs which are used in node.js.
* [phastlight](https://github.com/phastlight/phastlight): Command line tool and web server written in PHP 5.3+ inspired by Node.js
* [pilight](https://www.pilight.org/): home automation ("domotica")
* [pixie](https://github.com/pixie-lang/pixie): clojure-inspired lisp with a tracing JIT
* [Pixie-io](https://github.com/pixie-io/pixie): Open-source observability tool for Kubernetes applications.
* [potion](https://github.com/perl11/potion)/[p2](https://github.com/perl11/p2): runtime
* [racer](https://libraries.io/rubygems/racer): Ruby web server written as an C extension
* [scala-native-loop](https://github.com/scala-native/scala-native-loop): Extensible event loop and async-oriented IO for Scala Native; powered by libuv
* [Socket Runtime](https://sockets.sh): A runtime for creating native cross-platform software on mobile and desktop using HTML, CSS, and JavaScript
* [spider-gazelle](https://github.com/cotag/spider-gazelle): Ruby web server using libuv bindings
* [Suave](http://suave.io/): A simple web development F# library providing a lightweight web server and a set of combinators to manipulate route flow and task composition
* [Swish](https://github.com/becls/swish/): Concurrency engine with Erlang-like concepts. Includes a web server.
* [Trevi](https://github.com/Yoseob/Trevi): A powerful Swift Web Application Server Framework Project
* [Urbit](http://urbit.org): runtime
* [uv_callback](https://github.com/litesync/uv_callback) libuv thread communication
* [uvloop](https://github.com/MagicStack/uvloop): Ultra fast implementation of python's asyncio event loop on top of libuv
* [WPILib](https://github.com/wpilibsuite/allwpilib): Libraries for creating robot programs for the roboRIO.
* [Wren CLI](https://github.com/wren-lang/wren-cli): For io, process, scheduler and timer modules

### Other
* [libtuv](https://github.com/Samsung/libtuv): libuv fork for IoT and embedded systems

### Bindings
* [Ring](http://ring-lang.net)
   * [RingLibuv](http://ring-lang.sourceforge.net/doc1.7/libuv.html)
* Ruby
   * [libuv](https://github.com/cotag/libuv)
   * [uvrb](https://github.com/avalanche123/uvrb)
   * [ruv](https://github.com/aq1018/ruv)
   * [rbuv](https://github.com/rbuv/rbuv)
   * [mruby-uv](https://github.com/mattn/mruby-uv): mruby binding
* Lua
   * [luv](https://github.com/creationix/luv)
   * [lev](https://github.com/connectFree/lev)
   * [lluv](https://github.com/moteus/lua-lluv)
* C++11
   * [uvpp](https://github.com/larroy/uvpp) - Not complete, exposes very few aspects of `libuv`
   * [nsuv](https://github.com/nodesource/nsuv) - Template wrapper focused on enforcing compile-time type safety when propagating data
* C++17
   * [uvw](https://github.com/skypjack/uvw) - Header-only, event based, tiny and easy to use *libuv* wrapper in modern C++.
* Python
   * [Pyuv](https://github.com/saghul/pyuv)
   * [uvloop](https://github.com/MagicStack/uvloop) - Ultra fast asyncio event loop.
   * [gevent](http://www.gevent.org) - Coroutine-based concurrency library for Python
* C#
   * [NetUV](http://github.com/StormHub/NetUV)
   * [LibuvSharp](http://github.com/txdv/LibuvSharp)
* Perl 5
   * [UV](https://metacpan.org/pod/UV)
* [Raku](https://raku.org/)
   * [MoarVM](https://github.com/MoarVM/MoarVM) [uses](http://6guts.wordpress.com/2013/05/31/moarvm-a-virtual-machine-for-nqp-and-rakudo/) libuv
* PHP
   * [php-uv](https://github.com/bwoebi/php-uv)
* Go
   * [go-uv](https://github.com/mattn/go-uv)
* OCaml
   * [luv](https://github.com/aantron/luv)
   * [uwt](https://github.com/fdopen/uwt)
* ooc
   * [ooc-uv](https://github.com/nddrylliog/ooc-uv)
* dylan
   * [uv-dylan](https://github.com/waywardmonkeys/uv-dylan)
* R
   * [httpuv](https://github.com/rstudio/httpuv): HTTP and WebSocket server library for R
   * [fs](https://fs.r-lib.org/): Cross-platform file system operations
* Java
   * [libuv-java](https://java.net/projects/avatar-js/sources/libuv-java/show): Java bindings
* Nim
   * [nimuv](https://github.com/2vg/nimuv): Nim bindings
* Lisp
   * [cl-libuv](https://github.com/orthecreedence/cl-libuv) Common Lisp bindings
   * [cl-async](https://github.com/orthecreedence/cl-async) Common Lisp async abstraction on top of cl-libuv
* [Céu](http://www.ceu-lang.org)
   * [Céu-libuv](https://github.com/fsantanna/ceu-libuv)
* Delphi
   * [node.pas](https://github.com/vovach777/node.pas) NodeJS-like ecosystem
* Haskell
   * [Z.Haskell](https://z.haskell.world)
* C3
   * [libuv.c3l](https://github.com/velikoss/libuv.c3l)
* C
   * [uv_coroutine](https://github.com/zelang-dev/uv_coroutine) A memory safe focus *C framework*, combining [c-raii](https://zelang-dev.github.io/c-raii), `libuv`, **coroutine** and other concurrency primitives.
