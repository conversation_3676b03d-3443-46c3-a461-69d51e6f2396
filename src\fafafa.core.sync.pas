unit fafafa.core.sync;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  // 基础模块
  fafafa.core.base,
  
  // 同步原语模块
  fafafa.core.sync.types,
  fafafa.core.sync.atomic,
  fafafa.core.sync.primitives,
  fafafa.core.sync.containers;

// 重新导出核心类型
type
  // 基础接口
  ILock = fafafa.core.sync.types.ILock;
  IRWLock = fafafa.core.sync.types.IRWLock;
  IEvent = fafafa.core.sync.types.IEvent;
  ISemaphore = fafafa.core.sync.types.ISemaphore;
  ICondition = fafafa.core.sync.types.ICondition;
  
  // 等待结果
  TWaitResult = fafafa.core.sync.types.TWaitResult;
  
  // RAII自动锁
  TAutoLock = fafafa.core.sync.types.TAutoLock;
  TAutoReadLock = fafafa.core.sync.types.TAutoReadLock;
  TAutoWriteLock = fafafa.core.sync.types.TAutoWriteLock;
  
  // 原子类型
  TAtomicInt32 = fafafa.core.sync.atomic.TAtomicInt32;
  TAtomicUInt32 = fafafa.core.sync.atomic.TAtomicUInt32;
  TAtomicPointer = fafafa.core.sync.atomic.TAtomicPointer;
  TAtomicBoolean = fafafa.core.sync.atomic.TAtomicBoolean;
  TAtomicRefCount = fafafa.core.sync.atomic.TAtomicRefCount;
  
  // 同步对象
  TMutex = fafafa.core.sync.primitives.TMutex;
  TSpinLock = fafafa.core.sync.primitives.TSpinLock;
  TEvent = fafafa.core.sync.primitives.TEvent;
  TLightEvent = fafafa.core.sync.primitives.TLightEvent;
  TSemaphore = fafafa.core.sync.primitives.TSemaphore;
  TCountEvent = fafafa.core.sync.primitives.TCountEvent;
  
  // 无锁容器
  generic TLockFreeQueue<T> = fafafa.core.sync.containers.TLockFreeQueue<T>;
  generic TLockFreeStack<T> = fafafa.core.sync.containers.TLockFreeStack<T>;
  
  // 线程安全容器
  generic TThreadSafeQueue<T> = fafafa.core.sync.containers.TThreadSafeQueue<T>;
  generic TThreadSafeStack<T> = fafafa.core.sync.containers.TThreadSafeStack<T>;
  
  // 实用工具
  TAtomicCounter = fafafa.core.sync.containers.TAtomicCounter;

// 重新导出工厂函数
function CreateMutex: ILock;
function CreateSpinLock(aSpinCount: Cardinal = 4000): ILock;
function CreateEvent(aManualReset: Boolean = False; aInitialState: Boolean = False): IEvent;
function CreateLightEvent(aManualReset: Boolean = False; aInitialState: Boolean = False; 
  aSpinCount: Cardinal = 1000): IEvent;
function CreateSemaphore(aInitialCount: Integer = 0; aMaxCount: Integer = MaxInt): ISemaphore;

// 容器工厂函数
generic function CreateLockFreeQueue<T>: specialize TLockFreeQueue<T>;
generic function CreateLockFreeStack<T>: specialize TLockFreeStack<T>;
generic function CreateThreadSafeQueue<T>(aInitialCapacity: Integer = 16): specialize TThreadSafeQueue<T>;
generic function CreateThreadSafeStack<T>(aInitialCapacity: Integer = 16): specialize TThreadSafeStack<T>;

// 重新导出原子操作函数
function atomic_load_int32(var aTarget: LongInt): LongInt; inline;
function atomic_store_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;
function atomic_exchange_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;
function atomic_cas_int32(var aTarget: LongInt; aExpected, aDesired: LongInt): Boolean; inline;
function atomic_add_int32(var aTarget: LongInt; aValue: LongInt): LongInt; inline;

function atomic_load_uint32(var aTarget: Cardinal): Cardinal; inline;
function atomic_store_uint32(var aTarget: Cardinal; aValue: Cardinal): Cardinal; inline;
function atomic_cas_uint32(var aTarget: Cardinal; aExpected, aDesired: Cardinal): Boolean; inline;

function atomic_load_pointer(var aTarget: Pointer): Pointer; inline;
function atomic_store_pointer(var aTarget: Pointer; aValue: Pointer): Pointer; inline;
function atomic_cas_pointer(var aTarget: Pointer; aExpected, aDesired: Pointer): Boolean; inline;

// 重新导出C风格同步API
function sync_mutex_create: Pointer;
procedure sync_mutex_destroy(aMutex: Pointer);
function sync_mutex_lock(aMutex: Pointer): Boolean;
function sync_mutex_unlock(aMutex: Pointer): Boolean;
function sync_mutex_trylock(aMutex: Pointer; aTimeoutMs: Cardinal = 0): Boolean;

function sync_event_create(aManualReset: Boolean = False; aInitialState: Boolean = False): Pointer;
procedure sync_event_destroy(aEvent: Pointer);
procedure sync_event_set(aEvent: Pointer);
procedure sync_event_reset(aEvent: Pointer);
function sync_event_wait(aEvent: Pointer; aTimeoutMs: Cardinal = INFINITE): Integer;

// 便利宏和内联函数
{$DEFINE SYNC_AUTO_LOCK(lock) := TAutoLock.Create(lock)}
{$DEFINE SYNC_AUTO_READ_LOCK(rwlock) := TAutoReadLock.Create(rwlock)}
{$DEFINE SYNC_AUTO_WRITE_LOCK(rwlock) := TAutoWriteLock.Create(rwlock)}

// 性能测试和调试函数
function sync_performance_test_atomic_increment(aIterations: Integer): QWord;
function sync_performance_test_mutex_lock(aIterations: Integer): QWord;
function sync_performance_test_spinlock_lock(aIterations: Integer): QWord;

implementation

// 工厂函数实现

function CreateMutex: ILock;
begin
  Result := fafafa.core.sync.primitives.CreateMutex;
end;

function CreateSpinLock(aSpinCount: Cardinal): ILock;
begin
  Result := fafafa.core.sync.primitives.CreateSpinLock(aSpinCount);
end;

function CreateEvent(aManualReset: Boolean; aInitialState: Boolean): IEvent;
begin
  Result := fafafa.core.sync.primitives.CreateEvent(aManualReset, aInitialState);
end;

function CreateLightEvent(aManualReset: Boolean; aInitialState: Boolean; aSpinCount: Cardinal): IEvent;
begin
  Result := fafafa.core.sync.primitives.CreateLightEvent(aManualReset, aInitialState, aSpinCount);
end;

function CreateSemaphore(aInitialCount: Integer; aMaxCount: Integer): ISemaphore;
begin
  Result := fafafa.core.sync.primitives.CreateSemaphore(aInitialCount, aMaxCount);
end;

// 容器工厂函数实现

generic function CreateLockFreeQueue<T>: specialize TLockFreeQueue<T>;
begin
  Result := fafafa.core.sync.containers.CreateLockFreeQueue<T>;
end;

generic function CreateLockFreeStack<T>: specialize TLockFreeStack<T>;
begin
  Result := fafafa.core.sync.containers.CreateLockFreeStack<T>;
end;

generic function CreateThreadSafeQueue<T>(aInitialCapacity: Integer): specialize TThreadSafeQueue<T>;
begin
  Result := fafafa.core.sync.containers.CreateThreadSafeQueue<T>(aInitialCapacity);
end;

generic function CreateThreadSafeStack<T>(aInitialCapacity: Integer): specialize TThreadSafeStack<T>;
begin
  Result := fafafa.core.sync.containers.CreateThreadSafeStack<T>(aInitialCapacity);
end;

// 原子操作函数实现

function atomic_load_int32(var aTarget: LongInt): LongInt;
begin
  Result := fafafa.core.sync.atomic.atomic_load_int32(aTarget);
end;

function atomic_store_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := fafafa.core.sync.atomic.atomic_store_int32(aTarget, aValue);
end;

function atomic_exchange_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := fafafa.core.sync.atomic.atomic_exchange_int32(aTarget, aValue);
end;

function atomic_cas_int32(var aTarget: LongInt; aExpected, aDesired: LongInt): Boolean;
begin
  Result := fafafa.core.sync.atomic.atomic_cas_int32(aTarget, aExpected, aDesired);
end;

function atomic_add_int32(var aTarget: LongInt; aValue: LongInt): LongInt;
begin
  Result := fafafa.core.sync.atomic.atomic_add_int32(aTarget, aValue);
end;

function atomic_load_uint32(var aTarget: Cardinal): Cardinal;
begin
  Result := fafafa.core.sync.atomic.atomic_load_uint32(aTarget);
end;

function atomic_store_uint32(var aTarget: Cardinal; aValue: Cardinal): Cardinal;
begin
  Result := fafafa.core.sync.atomic.atomic_store_uint32(aTarget, aValue);
end;

function atomic_cas_uint32(var aTarget: Cardinal; aExpected, aDesired: Cardinal): Boolean;
begin
  Result := fafafa.core.sync.atomic.atomic_cas_uint32(aTarget, aExpected, aDesired);
end;

function atomic_load_pointer(var aTarget: Pointer): Pointer;
begin
  Result := fafafa.core.sync.atomic.atomic_load_pointer(aTarget);
end;

function atomic_store_pointer(var aTarget: Pointer; aValue: Pointer): Pointer;
begin
  Result := fafafa.core.sync.atomic.atomic_store_pointer(aTarget, aValue);
end;

function atomic_cas_pointer(var aTarget: Pointer; aExpected, aDesired: Pointer): Boolean;
begin
  Result := fafafa.core.sync.atomic.atomic_cas_pointer(aTarget, aExpected, aDesired);
end;

// C风格同步API实现

function sync_mutex_create: Pointer;
begin
  Result := fafafa.core.sync.primitives.sync_mutex_create;
end;

procedure sync_mutex_destroy(aMutex: Pointer);
begin
  fafafa.core.sync.primitives.sync_mutex_destroy(aMutex);
end;

function sync_mutex_lock(aMutex: Pointer): Boolean;
begin
  Result := fafafa.core.sync.primitives.sync_mutex_lock(aMutex);
end;

function sync_mutex_unlock(aMutex: Pointer): Boolean;
begin
  Result := fafafa.core.sync.primitives.sync_mutex_unlock(aMutex);
end;

function sync_mutex_trylock(aMutex: Pointer; aTimeoutMs: Cardinal): Boolean;
begin
  Result := fafafa.core.sync.primitives.sync_mutex_trylock(aMutex, aTimeoutMs);
end;

function sync_event_create(aManualReset: Boolean; aInitialState: Boolean): Pointer;
begin
  Result := fafafa.core.sync.primitives.sync_event_create(aManualReset, aInitialState);
end;

procedure sync_event_destroy(aEvent: Pointer);
begin
  fafafa.core.sync.primitives.sync_event_destroy(aEvent);
end;

procedure sync_event_set(aEvent: Pointer);
begin
  fafafa.core.sync.primitives.sync_event_set(aEvent);
end;

procedure sync_event_reset(aEvent: Pointer);
begin
  fafafa.core.sync.primitives.sync_event_reset(aEvent);
end;

function sync_event_wait(aEvent: Pointer; aTimeoutMs: Cardinal): Integer;
begin
  Result := fafafa.core.sync.primitives.sync_event_wait(aEvent, aTimeoutMs);
end;

// 性能测试函数实现

function sync_performance_test_atomic_increment(aIterations: Integer): QWord;
var
  Counter: TAtomicInt32;
  i: Integer;
  StartTime: QWord;
begin
  Counter.Store(0);
  StartTime := GetTickCount64;

  for i := 1 to aIterations do
    Counter.Increment;

  Result := GetTickCount64 - StartTime;
end;

function sync_performance_test_mutex_lock(aIterations: Integer): QWord;
var
  Mutex: ILock;
  i: Integer;
  StartTime: QWord;
begin
  Mutex := CreateMutex;
  StartTime := GetTickCount64;

  for i := 1 to aIterations do
  begin
    Mutex.Acquire;
    Mutex.Release;
  end;

  Result := GetTickCount64 - StartTime;
end;

function sync_performance_test_spinlock_lock(aIterations: Integer): QWord;
var
  SpinLock: ILock;
  i: Integer;
  StartTime: QWord;
begin
  SpinLock := CreateSpinLock;
  StartTime := GetTickCount64;

  for i := 1 to aIterations do
  begin
    SpinLock.Acquire;
    SpinLock.Release;
  end;

  Result := GetTickCount64 - StartTime;
end;

end.
