program benchmark_iterator;

{$mode objfpc}{$H+}
{$O3}

uses
  SysUtils, Windows;

const
  ITERATION_COUNT = 100000000; // 一亿次

type
  // --- 多态 (Polymorphism) 版本 --- 
  IBenchIterator = interface
  ['{1A7E8A5A-5B1A-4D7E-8F2B-2E4A6E1A0E4A}']
    function MoveNext: Boolean;
  end;

  TDummyState = record
    Counter: Integer;
  end;
  PDummyState = ^TDummyState;

  TPolymorphicIterator = class(TInterfacedObject, IBenchIterator)
  private
    FState: PDummyState;
  public
    constructor Create;
    destructor Destroy; override;
    function MoveNext: Boolean; virtual;
  end;

  // --- 回调 (Callback) 版本 ---
  TMoveNextProc = function(aState: PDummyState): Boolean of object;

  TCallbackIterator = class
  private
    FState: PDummyState;
    FMoveNextProc: TMoveNextProc;
    FOwner: TObject;
  public
    constructor Create(aOwner: TObject; aMoveNextProc: TMoveNextProc);
    destructor Destroy; override;
    function MoveNext: Boolean;
  end;

  // 一个持有具体实现的对象
  TDummyContainer = class
  public
    function MoveNextImplementation(aState: PDummyState): Boolean;
  end;

{ TPolymorphicIterator }

constructor TPolymorphicIterator.Create;
begin
  inherited Create;
  New(FState);
  FState^.Counter := 0;
end;

destructor TPolymorphicIterator.Destroy;
begin
  Dispose(FState);
  inherited Destroy;
end;

function TPolymorphicIterator.MoveNext: Boolean;
begin
  Inc(FState^.Counter);
  Result := FState^.Counter < ITERATION_COUNT;
end;

{ TCallbackIterator }

constructor TCallbackIterator.Create(aOwner: TObject; aMoveNextProc: TMoveNextProc);
begin
  inherited Create;
  New(FState);
  FState^.Counter := 0;
  FOwner := aOwner;
  FMoveNextProc := aMoveNextProc;
end;

destructor TCallbackIterator.Destroy;
begin
  Dispose(FState);
  inherited Destroy;
end;

function TCallbackIterator.MoveNext: Boolean;
begin
  Result := FMoveNextProc(FState);
end;

{ TDummyContainer }

function TDummyContainer.MoveNextImplementation(aState: PDummyState): Boolean;
begin
  Inc(aState^.Counter);
  Result := aState^.Counter < ITERATION_COUNT;
end;

var
  LPolyIter: IBenchIterator;
  LCallbackIter: TCallbackIterator;
  LContainer: TDummyContainer;
  LStartTime, LEndTime: UInt64;
  i: Integer;

begin
  WriteLn('Starting Iterator Benchmark...');
  WriteLn('Iterations per test: ', ITERATION_COUNT);
  WriteLn('------------------------------------');

  // --- 测试多态版本 ---
  LPolyIter := TPolymorphicIterator.Create;
  LStartTime := GetTickCount64;

  while LPolyIter.MoveNext do
  begin
    // 空循环体，只测试调用开销
  end;

  LEndTime := GetTickCount64;
  WriteLn('Polymorphic (Virtual Call) Time: ', LEndTime - LStartTime, ' ms');
  LPolyIter := nil;

  // --- 测试回调版本 ---
  LContainer := TDummyContainer.Create;
  LCallbackIter := TCallbackIterator.Create(LContainer, @LContainer.MoveNextImplementation);
  LStartTime := GetTickCount64;

  while LCallbackIter.MoveNext do
  begin
    // 空循环体，只测试调用开销
  end;

  LEndTime := GetTickCount64;
  WriteLn('Callback (Method Pointer) Time:  ', LEndTime - LStartTime, ' ms');
  LCallbackIter.Free;
  LContainer.Free;

  WriteLn('------------------------------------');
  WriteLn('Benchmark Finished. Press Enter to exit.');
  ReadLn;
end.
