/* Copyright Joyent, Inc. and other Node contributors. All rights reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 */

#ifndef UV_WIN_WINAPI_H_
#define UV_WIN_WINAPI_H_

#include <windows.h>


/*
 * Ntdll headers
 */
#ifndef STATUS_SEVERITY_SUCCESS
# define STATUS_SEVERITY_SUCCESS 0x0
#endif

#ifndef STATUS_SEVERITY_INFORMATIONAL
# define STATUS_SEVERITY_INFORMATIONAL 0x1
#endif

#ifndef STATUS_SEVERITY_WARNING
# define STATUS_SEVERITY_WARNING 0x2
#endif

#ifndef STATUS_SEVERITY_ERROR
# define STATUS_SEVERITY_ERROR 0x3
#endif

#ifndef FACILITY_NTWIN32
# define FACILITY_NTWIN32 0x7
#endif

#ifndef NT_SUCCESS
# define NT_SUCCESS(status) (((NTSTATUS) (status)) >= 0)
#endif

#ifndef NT_INFORMATION
# define NT_INFORMATION(status) ((((ULONG) (status)) >> 30) == 1)
#endif

#ifndef NT_WARNING
# define NT_WARNING(status) ((((ULONG) (status)) >> 30) == 2)
#endif

#ifndef NT_ERROR
# define NT_ERROR(status) ((((ULONG) (status)) >> 30) == 3)
#endif

#ifndef STATUS_SUCCESS
# define STATUS_SUCCESS ((NTSTATUS) 0x00000000L)
#endif

#ifndef STATUS_WAIT_0
# define STATUS_WAIT_0 ((NTSTATUS) 0x00000000L)
#endif

#ifndef STATUS_WAIT_1
# define STATUS_WAIT_1 ((NTSTATUS) 0x00000001L)
#endif

#ifndef STATUS_WAIT_2
# define STATUS_WAIT_2 ((NTSTATUS) 0x00000002L)
#endif

#ifndef STATUS_WAIT_3
# define STATUS_WAIT_3 ((NTSTATUS) 0x00000003L)
#endif

#ifndef STATUS_WAIT_63
# define STATUS_WAIT_63 ((NTSTATUS) 0x0000003FL)
#endif

#ifndef STATUS_ABANDONED
# define STATUS_ABANDONED ((NTSTATUS) 0x00000080L)
#endif

#ifndef STATUS_ABANDONED_WAIT_0
# define STATUS_ABANDONED_WAIT_0 ((NTSTATUS) 0x00000080L)
#endif

#ifndef STATUS_ABANDONED_WAIT_63
# define STATUS_ABANDONED_WAIT_63 ((NTSTATUS) 0x000000BFL)
#endif

#ifndef STATUS_USER_APC
# define STATUS_USER_APC ((NTSTATUS) 0x000000C0L)
#endif

#ifndef STATUS_KERNEL_APC
# define STATUS_KERNEL_APC ((NTSTATUS) 0x00000100L)
#endif

#ifndef STATUS_ALERTED
# define STATUS_ALERTED ((NTSTATUS) 0x00000101L)
#endif

#ifndef STATUS_TIMEOUT
# define STATUS_TIMEOUT ((NTSTATUS) 0x00000102L)
#endif

#ifndef STATUS_PENDING
# define STATUS_PENDING ((NTSTATUS) 0x00000103L)
#endif

#ifndef STATUS_REPARSE
# define STATUS_REPARSE ((NTSTATUS) 0x00000104L)
#endif

#ifndef STATUS_MORE_ENTRIES
# define STATUS_MORE_ENTRIES ((NTSTATUS) 0x00000105L)
#endif

#ifndef STATUS_NOT_ALL_ASSIGNED
# define STATUS_NOT_ALL_ASSIGNED ((NTSTATUS) 0x00000106L)
#endif

#ifndef STATUS_SOME_NOT_MAPPED
# define STATUS_SOME_NOT_MAPPED ((NTSTATUS) 0x00000107L)
#endif

#ifndef STATUS_OPLOCK_BREAK_IN_PROGRESS
# define STATUS_OPLOCK_BREAK_IN_PROGRESS ((NTSTATUS) 0x00000108L)
#endif

#ifndef STATUS_VOLUME_MOUNTED
# define STATUS_VOLUME_MOUNTED ((NTSTATUS) 0x00000109L)
#endif

#ifndef STATUS_RXACT_COMMITTED
# define STATUS_RXACT_COMMITTED ((NTSTATUS) 0x0000010AL)
#endif

#ifndef STATUS_NOTIFY_CLEANUP
# define STATUS_NOTIFY_CLEANUP ((NTSTATUS) 0x0000010BL)
#endif

#ifndef STATUS_NOTIFY_ENUM_DIR
# define STATUS_NOTIFY_ENUM_DIR ((NTSTATUS) 0x0000010CL)
#endif

#ifndef STATUS_NO_QUOTAS_FOR_ACCOUNT
# define STATUS_NO_QUOTAS_FOR_ACCOUNT ((NTSTATUS) 0x0000010DL)
#endif

#ifndef STATUS_PRIMARY_TRANSPORT_CONNECT_FAILED
# define STATUS_PRIMARY_TRANSPORT_CONNECT_FAILED ((NTSTATUS) 0x0000010EL)
#endif

#ifndef STATUS_PAGE_FAULT_TRANSITION
# define STATUS_PAGE_FAULT_TRANSITION ((NTSTATUS) 0x00000110L)
#endif

#ifndef STATUS_PAGE_FAULT_DEMAND_ZERO
# define STATUS_PAGE_FAULT_DEMAND_ZERO ((NTSTATUS) 0x00000111L)
#endif

#ifndef STATUS_PAGE_FAULT_COPY_ON_WRITE
# define STATUS_PAGE_FAULT_COPY_ON_WRITE ((NTSTATUS) 0x00000112L)
#endif

#ifndef STATUS_PAGE_FAULT_GUARD_PAGE
# define STATUS_PAGE_FAULT_GUARD_PAGE ((NTSTATUS) 0x00000113L)
#endif

#ifndef STATUS_PAGE_FAULT_PAGING_FILE
# define STATUS_PAGE_FAULT_PAGING_FILE ((NTSTATUS) 0x00000114L)
#endif

#ifndef STATUS_CACHE_PAGE_LOCKED
# define STATUS_CACHE_PAGE_LOCKED ((NTSTATUS) 0x00000115L)
#endif

#ifndef STATUS_CRASH_DUMP
# define STATUS_CRASH_DUMP ((NTSTATUS) 0x00000116L)
#endif

#ifndef STATUS_BUFFER_ALL_ZEROS
# define STATUS_BUFFER_ALL_ZEROS ((NTSTATUS) 0x00000117L)
#endif

#ifndef STATUS_REPARSE_OBJECT
# define STATUS_REPARSE_OBJECT ((NTSTATUS) 0x00000118L)
#endif

#ifndef STATUS_RESOURCE_REQUIREMENTS_CHANGED
# define STATUS_RESOURCE_REQUIREMENTS_CHANGED ((NTSTATUS) 0x00000119L)
#endif

#ifndef STATUS_TRANSLATION_COMPLETE
# define STATUS_TRANSLATION_COMPLETE ((NTSTATUS) 0x00000120L)
#endif

#ifndef STATUS_DS_MEMBERSHIP_EVALUATED_LOCALLY
# define STATUS_DS_MEMBERSHIP_EVALUATED_LOCALLY ((NTSTATUS) 0x00000121L)
#endif

#ifndef STATUS_NOTHING_TO_TERMINATE
# define STATUS_NOTHING_TO_TERMINATE ((NTSTATUS) 0x00000122L)
#endif

#ifndef STATUS_PROCESS_NOT_IN_JOB
# define STATUS_PROCESS_NOT_IN_JOB ((NTSTATUS) 0x00000123L)
#endif

#ifndef STATUS_PROCESS_IN_JOB
# define STATUS_PROCESS_IN_JOB ((NTSTATUS) 0x00000124L)
#endif

#ifndef STATUS_VOLSNAP_HIBERNATE_READY
# define STATUS_VOLSNAP_HIBERNATE_READY ((NTSTATUS) 0x00000125L)
#endif

#ifndef STATUS_FSFILTER_OP_COMPLETED_SUCCESSFULLY
# define STATUS_FSFILTER_OP_COMPLETED_SUCCESSFULLY ((NTSTATUS) 0x00000126L)
#endif

#ifndef STATUS_INTERRUPT_VECTOR_ALREADY_CONNECTED
# define STATUS_INTERRUPT_VECTOR_ALREADY_CONNECTED ((NTSTATUS) 0x00000127L)
#endif

#ifndef STATUS_INTERRUPT_STILL_CONNECTED
# define STATUS_INTERRUPT_STILL_CONNECTED ((NTSTATUS) 0x00000128L)
#endif

#ifndef STATUS_PROCESS_CLONED
# define STATUS_PROCESS_CLONED ((NTSTATUS) 0x00000129L)
#endif

#ifndef STATUS_FILE_LOCKED_WITH_ONLY_READERS
# define STATUS_FILE_LOCKED_WITH_ONLY_READERS ((NTSTATUS) 0x0000012AL)
#endif

#ifndef STATUS_FILE_LOCKED_WITH_WRITERS
# define STATUS_FILE_LOCKED_WITH_WRITERS ((NTSTATUS) 0x0000012BL)
#endif

#ifndef STATUS_RESOURCEMANAGER_READ_ONLY
# define STATUS_RESOURCEMANAGER_READ_ONLY ((NTSTATUS) 0x00000202L)
#endif

#ifndef STATUS_RING_PREVIOUSLY_EMPTY
# define STATUS_RING_PREVIOUSLY_EMPTY ((NTSTATUS) 0x00000210L)
#endif

#ifndef STATUS_RING_PREVIOUSLY_FULL
# define STATUS_RING_PREVIOUSLY_FULL ((NTSTATUS) 0x00000211L)
#endif

#ifndef STATUS_RING_PREVIOUSLY_ABOVE_QUOTA
# define STATUS_RING_PREVIOUSLY_ABOVE_QUOTA ((NTSTATUS) 0x00000212L)
#endif

#ifndef STATUS_RING_NEWLY_EMPTY
# define STATUS_RING_NEWLY_EMPTY ((NTSTATUS) 0x00000213L)
#endif

#ifndef STATUS_RING_SIGNAL_OPPOSITE_ENDPOINT
# define STATUS_RING_SIGNAL_OPPOSITE_ENDPOINT ((NTSTATUS) 0x00000214L)
#endif

#ifndef STATUS_OPLOCK_SWITCHED_TO_NEW_HANDLE
# define STATUS_OPLOCK_SWITCHED_TO_NEW_HANDLE ((NTSTATUS) 0x00000215L)
#endif

#ifndef STATUS_OPLOCK_HANDLE_CLOSED
# define STATUS_OPLOCK_HANDLE_CLOSED ((NTSTATUS) 0x00000216L)
#endif

#ifndef STATUS_WAIT_FOR_OPLOCK
# define STATUS_WAIT_FOR_OPLOCK ((NTSTATUS) 0x00000367L)
#endif

#ifndef STATUS_OBJECT_NAME_EXISTS
# define STATUS_OBJECT_NAME_EXISTS ((NTSTATUS) 0x40000000L)
#endif

#ifndef STATUS_THREAD_WAS_SUSPENDED
# define STATUS_THREAD_WAS_SUSPENDED ((NTSTATUS) 0x40000001L)
#endif

#ifndef STATUS_WORKING_SET_LIMIT_RANGE
# define STATUS_WORKING_SET_LIMIT_RANGE ((NTSTATUS) 0x40000002L)
#endif

#ifndef STATUS_IMAGE_NOT_AT_BASE
# define STATUS_IMAGE_NOT_AT_BASE ((NTSTATUS) 0x40000003L)
#endif

#ifndef STATUS_RXACT_STATE_CREATED
# define STATUS_RXACT_STATE_CREATED ((NTSTATUS) 0x40000004L)
#endif

#ifndef STATUS_SEGMENT_NOTIFICATION
# define STATUS_SEGMENT_NOTIFICATION ((NTSTATUS) 0x40000005L)
#endif

#ifndef STATUS_LOCAL_USER_SESSION_KEY
# define STATUS_LOCAL_USER_SESSION_KEY ((NTSTATUS) 0x40000006L)
#endif

#ifndef STATUS_BAD_CURRENT_DIRECTORY
# define STATUS_BAD_CURRENT_DIRECTORY ((NTSTATUS) 0x40000007L)
#endif

#ifndef STATUS_SERIAL_MORE_WRITES
# define STATUS_SERIAL_MORE_WRITES ((NTSTATUS) 0x40000008L)
#endif

#ifndef STATUS_REGISTRY_RECOVERED
# define STATUS_REGISTRY_RECOVERED ((NTSTATUS) 0x40000009L)
#endif

#ifndef STATUS_FT_READ_RECOVERY_FROM_BACKUP
# define STATUS_FT_READ_RECOVERY_FROM_BACKUP ((NTSTATUS) 0x4000000AL)
#endif

#ifndef STATUS_FT_WRITE_RECOVERY
# define STATUS_FT_WRITE_RECOVERY ((NTSTATUS) 0x4000000BL)
#endif

#ifndef STATUS_SERIAL_COUNTER_TIMEOUT
# define STATUS_SERIAL_COUNTER_TIMEOUT ((NTSTATUS) 0x4000000CL)
#endif

#ifndef STATUS_NULL_LM_PASSWORD
# define STATUS_NULL_LM_PASSWORD ((NTSTATUS) 0x4000000DL)
#endif

#ifndef STATUS_IMAGE_MACHINE_TYPE_MISMATCH
# define STATUS_IMAGE_MACHINE_TYPE_MISMATCH ((NTSTATUS) 0x4000000EL)
#endif

#ifndef STATUS_RECEIVE_PARTIAL
# define STATUS_RECEIVE_PARTIAL ((NTSTATUS) 0x4000000FL)
#endif

#ifndef STATUS_RECEIVE_EXPEDITED
# define STATUS_RECEIVE_EXPEDITED ((NTSTATUS) 0x40000010L)
#endif

#ifndef STATUS_RECEIVE_PARTIAL_EXPEDITED
# define STATUS_RECEIVE_PARTIAL_EXPEDITED ((NTSTATUS) 0x40000011L)
#endif

#ifndef STATUS_EVENT_DONE
# define STATUS_EVENT_DONE ((NTSTATUS) 0x40000012L)
#endif

#ifndef STATUS_EVENT_PENDING
# define STATUS_EVENT_PENDING ((NTSTATUS) 0x40000013L)
#endif

#ifndef STATUS_CHECKING_FILE_SYSTEM
# define STATUS_CHECKING_FILE_SYSTEM ((NTSTATUS) 0x40000014L)
#endif

#ifndef STATUS_FATAL_APP_EXIT
# define STATUS_FATAL_APP_EXIT ((NTSTATUS) 0x40000015L)
#endif

#ifndef STATUS_PREDEFINED_HANDLE
# define STATUS_PREDEFINED_HANDLE ((NTSTATUS) 0x40000016L)
#endif

#ifndef STATUS_WAS_UNLOCKED
# define STATUS_WAS_UNLOCKED ((NTSTATUS) 0x40000017L)
#endif

#ifndef STATUS_SERVICE_NOTIFICATION
# define STATUS_SERVICE_NOTIFICATION ((NTSTATUS) 0x40000018L)
#endif

#ifndef STATUS_WAS_LOCKED
# define STATUS_WAS_LOCKED ((NTSTATUS) 0x40000019L)
#endif

#ifndef STATUS_LOG_HARD_ERROR
# define STATUS_LOG_HARD_ERROR ((NTSTATUS) 0x4000001AL)
#endif

#ifndef STATUS_ALREADY_WIN32
# define STATUS_ALREADY_WIN32 ((NTSTATUS) 0x4000001BL)
#endif

#ifndef STATUS_WX86_UNSIMULATE
# define STATUS_WX86_UNSIMULATE ((NTSTATUS) 0x4000001CL)
#endif

#ifndef STATUS_WX86_CONTINUE
# define STATUS_WX86_CONTINUE ((NTSTATUS) 0x4000001DL)
#endif

#ifndef STATUS_WX86_SINGLE_STEP
# define STATUS_WX86_SINGLE_STEP ((NTSTATUS) 0x4000001EL)
#endif

#ifndef STATUS_WX86_BREAKPOINT
# define STATUS_WX86_BREAKPOINT ((NTSTATUS) 0x4000001FL)
#endif

#ifndef STATUS_WX86_EXCEPTION_CONTINUE
# define STATUS_WX86_EXCEPTION_CONTINUE ((NTSTATUS) 0x40000020L)
#endif

#ifndef STATUS_WX86_EXCEPTION_LASTCHANCE
# define STATUS_WX86_EXCEPTION_LASTCHANCE ((NTSTATUS) 0x40000021L)
#endif

#ifndef STATUS_WX86_EXCEPTION_CHAIN
# define STATUS_WX86_EXCEPTION_CHAIN ((NTSTATUS) 0x40000022L)
#endif

#ifndef STATUS_IMAGE_MACHINE_TYPE_MISMATCH_EXE
# define STATUS_IMAGE_MACHINE_TYPE_MISMATCH_EXE ((NTSTATUS) 0x40000023L)
#endif

#ifndef STATUS_NO_YIELD_PERFORMED
# define STATUS_NO_YIELD_PERFORMED ((NTSTATUS) 0x40000024L)
#endif

#ifndef STATUS_TIMER_RESUME_IGNORED
# define STATUS_TIMER_RESUME_IGNORED ((NTSTATUS) 0x40000025L)
#endif

#ifndef STATUS_ARBITRATION_UNHANDLED
# define STATUS_ARBITRATION_UNHANDLED ((NTSTATUS) 0x40000026L)
#endif

#ifndef STATUS_CARDBUS_NOT_SUPPORTED
# define STATUS_CARDBUS_NOT_SUPPORTED ((NTSTATUS) 0x40000027L)
#endif

#ifndef STATUS_WX86_CREATEWX86TIB
# define STATUS_WX86_CREATEWX86TIB ((NTSTATUS) 0x40000028L)
#endif

#ifndef STATUS_MP_PROCESSOR_MISMATCH
# define STATUS_MP_PROCESSOR_MISMATCH ((NTSTATUS) 0x40000029L)
#endif

#ifndef STATUS_HIBERNATED
# define STATUS_HIBERNATED ((NTSTATUS) 0x4000002AL)
#endif

#ifndef STATUS_RESUME_HIBERNATION
# define STATUS_RESUME_HIBERNATION ((NTSTATUS) 0x4000002BL)
#endif

#ifndef STATUS_FIRMWARE_UPDATED
# define STATUS_FIRMWARE_UPDATED ((NTSTATUS) 0x4000002CL)
#endif

#ifndef STATUS_DRIVERS_LEAKING_LOCKED_PAGES
# define STATUS_DRIVERS_LEAKING_LOCKED_PAGES ((NTSTATUS) 0x4000002DL)
#endif

#ifndef STATUS_MESSAGE_RETRIEVED
# define STATUS_MESSAGE_RETRIEVED ((NTSTATUS) 0x4000002EL)
#endif

#ifndef STATUS_SYSTEM_POWERSTATE_TRANSITION
# define STATUS_SYSTEM_POWERSTATE_TRANSITION ((NTSTATUS) 0x4000002FL)
#endif

#ifndef STATUS_ALPC_CHECK_COMPLETION_LIST
# define STATUS_ALPC_CHECK_COMPLETION_LIST ((NTSTATUS) 0x40000030L)
#endif

#ifndef STATUS_SYSTEM_POWERSTATE_COMPLEX_TRANSITION
# define STATUS_SYSTEM_POWERSTATE_COMPLEX_TRANSITION ((NTSTATUS) 0x40000031L)
#endif

#ifndef STATUS_ACCESS_AUDIT_BY_POLICY
# define STATUS_ACCESS_AUDIT_BY_POLICY ((NTSTATUS) 0x40000032L)
#endif

#ifndef STATUS_ABANDON_HIBERFILE
# define STATUS_ABANDON_HIBERFILE ((NTSTATUS) 0x40000033L)
#endif

#ifndef STATUS_BIZRULES_NOT_ENABLED
# define STATUS_BIZRULES_NOT_ENABLED ((NTSTATUS) 0x40000034L)
#endif

#ifndef STATUS_GUARD_PAGE_VIOLATION
# define STATUS_GUARD_PAGE_VIOLATION ((NTSTATUS) 0x80000001L)
#endif

#ifndef STATUS_DATATYPE_MISALIGNMENT
# define STATUS_DATATYPE_MISALIGNMENT ((NTSTATUS) 0x80000002L)
#endif

#ifndef STATUS_BREAKPOINT
# define STATUS_BREAKPOINT ((NTSTATUS) 0x80000003L)
#endif

#ifndef STATUS_SINGLE_STEP
# define STATUS_SINGLE_STEP ((NTSTATUS) 0x80000004L)
#endif

#ifndef STATUS_BUFFER_OVERFLOW
# define STATUS_BUFFER_OVERFLOW ((NTSTATUS) 0x80000005L)
#endif

#ifndef STATUS_NO_MORE_FILES
# define STATUS_NO_MORE_FILES ((NTSTATUS) 0x80000006L)
#endif

#ifndef STATUS_WAKE_SYSTEM_DEBUGGER
# define STATUS_WAKE_SYSTEM_DEBUGGER ((NTSTATUS) 0x80000007L)
#endif

#ifndef STATUS_HANDLES_CLOSED
# define STATUS_HANDLES_CLOSED ((NTSTATUS) 0x8000000AL)
#endif

#ifndef STATUS_NO_INHERITANCE
# define STATUS_NO_INHERITANCE ((NTSTATUS) 0x8000000BL)
#endif

#ifndef STATUS_GUID_SUBSTITUTION_MADE
# define STATUS_GUID_SUBSTITUTION_MADE ((NTSTATUS) 0x8000000CL)
#endif

#ifndef STATUS_PARTIAL_COPY
# define STATUS_PARTIAL_COPY ((NTSTATUS) 0x8000000DL)
#endif

#ifndef STATUS_DEVICE_PAPER_EMPTY
# define STATUS_DEVICE_PAPER_EMPTY ((NTSTATUS) 0x8000000EL)
#endif

#ifndef STATUS_DEVICE_POWERED_OFF
# define STATUS_DEVICE_POWERED_OFF ((NTSTATUS) 0x8000000FL)
#endif

#ifndef STATUS_DEVICE_OFF_LINE
# define STATUS_DEVICE_OFF_LINE ((NTSTATUS) 0x80000010L)
#endif

#ifndef STATUS_DEVICE_BUSY
# define STATUS_DEVICE_BUSY ((NTSTATUS) 0x80000011L)
#endif

#ifndef STATUS_NO_MORE_EAS
# define STATUS_NO_MORE_EAS ((NTSTATUS) 0x80000012L)
#endif

#ifndef STATUS_INVALID_EA_NAME
# define STATUS_INVALID_EA_NAME ((NTSTATUS) 0x80000013L)
#endif

#ifndef STATUS_EA_LIST_INCONSISTENT
# define STATUS_EA_LIST_INCONSISTENT ((NTSTATUS) 0x80000014L)
#endif

#ifndef STATUS_INVALID_EA_FLAG
# define STATUS_INVALID_EA_FLAG ((NTSTATUS) 0x80000015L)
#endif

#ifndef STATUS_VERIFY_REQUIRED
# define STATUS_VERIFY_REQUIRED ((NTSTATUS) 0x80000016L)
#endif

#ifndef STATUS_EXTRANEOUS_INFORMATION
# define STATUS_EXTRANEOUS_INFORMATION ((NTSTATUS) 0x80000017L)
#endif

#ifndef STATUS_RXACT_COMMIT_NECESSARY
# define STATUS_RXACT_COMMIT_NECESSARY ((NTSTATUS) 0x80000018L)
#endif

#ifndef STATUS_NO_MORE_ENTRIES
# define STATUS_NO_MORE_ENTRIES ((NTSTATUS) 0x8000001AL)
#endif

#ifndef STATUS_FILEMARK_DETECTED
# define STATUS_FILEMARK_DETECTED ((NTSTATUS) 0x8000001BL)
#endif

#ifndef STATUS_MEDIA_CHANGED
# define STATUS_MEDIA_CHANGED ((NTSTATUS) 0x8000001CL)
#endif

#ifndef STATUS_BUS_RESET
# define STATUS_BUS_RESET ((NTSTATUS) 0x8000001DL)
#endif

#ifndef STATUS_END_OF_MEDIA
# define STATUS_END_OF_MEDIA ((NTSTATUS) 0x8000001EL)
#endif

#ifndef STATUS_BEGINNING_OF_MEDIA
# define STATUS_BEGINNING_OF_MEDIA ((NTSTATUS) 0x8000001FL)
#endif

#ifndef STATUS_MEDIA_CHECK
# define STATUS_MEDIA_CHECK ((NTSTATUS) 0x80000020L)
#endif

#ifndef STATUS_SETMARK_DETECTED
# define STATUS_SETMARK_DETECTED ((NTSTATUS) 0x80000021L)
#endif

#ifndef STATUS_NO_DATA_DETECTED
# define STATUS_NO_DATA_DETECTED ((NTSTATUS) 0x80000022L)
#endif

#ifndef STATUS_REDIRECTOR_HAS_OPEN_HANDLES
# define STATUS_REDIRECTOR_HAS_OPEN_HANDLES ((NTSTATUS) 0x80000023L)
#endif

#ifndef STATUS_SERVER_HAS_OPEN_HANDLES
# define STATUS_SERVER_HAS_OPEN_HANDLES ((NTSTATUS) 0x80000024L)
#endif

#ifndef STATUS_ALREADY_DISCONNECTED
# define STATUS_ALREADY_DISCONNECTED ((NTSTATUS) 0x80000025L)
#endif

#ifndef STATUS_LONGJUMP
# define STATUS_LONGJUMP ((NTSTATUS) 0x80000026L)
#endif

#ifndef STATUS_CLEANER_CARTRIDGE_INSTALLED
# define STATUS_CLEANER_CARTRIDGE_INSTALLED ((NTSTATUS) 0x80000027L)
#endif

#ifndef STATUS_PLUGPLAY_QUERY_VETOED
# define STATUS_PLUGPLAY_QUERY_VETOED ((NTSTATUS) 0x80000028L)
#endif

#ifndef STATUS_UNWIND_CONSOLIDATE
# define STATUS_UNWIND_CONSOLIDATE ((NTSTATUS) 0x80000029L)
#endif

#ifndef STATUS_REGISTRY_HIVE_RECOVERED
# define STATUS_REGISTRY_HIVE_RECOVERED ((NTSTATUS) 0x8000002AL)
#endif

#ifndef STATUS_DLL_MIGHT_BE_INSECURE
# define STATUS_DLL_MIGHT_BE_INSECURE ((NTSTATUS) 0x8000002BL)
#endif

#ifndef STATUS_DLL_MIGHT_BE_INCOMPATIBLE
# define STATUS_DLL_MIGHT_BE_INCOMPATIBLE ((NTSTATUS) 0x8000002CL)
#endif

#ifndef STATUS_STOPPED_ON_SYMLINK
# define STATUS_STOPPED_ON_SYMLINK ((NTSTATUS) 0x8000002DL)
#endif

#ifndef STATUS_CANNOT_GRANT_REQUESTED_OPLOCK
# define STATUS_CANNOT_GRANT_REQUESTED_OPLOCK ((NTSTATUS) 0x8000002EL)
#endif

#ifndef STATUS_NO_ACE_CONDITION
# define STATUS_NO_ACE_CONDITION ((NTSTATUS) 0x8000002FL)
#endif

#ifndef STATUS_UNSUCCESSFUL
# define STATUS_UNSUCCESSFUL ((NTSTATUS) 0xC0000001L)
#endif

#ifndef STATUS_NOT_IMPLEMENTED
# define STATUS_NOT_IMPLEMENTED ((NTSTATUS) 0xC0000002L)
#endif

#ifndef STATUS_INVALID_INFO_CLASS
# define STATUS_INVALID_INFO_CLASS ((NTSTATUS) 0xC0000003L)
#endif

#ifndef STATUS_INFO_LENGTH_MISMATCH
# define STATUS_INFO_LENGTH_MISMATCH ((NTSTATUS) 0xC0000004L)
#endif

#ifndef STATUS_ACCESS_VIOLATION
# define STATUS_ACCESS_VIOLATION ((NTSTATUS) 0xC0000005L)
#endif

#ifndef STATUS_IN_PAGE_ERROR
# define STATUS_IN_PAGE_ERROR ((NTSTATUS) 0xC0000006L)
#endif

#ifndef STATUS_PAGEFILE_QUOTA
# define STATUS_PAGEFILE_QUOTA ((NTSTATUS) 0xC0000007L)
#endif

#ifndef STATUS_INVALID_HANDLE
# define STATUS_INVALID_HANDLE ((NTSTATUS) 0xC0000008L)
#endif

#ifndef STATUS_BAD_INITIAL_STACK
# define STATUS_BAD_INITIAL_STACK ((NTSTATUS) 0xC0000009L)
#endif

#ifndef STATUS_BAD_INITIAL_PC
# define STATUS_BAD_INITIAL_PC ((NTSTATUS) 0xC000000AL)
#endif

#ifndef STATUS_INVALID_CID
# define STATUS_INVALID_CID ((NTSTATUS) 0xC000000BL)
#endif

#ifndef STATUS_TIMER_NOT_CANCELED
# define STATUS_TIMER_NOT_CANCELED ((NTSTATUS) 0xC000000CL)
#endif

#ifndef STATUS_INVALID_PARAMETER
# define STATUS_INVALID_PARAMETER ((NTSTATUS) 0xC000000DL)
#endif

#ifndef STATUS_NO_SUCH_DEVICE
# define STATUS_NO_SUCH_DEVICE ((NTSTATUS) 0xC000000EL)
#endif

#ifndef STATUS_NO_SUCH_FILE
# define STATUS_NO_SUCH_FILE ((NTSTATUS) 0xC000000FL)
#endif

#ifndef STATUS_INVALID_DEVICE_REQUEST
# define STATUS_INVALID_DEVICE_REQUEST ((NTSTATUS) 0xC0000010L)
#endif

#ifndef STATUS_END_OF_FILE
# define STATUS_END_OF_FILE ((NTSTATUS) 0xC0000011L)
#endif

#ifndef STATUS_WRONG_VOLUME
# define STATUS_WRONG_VOLUME ((NTSTATUS) 0xC0000012L)
#endif

#ifndef STATUS_NO_MEDIA_IN_DEVICE
# define STATUS_NO_MEDIA_IN_DEVICE ((NTSTATUS) 0xC0000013L)
#endif

#ifndef STATUS_UNRECOGNIZED_MEDIA
# define STATUS_UNRECOGNIZED_MEDIA ((NTSTATUS) 0xC0000014L)
#endif

#ifndef STATUS_NONEXISTENT_SECTOR
# define STATUS_NONEXISTENT_SECTOR ((NTSTATUS) 0xC0000015L)
#endif

#ifndef STATUS_MORE_PROCESSING_REQUIRED
# define STATUS_MORE_PROCESSING_REQUIRED ((NTSTATUS) 0xC0000016L)
#endif

#ifndef STATUS_NO_MEMORY
# define STATUS_NO_MEMORY ((NTSTATUS) 0xC0000017L)
#endif

#ifndef STATUS_CONFLICTING_ADDRESSES
# define STATUS_CONFLICTING_ADDRESSES ((NTSTATUS) 0xC0000018L)
#endif

#ifndef STATUS_NOT_MAPPED_VIEW
# define STATUS_NOT_MAPPED_VIEW ((NTSTATUS) 0xC0000019L)
#endif

#ifndef STATUS_UNABLE_TO_FREE_VM
# define STATUS_UNABLE_TO_FREE_VM ((NTSTATUS) 0xC000001AL)
#endif

#ifndef STATUS_UNABLE_TO_DELETE_SECTION
# define STATUS_UNABLE_TO_DELETE_SECTION ((NTSTATUS) 0xC000001BL)
#endif

#ifndef STATUS_INVALID_SYSTEM_SERVICE
# define STATUS_INVALID_SYSTEM_SERVICE ((NTSTATUS) 0xC000001CL)
#endif

#ifndef STATUS_ILLEGAL_INSTRUCTION
# define STATUS_ILLEGAL_INSTRUCTION ((NTSTATUS) 0xC000001DL)
#endif

#ifndef STATUS_INVALID_LOCK_SEQUENCE
# define STATUS_INVALID_LOCK_SEQUENCE ((NTSTATUS) 0xC000001EL)
#endif

#ifndef STATUS_INVALID_VIEW_SIZE
# define STATUS_INVALID_VIEW_SIZE ((NTSTATUS) 0xC000001FL)
#endif

#ifndef STATUS_INVALID_FILE_FOR_SECTION
# define STATUS_INVALID_FILE_FOR_SECTION ((NTSTATUS) 0xC0000020L)
#endif

#ifndef STATUS_ALREADY_COMMITTED
# define STATUS_ALREADY_COMMITTED ((NTSTATUS) 0xC0000021L)
#endif

#ifndef STATUS_ACCESS_DENIED
# define STATUS_ACCESS_DENIED ((NTSTATUS) 0xC0000022L)
#endif

#ifndef STATUS_BUFFER_TOO_SMALL
# define STATUS_BUFFER_TOO_SMALL ((NTSTATUS) 0xC0000023L)
#endif

#ifndef STATUS_OBJECT_TYPE_MISMATCH
# define STATUS_OBJECT_TYPE_MISMATCH ((NTSTATUS) 0xC0000024L)
#endif

#ifndef STATUS_NONCONTINUABLE_EXCEPTION
# define STATUS_NONCONTINUABLE_EXCEPTION ((NTSTATUS) 0xC0000025L)
#endif

#ifndef STATUS_INVALID_DISPOSITION
# define STATUS_INVALID_DISPOSITION ((NTSTATUS) 0xC0000026L)
#endif

#ifndef STATUS_UNWIND
# define STATUS_UNWIND ((NTSTATUS) 0xC0000027L)
#endif

#ifndef STATUS_BAD_STACK
# define STATUS_BAD_STACK ((NTSTATUS) 0xC0000028L)
#endif

#ifndef STATUS_INVALID_UNWIND_TARGET
# define STATUS_INVALID_UNWIND_TARGET ((NTSTATUS) 0xC0000029L)
#endif

#ifndef STATUS_NOT_LOCKED
# define STATUS_NOT_LOCKED ((NTSTATUS) 0xC000002AL)
#endif

#ifndef STATUS_PARITY_ERROR
# define STATUS_PARITY_ERROR ((NTSTATUS) 0xC000002BL)
#endif

#ifndef STATUS_UNABLE_TO_DECOMMIT_VM
# define STATUS_UNABLE_TO_DECOMMIT_VM ((NTSTATUS) 0xC000002CL)
#endif

#ifndef STATUS_NOT_COMMITTED
# define STATUS_NOT_COMMITTED ((NTSTATUS) 0xC000002DL)
#endif

#ifndef STATUS_INVALID_PORT_ATTRIBUTES
# define STATUS_INVALID_PORT_ATTRIBUTES ((NTSTATUS) 0xC000002EL)
#endif

#ifndef STATUS_PORT_MESSAGE_TOO_LONG
# define STATUS_PORT_MESSAGE_TOO_LONG ((NTSTATUS) 0xC000002FL)
#endif

#ifndef STATUS_INVALID_PARAMETER_MIX
# define STATUS_INVALID_PARAMETER_MIX ((NTSTATUS) 0xC0000030L)
#endif

#ifndef STATUS_INVALID_QUOTA_LOWER
# define STATUS_INVALID_QUOTA_LOWER ((NTSTATUS) 0xC0000031L)
#endif

#ifndef STATUS_DISK_CORRUPT_ERROR
# define STATUS_DISK_CORRUPT_ERROR ((NTSTATUS) 0xC0000032L)
#endif

#ifndef STATUS_OBJECT_NAME_INVALID
# define STATUS_OBJECT_NAME_INVALID ((NTSTATUS) 0xC0000033L)
#endif

#ifndef STATUS_OBJECT_NAME_NOT_FOUND
# define STATUS_OBJECT_NAME_NOT_FOUND ((NTSTATUS) 0xC0000034L)
#endif

#ifndef STATUS_OBJECT_NAME_COLLISION
# define STATUS_OBJECT_NAME_COLLISION ((NTSTATUS) 0xC0000035L)
#endif

#ifndef STATUS_PORT_DISCONNECTED
# define STATUS_PORT_DISCONNECTED ((NTSTATUS) 0xC0000037L)
#endif

#ifndef STATUS_DEVICE_ALREADY_ATTACHED
# define STATUS_DEVICE_ALREADY_ATTACHED ((NTSTATUS) 0xC0000038L)
#endif

#ifndef STATUS_OBJECT_PATH_INVALID
# define STATUS_OBJECT_PATH_INVALID ((NTSTATUS) 0xC0000039L)
#endif

#ifndef STATUS_OBJECT_PATH_NOT_FOUND
# define STATUS_OBJECT_PATH_NOT_FOUND ((NTSTATUS) 0xC000003AL)
#endif

#ifndef STATUS_OBJECT_PATH_SYNTAX_BAD
# define STATUS_OBJECT_PATH_SYNTAX_BAD ((NTSTATUS) 0xC000003BL)
#endif

#ifndef STATUS_DATA_OVERRUN
# define STATUS_DATA_OVERRUN ((NTSTATUS) 0xC000003CL)
#endif

#ifndef STATUS_DATA_LATE_ERROR
# define STATUS_DATA_LATE_ERROR ((NTSTATUS) 0xC000003DL)
#endif

#ifndef STATUS_DATA_ERROR
# define STATUS_DATA_ERROR ((NTSTATUS) 0xC000003EL)
#endif

#ifndef STATUS_CRC_ERROR
# define STATUS_CRC_ERROR ((NTSTATUS) 0xC000003FL)
#endif

#ifndef STATUS_SECTION_TOO_BIG
# define STATUS_SECTION_TOO_BIG ((NTSTATUS) 0xC0000040L)
#endif

#ifndef STATUS_PORT_CONNECTION_REFUSED
# define STATUS_PORT_CONNECTION_REFUSED ((NTSTATUS) 0xC0000041L)
#endif

#ifndef STATUS_INVALID_PORT_HANDLE
# define STATUS_INVALID_PORT_HANDLE ((NTSTATUS) 0xC0000042L)
#endif

#ifndef STATUS_SHARING_VIOLATION
# define STATUS_SHARING_VIOLATION ((NTSTATUS) 0xC0000043L)
#endif

#ifndef STATUS_QUOTA_EXCEEDED
# define STATUS_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000044L)
#endif

#ifndef STATUS_INVALID_PAGE_PROTECTION
# define STATUS_INVALID_PAGE_PROTECTION ((NTSTATUS) 0xC0000045L)
#endif

#ifndef STATUS_MUTANT_NOT_OWNED
# define STATUS_MUTANT_NOT_OWNED ((NTSTATUS) 0xC0000046L)
#endif

#ifndef STATUS_SEMAPHORE_LIMIT_EXCEEDED
# define STATUS_SEMAPHORE_LIMIT_EXCEEDED ((NTSTATUS) 0xC0000047L)
#endif

#ifndef STATUS_PORT_ALREADY_SET
# define STATUS_PORT_ALREADY_SET ((NTSTATUS) 0xC0000048L)
#endif

#ifndef STATUS_SECTION_NOT_IMAGE
# define STATUS_SECTION_NOT_IMAGE ((NTSTATUS) 0xC0000049L)
#endif

#ifndef STATUS_SUSPEND_COUNT_EXCEEDED
# define STATUS_SUSPEND_COUNT_EXCEEDED ((NTSTATUS) 0xC000004AL)
#endif

#ifndef STATUS_THREAD_IS_TERMINATING
# define STATUS_THREAD_IS_TERMINATING ((NTSTATUS) 0xC000004BL)
#endif

#ifndef STATUS_BAD_WORKING_SET_LIMIT
# define STATUS_BAD_WORKING_SET_LIMIT ((NTSTATUS) 0xC000004CL)
#endif

#ifndef STATUS_INCOMPATIBLE_FILE_MAP
# define STATUS_INCOMPATIBLE_FILE_MAP ((NTSTATUS) 0xC000004DL)
#endif

#ifndef STATUS_SECTION_PROTECTION
# define STATUS_SECTION_PROTECTION ((NTSTATUS) 0xC000004EL)
#endif

#ifndef STATUS_EAS_NOT_SUPPORTED
# define STATUS_EAS_NOT_SUPPORTED ((NTSTATUS) 0xC000004FL)
#endif

#ifndef STATUS_EA_TOO_LARGE
# define STATUS_EA_TOO_LARGE ((NTSTATUS) 0xC0000050L)
#endif

#ifndef STATUS_NONEXISTENT_EA_ENTRY
# define STATUS_NONEXISTENT_EA_ENTRY ((NTSTATUS) 0xC0000051L)
#endif

#ifndef STATUS_NO_EAS_ON_FILE
# define STATUS_NO_EAS_ON_FILE ((NTSTATUS) 0xC0000052L)
#endif

#ifndef STATUS_EA_CORRUPT_ERROR
# define STATUS_EA_CORRUPT_ERROR ((NTSTATUS) 0xC0000053L)
#endif

#ifndef STATUS_FILE_LOCK_CONFLICT
# define STATUS_FILE_LOCK_CONFLICT ((NTSTATUS) 0xC0000054L)
#endif

#ifndef STATUS_LOCK_NOT_GRANTED
# define STATUS_LOCK_NOT_GRANTED ((NTSTATUS) 0xC0000055L)
#endif

#ifndef STATUS_DELETE_PENDING
# define STATUS_DELETE_PENDING ((NTSTATUS) 0xC0000056L)
#endif

#ifndef STATUS_CTL_FILE_NOT_SUPPORTED
# define STATUS_CTL_FILE_NOT_SUPPORTED ((NTSTATUS) 0xC0000057L)
#endif

#ifndef STATUS_UNKNOWN_REVISION
# define STATUS_UNKNOWN_REVISION ((NTSTATUS) 0xC0000058L)
#endif

#ifndef STATUS_REVISION_MISMATCH
# define STATUS_REVISION_MISMATCH ((NTSTATUS) 0xC0000059L)
#endif

#ifndef STATUS_INVALID_OWNER
# define STATUS_INVALID_OWNER ((NTSTATUS) 0xC000005AL)
#endif

#ifndef STATUS_INVALID_PRIMARY_GROUP
# define STATUS_INVALID_PRIMARY_GROUP ((NTSTATUS) 0xC000005BL)
#endif

#ifndef STATUS_NO_IMPERSONATION_TOKEN
# define STATUS_NO_IMPERSONATION_TOKEN ((NTSTATUS) 0xC000005CL)
#endif

#ifndef STATUS_CANT_DISABLE_MANDATORY
# define STATUS_CANT_DISABLE_MANDATORY ((NTSTATUS) 0xC000005DL)
#endif

#ifndef STATUS_NO_LOGON_SERVERS
# define STATUS_NO_LOGON_SERVERS ((NTSTATUS) 0xC000005EL)
#endif

#ifndef STATUS_NO_SUCH_LOGON_SESSION
# define STATUS_NO_SUCH_LOGON_SESSION ((NTSTATUS) 0xC000005FL)
#endif

#ifndef STATUS_NO_SUCH_PRIVILEGE
# define STATUS_NO_SUCH_PRIVILEGE ((NTSTATUS) 0xC0000060L)
#endif

#ifndef STATUS_PRIVILEGE_NOT_HELD
# define STATUS_PRIVILEGE_NOT_HELD ((NTSTATUS) 0xC0000061L)
#endif

#ifndef STATUS_INVALID_ACCOUNT_NAME
# define STATUS_INVALID_ACCOUNT_NAME ((NTSTATUS) 0xC0000062L)
#endif

#ifndef STATUS_USER_EXISTS
# define STATUS_USER_EXISTS ((NTSTATUS) 0xC0000063L)
#endif

#ifndef STATUS_NO_SUCH_USER
# define STATUS_NO_SUCH_USER ((NTSTATUS) 0xC0000064L)
#endif

#ifndef STATUS_GROUP_EXISTS
# define STATUS_GROUP_EXISTS ((NTSTATUS) 0xC0000065L)
#endif

#ifndef STATUS_NO_SUCH_GROUP
# define STATUS_NO_SUCH_GROUP ((NTSTATUS) 0xC0000066L)
#endif

#ifndef STATUS_MEMBER_IN_GROUP
# define STATUS_MEMBER_IN_GROUP ((NTSTATUS) 0xC0000067L)
#endif

#ifndef STATUS_MEMBER_NOT_IN_GROUP
# define STATUS_MEMBER_NOT_IN_GROUP ((NTSTATUS) 0xC0000068L)
#endif

#ifndef STATUS_LAST_ADMIN
# define STATUS_LAST_ADMIN ((NTSTATUS) 0xC0000069L)
#endif

#ifndef STATUS_WRONG_PASSWORD
# define STATUS_WRONG_PASSWORD ((NTSTATUS) 0xC000006AL)
#endif

#ifndef STATUS_ILL_FORMED_PASSWORD
# define STATUS_ILL_FORMED_PASSWORD ((NTSTATUS) 0xC000006BL)
#endif

#ifndef STATUS_PASSWORD_RESTRICTION
# define STATUS_PASSWORD_RESTRICTION ((NTSTATUS) 0xC000006CL)
#endif

#ifndef STATUS_LOGON_FAILURE
# define STATUS_LOGON_FAILURE ((NTSTATUS) 0xC000006DL)
#endif

#ifndef STATUS_ACCOUNT_RESTRICTION
# define STATUS_ACCOUNT_RESTRICTION ((NTSTATUS) 0xC000006EL)
#endif

#ifndef STATUS_INVALID_LOGON_HOURS
# define STATUS_INVALID_LOGON_HOURS ((NTSTATUS) 0xC000006FL)
#endif

#ifndef STATUS_INVALID_WORKSTATION
# define STATUS_INVALID_WORKSTATION ((NTSTATUS) 0xC0000070L)
#endif

#ifndef STATUS_PASSWORD_EXPIRED
# define STATUS_PASSWORD_EXPIRED ((NTSTATUS) 0xC0000071L)
#endif

#ifndef STATUS_ACCOUNT_DISABLED
# define STATUS_ACCOUNT_DISABLED ((NTSTATUS) 0xC0000072L)
#endif

#ifndef STATUS_NONE_MAPPED
# define STATUS_NONE_MAPPED ((NTSTATUS) 0xC0000073L)
#endif

#ifndef STATUS_TOO_MANY_LUIDS_REQUESTED
# define STATUS_TOO_MANY_LUIDS_REQUESTED ((NTSTATUS) 0xC0000074L)
#endif

#ifndef STATUS_LUIDS_EXHAUSTED
# define STATUS_LUIDS_EXHAUSTED ((NTSTATUS) 0xC0000075L)
#endif

#ifndef STATUS_INVALID_SUB_AUTHORITY
# define STATUS_INVALID_SUB_AUTHORITY ((NTSTATUS) 0xC0000076L)
#endif

#ifndef STATUS_INVALID_ACL
# define STATUS_INVALID_ACL ((NTSTATUS) 0xC0000077L)
#endif

#ifndef STATUS_INVALID_SID
# define STATUS_INVALID_SID ((NTSTATUS) 0xC0000078L)
#endif

#ifndef STATUS_INVALID_SECURITY_DESCR
# define STATUS_INVALID_SECURITY_DESCR ((NTSTATUS) 0xC0000079L)
#endif

#ifndef STATUS_PROCEDURE_NOT_FOUND
# define STATUS_PROCEDURE_NOT_FOUND ((NTSTATUS) 0xC000007AL)
#endif

#ifndef STATUS_INVALID_IMAGE_FORMAT
# define STATUS_INVALID_IMAGE_FORMAT ((NTSTATUS) 0xC000007BL)
#endif

#ifndef STATUS_NO_TOKEN
# define STATUS_NO_TOKEN ((NTSTATUS) 0xC000007CL)
#endif

#ifndef STATUS_BAD_INHERITANCE_ACL
# define STATUS_BAD_INHERITANCE_ACL ((NTSTATUS) 0xC000007DL)
#endif

#ifndef STATUS_RANGE_NOT_LOCKED
# define STATUS_RANGE_NOT_LOCKED ((NTSTATUS) 0xC000007EL)
#endif

#ifndef STATUS_DISK_FULL
# define STATUS_DISK_FULL ((NTSTATUS) 0xC000007FL)
#endif

#ifndef STATUS_SERVER_DISABLED
# define STATUS_SERVER_DISABLED ((NTSTATUS) 0xC0000080L)
#endif

#ifndef STATUS_SERVER_NOT_DISABLED
# define STATUS_SERVER_NOT_DISABLED ((NTSTATUS) 0xC0000081L)
#endif

#ifndef STATUS_TOO_MANY_GUIDS_REQUESTED
# define STATUS_TOO_MANY_GUIDS_REQUESTED ((NTSTATUS) 0xC0000082L)
#endif

#ifndef STATUS_GUIDS_EXHAUSTED
# define STATUS_GUIDS_EXHAUSTED ((NTSTATUS) 0xC0000083L)
#endif

#ifndef STATUS_INVALID_ID_AUTHORITY
# define STATUS_INVALID_ID_AUTHORITY ((NTSTATUS) 0xC0000084L)
#endif

#ifndef STATUS_AGENTS_EXHAUSTED
# define STATUS_AGENTS_EXHAUSTED ((NTSTATUS) 0xC0000085L)
#endif

#ifndef STATUS_INVALID_VOLUME_LABEL
# define STATUS_INVALID_VOLUME_LABEL ((NTSTATUS) 0xC0000086L)
#endif

#ifndef STATUS_SECTION_NOT_EXTENDED
# define STATUS_SECTION_NOT_EXTENDED ((NTSTATUS) 0xC0000087L)
#endif

#ifndef STATUS_NOT_MAPPED_DATA
# define STATUS_NOT_MAPPED_DATA ((NTSTATUS) 0xC0000088L)
#endif

#ifndef STATUS_RESOURCE_DATA_NOT_FOUND
# define STATUS_RESOURCE_DATA_NOT_FOUND ((NTSTATUS) 0xC0000089L)
#endif

#ifndef STATUS_RESOURCE_TYPE_NOT_FOUND
# define STATUS_RESOURCE_TYPE_NOT_FOUND ((NTSTATUS) 0xC000008AL)
#endif

#ifndef STATUS_RESOURCE_NAME_NOT_FOUND
# define STATUS_RESOURCE_NAME_NOT_FOUND ((NTSTATUS) 0xC000008BL)
#endif

#ifndef STATUS_ARRAY_BOUNDS_EXCEEDED
# define STATUS_ARRAY_BOUNDS_EXCEEDED ((NTSTATUS) 0xC000008CL)
#endif

#ifndef STATUS_FLOAT_DENORMAL_OPERAND
# define STATUS_FLOAT_DENORMAL_OPERAND ((NTSTATUS) 0xC000008DL)
#endif

#ifndef STATUS_FLOAT_DIVIDE_BY_ZERO
# define STATUS_FLOAT_DIVIDE_BY_ZERO ((NTSTATUS) 0xC000008EL)
#endif

#ifndef STATUS_FLOAT_INEXACT_RESULT
# define STATUS_FLOAT_INEXACT_RESULT ((NTSTATUS) 0xC000008FL)
#endif

#ifndef STATUS_FLOAT_INVALID_OPERATION
# define STATUS_FLOAT_INVALID_OPERATION ((NTSTATUS) 0xC0000090L)
#endif

#ifndef STATUS_FLOAT_OVERFLOW
# define STATUS_FLOAT_OVERFLOW ((NTSTATUS) 0xC0000091L)
#endif

#ifndef STATUS_FLOAT_STACK_CHECK
# define STATUS_FLOAT_STACK_CHECK ((NTSTATUS) 0xC0000092L)
#endif

#ifndef STATUS_FLOAT_UNDERFLOW
# define STATUS_FLOAT_UNDERFLOW ((NTSTATUS) 0xC0000093L)
#endif

#ifndef STATUS_INTEGER_DIVIDE_BY_ZERO
# define STATUS_INTEGER_DIVIDE_BY_ZERO ((NTSTATUS) 0xC0000094L)
#endif

#ifndef STATUS_INTEGER_OVERFLOW
# define STATUS_INTEGER_OVERFLOW ((NTSTATUS) 0xC0000095L)
#endif

#ifndef STATUS_PRIVILEGED_INSTRUCTION
# define STATUS_PRIVILEGED_INSTRUCTION ((NTSTATUS) 0xC0000096L)
#endif

#ifndef STATUS_TOO_MANY_PAGING_FILES
# define STATUS_TOO_MANY_PAGING_FILES ((NTSTATUS) 0xC0000097L)
#endif

#ifndef STATUS_FILE_INVALID
# define STATUS_FILE_INVALID ((NTSTATUS) 0xC0000098L)
#endif

#ifndef STATUS_ALLOTTED_SPACE_EXCEEDED
# define STATUS_ALLOTTED_SPACE_EXCEEDED ((NTSTATUS) 0xC0000099L)
#endif

#ifndef STATUS_INSUFFICIENT_RESOURCES
# define STATUS_INSUFFICIENT_RESOURCES ((NTSTATUS) 0xC000009AL)
#endif

#ifndef STATUS_DFS_EXIT_PATH_FOUND
# define STATUS_DFS_EXIT_PATH_FOUND ((NTSTATUS) 0xC000009BL)
#endif

#ifndef STATUS_DEVICE_DATA_ERROR
# define STATUS_DEVICE_DATA_ERROR ((NTSTATUS) 0xC000009CL)
#endif

#ifndef STATUS_DEVICE_NOT_CONNECTED
# define STATUS_DEVICE_NOT_CONNECTED ((NTSTATUS) 0xC000009DL)
#endif

#ifndef STATUS_DEVICE_POWER_FAILURE
# define STATUS_DEVICE_POWER_FAILURE ((NTSTATUS) 0xC000009EL)
#endif

#ifndef STATUS_FREE_VM_NOT_AT_BASE
# define STATUS_FREE_VM_NOT_AT_BASE ((NTSTATUS) 0xC000009FL)
#endif

#ifndef STATUS_MEMORY_NOT_ALLOCATED
# define STATUS_MEMORY_NOT_ALLOCATED ((NTSTATUS) 0xC00000A0L)
#endif

#ifndef STATUS_WORKING_SET_QUOTA
# define STATUS_WORKING_SET_QUOTA ((NTSTATUS) 0xC00000A1L)
#endif

#ifndef STATUS_MEDIA_WRITE_PROTECTED
# define STATUS_MEDIA_WRITE_PROTECTED ((NTSTATUS) 0xC00000A2L)
#endif

#ifndef STATUS_DEVICE_NOT_READY
# define STATUS_DEVICE_NOT_READY ((NTSTATUS) 0xC00000A3L)
#endif

#ifndef STATUS_INVALID_GROUP_ATTRIBUTES
# define STATUS_INVALID_GROUP_ATTRIBUTES ((NTSTATUS) 0xC00000A4L)
#endif

#ifndef STATUS_BAD_IMPERSONATION_LEVEL
# define STATUS_BAD_IMPERSONATION_LEVEL ((NTSTATUS) 0xC00000A5L)
#endif

#ifndef STATUS_CANT_OPEN_ANONYMOUS
# define STATUS_CANT_OPEN_ANONYMOUS ((NTSTATUS) 0xC00000A6L)
#endif

#ifndef STATUS_BAD_VALIDATION_CLASS
# define STATUS_BAD_VALIDATION_CLASS ((NTSTATUS) 0xC00000A7L)
#endif

#ifndef STATUS_BAD_TOKEN_TYPE
# define STATUS_BAD_TOKEN_TYPE ((NTSTATUS) 0xC00000A8L)
#endif

#ifndef STATUS_BAD_MASTER_BOOT_RECORD
# define STATUS_BAD_MASTER_BOOT_RECORD ((NTSTATUS) 0xC00000A9L)
#endif

#ifndef STATUS_INSTRUCTION_MISALIGNMENT
# define STATUS_INSTRUCTION_MISALIGNMENT ((NTSTATUS) 0xC00000AAL)
#endif

#ifndef STATUS_INSTANCE_NOT_AVAILABLE
# define STATUS_INSTANCE_NOT_AVAILABLE ((NTSTATUS) 0xC00000ABL)
#endif

#ifndef STATUS_PIPE_NOT_AVAILABLE
# define STATUS_PIPE_NOT_AVAILABLE ((NTSTATUS) 0xC00000ACL)
#endif

#ifndef STATUS_INVALID_PIPE_STATE
# define STATUS_INVALID_PIPE_STATE ((NTSTATUS) 0xC00000ADL)
#endif

#ifndef STATUS_PIPE_BUSY
# define STATUS_PIPE_BUSY ((NTSTATUS) 0xC00000AEL)
#endif

#ifndef STATUS_ILLEGAL_FUNCTION
# define STATUS_ILLEGAL_FUNCTION ((NTSTATUS) 0xC00000AFL)
#endif

#ifndef STATUS_PIPE_DISCONNECTED
# define STATUS_PIPE_DISCONNECTED ((NTSTATUS) 0xC00000B0L)
#endif

#ifndef STATUS_PIPE_CLOSING
# define STATUS_PIPE_CLOSING ((NTSTATUS) 0xC00000B1L)
#endif

#ifndef STATUS_PIPE_CONNECTED
# define STATUS_PIPE_CONNECTED ((NTSTATUS) 0xC00000B2L)
#endif

#ifndef STATUS_PIPE_LISTENING
# define STATUS_PIPE_LISTENING ((NTSTATUS) 0xC00000B3L)
#endif

#ifndef STATUS_INVALID_READ_MODE
# define STATUS_INVALID_READ_MODE ((NTSTATUS) 0xC00000B4L)
#endif

#ifndef STATUS_IO_TIMEOUT
# define STATUS_IO_TIMEOUT ((NTSTATUS) 0xC00000B5L)
#endif

#ifndef STATUS_FILE_FORCED_CLOSED
# define STATUS_FILE_FORCED_CLOSED ((NTSTATUS) 0xC00000B6L)
#endif

#ifndef STATUS_PROFILING_NOT_STARTED
# define STATUS_PROFILING_NOT_STARTED ((NTSTATUS) 0xC00000B7L)
#endif

#ifndef STATUS_PROFILING_NOT_STOPPED
# define STATUS_PROFILING_NOT_STOPPED ((NTSTATUS) 0xC00000B8L)
#endif

#ifndef STATUS_COULD_NOT_INTERPRET
# define STATUS_COULD_NOT_INTERPRET ((NTSTATUS) 0xC00000B9L)
#endif

#ifndef STATUS_FILE_IS_A_DIRECTORY
# define STATUS_FILE_IS_A_DIRECTORY ((NTSTATUS) 0xC00000BAL)
#endif

#ifndef STATUS_NOT_SUPPORTED
# define STATUS_NOT_SUPPORTED ((NTSTATUS) 0xC00000BBL)
#endif

#ifndef STATUS_REMOTE_NOT_LISTENING
# define STATUS_REMOTE_NOT_LISTENING ((NTSTATUS) 0xC00000BCL)
#endif

#ifndef STATUS_DUPLICATE_NAME
# define STATUS_DUPLICATE_NAME ((NTSTATUS) 0xC00000BDL)
#endif

#ifndef STATUS_BAD_NETWORK_PATH
# define STATUS_BAD_NETWORK_PATH ((NTSTATUS) 0xC00000BEL)
#endif

#ifndef STATUS_NETWORK_BUSY
# define STATUS_NETWORK_BUSY ((NTSTATUS) 0xC00000BFL)
#endif

#ifndef STATUS_DEVICE_DOES_NOT_EXIST
# define STATUS_DEVICE_DOES_NOT_EXIST ((NTSTATUS) 0xC00000C0L)
#endif

#ifndef STATUS_TOO_MANY_COMMANDS
# define STATUS_TOO_MANY_COMMANDS ((NTSTATUS) 0xC00000C1L)
#endif

#ifndef STATUS_ADAPTER_HARDWARE_ERROR
# define STATUS_ADAPTER_HARDWARE_ERROR ((NTSTATUS) 0xC00000C2L)
#endif

#ifndef STATUS_INVALID_NETWORK_RESPONSE
# define STATUS_INVALID_NETWORK_RESPONSE ((NTSTATUS) 0xC00000C3L)
#endif

#ifndef STATUS_UNEXPECTED_NETWORK_ERROR
# define STATUS_UNEXPECTED_NETWORK_ERROR ((NTSTATUS) 0xC00000C4L)
#endif

#ifndef STATUS_BAD_REMOTE_ADAPTER
# define STATUS_BAD_REMOTE_ADAPTER ((NTSTATUS) 0xC00000C5L)
#endif

#ifndef STATUS_PRINT_QUEUE_FULL
# define STATUS_PRINT_QUEUE_FULL ((NTSTATUS) 0xC00000C6L)
#endif

#ifndef STATUS_NO_SPOOL_SPACE
# define STATUS_NO_SPOOL_SPACE ((NTSTATUS) 0xC00000C7L)
#endif

#ifndef STATUS_PRINT_CANCELLED
# define STATUS_PRINT_CANCELLED ((NTSTATUS) 0xC00000C8L)
#endif

#ifndef STATUS_NETWORK_NAME_DELETED
# define STATUS_NETWORK_NAME_DELETED ((NTSTATUS) 0xC00000C9L)
#endif

#ifndef STATUS_NETWORK_ACCESS_DENIED
# define STATUS_NETWORK_ACCESS_DENIED ((NTSTATUS) 0xC00000CAL)
#endif

#ifndef STATUS_BAD_DEVICE_TYPE
# define STATUS_BAD_DEVICE_TYPE ((NTSTATUS) 0xC00000CBL)
#endif

#ifndef STATUS_BAD_NETWORK_NAME
# define STATUS_BAD_NETWORK_NAME ((NTSTATUS) 0xC00000CCL)
#endif

#ifndef STATUS_TOO_MANY_NAMES
# define STATUS_TOO_MANY_NAMES ((NTSTATUS) 0xC00000CDL)
#endif

#ifndef STATUS_TOO_MANY_SESSIONS
# define STATUS_TOO_MANY_SESSIONS ((NTSTATUS) 0xC00000CEL)
#endif

#ifndef STATUS_SHARING_PAUSED
# define STATUS_SHARING_PAUSED ((NTSTATUS) 0xC00000CFL)
#endif

#ifndef STATUS_REQUEST_NOT_ACCEPTED
# define STATUS_REQUEST_NOT_ACCEPTED ((NTSTATUS) 0xC00000D0L)
#endif

#ifndef STATUS_REDIRECTOR_PAUSED
# define STATUS_REDIRECTOR_PAUSED ((NTSTATUS) 0xC00000D1L)
#endif

#ifndef STATUS_NET_WRITE_FAULT
# define STATUS_NET_WRITE_FAULT ((NTSTATUS) 0xC00000D2L)
#endif

#ifndef STATUS_PROFILING_AT_LIMIT
# define STATUS_PROFILING_AT_LIMIT ((NTSTATUS) 0xC00000D3L)
#endif

#ifndef STATUS_NOT_SAME_DEVICE
# define STATUS_NOT_SAME_DEVICE ((NTSTATUS) 0xC00000D4L)
#endif

#ifndef STATUS_FILE_RENAMED
# define STATUS_FILE_RENAMED ((NTSTATUS) 0xC00000D5L)
#endif

#ifndef STATUS_VIRTUAL_CIRCUIT_CLOSED
# define STATUS_VIRTUAL_CIRCUIT_CLOSED ((NTSTATUS) 0xC00000D6L)
#endif

#ifndef STATUS_NO_SECURITY_ON_OBJECT
# define STATUS_NO_SECURITY_ON_OBJECT ((NTSTATUS) 0xC00000D7L)
#endif

#ifndef STATUS_CANT_WAIT
# define STATUS_CANT_WAIT ((NTSTATUS) 0xC00000D8L)
#endif

#ifndef STATUS_PIPE_EMPTY
# define STATUS_PIPE_EMPTY ((NTSTATUS) 0xC00000D9L)
#endif

#ifndef STATUS_CANT_ACCESS_DOMAIN_INFO
# define STATUS_CANT_ACCESS_DOMAIN_INFO ((NTSTATUS) 0xC00000DAL)
#endif

#ifndef STATUS_CANT_TERMINATE_SELF
# define STATUS_CANT_TERMINATE_SELF ((NTSTATUS) 0xC00000DBL)
#endif

#ifndef STATUS_INVALID_SERVER_STATE
# define STATUS_INVALID_SERVER_STATE ((NTSTATUS) 0xC00000DCL)
#endif

#ifndef STATUS_INVALID_DOMAIN_STATE
# define STATUS_INVALID_DOMAIN_STATE ((NTSTATUS) 0xC00000DDL)
#endif

#ifndef STATUS_INVALID_DOMAIN_ROLE
# define STATUS_INVALID_DOMAIN_ROLE ((NTSTATUS) 0xC00000DEL)
#endif

#ifndef STATUS_NO_SUCH_DOMAIN
# define STATUS_NO_SUCH_DOMAIN ((NTSTATUS) 0xC00000DFL)
#endif

#ifndef STATUS_DOMAIN_EXISTS
# define STATUS_DOMAIN_EXISTS ((NTSTATUS) 0xC00000E0L)
#endif

#ifndef STATUS_DOMAIN_LIMIT_EXCEEDED
# define STATUS_DOMAIN_LIMIT_EXCEEDED ((NTSTATUS) 0xC00000E1L)
#endif

#ifndef STATUS_OPLOCK_NOT_GRANTED
# define STATUS_OPLOCK_NOT_GRANTED ((NTSTATUS) 0xC00000E2L)
#endif

#ifndef STATUS_INVALID_OPLOCK_PROTOCOL
# define STATUS_INVALID_OPLOCK_PROTOCOL ((NTSTATUS) 0xC00000E3L)
#endif

#ifndef STATUS_INTERNAL_DB_CORRUPTION
# define STATUS_INTERNAL_DB_CORRUPTION ((NTSTATUS) 0xC00000E4L)
#endif

#ifndef STATUS_INTERNAL_ERROR
# define STATUS_INTERNAL_ERROR ((NTSTATUS) 0xC00000E5L)
#endif

#ifndef STATUS_GENERIC_NOT_MAPPED
# define STATUS_GENERIC_NOT_MAPPED ((NTSTATUS) 0xC00000E6L)
#endif

#ifndef STATUS_BAD_DESCRIPTOR_FORMAT
# define STATUS_BAD_DESCRIPTOR_FORMAT ((NTSTATUS) 0xC00000E7L)
#endif

#ifndef STATUS_INVALID_USER_BUFFER
# define STATUS_INVALID_USER_BUFFER ((NTSTATUS) 0xC00000E8L)
#endif

#ifndef STATUS_UNEXPECTED_IO_ERROR
# define STATUS_UNEXPECTED_IO_ERROR ((NTSTATUS) 0xC00000E9L)
#endif

#ifndef STATUS_UNEXPECTED_MM_CREATE_ERR
# define STATUS_UNEXPECTED_MM_CREATE_ERR ((NTSTATUS) 0xC00000EAL)
#endif

#ifndef STATUS_UNEXPECTED_MM_MAP_ERROR
# define STATUS_UNEXPECTED_MM_MAP_ERROR ((NTSTATUS) 0xC00000EBL)
#endif

#ifndef STATUS_UNEXPECTED_MM_EXTEND_ERR
# define STATUS_UNEXPECTED_MM_EXTEND_ERR ((NTSTATUS) 0xC00000ECL)
#endif

#ifndef STATUS_NOT_LOGON_PROCESS
# define STATUS_NOT_LOGON_PROCESS ((NTSTATUS) 0xC00000EDL)
#endif

#ifndef STATUS_LOGON_SESSION_EXISTS
# define STATUS_LOGON_SESSION_EXISTS ((NTSTATUS) 0xC00000EEL)
#endif

#ifndef STATUS_INVALID_PARAMETER_1
# define STATUS_INVALID_PARAMETER_1 ((NTSTATUS) 0xC00000EFL)
#endif

#ifndef STATUS_INVALID_PARAMETER_2
# define STATUS_INVALID_PARAMETER_2 ((NTSTATUS) 0xC00000F0L)
#endif

#ifndef STATUS_INVALID_PARAMETER_3
# define STATUS_INVALID_PARAMETER_3 ((NTSTATUS) 0xC00000F1L)
#endif

#ifndef STATUS_INVALID_PARAMETER_4
# define STATUS_INVALID_PARAMETER_4 ((NTSTATUS) 0xC00000F2L)
#endif

#ifndef STATUS_INVALID_PARAMETER_5
# define STATUS_INVALID_PARAMETER_5 ((NTSTATUS) 0xC00000F3L)
#endif

#ifndef STATUS_INVALID_PARAMETER_6
# define STATUS_INVALID_PARAMETER_6 ((NTSTATUS) 0xC00000F4L)
#endif

#ifndef STATUS_INVALID_PARAMETER_7
# define STATUS_INVALID_PARAMETER_7 ((NTSTATUS) 0xC00000F5L)
#endif

#ifndef STATUS_INVALID_PARAMETER_8
# define STATUS_INVALID_PARAMETER_8 ((NTSTATUS) 0xC00000F6L)
#endif

#ifndef STATUS_INVALID_PARAMETER_9
# define STATUS_INVALID_PARAMETER_9 ((NTSTATUS) 0xC00000F7L)
#endif

#ifndef STATUS_INVALID_PARAMETER_10
# define STATUS_INVALID_PARAMETER_10 ((NTSTATUS) 0xC00000F8L)
#endif

#ifndef STATUS_INVALID_PARAMETER_11
# define STATUS_INVALID_PARAMETER_11 ((NTSTATUS) 0xC00000F9L)
#endif

#ifndef STATUS_INVALID_PARAMETER_12
# define STATUS_INVALID_PARAMETER_12 ((NTSTATUS) 0xC00000FAL)
#endif

#ifndef STATUS_REDIRECTOR_NOT_STARTED
# define STATUS_REDIRECTOR_NOT_STARTED ((NTSTATUS) 0xC00000FBL)
#endif

#ifndef STATUS_REDIRECTOR_STARTED
# define STATUS_REDIRECTOR_STARTED ((NTSTATUS) 0xC00000FCL)
#endif

#ifndef STATUS_STACK_OVERFLOW
# define STATUS_STACK_OVERFLOW ((NTSTATUS) 0xC00000FDL)
#endif

#ifndef STATUS_NO_SUCH_PACKAGE
# define STATUS_NO_SUCH_PACKAGE ((NTSTATUS) 0xC00000FEL)
#endif

#ifndef STATUS_BAD_FUNCTION_TABLE
# define STATUS_BAD_FUNCTION_TABLE ((NTSTATUS) 0xC00000FFL)
#endif

#ifndef STATUS_VARIABLE_NOT_FOUND
# define STATUS_VARIABLE_NOT_FOUND ((NTSTATUS) 0xC0000100L)
#endif

#ifndef STATUS_DIRECTORY_NOT_EMPTY
# define STATUS_DIRECTORY_NOT_EMPTY ((NTSTATUS) 0xC0000101L)
#endif

#ifndef STATUS_FILE_CORRUPT_ERROR
# define STATUS_FILE_CORRUPT_ERROR ((NTSTATUS) 0xC0000102L)
#endif

#ifndef STATUS_NOT_A_DIRECTORY
# define STATUS_NOT_A_DIRECTORY ((NTSTATUS) 0xC0000103L)
#endif

#ifndef STATUS_BAD_LOGON_SESSION_STATE
# define STATUS_BAD_LOGON_SESSION_STATE ((NTSTATUS) 0xC0000104L)
#endif

#ifndef STATUS_LOGON_SESSION_COLLISION
# define STATUS_LOGON_SESSION_COLLISION ((NTSTATUS) 0xC0000105L)
#endif

#ifndef STATUS_NAME_TOO_LONG
# define STATUS_NAME_TOO_LONG ((NTSTATUS) 0xC0000106L)
#endif

#ifndef STATUS_FILES_OPEN
# define STATUS_FILES_OPEN ((NTSTATUS) 0xC0000107L)
#endif

#ifndef STATUS_CONNECTION_IN_USE
# define STATUS_CONNECTION_IN_USE ((NTSTATUS) 0xC0000108L)
#endif

#ifndef STATUS_MESSAGE_NOT_FOUND
# define STATUS_MESSAGE_NOT_FOUND ((NTSTATUS) 0xC0000109L)
#endif

#ifndef STATUS_PROCESS_IS_TERMINATING
# define STATUS_PROCESS_IS_TERMINATING ((NTSTATUS) 0xC000010AL)
#endif

#ifndef STATUS_INVALID_LOGON_TYPE
# define STATUS_INVALID_LOGON_TYPE ((NTSTATUS) 0xC000010BL)
#endif

#ifndef STATUS_NO_GUID_TRANSLATION
# define STATUS_NO_GUID_TRANSLATION ((NTSTATUS) 0xC000010CL)
#endif

#ifndef STATUS_CANNOT_IMPERSONATE
# define STATUS_CANNOT_IMPERSONATE ((NTSTATUS) 0xC000010DL)
#endif

#ifndef STATUS_IMAGE_ALREADY_LOADED
# define STATUS_IMAGE_ALREADY_LOADED ((NTSTATUS) 0xC000010EL)
#endif

#ifndef STATUS_ABIOS_NOT_PRESENT
# define STATUS_ABIOS_NOT_PRESENT ((NTSTATUS) 0xC000010FL)
#endif

#ifndef STATUS_ABIOS_LID_NOT_EXIST
# define STATUS_ABIOS_LID_NOT_EXIST ((NTSTATUS) 0xC0000110L)
#endif

#ifndef STATUS_ABIOS_LID_ALREADY_OWNED
# define STATUS_ABIOS_LID_ALREADY_OWNED ((NTSTATUS) 0xC0000111L)
#endif

#ifndef STATUS_ABIOS_NOT_LID_OWNER
# define STATUS_ABIOS_NOT_LID_OWNER ((NTSTATUS) 0xC0000112L)
#endif

#ifndef STATUS_ABIOS_INVALID_COMMAND
# define STATUS_ABIOS_INVALID_COMMAND ((NTSTATUS) 0xC0000113L)
#endif

#ifndef STATUS_ABIOS_INVALID_LID
# define STATUS_ABIOS_INVALID_LID ((NTSTATUS) 0xC0000114L)
#endif

#ifndef STATUS_ABIOS_SELECTOR_NOT_AVAILABLE
# define STATUS_ABIOS_SELECTOR_NOT_AVAILABLE ((NTSTATUS) 0xC0000115L)
#endif

#ifndef STATUS_ABIOS_INVALID_SELECTOR
# define STATUS_ABIOS_INVALID_SELECTOR ((NTSTATUS) 0xC0000116L)
#endif

#ifndef STATUS_NO_LDT
# define STATUS_NO_LDT ((NTSTATUS) 0xC0000117L)
#endif

#ifndef STATUS_INVALID_LDT_SIZE
# define STATUS_INVALID_LDT_SIZE ((NTSTATUS) 0xC0000118L)
#endif

#ifndef STATUS_INVALID_LDT_OFFSET
# define STATUS_INVALID_LDT_OFFSET ((NTSTATUS) 0xC0000119L)
#endif

#ifndef STATUS_INVALID_LDT_DESCRIPTOR
# define STATUS_INVALID_LDT_DESCRIPTOR ((NTSTATUS) 0xC000011AL)
#endif

#ifndef STATUS_INVALID_IMAGE_NE_FORMAT
# define STATUS_INVALID_IMAGE_NE_FORMAT ((NTSTATUS) 0xC000011BL)
#endif

#ifndef STATUS_RXACT_INVALID_STATE
# define STATUS_RXACT_INVALID_STATE ((NTSTATUS) 0xC000011CL)
#endif

#ifndef STATUS_RXACT_COMMIT_FAILURE
# define STATUS_RXACT_COMMIT_FAILURE ((NTSTATUS) 0xC000011DL)
#endif

#ifndef STATUS_MAPPED_FILE_SIZE_ZERO
# define STATUS_MAPPED_FILE_SIZE_ZERO ((NTSTATUS) 0xC000011EL)
#endif

#ifndef STATUS_TOO_MANY_OPENED_FILES
# define STATUS_TOO_MANY_OPENED_FILES ((NTSTATUS) 0xC000011FL)
#endif

#ifndef STATUS_CANCELLED
# define STATUS_CANCELLED ((NTSTATUS) 0xC0000120L)
#endif

#ifndef STATUS_CANNOT_DELETE
# define STATUS_CANNOT_DELETE ((NTSTATUS) 0xC0000121L)
#endif

#ifndef STATUS_INVALID_COMPUTER_NAME
# define STATUS_INVALID_COMPUTER_NAME ((NTSTATUS) 0xC0000122L)
#endif

#ifndef STATUS_FILE_DELETED
# define STATUS_FILE_DELETED ((NTSTATUS) 0xC0000123L)
#endif

#ifndef STATUS_SPECIAL_ACCOUNT
# define STATUS_SPECIAL_ACCOUNT ((NTSTATUS) 0xC0000124L)
#endif

#ifndef STATUS_SPECIAL_GROUP
# define STATUS_SPECIAL_GROUP ((NTSTATUS) 0xC0000125L)
#endif

#ifndef STATUS_SPECIAL_USER
# define STATUS_SPECIAL_USER ((NTSTATUS) 0xC0000126L)
#endif

#ifndef STATUS_MEMBERS_PRIMARY_GROUP
# define STATUS_MEMBERS_PRIMARY_GROUP ((NTSTATUS) 0xC0000127L)
#endif

#ifndef STATUS_FILE_CLOSED
# define STATUS_FILE_CLOSED ((NTSTATUS) 0xC0000128L)
#endif

#ifndef STATUS_TOO_MANY_THREADS
# define STATUS_TOO_MANY_THREADS ((NTSTATUS) 0xC0000129L)
#endif

#ifndef STATUS_THREAD_NOT_IN_PROCESS
# define STATUS_THREAD_NOT_IN_PROCESS ((NTSTATUS) 0xC000012AL)
#endif

#ifndef STATUS_TOKEN_ALREADY_IN_USE
# define STATUS_TOKEN_ALREADY_IN_USE ((NTSTATUS) 0xC000012BL)
#endif

#ifndef STATUS_PAGEFILE_QUOTA_EXCEEDED
# define STATUS_PAGEFILE_QUOTA_EXCEEDED ((NTSTATUS) 0xC000012CL)
#endif

#ifndef STATUS_COMMITMENT_LIMIT
# define STATUS_COMMITMENT_LIMIT ((NTSTATUS) 0xC000012DL)
#endif

#ifndef STATUS_INVALID_IMAGE_LE_FORMAT
# define STATUS_INVALID_IMAGE_LE_FORMAT ((NTSTATUS) 0xC000012EL)
#endif

#ifndef STATUS_INVALID_IMAGE_NOT_MZ
# define STATUS_INVALID_IMAGE_NOT_MZ ((NTSTATUS) 0xC000012FL)
#endif

#ifndef STATUS_INVALID_IMAGE_PROTECT
# define STATUS_INVALID_IMAGE_PROTECT ((NTSTATUS) 0xC0000130L)
#endif

#ifndef STATUS_INVALID_IMAGE_WIN_16
# define STATUS_INVALID_IMAGE_WIN_16 ((NTSTATUS) 0xC0000131L)
#endif

#ifndef STATUS_LOGON_SERVER_CONFLICT
# define STATUS_LOGON_SERVER_CONFLICT ((NTSTATUS) 0xC0000132L)
#endif

#ifndef STATUS_TIME_DIFFERENCE_AT_DC
# define STATUS_TIME_DIFFERENCE_AT_DC ((NTSTATUS) 0xC0000133L)
#endif

#ifndef STATUS_SYNCHRONIZATION_REQUIRED
# define STATUS_SYNCHRONIZATION_REQUIRED ((NTSTATUS) 0xC0000134L)
#endif

#ifndef STATUS_DLL_NOT_FOUND
# define STATUS_DLL_NOT_FOUND ((NTSTATUS) 0xC0000135L)
#endif

#ifndef STATUS_OPEN_FAILED
# define STATUS_OPEN_FAILED ((NTSTATUS) 0xC0000136L)
#endif

#ifndef STATUS_IO_PRIVILEGE_FAILED
# define STATUS_IO_PRIVILEGE_FAILED ((NTSTATUS) 0xC0000137L)
#endif

#ifndef STATUS_ORDINAL_NOT_FOUND
# define STATUS_ORDINAL_NOT_FOUND ((NTSTATUS) 0xC0000138L)
#endif

#ifndef STATUS_ENTRYPOINT_NOT_FOUND
# define STATUS_ENTRYPOINT_NOT_FOUND ((NTSTATUS) 0xC0000139L)
#endif

#ifndef STATUS_CONTROL_C_EXIT
# define STATUS_CONTROL_C_EXIT ((NTSTATUS) 0xC000013AL)
#endif

#ifndef STATUS_LOCAL_DISCONNECT
# define STATUS_LOCAL_DISCONNECT ((NTSTATUS) 0xC000013BL)
#endif

#ifndef STATUS_REMOTE_DISCONNECT
# define STATUS_REMOTE_DISCONNECT ((NTSTATUS) 0xC000013CL)
#endif

#ifndef STATUS_REMOTE_RESOURCES
# define STATUS_REMOTE_RESOURCES ((NTSTATUS) 0xC000013DL)
#endif

#ifndef STATUS_LINK_FAILED
# define STATUS_LINK_FAILED ((NTSTATUS) 0xC000013EL)
#endif

#ifndef STATUS_LINK_TIMEOUT
# define STATUS_LINK_TIMEOUT ((NTSTATUS) 0xC000013FL)
#endif

#ifndef STATUS_INVALID_CONNECTION
# define STATUS_INVALID_CONNECTION ((NTSTATUS) 0xC0000140L)
#endif

#ifndef STATUS_INVALID_ADDRESS
# define STATUS_INVALID_ADDRESS ((NTSTATUS) 0xC0000141L)
#endif

#ifndef STATUS_DLL_INIT_FAILED
# define STATUS_DLL_INIT_FAILED ((NTSTATUS) 0xC0000142L)
#endif

#ifndef STATUS_MISSING_SYSTEMFILE
# define STATUS_MISSING_SYSTEMFILE ((NTSTATUS) 0xC0000143L)
#endif

#ifndef STATUS_UNHANDLED_EXCEPTION
# define STATUS_UNHANDLED_EXCEPTION ((NTSTATUS) 0xC0000144L)
#endif

#ifndef STATUS_APP_INIT_FAILURE
# define STATUS_APP_INIT_FAILURE ((NTSTATUS) 0xC0000145L)
#endif

#ifndef STATUS_PAGEFILE_CREATE_FAILED
# define STATUS_PAGEFILE_CREATE_FAILED ((NTSTATUS) 0xC0000146L)
#endif

#ifndef STATUS_NO_PAGEFILE
# define STATUS_NO_PAGEFILE ((NTSTATUS) 0xC0000147L)
#endif

#ifndef STATUS_INVALID_LEVEL
# define STATUS_INVALID_LEVEL ((NTSTATUS) 0xC0000148L)
#endif

#ifndef STATUS_WRONG_PASSWORD_CORE
# define STATUS_WRONG_PASSWORD_CORE ((NTSTATUS) 0xC0000149L)
#endif

#ifndef STATUS_ILLEGAL_FLOAT_CONTEXT
# define STATUS_ILLEGAL_FLOAT_CONTEXT ((NTSTATUS) 0xC000014AL)
#endif

#ifndef STATUS_PIPE_BROKEN
# define STATUS_PIPE_BROKEN ((NTSTATUS) 0xC000014BL)
#endif

#ifndef STATUS_REGISTRY_CORRUPT
# define STATUS_REGISTRY_CORRUPT ((NTSTATUS) 0xC000014CL)
#endif

#ifndef STATUS_REGISTRY_IO_FAILED
# define STATUS_REGISTRY_IO_FAILED ((NTSTATUS) 0xC000014DL)
#endif

#ifndef STATUS_NO_EVENT_PAIR
# define STATUS_NO_EVENT_PAIR ((NTSTATUS) 0xC000014EL)
#endif

#ifndef STATUS_UNRECOGNIZED_VOLUME
# define STATUS_UNRECOGNIZED_VOLUME ((NTSTATUS) 0xC000014FL)
#endif

#ifndef STATUS_SERIAL_NO_DEVICE_INITED
# define STATUS_SERIAL_NO_DEVICE_INITED ((NTSTATUS) 0xC0000150L)
#endif

#ifndef STATUS_NO_SUCH_ALIAS
# define STATUS_NO_SUCH_ALIAS ((NTSTATUS) 0xC0000151L)
#endif

#ifndef STATUS_MEMBER_NOT_IN_ALIAS
# define STATUS_MEMBER_NOT_IN_ALIAS ((NTSTATUS) 0xC0000152L)
#endif

#ifndef STATUS_MEMBER_IN_ALIAS
# define STATUS_MEMBER_IN_ALIAS ((NTSTATUS) 0xC0000153L)
#endif

#ifndef STATUS_ALIAS_EXISTS
# define STATUS_ALIAS_EXISTS ((NTSTATUS) 0xC0000154L)
#endif

#ifndef STATUS_LOGON_NOT_GRANTED
# define STATUS_LOGON_NOT_GRANTED ((NTSTATUS) 0xC0000155L)
#endif

#ifndef STATUS_TOO_MANY_SECRETS
# define STATUS_TOO_MANY_SECRETS ((NTSTATUS) 0xC0000156L)
#endif

#ifndef STATUS_SECRET_TOO_LONG
# define STATUS_SECRET_TOO_LONG ((NTSTATUS) 0xC0000157L)
#endif

#ifndef STATUS_INTERNAL_DB_ERROR
# define STATUS_INTERNAL_DB_ERROR ((NTSTATUS) 0xC0000158L)
#endif

#ifndef STATUS_FULLSCREEN_MODE
# define STATUS_FULLSCREEN_MODE ((NTSTATUS) 0xC0000159L)
#endif

#ifndef STATUS_TOO_MANY_CONTEXT_IDS
# define STATUS_TOO_MANY_CONTEXT_IDS ((NTSTATUS) 0xC000015AL)
#endif

#ifndef STATUS_LOGON_TYPE_NOT_GRANTED
# define STATUS_LOGON_TYPE_NOT_GRANTED ((NTSTATUS) 0xC000015BL)
#endif

#ifndef STATUS_NOT_REGISTRY_FILE
# define STATUS_NOT_REGISTRY_FILE ((NTSTATUS) 0xC000015CL)
#endif

#ifndef STATUS_NT_CROSS_ENCRYPTION_REQUIRED
# define STATUS_NT_CROSS_ENCRYPTION_REQUIRED ((NTSTATUS) 0xC000015DL)
#endif

#ifndef STATUS_DOMAIN_CTRLR_CONFIG_ERROR
# define STATUS_DOMAIN_CTRLR_CONFIG_ERROR ((NTSTATUS) 0xC000015EL)
#endif

#ifndef STATUS_FT_MISSING_MEMBER
# define STATUS_FT_MISSING_MEMBER ((NTSTATUS) 0xC000015FL)
#endif

#ifndef STATUS_ILL_FORMED_SERVICE_ENTRY
# define STATUS_ILL_FORMED_SERVICE_ENTRY ((NTSTATUS) 0xC0000160L)
#endif

#ifndef STATUS_ILLEGAL_CHARACTER
# define STATUS_ILLEGAL_CHARACTER ((NTSTATUS) 0xC0000161L)
#endif

#ifndef STATUS_UNMAPPABLE_CHARACTER
# define STATUS_UNMAPPABLE_CHARACTER ((NTSTATUS) 0xC0000162L)
#endif

#ifndef STATUS_UNDEFINED_CHARACTER
# define STATUS_UNDEFINED_CHARACTER ((NTSTATUS) 0xC0000163L)
#endif

#ifndef STATUS_FLOPPY_VOLUME
# define STATUS_FLOPPY_VOLUME ((NTSTATUS) 0xC0000164L)
#endif

#ifndef STATUS_FLOPPY_ID_MARK_NOT_FOUND
# define STATUS_FLOPPY_ID_MARK_NOT_FOUND ((NTSTATUS) 0xC0000165L)
#endif

#ifndef STATUS_FLOPPY_WRONG_CYLINDER
# define STATUS_FLOPPY_WRONG_CYLINDER ((NTSTATUS) 0xC0000166L)
#endif

#ifndef STATUS_FLOPPY_UNKNOWN_ERROR
# define STATUS_FLOPPY_UNKNOWN_ERROR ((NTSTATUS) 0xC0000167L)
#endif

#ifndef STATUS_FLOPPY_BAD_REGISTERS
# define STATUS_FLOPPY_BAD_REGISTERS ((NTSTATUS) 0xC0000168L)
#endif

#ifndef STATUS_DISK_RECALIBRATE_FAILED
# define STATUS_DISK_RECALIBRATE_FAILED ((NTSTATUS) 0xC0000169L)
#endif

#ifndef STATUS_DISK_OPERATION_FAILED
# define STATUS_DISK_OPERATION_FAILED ((NTSTATUS) 0xC000016AL)
#endif

#ifndef STATUS_DISK_RESET_FAILED
# define STATUS_DISK_RESET_FAILED ((NTSTATUS) 0xC000016BL)
#endif

#ifndef STATUS_SHARED_IRQ_BUSY
# define STATUS_SHARED_IRQ_BUSY ((NTSTATUS) 0xC000016CL)
#endif

#ifndef STATUS_FT_ORPHANING
# define STATUS_FT_ORPHANING ((NTSTATUS) 0xC000016DL)
#endif

#ifndef STATUS_BIOS_FAILED_TO_CONNECT_INTERRUPT
# define STATUS_BIOS_FAILED_TO_CONNECT_INTERRUPT ((NTSTATUS) 0xC000016EL)
#endif

#ifndef STATUS_PARTITION_FAILURE
# define STATUS_PARTITION_FAILURE ((NTSTATUS) 0xC0000172L)
#endif

#ifndef STATUS_INVALID_BLOCK_LENGTH
# define STATUS_INVALID_BLOCK_LENGTH ((NTSTATUS) 0xC0000173L)
#endif

#ifndef STATUS_DEVICE_NOT_PARTITIONED
# define STATUS_DEVICE_NOT_PARTITIONED ((NTSTATUS) 0xC0000174L)
#endif

#ifndef STATUS_UNABLE_TO_LOCK_MEDIA
# define STATUS_UNABLE_TO_LOCK_MEDIA ((NTSTATUS) 0xC0000175L)
#endif

#ifndef STATUS_UNABLE_TO_UNLOAD_MEDIA
# define STATUS_UNABLE_TO_UNLOAD_MEDIA ((NTSTATUS) 0xC0000176L)
#endif

#ifndef STATUS_EOM_OVERFLOW
# define STATUS_EOM_OVERFLOW ((NTSTATUS) 0xC0000177L)
#endif

#ifndef STATUS_NO_MEDIA
# define STATUS_NO_MEDIA ((NTSTATUS) 0xC0000178L)
#endif

#ifndef STATUS_NO_SUCH_MEMBER
# define STATUS_NO_SUCH_MEMBER ((NTSTATUS) 0xC000017AL)
#endif

#ifndef STATUS_INVALID_MEMBER
# define STATUS_INVALID_MEMBER ((NTSTATUS) 0xC000017BL)
#endif

#ifndef STATUS_KEY_DELETED
# define STATUS_KEY_DELETED ((NTSTATUS) 0xC000017CL)
#endif

#ifndef STATUS_NO_LOG_SPACE
# define STATUS_NO_LOG_SPACE ((NTSTATUS) 0xC000017DL)
#endif

#ifndef STATUS_TOO_MANY_SIDS
# define STATUS_TOO_MANY_SIDS ((NTSTATUS) 0xC000017EL)
#endif

#ifndef STATUS_LM_CROSS_ENCRYPTION_REQUIRED
# define STATUS_LM_CROSS_ENCRYPTION_REQUIRED ((NTSTATUS) 0xC000017FL)
#endif

#ifndef STATUS_KEY_HAS_CHILDREN
# define STATUS_KEY_HAS_CHILDREN ((NTSTATUS) 0xC0000180L)
#endif

#ifndef STATUS_CHILD_MUST_BE_VOLATILE
# define STATUS_CHILD_MUST_BE_VOLATILE ((NTSTATUS) 0xC0000181L)
#endif

#ifndef STATUS_DEVICE_CONFIGURATION_ERROR
# define STATUS_DEVICE_CONFIGURATION_ERROR ((NTSTATUS) 0xC0000182L)
#endif

#ifndef STATUS_DRIVER_INTERNAL_ERROR
# define STATUS_DRIVER_INTERNAL_ERROR ((NTSTATUS) 0xC0000183L)
#endif

#ifndef STATUS_INVALID_DEVICE_STATE
# define STATUS_INVALID_DEVICE_STATE ((NTSTATUS) 0xC0000184L)
#endif

#ifndef STATUS_IO_DEVICE_ERROR
# define STATUS_IO_DEVICE_ERROR ((NTSTATUS) 0xC0000185L)
#endif

#ifndef STATUS_DEVICE_PROTOCOL_ERROR
# define STATUS_DEVICE_PROTOCOL_ERROR ((NTSTATUS) 0xC0000186L)
#endif

#ifndef STATUS_BACKUP_CONTROLLER
# define STATUS_BACKUP_CONTROLLER ((NTSTATUS) 0xC0000187L)
#endif

#ifndef STATUS_LOG_FILE_FULL
# define STATUS_LOG_FILE_FULL ((NTSTATUS) 0xC0000188L)
#endif

#ifndef STATUS_TOO_LATE
# define STATUS_TOO_LATE ((NTSTATUS) 0xC0000189L)
#endif

#ifndef STATUS_NO_TRUST_LSA_SECRET
# define STATUS_NO_TRUST_LSA_SECRET ((NTSTATUS) 0xC000018AL)
#endif

#ifndef STATUS_NO_TRUST_SAM_ACCOUNT
# define STATUS_NO_TRUST_SAM_ACCOUNT ((NTSTATUS) 0xC000018BL)
#endif

#ifndef STATUS_TRUSTED_DOMAIN_FAILURE
# define STATUS_TRUSTED_DOMAIN_FAILURE ((NTSTATUS) 0xC000018CL)
#endif

#ifndef STATUS_TRUSTED_RELATIONSHIP_FAILURE
# define STATUS_TRUSTED_RELATIONSHIP_FAILURE ((NTSTATUS) 0xC000018DL)
#endif

#ifndef STATUS_EVENTLOG_FILE_CORRUPT
# define STATUS_EVENTLOG_FILE_CORRUPT ((NTSTATUS) 0xC000018EL)
#endif

#ifndef STATUS_EVENTLOG_CANT_START
# define STATUS_EVENTLOG_CANT_START ((NTSTATUS) 0xC000018FL)
#endif

#ifndef STATUS_TRUST_FAILURE
# define STATUS_TRUST_FAILURE ((NTSTATUS) 0xC0000190L)
#endif

#ifndef STATUS_MUTANT_LIMIT_EXCEEDED
# define STATUS_MUTANT_LIMIT_EXCEEDED ((NTSTATUS) 0xC0000191L)
#endif

#ifndef STATUS_NETLOGON_NOT_STARTED
# define STATUS_NETLOGON_NOT_STARTED ((NTSTATUS) 0xC0000192L)
#endif

#ifndef STATUS_ACCOUNT_EXPIRED
# define STATUS_ACCOUNT_EXPIRED ((NTSTATUS) 0xC0000193L)
#endif

#ifndef STATUS_POSSIBLE_DEADLOCK
# define STATUS_POSSIBLE_DEADLOCK ((NTSTATUS) 0xC0000194L)
#endif

#ifndef STATUS_NETWORK_CREDENTIAL_CONFLICT
# define STATUS_NETWORK_CREDENTIAL_CONFLICT ((NTSTATUS) 0xC0000195L)
#endif

#ifndef STATUS_REMOTE_SESSION_LIMIT
# define STATUS_REMOTE_SESSION_LIMIT ((NTSTATUS) 0xC0000196L)
#endif

#ifndef STATUS_EVENTLOG_FILE_CHANGED
# define STATUS_EVENTLOG_FILE_CHANGED ((NTSTATUS) 0xC0000197L)
#endif

#ifndef STATUS_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT
# define STATUS_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT ((NTSTATUS) 0xC0000198L)
#endif

#ifndef STATUS_NOLOGON_WORKSTATION_TRUST_ACCOUNT
# define STATUS_NOLOGON_WORKSTATION_TRUST_ACCOUNT ((NTSTATUS) 0xC0000199L)
#endif

#ifndef STATUS_NOLOGON_SERVER_TRUST_ACCOUNT
# define STATUS_NOLOGON_SERVER_TRUST_ACCOUNT ((NTSTATUS) 0xC000019AL)
#endif

#ifndef STATUS_DOMAIN_TRUST_INCONSISTENT
# define STATUS_DOMAIN_TRUST_INCONSISTENT ((NTSTATUS) 0xC000019BL)
#endif

#ifndef STATUS_FS_DRIVER_REQUIRED
# define STATUS_FS_DRIVER_REQUIRED ((NTSTATUS) 0xC000019CL)
#endif

#ifndef STATUS_IMAGE_ALREADY_LOADED_AS_DLL
# define STATUS_IMAGE_ALREADY_LOADED_AS_DLL ((NTSTATUS) 0xC000019DL)
#endif

#ifndef STATUS_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING
# define STATUS_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING ((NTSTATUS) 0xC000019EL)
#endif

#ifndef STATUS_SHORT_NAMES_NOT_ENABLED_ON_VOLUME
# define STATUS_SHORT_NAMES_NOT_ENABLED_ON_VOLUME ((NTSTATUS) 0xC000019FL)
#endif

#ifndef STATUS_SECURITY_STREAM_IS_INCONSISTENT
# define STATUS_SECURITY_STREAM_IS_INCONSISTENT ((NTSTATUS) 0xC00001A0L)
#endif

#ifndef STATUS_INVALID_LOCK_RANGE
# define STATUS_INVALID_LOCK_RANGE ((NTSTATUS) 0xC00001A1L)
#endif

#ifndef STATUS_INVALID_ACE_CONDITION
# define STATUS_INVALID_ACE_CONDITION ((NTSTATUS) 0xC00001A2L)
#endif

#ifndef STATUS_IMAGE_SUBSYSTEM_NOT_PRESENT
# define STATUS_IMAGE_SUBSYSTEM_NOT_PRESENT ((NTSTATUS) 0xC00001A3L)
#endif

#ifndef STATUS_NOTIFICATION_GUID_ALREADY_DEFINED
# define STATUS_NOTIFICATION_GUID_ALREADY_DEFINED ((NTSTATUS) 0xC00001A4L)
#endif

#ifndef STATUS_NETWORK_OPEN_RESTRICTION
# define STATUS_NETWORK_OPEN_RESTRICTION ((NTSTATUS) 0xC0000201L)
#endif

#ifndef STATUS_NO_USER_SESSION_KEY
# define STATUS_NO_USER_SESSION_KEY ((NTSTATUS) 0xC0000202L)
#endif

#ifndef STATUS_USER_SESSION_DELETED
# define STATUS_USER_SESSION_DELETED ((NTSTATUS) 0xC0000203L)
#endif

#ifndef STATUS_RESOURCE_LANG_NOT_FOUND
# define STATUS_RESOURCE_LANG_NOT_FOUND ((NTSTATUS) 0xC0000204L)
#endif

#ifndef STATUS_INSUFF_SERVER_RESOURCES
# define STATUS_INSUFF_SERVER_RESOURCES ((NTSTATUS) 0xC0000205L)
#endif

#ifndef STATUS_INVALID_BUFFER_SIZE
# define STATUS_INVALID_BUFFER_SIZE ((NTSTATUS) 0xC0000206L)
#endif

#ifndef STATUS_INVALID_ADDRESS_COMPONENT
# define STATUS_INVALID_ADDRESS_COMPONENT ((NTSTATUS) 0xC0000207L)
#endif

#ifndef STATUS_INVALID_ADDRESS_WILDCARD
# define STATUS_INVALID_ADDRESS_WILDCARD ((NTSTATUS) 0xC0000208L)
#endif

#ifndef STATUS_TOO_MANY_ADDRESSES
# define STATUS_TOO_MANY_ADDRESSES ((NTSTATUS) 0xC0000209L)
#endif

#ifndef STATUS_ADDRESS_ALREADY_EXISTS
# define STATUS_ADDRESS_ALREADY_EXISTS ((NTSTATUS) 0xC000020AL)
#endif

#ifndef STATUS_ADDRESS_CLOSED
# define STATUS_ADDRESS_CLOSED ((NTSTATUS) 0xC000020BL)
#endif

#ifndef STATUS_CONNECTION_DISCONNECTED
# define STATUS_CONNECTION_DISCONNECTED ((NTSTATUS) 0xC000020CL)
#endif

#ifndef STATUS_CONNECTION_RESET
# define STATUS_CONNECTION_RESET ((NTSTATUS) 0xC000020DL)
#endif

#ifndef STATUS_TOO_MANY_NODES
# define STATUS_TOO_MANY_NODES ((NTSTATUS) 0xC000020EL)
#endif

#ifndef STATUS_TRANSACTION_ABORTED
# define STATUS_TRANSACTION_ABORTED ((NTSTATUS) 0xC000020FL)
#endif

#ifndef STATUS_TRANSACTION_TIMED_OUT
# define STATUS_TRANSACTION_TIMED_OUT ((NTSTATUS) 0xC0000210L)
#endif

#ifndef STATUS_TRANSACTION_NO_RELEASE
# define STATUS_TRANSACTION_NO_RELEASE ((NTSTATUS) 0xC0000211L)
#endif

#ifndef STATUS_TRANSACTION_NO_MATCH
# define STATUS_TRANSACTION_NO_MATCH ((NTSTATUS) 0xC0000212L)
#endif

#ifndef STATUS_TRANSACTION_RESPONDED
# define STATUS_TRANSACTION_RESPONDED ((NTSTATUS) 0xC0000213L)
#endif

#ifndef STATUS_TRANSACTION_INVALID_ID
# define STATUS_TRANSACTION_INVALID_ID ((NTSTATUS) 0xC0000214L)
#endif

#ifndef STATUS_TRANSACTION_INVALID_TYPE
# define STATUS_TRANSACTION_INVALID_TYPE ((NTSTATUS) 0xC0000215L)
#endif

#ifndef STATUS_NOT_SERVER_SESSION
# define STATUS_NOT_SERVER_SESSION ((NTSTATUS) 0xC0000216L)
#endif

#ifndef STATUS_NOT_CLIENT_SESSION
# define STATUS_NOT_CLIENT_SESSION ((NTSTATUS) 0xC0000217L)
#endif

#ifndef STATUS_CANNOT_LOAD_REGISTRY_FILE
# define STATUS_CANNOT_LOAD_REGISTRY_FILE ((NTSTATUS) 0xC0000218L)
#endif

#ifndef STATUS_DEBUG_ATTACH_FAILED
# define STATUS_DEBUG_ATTACH_FAILED ((NTSTATUS) 0xC0000219L)
#endif

#ifndef STATUS_SYSTEM_PROCESS_TERMINATED
# define STATUS_SYSTEM_PROCESS_TERMINATED ((NTSTATUS) 0xC000021AL)
#endif

#ifndef STATUS_DATA_NOT_ACCEPTED
# define STATUS_DATA_NOT_ACCEPTED ((NTSTATUS) 0xC000021BL)
#endif

#ifndef STATUS_NO_BROWSER_SERVERS_FOUND
# define STATUS_NO_BROWSER_SERVERS_FOUND ((NTSTATUS) 0xC000021CL)
#endif

#ifndef STATUS_VDM_HARD_ERROR
# define STATUS_VDM_HARD_ERROR ((NTSTATUS) 0xC000021DL)
#endif

#ifndef STATUS_DRIVER_CANCEL_TIMEOUT
# define STATUS_DRIVER_CANCEL_TIMEOUT ((NTSTATUS) 0xC000021EL)
#endif

#ifndef STATUS_REPLY_MESSAGE_MISMATCH
# define STATUS_REPLY_MESSAGE_MISMATCH ((NTSTATUS) 0xC000021FL)
#endif

#ifndef STATUS_MAPPED_ALIGNMENT
# define STATUS_MAPPED_ALIGNMENT ((NTSTATUS) 0xC0000220L)
#endif

#ifndef STATUS_IMAGE_CHECKSUM_MISMATCH
# define STATUS_IMAGE_CHECKSUM_MISMATCH ((NTSTATUS) 0xC0000221L)
#endif

#ifndef STATUS_LOST_WRITEBEHIND_DATA
# define STATUS_LOST_WRITEBEHIND_DATA ((NTSTATUS) 0xC0000222L)
#endif

#ifndef STATUS_CLIENT_SERVER_PARAMETERS_INVALID
# define STATUS_CLIENT_SERVER_PARAMETERS_INVALID ((NTSTATUS) 0xC0000223L)
#endif

#ifndef STATUS_PASSWORD_MUST_CHANGE
# define STATUS_PASSWORD_MUST_CHANGE ((NTSTATUS) 0xC0000224L)
#endif

#ifndef STATUS_NOT_FOUND
# define STATUS_NOT_FOUND ((NTSTATUS) 0xC0000225L)
#endif

#ifndef STATUS_NOT_TINY_STREAM
# define STATUS_NOT_TINY_STREAM ((NTSTATUS) 0xC0000226L)
#endif

#ifndef STATUS_RECOVERY_FAILURE
# define STATUS_RECOVERY_FAILURE ((NTSTATUS) 0xC0000227L)
#endif

#ifndef STATUS_STACK_OVERFLOW_READ
# define STATUS_STACK_OVERFLOW_READ ((NTSTATUS) 0xC0000228L)
#endif

#ifndef STATUS_FAIL_CHECK
# define STATUS_FAIL_CHECK ((NTSTATUS) 0xC0000229L)
#endif

#ifndef STATUS_DUPLICATE_OBJECTID
# define STATUS_DUPLICATE_OBJECTID ((NTSTATUS) 0xC000022AL)
#endif

#ifndef STATUS_OBJECTID_EXISTS
# define STATUS_OBJECTID_EXISTS ((NTSTATUS) 0xC000022BL)
#endif

#ifndef STATUS_CONVERT_TO_LARGE
# define STATUS_CONVERT_TO_LARGE ((NTSTATUS) 0xC000022CL)
#endif

#ifndef STATUS_RETRY
# define STATUS_RETRY ((NTSTATUS) 0xC000022DL)
#endif

#ifndef STATUS_FOUND_OUT_OF_SCOPE
# define STATUS_FOUND_OUT_OF_SCOPE ((NTSTATUS) 0xC000022EL)
#endif

#ifndef STATUS_ALLOCATE_BUCKET
# define STATUS_ALLOCATE_BUCKET ((NTSTATUS) 0xC000022FL)
#endif

#ifndef STATUS_PROPSET_NOT_FOUND
# define STATUS_PROPSET_NOT_FOUND ((NTSTATUS) 0xC0000230L)
#endif

#ifndef STATUS_MARSHALL_OVERFLOW
# define STATUS_MARSHALL_OVERFLOW ((NTSTATUS) 0xC0000231L)
#endif

#ifndef STATUS_INVALID_VARIANT
# define STATUS_INVALID_VARIANT ((NTSTATUS) 0xC0000232L)
#endif

#ifndef STATUS_DOMAIN_CONTROLLER_NOT_FOUND
# define STATUS_DOMAIN_CONTROLLER_NOT_FOUND ((NTSTATUS) 0xC0000233L)
#endif

#ifndef STATUS_ACCOUNT_LOCKED_OUT
# define STATUS_ACCOUNT_LOCKED_OUT ((NTSTATUS) 0xC0000234L)
#endif

#ifndef STATUS_HANDLE_NOT_CLOSABLE
# define STATUS_HANDLE_NOT_CLOSABLE ((NTSTATUS) 0xC0000235L)
#endif

#ifndef STATUS_CONNECTION_REFUSED
# define STATUS_CONNECTION_REFUSED ((NTSTATUS) 0xC0000236L)
#endif

#ifndef STATUS_GRACEFUL_DISCONNECT
# define STATUS_GRACEFUL_DISCONNECT ((NTSTATUS) 0xC0000237L)
#endif

#ifndef STATUS_ADDRESS_ALREADY_ASSOCIATED
# define STATUS_ADDRESS_ALREADY_ASSOCIATED ((NTSTATUS) 0xC0000238L)
#endif

#ifndef STATUS_ADDRESS_NOT_ASSOCIATED
# define STATUS_ADDRESS_NOT_ASSOCIATED ((NTSTATUS) 0xC0000239L)
#endif

#ifndef STATUS_CONNECTION_INVALID
# define STATUS_CONNECTION_INVALID ((NTSTATUS) 0xC000023AL)
#endif

#ifndef STATUS_CONNECTION_ACTIVE
# define STATUS_CONNECTION_ACTIVE ((NTSTATUS) 0xC000023BL)
#endif

#ifndef STATUS_NETWORK_UNREACHABLE
# define STATUS_NETWORK_UNREACHABLE ((NTSTATUS) 0xC000023CL)
#endif

#ifndef STATUS_HOST_UNREACHABLE
# define STATUS_HOST_UNREACHABLE ((NTSTATUS) 0xC000023DL)
#endif

#ifndef STATUS_PROTOCOL_UNREACHABLE
# define STATUS_PROTOCOL_UNREACHABLE ((NTSTATUS) 0xC000023EL)
#endif

#ifndef STATUS_PORT_UNREACHABLE
# define STATUS_PORT_UNREACHABLE ((NTSTATUS) 0xC000023FL)
#endif

#ifndef STATUS_REQUEST_ABORTED
# define STATUS_REQUEST_ABORTED ((NTSTATUS) 0xC0000240L)
#endif

#ifndef STATUS_CONNECTION_ABORTED
# define STATUS_CONNECTION_ABORTED ((NTSTATUS) 0xC0000241L)
#endif

#ifndef STATUS_BAD_COMPRESSION_BUFFER
# define STATUS_BAD_COMPRESSION_BUFFER ((NTSTATUS) 0xC0000242L)
#endif

#ifndef STATUS_USER_MAPPED_FILE
# define STATUS_USER_MAPPED_FILE ((NTSTATUS) 0xC0000243L)
#endif

#ifndef STATUS_AUDIT_FAILED
# define STATUS_AUDIT_FAILED ((NTSTATUS) 0xC0000244L)
#endif

#ifndef STATUS_TIMER_RESOLUTION_NOT_SET
# define STATUS_TIMER_RESOLUTION_NOT_SET ((NTSTATUS) 0xC0000245L)
#endif

#ifndef STATUS_CONNECTION_COUNT_LIMIT
# define STATUS_CONNECTION_COUNT_LIMIT ((NTSTATUS) 0xC0000246L)
#endif

#ifndef STATUS_LOGIN_TIME_RESTRICTION
# define STATUS_LOGIN_TIME_RESTRICTION ((NTSTATUS) 0xC0000247L)
#endif

#ifndef STATUS_LOGIN_WKSTA_RESTRICTION
# define STATUS_LOGIN_WKSTA_RESTRICTION ((NTSTATUS) 0xC0000248L)
#endif

#ifndef STATUS_IMAGE_MP_UP_MISMATCH
# define STATUS_IMAGE_MP_UP_MISMATCH ((NTSTATUS) 0xC0000249L)
#endif

#ifndef STATUS_INSUFFICIENT_LOGON_INFO
# define STATUS_INSUFFICIENT_LOGON_INFO ((NTSTATUS) 0xC0000250L)
#endif

#ifndef STATUS_BAD_DLL_ENTRYPOINT
# define STATUS_BAD_DLL_ENTRYPOINT ((NTSTATUS) 0xC0000251L)
#endif

#ifndef STATUS_BAD_SERVICE_ENTRYPOINT
# define STATUS_BAD_SERVICE_ENTRYPOINT ((NTSTATUS) 0xC0000252L)
#endif

#ifndef STATUS_LPC_REPLY_LOST
# define STATUS_LPC_REPLY_LOST ((NTSTATUS) 0xC0000253L)
#endif

#ifndef STATUS_IP_ADDRESS_CONFLICT1
# define STATUS_IP_ADDRESS_CONFLICT1 ((NTSTATUS) 0xC0000254L)
#endif

#ifndef STATUS_IP_ADDRESS_CONFLICT2
# define STATUS_IP_ADDRESS_CONFLICT2 ((NTSTATUS) 0xC0000255L)
#endif

#ifndef STATUS_REGISTRY_QUOTA_LIMIT
# define STATUS_REGISTRY_QUOTA_LIMIT ((NTSTATUS) 0xC0000256L)
#endif

#ifndef STATUS_PATH_NOT_COVERED
# define STATUS_PATH_NOT_COVERED ((NTSTATUS) 0xC0000257L)
#endif

#ifndef STATUS_NO_CALLBACK_ACTIVE
# define STATUS_NO_CALLBACK_ACTIVE ((NTSTATUS) 0xC0000258L)
#endif

#ifndef STATUS_LICENSE_QUOTA_EXCEEDED
# define STATUS_LICENSE_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000259L)
#endif

#ifndef STATUS_PWD_TOO_SHORT
# define STATUS_PWD_TOO_SHORT ((NTSTATUS) 0xC000025AL)
#endif

#ifndef STATUS_PWD_TOO_RECENT
# define STATUS_PWD_TOO_RECENT ((NTSTATUS) 0xC000025BL)
#endif

#ifndef STATUS_PWD_HISTORY_CONFLICT
# define STATUS_PWD_HISTORY_CONFLICT ((NTSTATUS) 0xC000025CL)
#endif

#ifndef STATUS_PLUGPLAY_NO_DEVICE
# define STATUS_PLUGPLAY_NO_DEVICE ((NTSTATUS) 0xC000025EL)
#endif

#ifndef STATUS_UNSUPPORTED_COMPRESSION
# define STATUS_UNSUPPORTED_COMPRESSION ((NTSTATUS) 0xC000025FL)
#endif

#ifndef STATUS_INVALID_HW_PROFILE
# define STATUS_INVALID_HW_PROFILE ((NTSTATUS) 0xC0000260L)
#endif

#ifndef STATUS_INVALID_PLUGPLAY_DEVICE_PATH
# define STATUS_INVALID_PLUGPLAY_DEVICE_PATH ((NTSTATUS) 0xC0000261L)
#endif

#ifndef STATUS_DRIVER_ORDINAL_NOT_FOUND
# define STATUS_DRIVER_ORDINAL_NOT_FOUND ((NTSTATUS) 0xC0000262L)
#endif

#ifndef STATUS_DRIVER_ENTRYPOINT_NOT_FOUND
# define STATUS_DRIVER_ENTRYPOINT_NOT_FOUND ((NTSTATUS) 0xC0000263L)
#endif

#ifndef STATUS_RESOURCE_NOT_OWNED
# define STATUS_RESOURCE_NOT_OWNED ((NTSTATUS) 0xC0000264L)
#endif

#ifndef STATUS_TOO_MANY_LINKS
# define STATUS_TOO_MANY_LINKS ((NTSTATUS) 0xC0000265L)
#endif

#ifndef STATUS_QUOTA_LIST_INCONSISTENT
# define STATUS_QUOTA_LIST_INCONSISTENT ((NTSTATUS) 0xC0000266L)
#endif

#ifndef STATUS_FILE_IS_OFFLINE
# define STATUS_FILE_IS_OFFLINE ((NTSTATUS) 0xC0000267L)
#endif

#ifndef STATUS_EVALUATION_EXPIRATION
# define STATUS_EVALUATION_EXPIRATION ((NTSTATUS) 0xC0000268L)
#endif

#ifndef STATUS_ILLEGAL_DLL_RELOCATION
# define STATUS_ILLEGAL_DLL_RELOCATION ((NTSTATUS) 0xC0000269L)
#endif

#ifndef STATUS_LICENSE_VIOLATION
# define STATUS_LICENSE_VIOLATION ((NTSTATUS) 0xC000026AL)
#endif

#ifndef STATUS_DLL_INIT_FAILED_LOGOFF
# define STATUS_DLL_INIT_FAILED_LOGOFF ((NTSTATUS) 0xC000026BL)
#endif

#ifndef STATUS_DRIVER_UNABLE_TO_LOAD
# define STATUS_DRIVER_UNABLE_TO_LOAD ((NTSTATUS) 0xC000026CL)
#endif

#ifndef STATUS_DFS_UNAVAILABLE
# define STATUS_DFS_UNAVAILABLE ((NTSTATUS) 0xC000026DL)
#endif

#ifndef STATUS_VOLUME_DISMOUNTED
# define STATUS_VOLUME_DISMOUNTED ((NTSTATUS) 0xC000026EL)
#endif

#ifndef STATUS_WX86_INTERNAL_ERROR
# define STATUS_WX86_INTERNAL_ERROR ((NTSTATUS) 0xC000026FL)
#endif

#ifndef STATUS_WX86_FLOAT_STACK_CHECK
# define STATUS_WX86_FLOAT_STACK_CHECK ((NTSTATUS) 0xC0000270L)
#endif

#ifndef STATUS_VALIDATE_CONTINUE
# define STATUS_VALIDATE_CONTINUE ((NTSTATUS) 0xC0000271L)
#endif

#ifndef STATUS_NO_MATCH
# define STATUS_NO_MATCH ((NTSTATUS) 0xC0000272L)
#endif

#ifndef STATUS_NO_MORE_MATCHES
# define STATUS_NO_MORE_MATCHES ((NTSTATUS) 0xC0000273L)
#endif

#ifndef STATUS_NOT_A_REPARSE_POINT
# define STATUS_NOT_A_REPARSE_POINT ((NTSTATUS) 0xC0000275L)
#endif

#ifndef STATUS_IO_REPARSE_TAG_INVALID
# define STATUS_IO_REPARSE_TAG_INVALID ((NTSTATUS) 0xC0000276L)
#endif

#ifndef STATUS_IO_REPARSE_TAG_MISMATCH
# define STATUS_IO_REPARSE_TAG_MISMATCH ((NTSTATUS) 0xC0000277L)
#endif

#ifndef STATUS_IO_REPARSE_DATA_INVALID
# define STATUS_IO_REPARSE_DATA_INVALID ((NTSTATUS) 0xC0000278L)
#endif

#ifndef STATUS_IO_REPARSE_TAG_NOT_HANDLED
# define STATUS_IO_REPARSE_TAG_NOT_HANDLED ((NTSTATUS) 0xC0000279L)
#endif

#ifndef STATUS_REPARSE_POINT_NOT_RESOLVED
# define STATUS_REPARSE_POINT_NOT_RESOLVED ((NTSTATUS) 0xC0000280L)
#endif

#ifndef STATUS_DIRECTORY_IS_A_REPARSE_POINT
# define STATUS_DIRECTORY_IS_A_REPARSE_POINT ((NTSTATUS) 0xC0000281L)
#endif

#ifndef STATUS_RANGE_LIST_CONFLICT
# define STATUS_RANGE_LIST_CONFLICT ((NTSTATUS) 0xC0000282L)
#endif

#ifndef STATUS_SOURCE_ELEMENT_EMPTY
# define STATUS_SOURCE_ELEMENT_EMPTY ((NTSTATUS) 0xC0000283L)
#endif

#ifndef STATUS_DESTINATION_ELEMENT_FULL
# define STATUS_DESTINATION_ELEMENT_FULL ((NTSTATUS) 0xC0000284L)
#endif

#ifndef STATUS_ILLEGAL_ELEMENT_ADDRESS
# define STATUS_ILLEGAL_ELEMENT_ADDRESS ((NTSTATUS) 0xC0000285L)
#endif

#ifndef STATUS_MAGAZINE_NOT_PRESENT
# define STATUS_MAGAZINE_NOT_PRESENT ((NTSTATUS) 0xC0000286L)
#endif

#ifndef STATUS_REINITIALIZATION_NEEDED
# define STATUS_REINITIALIZATION_NEEDED ((NTSTATUS) 0xC0000287L)
#endif

#ifndef STATUS_DEVICE_REQUIRES_CLEANING
# define STATUS_DEVICE_REQUIRES_CLEANING ((NTSTATUS) 0x80000288L)
#endif

#ifndef STATUS_DEVICE_DOOR_OPEN
# define STATUS_DEVICE_DOOR_OPEN ((NTSTATUS) 0x80000289L)
#endif

#ifndef STATUS_ENCRYPTION_FAILED
# define STATUS_ENCRYPTION_FAILED ((NTSTATUS) 0xC000028AL)
#endif

#ifndef STATUS_DECRYPTION_FAILED
# define STATUS_DECRYPTION_FAILED ((NTSTATUS) 0xC000028BL)
#endif

#ifndef STATUS_RANGE_NOT_FOUND
# define STATUS_RANGE_NOT_FOUND ((NTSTATUS) 0xC000028CL)
#endif

#ifndef STATUS_NO_RECOVERY_POLICY
# define STATUS_NO_RECOVERY_POLICY ((NTSTATUS) 0xC000028DL)
#endif

#ifndef STATUS_NO_EFS
# define STATUS_NO_EFS ((NTSTATUS) 0xC000028EL)
#endif

#ifndef STATUS_WRONG_EFS
# define STATUS_WRONG_EFS ((NTSTATUS) 0xC000028FL)
#endif

#ifndef STATUS_NO_USER_KEYS
# define STATUS_NO_USER_KEYS ((NTSTATUS) 0xC0000290L)
#endif

#ifndef STATUS_FILE_NOT_ENCRYPTED
# define STATUS_FILE_NOT_ENCRYPTED ((NTSTATUS) 0xC0000291L)
#endif

#ifndef STATUS_NOT_EXPORT_FORMAT
# define STATUS_NOT_EXPORT_FORMAT ((NTSTATUS) 0xC0000292L)
#endif

#ifndef STATUS_FILE_ENCRYPTED
# define STATUS_FILE_ENCRYPTED ((NTSTATUS) 0xC0000293L)
#endif

#ifndef STATUS_WAKE_SYSTEM
# define STATUS_WAKE_SYSTEM ((NTSTATUS) 0x40000294L)
#endif

#ifndef STATUS_WMI_GUID_NOT_FOUND
# define STATUS_WMI_GUID_NOT_FOUND ((NTSTATUS) 0xC0000295L)
#endif

#ifndef STATUS_WMI_INSTANCE_NOT_FOUND
# define STATUS_WMI_INSTANCE_NOT_FOUND ((NTSTATUS) 0xC0000296L)
#endif

#ifndef STATUS_WMI_ITEMID_NOT_FOUND
# define STATUS_WMI_ITEMID_NOT_FOUND ((NTSTATUS) 0xC0000297L)
#endif

#ifndef STATUS_WMI_TRY_AGAIN
# define STATUS_WMI_TRY_AGAIN ((NTSTATUS) 0xC0000298L)
#endif

#ifndef STATUS_SHARED_POLICY
# define STATUS_SHARED_POLICY ((NTSTATUS) 0xC0000299L)
#endif

#ifndef STATUS_POLICY_OBJECT_NOT_FOUND
# define STATUS_POLICY_OBJECT_NOT_FOUND ((NTSTATUS) 0xC000029AL)
#endif

#ifndef STATUS_POLICY_ONLY_IN_DS
# define STATUS_POLICY_ONLY_IN_DS ((NTSTATUS) 0xC000029BL)
#endif

#ifndef STATUS_VOLUME_NOT_UPGRADED
# define STATUS_VOLUME_NOT_UPGRADED ((NTSTATUS) 0xC000029CL)
#endif

#ifndef STATUS_REMOTE_STORAGE_NOT_ACTIVE
# define STATUS_REMOTE_STORAGE_NOT_ACTIVE ((NTSTATUS) 0xC000029DL)
#endif

#ifndef STATUS_REMOTE_STORAGE_MEDIA_ERROR
# define STATUS_REMOTE_STORAGE_MEDIA_ERROR ((NTSTATUS) 0xC000029EL)
#endif

#ifndef STATUS_NO_TRACKING_SERVICE
# define STATUS_NO_TRACKING_SERVICE ((NTSTATUS) 0xC000029FL)
#endif

#ifndef STATUS_SERVER_SID_MISMATCH
# define STATUS_SERVER_SID_MISMATCH ((NTSTATUS) 0xC00002A0L)
#endif

#ifndef STATUS_DS_NO_ATTRIBUTE_OR_VALUE
# define STATUS_DS_NO_ATTRIBUTE_OR_VALUE ((NTSTATUS) 0xC00002A1L)
#endif

#ifndef STATUS_DS_INVALID_ATTRIBUTE_SYNTAX
# define STATUS_DS_INVALID_ATTRIBUTE_SYNTAX ((NTSTATUS) 0xC00002A2L)
#endif

#ifndef STATUS_DS_ATTRIBUTE_TYPE_UNDEFINED
# define STATUS_DS_ATTRIBUTE_TYPE_UNDEFINED ((NTSTATUS) 0xC00002A3L)
#endif

#ifndef STATUS_DS_ATTRIBUTE_OR_VALUE_EXISTS
# define STATUS_DS_ATTRIBUTE_OR_VALUE_EXISTS ((NTSTATUS) 0xC00002A4L)
#endif

#ifndef STATUS_DS_BUSY
# define STATUS_DS_BUSY ((NTSTATUS) 0xC00002A5L)
#endif

#ifndef STATUS_DS_UNAVAILABLE
# define STATUS_DS_UNAVAILABLE ((NTSTATUS) 0xC00002A6L)
#endif

#ifndef STATUS_DS_NO_RIDS_ALLOCATED
# define STATUS_DS_NO_RIDS_ALLOCATED ((NTSTATUS) 0xC00002A7L)
#endif

#ifndef STATUS_DS_NO_MORE_RIDS
# define STATUS_DS_NO_MORE_RIDS ((NTSTATUS) 0xC00002A8L)
#endif

#ifndef STATUS_DS_INCORRECT_ROLE_OWNER
# define STATUS_DS_INCORRECT_ROLE_OWNER ((NTSTATUS) 0xC00002A9L)
#endif

#ifndef STATUS_DS_RIDMGR_INIT_ERROR
# define STATUS_DS_RIDMGR_INIT_ERROR ((NTSTATUS) 0xC00002AAL)
#endif

#ifndef STATUS_DS_OBJ_CLASS_VIOLATION
# define STATUS_DS_OBJ_CLASS_VIOLATION ((NTSTATUS) 0xC00002ABL)
#endif

#ifndef STATUS_DS_CANT_ON_NON_LEAF
# define STATUS_DS_CANT_ON_NON_LEAF ((NTSTATUS) 0xC00002ACL)
#endif

#ifndef STATUS_DS_CANT_ON_RDN
# define STATUS_DS_CANT_ON_RDN ((NTSTATUS) 0xC00002ADL)
#endif

#ifndef STATUS_DS_CANT_MOD_OBJ_CLASS
# define STATUS_DS_CANT_MOD_OBJ_CLASS ((NTSTATUS) 0xC00002AEL)
#endif

#ifndef STATUS_DS_CROSS_DOM_MOVE_FAILED
# define STATUS_DS_CROSS_DOM_MOVE_FAILED ((NTSTATUS) 0xC00002AFL)
#endif

#ifndef STATUS_DS_GC_NOT_AVAILABLE
# define STATUS_DS_GC_NOT_AVAILABLE ((NTSTATUS) 0xC00002B0L)
#endif

#ifndef STATUS_DIRECTORY_SERVICE_REQUIRED
# define STATUS_DIRECTORY_SERVICE_REQUIRED ((NTSTATUS) 0xC00002B1L)
#endif

#ifndef STATUS_REPARSE_ATTRIBUTE_CONFLICT
# define STATUS_REPARSE_ATTRIBUTE_CONFLICT ((NTSTATUS) 0xC00002B2L)
#endif

#ifndef STATUS_CANT_ENABLE_DENY_ONLY
# define STATUS_CANT_ENABLE_DENY_ONLY ((NTSTATUS) 0xC00002B3L)
#endif

#ifndef STATUS_FLOAT_MULTIPLE_FAULTS
# define STATUS_FLOAT_MULTIPLE_FAULTS ((NTSTATUS) 0xC00002B4L)
#endif

#ifndef STATUS_FLOAT_MULTIPLE_TRAPS
# define STATUS_FLOAT_MULTIPLE_TRAPS ((NTSTATUS) 0xC00002B5L)
#endif

#ifndef STATUS_DEVICE_REMOVED
# define STATUS_DEVICE_REMOVED ((NTSTATUS) 0xC00002B6L)
#endif

#ifndef STATUS_JOURNAL_DELETE_IN_PROGRESS
# define STATUS_JOURNAL_DELETE_IN_PROGRESS ((NTSTATUS) 0xC00002B7L)
#endif

#ifndef STATUS_JOURNAL_NOT_ACTIVE
# define STATUS_JOURNAL_NOT_ACTIVE ((NTSTATUS) 0xC00002B8L)
#endif

#ifndef STATUS_NOINTERFACE
# define STATUS_NOINTERFACE ((NTSTATUS) 0xC00002B9L)
#endif

#ifndef STATUS_DS_ADMIN_LIMIT_EXCEEDED
# define STATUS_DS_ADMIN_LIMIT_EXCEEDED ((NTSTATUS) 0xC00002C1L)
#endif

#ifndef STATUS_DRIVER_FAILED_SLEEP
# define STATUS_DRIVER_FAILED_SLEEP ((NTSTATUS) 0xC00002C2L)
#endif

#ifndef STATUS_MUTUAL_AUTHENTICATION_FAILED
# define STATUS_MUTUAL_AUTHENTICATION_FAILED ((NTSTATUS) 0xC00002C3L)
#endif

#ifndef STATUS_CORRUPT_SYSTEM_FILE
# define STATUS_CORRUPT_SYSTEM_FILE ((NTSTATUS) 0xC00002C4L)
#endif

#ifndef STATUS_DATATYPE_MISALIGNMENT_ERROR
# define STATUS_DATATYPE_MISALIGNMENT_ERROR ((NTSTATUS) 0xC00002C5L)
#endif

#ifndef STATUS_WMI_READ_ONLY
# define STATUS_WMI_READ_ONLY ((NTSTATUS) 0xC00002C6L)
#endif

#ifndef STATUS_WMI_SET_FAILURE
# define STATUS_WMI_SET_FAILURE ((NTSTATUS) 0xC00002C7L)
#endif

#ifndef STATUS_COMMITMENT_MINIMUM
# define STATUS_COMMITMENT_MINIMUM ((NTSTATUS) 0xC00002C8L)
#endif

#ifndef STATUS_REG_NAT_CONSUMPTION
# define STATUS_REG_NAT_CONSUMPTION ((NTSTATUS) 0xC00002C9L)
#endif

#ifndef STATUS_TRANSPORT_FULL
# define STATUS_TRANSPORT_FULL ((NTSTATUS) 0xC00002CAL)
#endif

#ifndef STATUS_DS_SAM_INIT_FAILURE
# define STATUS_DS_SAM_INIT_FAILURE ((NTSTATUS) 0xC00002CBL)
#endif

#ifndef STATUS_ONLY_IF_CONNECTED
# define STATUS_ONLY_IF_CONNECTED ((NTSTATUS) 0xC00002CCL)
#endif

#ifndef STATUS_DS_SENSITIVE_GROUP_VIOLATION
# define STATUS_DS_SENSITIVE_GROUP_VIOLATION ((NTSTATUS) 0xC00002CDL)
#endif

#ifndef STATUS_PNP_RESTART_ENUMERATION
# define STATUS_PNP_RESTART_ENUMERATION ((NTSTATUS) 0xC00002CEL)
#endif

#ifndef STATUS_JOURNAL_ENTRY_DELETED
# define STATUS_JOURNAL_ENTRY_DELETED ((NTSTATUS) 0xC00002CFL)
#endif

#ifndef STATUS_DS_CANT_MOD_PRIMARYGROUPID
# define STATUS_DS_CANT_MOD_PRIMARYGROUPID ((NTSTATUS) 0xC00002D0L)
#endif

#ifndef STATUS_SYSTEM_IMAGE_BAD_SIGNATURE
# define STATUS_SYSTEM_IMAGE_BAD_SIGNATURE ((NTSTATUS) 0xC00002D1L)
#endif

#ifndef STATUS_PNP_REBOOT_REQUIRED
# define STATUS_PNP_REBOOT_REQUIRED ((NTSTATUS) 0xC00002D2L)
#endif

#ifndef STATUS_POWER_STATE_INVALID
# define STATUS_POWER_STATE_INVALID ((NTSTATUS) 0xC00002D3L)
#endif

#ifndef STATUS_DS_INVALID_GROUP_TYPE
# define STATUS_DS_INVALID_GROUP_TYPE ((NTSTATUS) 0xC00002D4L)
#endif

#ifndef STATUS_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN
# define STATUS_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN ((NTSTATUS) 0xC00002D5L)
#endif

#ifndef STATUS_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN
# define STATUS_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN ((NTSTATUS) 0xC00002D6L)
#endif

#ifndef STATUS_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER
# define STATUS_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER ((NTSTATUS) 0xC00002D7L)
#endif

#ifndef STATUS_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER
# define STATUS_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER ((NTSTATUS) 0xC00002D8L)
#endif

#ifndef STATUS_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER
# define STATUS_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER ((NTSTATUS) 0xC00002D9L)
#endif

#ifndef STATUS_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER
# define STATUS_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER ((NTSTATUS) 0xC00002DAL)
#endif

#ifndef STATUS_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER
# define STATUS_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER ((NTSTATUS) 0xC00002DBL)
#endif

#ifndef STATUS_DS_HAVE_PRIMARY_MEMBERS
# define STATUS_DS_HAVE_PRIMARY_MEMBERS ((NTSTATUS) 0xC00002DCL)
#endif

#ifndef STATUS_WMI_NOT_SUPPORTED
# define STATUS_WMI_NOT_SUPPORTED ((NTSTATUS) 0xC00002DDL)
#endif

#ifndef STATUS_INSUFFICIENT_POWER
# define STATUS_INSUFFICIENT_POWER ((NTSTATUS) 0xC00002DEL)
#endif

#ifndef STATUS_SAM_NEED_BOOTKEY_PASSWORD
# define STATUS_SAM_NEED_BOOTKEY_PASSWORD ((NTSTATUS) 0xC00002DFL)
#endif

#ifndef STATUS_SAM_NEED_BOOTKEY_FLOPPY
# define STATUS_SAM_NEED_BOOTKEY_FLOPPY ((NTSTATUS) 0xC00002E0L)
#endif

#ifndef STATUS_DS_CANT_START
# define STATUS_DS_CANT_START ((NTSTATUS) 0xC00002E1L)
#endif

#ifndef STATUS_DS_INIT_FAILURE
# define STATUS_DS_INIT_FAILURE ((NTSTATUS) 0xC00002E2L)
#endif

#ifndef STATUS_SAM_INIT_FAILURE
# define STATUS_SAM_INIT_FAILURE ((NTSTATUS) 0xC00002E3L)
#endif

#ifndef STATUS_DS_GC_REQUIRED
# define STATUS_DS_GC_REQUIRED ((NTSTATUS) 0xC00002E4L)
#endif

#ifndef STATUS_DS_LOCAL_MEMBER_OF_LOCAL_ONLY
# define STATUS_DS_LOCAL_MEMBER_OF_LOCAL_ONLY ((NTSTATUS) 0xC00002E5L)
#endif

#ifndef STATUS_DS_NO_FPO_IN_UNIVERSAL_GROUPS
# define STATUS_DS_NO_FPO_IN_UNIVERSAL_GROUPS ((NTSTATUS) 0xC00002E6L)
#endif

#ifndef STATUS_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED
# define STATUS_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED ((NTSTATUS) 0xC00002E7L)
#endif

#ifndef STATUS_MULTIPLE_FAULT_VIOLATION
# define STATUS_MULTIPLE_FAULT_VIOLATION ((NTSTATUS) 0xC00002E8L)
#endif

#ifndef STATUS_CURRENT_DOMAIN_NOT_ALLOWED
# define STATUS_CURRENT_DOMAIN_NOT_ALLOWED ((NTSTATUS) 0xC00002E9L)
#endif

#ifndef STATUS_CANNOT_MAKE
# define STATUS_CANNOT_MAKE ((NTSTATUS) 0xC00002EAL)
#endif

#ifndef STATUS_SYSTEM_SHUTDOWN
# define STATUS_SYSTEM_SHUTDOWN ((NTSTATUS) 0xC00002EBL)
#endif

#ifndef STATUS_DS_INIT_FAILURE_CONSOLE
# define STATUS_DS_INIT_FAILURE_CONSOLE ((NTSTATUS) 0xC00002ECL)
#endif

#ifndef STATUS_DS_SAM_INIT_FAILURE_CONSOLE
# define STATUS_DS_SAM_INIT_FAILURE_CONSOLE ((NTSTATUS) 0xC00002EDL)
#endif

#ifndef STATUS_UNFINISHED_CONTEXT_DELETED
# define STATUS_UNFINISHED_CONTEXT_DELETED ((NTSTATUS) 0xC00002EEL)
#endif

#ifndef STATUS_NO_TGT_REPLY
# define STATUS_NO_TGT_REPLY ((NTSTATUS) 0xC00002EFL)
#endif

#ifndef STATUS_OBJECTID_NOT_FOUND
# define STATUS_OBJECTID_NOT_FOUND ((NTSTATUS) 0xC00002F0L)
#endif

#ifndef STATUS_NO_IP_ADDRESSES
# define STATUS_NO_IP_ADDRESSES ((NTSTATUS) 0xC00002F1L)
#endif

#ifndef STATUS_WRONG_CREDENTIAL_HANDLE
# define STATUS_WRONG_CREDENTIAL_HANDLE ((NTSTATUS) 0xC00002F2L)
#endif

#ifndef STATUS_CRYPTO_SYSTEM_INVALID
# define STATUS_CRYPTO_SYSTEM_INVALID ((NTSTATUS) 0xC00002F3L)
#endif

#ifndef STATUS_MAX_REFERRALS_EXCEEDED
# define STATUS_MAX_REFERRALS_EXCEEDED ((NTSTATUS) 0xC00002F4L)
#endif

#ifndef STATUS_MUST_BE_KDC
# define STATUS_MUST_BE_KDC ((NTSTATUS) 0xC00002F5L)
#endif

#ifndef STATUS_STRONG_CRYPTO_NOT_SUPPORTED
# define STATUS_STRONG_CRYPTO_NOT_SUPPORTED ((NTSTATUS) 0xC00002F6L)
#endif

#ifndef STATUS_TOO_MANY_PRINCIPALS
# define STATUS_TOO_MANY_PRINCIPALS ((NTSTATUS) 0xC00002F7L)
#endif

#ifndef STATUS_NO_PA_DATA
# define STATUS_NO_PA_DATA ((NTSTATUS) 0xC00002F8L)
#endif

#ifndef STATUS_PKINIT_NAME_MISMATCH
# define STATUS_PKINIT_NAME_MISMATCH ((NTSTATUS) 0xC00002F9L)
#endif

#ifndef STATUS_SMARTCARD_LOGON_REQUIRED
# define STATUS_SMARTCARD_LOGON_REQUIRED ((NTSTATUS) 0xC00002FAL)
#endif

#ifndef STATUS_KDC_INVALID_REQUEST
# define STATUS_KDC_INVALID_REQUEST ((NTSTATUS) 0xC00002FBL)
#endif

#ifndef STATUS_KDC_UNABLE_TO_REFER
# define STATUS_KDC_UNABLE_TO_REFER ((NTSTATUS) 0xC00002FCL)
#endif

#ifndef STATUS_KDC_UNKNOWN_ETYPE
# define STATUS_KDC_UNKNOWN_ETYPE ((NTSTATUS) 0xC00002FDL)
#endif

#ifndef STATUS_SHUTDOWN_IN_PROGRESS
# define STATUS_SHUTDOWN_IN_PROGRESS ((NTSTATUS) 0xC00002FEL)
#endif

#ifndef STATUS_SERVER_SHUTDOWN_IN_PROGRESS
# define STATUS_SERVER_SHUTDOWN_IN_PROGRESS ((NTSTATUS) 0xC00002FFL)
#endif

#ifndef STATUS_NOT_SUPPORTED_ON_SBS
# define STATUS_NOT_SUPPORTED_ON_SBS ((NTSTATUS) 0xC0000300L)
#endif

#ifndef STATUS_WMI_GUID_DISCONNECTED
# define STATUS_WMI_GUID_DISCONNECTED ((NTSTATUS) 0xC0000301L)
#endif

#ifndef STATUS_WMI_ALREADY_DISABLED
# define STATUS_WMI_ALREADY_DISABLED ((NTSTATUS) 0xC0000302L)
#endif

#ifndef STATUS_WMI_ALREADY_ENABLED
# define STATUS_WMI_ALREADY_ENABLED ((NTSTATUS) 0xC0000303L)
#endif

#ifndef STATUS_MFT_TOO_FRAGMENTED
# define STATUS_MFT_TOO_FRAGMENTED ((NTSTATUS) 0xC0000304L)
#endif

#ifndef STATUS_COPY_PROTECTION_FAILURE
# define STATUS_COPY_PROTECTION_FAILURE ((NTSTATUS) 0xC0000305L)
#endif

#ifndef STATUS_CSS_AUTHENTICATION_FAILURE
# define STATUS_CSS_AUTHENTICATION_FAILURE ((NTSTATUS) 0xC0000306L)
#endif

#ifndef STATUS_CSS_KEY_NOT_PRESENT
# define STATUS_CSS_KEY_NOT_PRESENT ((NTSTATUS) 0xC0000307L)
#endif

#ifndef STATUS_CSS_KEY_NOT_ESTABLISHED
# define STATUS_CSS_KEY_NOT_ESTABLISHED ((NTSTATUS) 0xC0000308L)
#endif

#ifndef STATUS_CSS_SCRAMBLED_SECTOR
# define STATUS_CSS_SCRAMBLED_SECTOR ((NTSTATUS) 0xC0000309L)
#endif

#ifndef STATUS_CSS_REGION_MISMATCH
# define STATUS_CSS_REGION_MISMATCH ((NTSTATUS) 0xC000030AL)
#endif

#ifndef STATUS_CSS_RESETS_EXHAUSTED
# define STATUS_CSS_RESETS_EXHAUSTED ((NTSTATUS) 0xC000030BL)
#endif

#ifndef STATUS_PKINIT_FAILURE
# define STATUS_PKINIT_FAILURE ((NTSTATUS) 0xC0000320L)
#endif

#ifndef STATUS_SMARTCARD_SUBSYSTEM_FAILURE
# define STATUS_SMARTCARD_SUBSYSTEM_FAILURE ((NTSTATUS) 0xC0000321L)
#endif

#ifndef STATUS_NO_KERB_KEY
# define STATUS_NO_KERB_KEY ((NTSTATUS) 0xC0000322L)
#endif

#ifndef STATUS_HOST_DOWN
# define STATUS_HOST_DOWN ((NTSTATUS) 0xC0000350L)
#endif

#ifndef STATUS_UNSUPPORTED_PREAUTH
# define STATUS_UNSUPPORTED_PREAUTH ((NTSTATUS) 0xC0000351L)
#endif

#ifndef STATUS_EFS_ALG_BLOB_TOO_BIG
# define STATUS_EFS_ALG_BLOB_TOO_BIG ((NTSTATUS) 0xC0000352L)
#endif

#ifndef STATUS_PORT_NOT_SET
# define STATUS_PORT_NOT_SET ((NTSTATUS) 0xC0000353L)
#endif

#ifndef STATUS_DEBUGGER_INACTIVE
# define STATUS_DEBUGGER_INACTIVE ((NTSTATUS) 0xC0000354L)
#endif

#ifndef STATUS_DS_VERSION_CHECK_FAILURE
# define STATUS_DS_VERSION_CHECK_FAILURE ((NTSTATUS) 0xC0000355L)
#endif

#ifndef STATUS_AUDITING_DISABLED
# define STATUS_AUDITING_DISABLED ((NTSTATUS) 0xC0000356L)
#endif

#ifndef STATUS_PRENT4_MACHINE_ACCOUNT
# define STATUS_PRENT4_MACHINE_ACCOUNT ((NTSTATUS) 0xC0000357L)
#endif

#ifndef STATUS_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER
# define STATUS_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER ((NTSTATUS) 0xC0000358L)
#endif

#ifndef STATUS_INVALID_IMAGE_WIN_32
# define STATUS_INVALID_IMAGE_WIN_32 ((NTSTATUS) 0xC0000359L)
#endif

#ifndef STATUS_INVALID_IMAGE_WIN_64
# define STATUS_INVALID_IMAGE_WIN_64 ((NTSTATUS) 0xC000035AL)
#endif

#ifndef STATUS_BAD_BINDINGS
# define STATUS_BAD_BINDINGS ((NTSTATUS) 0xC000035BL)
#endif

#ifndef STATUS_NETWORK_SESSION_EXPIRED
# define STATUS_NETWORK_SESSION_EXPIRED ((NTSTATUS) 0xC000035CL)
#endif

#ifndef STATUS_APPHELP_BLOCK
# define STATUS_APPHELP_BLOCK ((NTSTATUS) 0xC000035DL)
#endif

#ifndef STATUS_ALL_SIDS_FILTERED
# define STATUS_ALL_SIDS_FILTERED ((NTSTATUS) 0xC000035EL)
#endif

#ifndef STATUS_NOT_SAFE_MODE_DRIVER
# define STATUS_NOT_SAFE_MODE_DRIVER ((NTSTATUS) 0xC000035FL)
#endif

#ifndef STATUS_ACCESS_DISABLED_BY_POLICY_DEFAULT
# define STATUS_ACCESS_DISABLED_BY_POLICY_DEFAULT ((NTSTATUS) 0xC0000361L)
#endif

#ifndef STATUS_ACCESS_DISABLED_BY_POLICY_PATH
# define STATUS_ACCESS_DISABLED_BY_POLICY_PATH ((NTSTATUS) 0xC0000362L)
#endif

#ifndef STATUS_ACCESS_DISABLED_BY_POLICY_PUBLISHER
# define STATUS_ACCESS_DISABLED_BY_POLICY_PUBLISHER ((NTSTATUS) 0xC0000363L)
#endif

#ifndef STATUS_ACCESS_DISABLED_BY_POLICY_OTHER
# define STATUS_ACCESS_DISABLED_BY_POLICY_OTHER ((NTSTATUS) 0xC0000364L)
#endif

#ifndef STATUS_FAILED_DRIVER_ENTRY
# define STATUS_FAILED_DRIVER_ENTRY ((NTSTATUS) 0xC0000365L)
#endif

#ifndef STATUS_DEVICE_ENUMERATION_ERROR
# define STATUS_DEVICE_ENUMERATION_ERROR ((NTSTATUS) 0xC0000366L)
#endif

#ifndef STATUS_MOUNT_POINT_NOT_RESOLVED
# define STATUS_MOUNT_POINT_NOT_RESOLVED ((NTSTATUS) 0xC0000368L)
#endif

#ifndef STATUS_INVALID_DEVICE_OBJECT_PARAMETER
# define STATUS_INVALID_DEVICE_OBJECT_PARAMETER ((NTSTATUS) 0xC0000369L)
#endif

#ifndef STATUS_MCA_OCCURED
# define STATUS_MCA_OCCURED ((NTSTATUS) 0xC000036AL)
#endif

#ifndef STATUS_DRIVER_BLOCKED_CRITICAL
# define STATUS_DRIVER_BLOCKED_CRITICAL ((NTSTATUS) 0xC000036BL)
#endif

#ifndef STATUS_DRIVER_BLOCKED
# define STATUS_DRIVER_BLOCKED ((NTSTATUS) 0xC000036CL)
#endif

#ifndef STATUS_DRIVER_DATABASE_ERROR
# define STATUS_DRIVER_DATABASE_ERROR ((NTSTATUS) 0xC000036DL)
#endif

#ifndef STATUS_SYSTEM_HIVE_TOO_LARGE
# define STATUS_SYSTEM_HIVE_TOO_LARGE ((NTSTATUS) 0xC000036EL)
#endif

#ifndef STATUS_INVALID_IMPORT_OF_NON_DLL
# define STATUS_INVALID_IMPORT_OF_NON_DLL ((NTSTATUS) 0xC000036FL)
#endif

#ifndef STATUS_DS_SHUTTING_DOWN
# define STATUS_DS_SHUTTING_DOWN ((NTSTATUS) 0x40000370L)
#endif

#ifndef STATUS_NO_SECRETS
# define STATUS_NO_SECRETS ((NTSTATUS) 0xC0000371L)
#endif

#ifndef STATUS_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY
# define STATUS_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY ((NTSTATUS) 0xC0000372L)
#endif

#ifndef STATUS_FAILED_STACK_SWITCH
# define STATUS_FAILED_STACK_SWITCH ((NTSTATUS) 0xC0000373L)
#endif

#ifndef STATUS_HEAP_CORRUPTION
# define STATUS_HEAP_CORRUPTION ((NTSTATUS) 0xC0000374L)
#endif

#ifndef STATUS_SMARTCARD_WRONG_PIN
# define STATUS_SMARTCARD_WRONG_PIN ((NTSTATUS) 0xC0000380L)
#endif

#ifndef STATUS_SMARTCARD_CARD_BLOCKED
# define STATUS_SMARTCARD_CARD_BLOCKED ((NTSTATUS) 0xC0000381L)
#endif

#ifndef STATUS_SMARTCARD_CARD_NOT_AUTHENTICATED
# define STATUS_SMARTCARD_CARD_NOT_AUTHENTICATED ((NTSTATUS) 0xC0000382L)
#endif

#ifndef STATUS_SMARTCARD_NO_CARD
# define STATUS_SMARTCARD_NO_CARD ((NTSTATUS) 0xC0000383L)
#endif

#ifndef STATUS_SMARTCARD_NO_KEY_CONTAINER
# define STATUS_SMARTCARD_NO_KEY_CONTAINER ((NTSTATUS) 0xC0000384L)
#endif

#ifndef STATUS_SMARTCARD_NO_CERTIFICATE
# define STATUS_SMARTCARD_NO_CERTIFICATE ((NTSTATUS) 0xC0000385L)
#endif

#ifndef STATUS_SMARTCARD_NO_KEYSET
# define STATUS_SMARTCARD_NO_KEYSET ((NTSTATUS) 0xC0000386L)
#endif

#ifndef STATUS_SMARTCARD_IO_ERROR
# define STATUS_SMARTCARD_IO_ERROR ((NTSTATUS) 0xC0000387L)
#endif

#ifndef STATUS_DOWNGRADE_DETECTED
# define STATUS_DOWNGRADE_DETECTED ((NTSTATUS) 0xC0000388L)
#endif

#ifndef STATUS_SMARTCARD_CERT_REVOKED
# define STATUS_SMARTCARD_CERT_REVOKED ((NTSTATUS) 0xC0000389L)
#endif

#ifndef STATUS_ISSUING_CA_UNTRUSTED
# define STATUS_ISSUING_CA_UNTRUSTED ((NTSTATUS) 0xC000038AL)
#endif

#ifndef STATUS_REVOCATION_OFFLINE_C
# define STATUS_REVOCATION_OFFLINE_C ((NTSTATUS) 0xC000038BL)
#endif

#ifndef STATUS_PKINIT_CLIENT_FAILURE
# define STATUS_PKINIT_CLIENT_FAILURE ((NTSTATUS) 0xC000038CL)
#endif

#ifndef STATUS_SMARTCARD_CERT_EXPIRED
# define STATUS_SMARTCARD_CERT_EXPIRED ((NTSTATUS) 0xC000038DL)
#endif

#ifndef STATUS_DRIVER_FAILED_PRIOR_UNLOAD
# define STATUS_DRIVER_FAILED_PRIOR_UNLOAD ((NTSTATUS) 0xC000038EL)
#endif

#ifndef STATUS_SMARTCARD_SILENT_CONTEXT
# define STATUS_SMARTCARD_SILENT_CONTEXT ((NTSTATUS) 0xC000038FL)
#endif

#ifndef STATUS_PER_USER_TRUST_QUOTA_EXCEEDED
# define STATUS_PER_USER_TRUST_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000401L)
#endif

#ifndef STATUS_ALL_USER_TRUST_QUOTA_EXCEEDED
# define STATUS_ALL_USER_TRUST_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000402L)
#endif

#ifndef STATUS_USER_DELETE_TRUST_QUOTA_EXCEEDED
# define STATUS_USER_DELETE_TRUST_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000403L)
#endif

#ifndef STATUS_DS_NAME_NOT_UNIQUE
# define STATUS_DS_NAME_NOT_UNIQUE ((NTSTATUS) 0xC0000404L)
#endif

#ifndef STATUS_DS_DUPLICATE_ID_FOUND
# define STATUS_DS_DUPLICATE_ID_FOUND ((NTSTATUS) 0xC0000405L)
#endif

#ifndef STATUS_DS_GROUP_CONVERSION_ERROR
# define STATUS_DS_GROUP_CONVERSION_ERROR ((NTSTATUS) 0xC0000406L)
#endif

#ifndef STATUS_VOLSNAP_PREPARE_HIBERNATE
# define STATUS_VOLSNAP_PREPARE_HIBERNATE ((NTSTATUS) 0xC0000407L)
#endif

#ifndef STATUS_USER2USER_REQUIRED
# define STATUS_USER2USER_REQUIRED ((NTSTATUS) 0xC0000408L)
#endif

#ifndef STATUS_STACK_BUFFER_OVERRUN
# define STATUS_STACK_BUFFER_OVERRUN ((NTSTATUS) 0xC0000409L)
#endif

#ifndef STATUS_NO_S4U_PROT_SUPPORT
# define STATUS_NO_S4U_PROT_SUPPORT ((NTSTATUS) 0xC000040AL)
#endif

#ifndef STATUS_CROSSREALM_DELEGATION_FAILURE
# define STATUS_CROSSREALM_DELEGATION_FAILURE ((NTSTATUS) 0xC000040BL)
#endif

#ifndef STATUS_REVOCATION_OFFLINE_KDC
# define STATUS_REVOCATION_OFFLINE_KDC ((NTSTATUS) 0xC000040CL)
#endif

#ifndef STATUS_ISSUING_CA_UNTRUSTED_KDC
# define STATUS_ISSUING_CA_UNTRUSTED_KDC ((NTSTATUS) 0xC000040DL)
#endif

#ifndef STATUS_KDC_CERT_EXPIRED
# define STATUS_KDC_CERT_EXPIRED ((NTSTATUS) 0xC000040EL)
#endif

#ifndef STATUS_KDC_CERT_REVOKED
# define STATUS_KDC_CERT_REVOKED ((NTSTATUS) 0xC000040FL)
#endif

#ifndef STATUS_PARAMETER_QUOTA_EXCEEDED
# define STATUS_PARAMETER_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000410L)
#endif

#ifndef STATUS_HIBERNATION_FAILURE
# define STATUS_HIBERNATION_FAILURE ((NTSTATUS) 0xC0000411L)
#endif

#ifndef STATUS_DELAY_LOAD_FAILED
# define STATUS_DELAY_LOAD_FAILED ((NTSTATUS) 0xC0000412L)
#endif

#ifndef STATUS_AUTHENTICATION_FIREWALL_FAILED
# define STATUS_AUTHENTICATION_FIREWALL_FAILED ((NTSTATUS) 0xC0000413L)
#endif

#ifndef STATUS_VDM_DISALLOWED
# define STATUS_VDM_DISALLOWED ((NTSTATUS) 0xC0000414L)
#endif

#ifndef STATUS_HUNG_DISPLAY_DRIVER_THREAD
# define STATUS_HUNG_DISPLAY_DRIVER_THREAD ((NTSTATUS) 0xC0000415L)
#endif

#ifndef STATUS_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE
# define STATUS_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE ((NTSTATUS) 0xC0000416L)
#endif

#ifndef STATUS_INVALID_CRUNTIME_PARAMETER
# define STATUS_INVALID_CRUNTIME_PARAMETER ((NTSTATUS) 0xC0000417L)
#endif

#ifndef STATUS_NTLM_BLOCKED
# define STATUS_NTLM_BLOCKED ((NTSTATUS) 0xC0000418L)
#endif

#ifndef STATUS_DS_SRC_SID_EXISTS_IN_FOREST
# define STATUS_DS_SRC_SID_EXISTS_IN_FOREST ((NTSTATUS) 0xC0000419L)
#endif

#ifndef STATUS_DS_DOMAIN_NAME_EXISTS_IN_FOREST
# define STATUS_DS_DOMAIN_NAME_EXISTS_IN_FOREST ((NTSTATUS) 0xC000041AL)
#endif

#ifndef STATUS_DS_FLAT_NAME_EXISTS_IN_FOREST
# define STATUS_DS_FLAT_NAME_EXISTS_IN_FOREST ((NTSTATUS) 0xC000041BL)
#endif

#ifndef STATUS_INVALID_USER_PRINCIPAL_NAME
# define STATUS_INVALID_USER_PRINCIPAL_NAME ((NTSTATUS) 0xC000041CL)
#endif

#ifndef STATUS_FATAL_USER_CALLBACK_EXCEPTION
# define STATUS_FATAL_USER_CALLBACK_EXCEPTION ((NTSTATUS) 0xC000041DL)
#endif

#ifndef STATUS_ASSERTION_FAILURE
# define STATUS_ASSERTION_FAILURE ((NTSTATUS) 0xC0000420L)
#endif

#ifndef STATUS_VERIFIER_STOP
# define STATUS_VERIFIER_STOP ((NTSTATUS) 0xC0000421L)
#endif

#ifndef STATUS_CALLBACK_POP_STACK
# define STATUS_CALLBACK_POP_STACK ((NTSTATUS) 0xC0000423L)
#endif

#ifndef STATUS_INCOMPATIBLE_DRIVER_BLOCKED
# define STATUS_INCOMPATIBLE_DRIVER_BLOCKED ((NTSTATUS) 0xC0000424L)
#endif

#ifndef STATUS_HIVE_UNLOADED
# define STATUS_HIVE_UNLOADED ((NTSTATUS) 0xC0000425L)
#endif

#ifndef STATUS_COMPRESSION_DISABLED
# define STATUS_COMPRESSION_DISABLED ((NTSTATUS) 0xC0000426L)
#endif

#ifndef STATUS_FILE_SYSTEM_LIMITATION
# define STATUS_FILE_SYSTEM_LIMITATION ((NTSTATUS) 0xC0000427L)
#endif

#ifndef STATUS_INVALID_IMAGE_HASH
# define STATUS_INVALID_IMAGE_HASH ((NTSTATUS) 0xC0000428L)
#endif

#ifndef STATUS_NOT_CAPABLE
# define STATUS_NOT_CAPABLE ((NTSTATUS) 0xC0000429L)
#endif

#ifndef STATUS_REQUEST_OUT_OF_SEQUENCE
# define STATUS_REQUEST_OUT_OF_SEQUENCE ((NTSTATUS) 0xC000042AL)
#endif

#ifndef STATUS_IMPLEMENTATION_LIMIT
# define STATUS_IMPLEMENTATION_LIMIT ((NTSTATUS) 0xC000042BL)
#endif

#ifndef STATUS_ELEVATION_REQUIRED
# define STATUS_ELEVATION_REQUIRED ((NTSTATUS) 0xC000042CL)
#endif

#ifndef STATUS_NO_SECURITY_CONTEXT
# define STATUS_NO_SECURITY_CONTEXT ((NTSTATUS) 0xC000042DL)
#endif

#ifndef STATUS_PKU2U_CERT_FAILURE
# define STATUS_PKU2U_CERT_FAILURE ((NTSTATUS) 0xC000042FL)
#endif

#ifndef STATUS_BEYOND_VDL
# define STATUS_BEYOND_VDL ((NTSTATUS) 0xC0000432L)
#endif

#ifndef STATUS_ENCOUNTERED_WRITE_IN_PROGRESS
# define STATUS_ENCOUNTERED_WRITE_IN_PROGRESS ((NTSTATUS) 0xC0000433L)
#endif

#ifndef STATUS_PTE_CHANGED
# define STATUS_PTE_CHANGED ((NTSTATUS) 0xC0000434L)
#endif

#ifndef STATUS_PURGE_FAILED
# define STATUS_PURGE_FAILED ((NTSTATUS) 0xC0000435L)
#endif

#ifndef STATUS_CRED_REQUIRES_CONFIRMATION
# define STATUS_CRED_REQUIRES_CONFIRMATION ((NTSTATUS) 0xC0000440L)
#endif

#ifndef STATUS_CS_ENCRYPTION_INVALID_SERVER_RESPONSE
# define STATUS_CS_ENCRYPTION_INVALID_SERVER_RESPONSE ((NTSTATUS) 0xC0000441L)
#endif

#ifndef STATUS_CS_ENCRYPTION_UNSUPPORTED_SERVER
# define STATUS_CS_ENCRYPTION_UNSUPPORTED_SERVER ((NTSTATUS) 0xC0000442L)
#endif

#ifndef STATUS_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE
# define STATUS_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE ((NTSTATUS) 0xC0000443L)
#endif

#ifndef STATUS_CS_ENCRYPTION_NEW_ENCRYPTED_FILE
# define STATUS_CS_ENCRYPTION_NEW_ENCRYPTED_FILE ((NTSTATUS) 0xC0000444L)
#endif

#ifndef STATUS_CS_ENCRYPTION_FILE_NOT_CSE
# define STATUS_CS_ENCRYPTION_FILE_NOT_CSE ((NTSTATUS) 0xC0000445L)
#endif

#ifndef STATUS_INVALID_LABEL
# define STATUS_INVALID_LABEL ((NTSTATUS) 0xC0000446L)
#endif

#ifndef STATUS_DRIVER_PROCESS_TERMINATED
# define STATUS_DRIVER_PROCESS_TERMINATED ((NTSTATUS) 0xC0000450L)
#endif

#ifndef STATUS_AMBIGUOUS_SYSTEM_DEVICE
# define STATUS_AMBIGUOUS_SYSTEM_DEVICE ((NTSTATUS) 0xC0000451L)
#endif

#ifndef STATUS_SYSTEM_DEVICE_NOT_FOUND
# define STATUS_SYSTEM_DEVICE_NOT_FOUND ((NTSTATUS) 0xC0000452L)
#endif

#ifndef STATUS_RESTART_BOOT_APPLICATION
# define STATUS_RESTART_BOOT_APPLICATION ((NTSTATUS) 0xC0000453L)
#endif

#ifndef STATUS_INSUFFICIENT_NVRAM_RESOURCES
# define STATUS_INSUFFICIENT_NVRAM_RESOURCES ((NTSTATUS) 0xC0000454L)
#endif

#ifndef STATUS_INVALID_TASK_NAME
# define STATUS_INVALID_TASK_NAME ((NTSTATUS) 0xC0000500L)
#endif

#ifndef STATUS_INVALID_TASK_INDEX
# define STATUS_INVALID_TASK_INDEX ((NTSTATUS) 0xC0000501L)
#endif

#ifndef STATUS_THREAD_ALREADY_IN_TASK
# define STATUS_THREAD_ALREADY_IN_TASK ((NTSTATUS) 0xC0000502L)
#endif

#ifndef STATUS_CALLBACK_BYPASS
# define STATUS_CALLBACK_BYPASS ((NTSTATUS) 0xC0000503L)
#endif

#ifndef STATUS_FAIL_FAST_EXCEPTION
# define STATUS_FAIL_FAST_EXCEPTION ((NTSTATUS) 0xC0000602L)
#endif

#ifndef STATUS_IMAGE_CERT_REVOKED
# define STATUS_IMAGE_CERT_REVOKED ((NTSTATUS) 0xC0000603L)
#endif

#ifndef STATUS_PORT_CLOSED
# define STATUS_PORT_CLOSED ((NTSTATUS) 0xC0000700L)
#endif

#ifndef STATUS_MESSAGE_LOST
# define STATUS_MESSAGE_LOST ((NTSTATUS) 0xC0000701L)
#endif

#ifndef STATUS_INVALID_MESSAGE
# define STATUS_INVALID_MESSAGE ((NTSTATUS) 0xC0000702L)
#endif

#ifndef STATUS_REQUEST_CANCELED
# define STATUS_REQUEST_CANCELED ((NTSTATUS) 0xC0000703L)
#endif

#ifndef STATUS_RECURSIVE_DISPATCH
# define STATUS_RECURSIVE_DISPATCH ((NTSTATUS) 0xC0000704L)
#endif

#ifndef STATUS_LPC_RECEIVE_BUFFER_EXPECTED
# define STATUS_LPC_RECEIVE_BUFFER_EXPECTED ((NTSTATUS) 0xC0000705L)
#endif

#ifndef STATUS_LPC_INVALID_CONNECTION_USAGE
# define STATUS_LPC_INVALID_CONNECTION_USAGE ((NTSTATUS) 0xC0000706L)
#endif

#ifndef STATUS_LPC_REQUESTS_NOT_ALLOWED
# define STATUS_LPC_REQUESTS_NOT_ALLOWED ((NTSTATUS) 0xC0000707L)
#endif

#ifndef STATUS_RESOURCE_IN_USE
# define STATUS_RESOURCE_IN_USE ((NTSTATUS) 0xC0000708L)
#endif

#ifndef STATUS_HARDWARE_MEMORY_ERROR
# define STATUS_HARDWARE_MEMORY_ERROR ((NTSTATUS) 0xC0000709L)
#endif

#ifndef STATUS_THREADPOOL_HANDLE_EXCEPTION
# define STATUS_THREADPOOL_HANDLE_EXCEPTION ((NTSTATUS) 0xC000070AL)
#endif

#ifndef STATUS_THREADPOOL_SET_EVENT_ON_COMPLETION_FAILED
# define STATUS_THREADPOOL_SET_EVENT_ON_COMPLETION_FAILED ((NTSTATUS) 0xC000070BL)
#endif

#ifndef STATUS_THREADPOOL_RELEASE_SEMAPHORE_ON_COMPLETION_FAILED
# define STATUS_THREADPOOL_RELEASE_SEMAPHORE_ON_COMPLETION_FAILED ((NTSTATUS) 0xC000070CL)
#endif

#ifndef STATUS_THREADPOOL_RELEASE_MUTEX_ON_COMPLETION_FAILED
# define STATUS_THREADPOOL_RELEASE_MUTEX_ON_COMPLETION_FAILED ((NTSTATUS) 0xC000070DL)
#endif

#ifndef STATUS_THREADPOOL_FREE_LIBRARY_ON_COMPLETION_FAILED
# define STATUS_THREADPOOL_FREE_LIBRARY_ON_COMPLETION_FAILED ((NTSTATUS) 0xC000070EL)
#endif

#ifndef STATUS_THREADPOOL_RELEASED_DURING_OPERATION
# define STATUS_THREADPOOL_RELEASED_DURING_OPERATION ((NTSTATUS) 0xC000070FL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_WHILE_IMPERSONATING
# define STATUS_CALLBACK_RETURNED_WHILE_IMPERSONATING ((NTSTATUS) 0xC0000710L)
#endif

#ifndef STATUS_APC_RETURNED_WHILE_IMPERSONATING
# define STATUS_APC_RETURNED_WHILE_IMPERSONATING ((NTSTATUS) 0xC0000711L)
#endif

#ifndef STATUS_PROCESS_IS_PROTECTED
# define STATUS_PROCESS_IS_PROTECTED ((NTSTATUS) 0xC0000712L)
#endif

#ifndef STATUS_MCA_EXCEPTION
# define STATUS_MCA_EXCEPTION ((NTSTATUS) 0xC0000713L)
#endif

#ifndef STATUS_CERTIFICATE_MAPPING_NOT_UNIQUE
# define STATUS_CERTIFICATE_MAPPING_NOT_UNIQUE ((NTSTATUS) 0xC0000714L)
#endif

#ifndef STATUS_SYMLINK_CLASS_DISABLED
# define STATUS_SYMLINK_CLASS_DISABLED ((NTSTATUS) 0xC0000715L)
#endif

#ifndef STATUS_INVALID_IDN_NORMALIZATION
# define STATUS_INVALID_IDN_NORMALIZATION ((NTSTATUS) 0xC0000716L)
#endif

#ifndef STATUS_NO_UNICODE_TRANSLATION
# define STATUS_NO_UNICODE_TRANSLATION ((NTSTATUS) 0xC0000717L)
#endif

#ifndef STATUS_ALREADY_REGISTERED
# define STATUS_ALREADY_REGISTERED ((NTSTATUS) 0xC0000718L)
#endif

#ifndef STATUS_CONTEXT_MISMATCH
# define STATUS_CONTEXT_MISMATCH ((NTSTATUS) 0xC0000719L)
#endif

#ifndef STATUS_PORT_ALREADY_HAS_COMPLETION_LIST
# define STATUS_PORT_ALREADY_HAS_COMPLETION_LIST ((NTSTATUS) 0xC000071AL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_THREAD_PRIORITY
# define STATUS_CALLBACK_RETURNED_THREAD_PRIORITY ((NTSTATUS) 0xC000071BL)
#endif

#ifndef STATUS_INVALID_THREAD
# define STATUS_INVALID_THREAD ((NTSTATUS) 0xC000071CL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_TRANSACTION
# define STATUS_CALLBACK_RETURNED_TRANSACTION ((NTSTATUS) 0xC000071DL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_LDR_LOCK
# define STATUS_CALLBACK_RETURNED_LDR_LOCK ((NTSTATUS) 0xC000071EL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_LANG
# define STATUS_CALLBACK_RETURNED_LANG ((NTSTATUS) 0xC000071FL)
#endif

#ifndef STATUS_CALLBACK_RETURNED_PRI_BACK
# define STATUS_CALLBACK_RETURNED_PRI_BACK ((NTSTATUS) 0xC0000720L)
#endif

#ifndef STATUS_CALLBACK_RETURNED_THREAD_AFFINITY
# define STATUS_CALLBACK_RETURNED_THREAD_AFFINITY ((NTSTATUS) 0xC0000721L)
#endif

#ifndef STATUS_DISK_REPAIR_DISABLED
# define STATUS_DISK_REPAIR_DISABLED ((NTSTATUS) 0xC0000800L)
#endif

#ifndef STATUS_DS_DOMAIN_RENAME_IN_PROGRESS
# define STATUS_DS_DOMAIN_RENAME_IN_PROGRESS ((NTSTATUS) 0xC0000801L)
#endif

#ifndef STATUS_DISK_QUOTA_EXCEEDED
# define STATUS_DISK_QUOTA_EXCEEDED ((NTSTATUS) 0xC0000802L)
#endif

#ifndef STATUS_DATA_LOST_REPAIR
# define STATUS_DATA_LOST_REPAIR ((NTSTATUS) 0x80000803L)
#endif

#ifndef STATUS_CONTENT_BLOCKED
# define STATUS_CONTENT_BLOCKED ((NTSTATUS) 0xC0000804L)
#endif

#ifndef STATUS_BAD_CLUSTERS
# define STATUS_BAD_CLUSTERS ((NTSTATUS) 0xC0000805L)
#endif

#ifndef STATUS_VOLUME_DIRTY
# define STATUS_VOLUME_DIRTY ((NTSTATUS) 0xC0000806L)
#endif

#ifndef STATUS_FILE_CHECKED_OUT
# define STATUS_FILE_CHECKED_OUT ((NTSTATUS) 0xC0000901L)
#endif

#ifndef STATUS_CHECKOUT_REQUIRED
# define STATUS_CHECKOUT_REQUIRED ((NTSTATUS) 0xC0000902L)
#endif

#ifndef STATUS_BAD_FILE_TYPE
# define STATUS_BAD_FILE_TYPE ((NTSTATUS) 0xC0000903L)
#endif

#ifndef STATUS_FILE_TOO_LARGE
# define STATUS_FILE_TOO_LARGE ((NTSTATUS) 0xC0000904L)
#endif

#ifndef STATUS_FORMS_AUTH_REQUIRED
# define STATUS_FORMS_AUTH_REQUIRED ((NTSTATUS) 0xC0000905L)
#endif

#ifndef STATUS_VIRUS_INFECTED
# define STATUS_VIRUS_INFECTED ((NTSTATUS) 0xC0000906L)
#endif

#ifndef STATUS_VIRUS_DELETED
# define STATUS_VIRUS_DELETED ((NTSTATUS) 0xC0000907L)
#endif

#ifndef STATUS_BAD_MCFG_TABLE
# define STATUS_BAD_MCFG_TABLE ((NTSTATUS) 0xC0000908L)
#endif

#ifndef STATUS_CANNOT_BREAK_OPLOCK
# define STATUS_CANNOT_BREAK_OPLOCK ((NTSTATUS) 0xC0000909L)
#endif

#ifndef STATUS_WOW_ASSERTION
# define STATUS_WOW_ASSERTION ((NTSTATUS) 0xC0009898L)
#endif

#ifndef STATUS_INVALID_SIGNATURE
# define STATUS_INVALID_SIGNATURE ((NTSTATUS) 0xC000A000L)
#endif

#ifndef STATUS_HMAC_NOT_SUPPORTED
# define STATUS_HMAC_NOT_SUPPORTED ((NTSTATUS) 0xC000A001L)
#endif

#ifndef STATUS_AUTH_TAG_MISMATCH
# define STATUS_AUTH_TAG_MISMATCH ((NTSTATUS) 0xC000A002L)
#endif

#ifndef STATUS_IPSEC_QUEUE_OVERFLOW
# define STATUS_IPSEC_QUEUE_OVERFLOW ((NTSTATUS) 0xC000A010L)
#endif

#ifndef STATUS_ND_QUEUE_OVERFLOW
# define STATUS_ND_QUEUE_OVERFLOW ((NTSTATUS) 0xC000A011L)
#endif

#ifndef STATUS_HOPLIMIT_EXCEEDED
# define STATUS_HOPLIMIT_EXCEEDED ((NTSTATUS) 0xC000A012L)
#endif

#ifndef STATUS_PROTOCOL_NOT_SUPPORTED
# define STATUS_PROTOCOL_NOT_SUPPORTED ((NTSTATUS) 0xC000A013L)
#endif

#ifndef STATUS_FASTPATH_REJECTED
# define STATUS_FASTPATH_REJECTED ((NTSTATUS) 0xC000A014L)
#endif

#ifndef STATUS_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED
# define STATUS_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED ((NTSTATUS) 0xC000A080L)
#endif

#ifndef STATUS_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR
# define STATUS_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR ((NTSTATUS) 0xC000A081L)
#endif

#ifndef STATUS_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR
# define STATUS_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR ((NTSTATUS) 0xC000A082L)
#endif

#ifndef STATUS_XML_PARSE_ERROR
# define STATUS_XML_PARSE_ERROR ((NTSTATUS) 0xC000A083L)
#endif

#ifndef STATUS_XMLDSIG_ERROR
# define STATUS_XMLDSIG_ERROR ((NTSTATUS) 0xC000A084L)
#endif

#ifndef STATUS_WRONG_COMPARTMENT
# define STATUS_WRONG_COMPARTMENT ((NTSTATUS) 0xC000A085L)
#endif

#ifndef STATUS_AUTHIP_FAILURE
# define STATUS_AUTHIP_FAILURE ((NTSTATUS) 0xC000A086L)
#endif

#ifndef STATUS_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS
# define STATUS_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS ((NTSTATUS) 0xC000A087L)
#endif

#ifndef STATUS_DS_OID_NOT_FOUND
# define STATUS_DS_OID_NOT_FOUND ((NTSTATUS) 0xC000A088L)
#endif

#ifndef STATUS_HASH_NOT_SUPPORTED
# define STATUS_HASH_NOT_SUPPORTED ((NTSTATUS) 0xC000A100L)
#endif

#ifndef STATUS_HASH_NOT_PRESENT
# define STATUS_HASH_NOT_PRESENT ((NTSTATUS) 0xC000A101L)
#endif

/* This is not the NTSTATUS_FROM_WIN32 that the DDK provides, because the DDK
 * got it wrong! */
#ifdef NTSTATUS_FROM_WIN32
# undef NTSTATUS_FROM_WIN32
#endif
#define NTSTATUS_FROM_WIN32(error) ((NTSTATUS) (error) <= 0 ? \
        ((NTSTATUS) (error)) : ((NTSTATUS) (((error) & 0x0000FFFF) | \
        (FACILITY_NTWIN32 << 16) | ERROR_SEVERITY_WARNING)))

#ifndef JOB_OBJECT_LIMIT_PROCESS_MEMORY
# define JOB_OBJECT_LIMIT_PROCESS_MEMORY             0x00000100
#endif
#ifndef JOB_OBJECT_LIMIT_JOB_MEMORY
# define JOB_OBJECT_LIMIT_JOB_MEMORY                 0x00000200
#endif
#ifndef JOB_OBJECT_LIMIT_DIE_ON_UNHANDLED_EXCEPTION
# define JOB_OBJECT_LIMIT_DIE_ON_UNHANDLED_EXCEPTION 0x00000400
#endif
#ifndef JOB_OBJECT_LIMIT_BREAKAWAY_OK
# define JOB_OBJECT_LIMIT_BREAKAWAY_OK               0x00000800
#endif
#ifndef JOB_OBJECT_LIMIT_SILENT_BREAKAWAY_OK
# define JOB_OBJECT_LIMIT_SILENT_BREAKAWAY_OK        0x00001000
#endif
#ifndef JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE
# define JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE          0x00002000
#endif

#ifndef SYMBOLIC_LINK_FLAG_ALLOW_UNPRIVILEGED_CREATE
# define SYMBOLIC_LINK_FLAG_ALLOW_UNPRIVILEGED_CREATE 0x00000002
#endif

/* from winternl.h */
#if !defined(__UNICODE_STRING_DEFINED) && defined(__MINGW32__)
#define __UNICODE_STRING_DEFINED
#endif
typedef struct _UNICODE_STRING {
  USHORT Length;
  USHORT MaximumLength;
  PWSTR  Buffer;
} UNICODE_STRING, *PUNICODE_STRING;

typedef const UNICODE_STRING *PCUNICODE_STRING;

/* from ntifs.h */
#ifndef DEVICE_TYPE
# define DEVICE_TYPE DWORD
#endif

#ifndef NTDDI_WIN11_ZN
# define NTDDI_WIN11_ZN  0x0A00000E
#endif

/* API is defined in newer SDKS */
#if (NTDDI_VERSION < NTDDI_WIN11_ZN)
typedef struct _FILE_STAT_BASIC_INFORMATION {
  LARGE_INTEGER FileId;
  LARGE_INTEGER CreationTime;
  LARGE_INTEGER LastAccessTime;
  LARGE_INTEGER LastWriteTime;
  LARGE_INTEGER ChangeTime;
  LARGE_INTEGER AllocationSize;
  LARGE_INTEGER EndOfFile;
  ULONG FileAttributes;
  ULONG ReparseTag;
  ULONG NumberOfLinks;
  ULONG DeviceType;
  ULONG DeviceCharacteristics;
  ULONG Reserved;
  LARGE_INTEGER VolumeSerialNumber;
  FILE_ID_128 FileId128;
} FILE_STAT_BASIC_INFORMATION;
#endif

typedef struct _REPARSE_DATA_BUFFER {
  ULONG  ReparseTag;
  USHORT ReparseDataLength;
  USHORT Reserved;
  union {
    struct {
      USHORT SubstituteNameOffset;
      USHORT SubstituteNameLength;
      USHORT PrintNameOffset;
      USHORT PrintNameLength;
      ULONG Flags;
      WCHAR PathBuffer[1];
    } SymbolicLinkReparseBuffer;
    struct {
      USHORT SubstituteNameOffset;
      USHORT SubstituteNameLength;
      USHORT PrintNameOffset;
      USHORT PrintNameLength;
      WCHAR PathBuffer[1];
    } MountPointReparseBuffer;
    struct {
      UCHAR  DataBuffer[1];
    } GenericReparseBuffer;
    struct {
      ULONG StringCount;
      WCHAR StringList[1];
    } AppExecLinkReparseBuffer;
  };
} REPARSE_DATA_BUFFER, *PREPARSE_DATA_BUFFER;

typedef struct _IO_STATUS_BLOCK {
  union {
    NTSTATUS Status;
    PVOID Pointer;
  };
  ULONG_PTR Information;
} IO_STATUS_BLOCK, *PIO_STATUS_BLOCK;

typedef enum _FILE_INFORMATION_CLASS {
  FileDirectoryInformation = 1,
  FileFullDirectoryInformation,
  FileBothDirectoryInformation,
  FileBasicInformation,
  FileStandardInformation,
  FileInternalInformation,
  FileEaInformation,
  FileAccessInformation,
  FileNameInformation,
  FileRenameInformation,
  FileLinkInformation,
  FileNamesInformation,
  FileDispositionInformation,
  FilePositionInformation,
  FileFullEaInformation,
  FileModeInformation,
  FileAlignmentInformation,
  FileAllInformation,
  FileAllocationInformation,
  FileEndOfFileInformation,
  FileAlternateNameInformation,
  FileStreamInformation,
  FilePipeInformation,
  FilePipeLocalInformation,
  FilePipeRemoteInformation,
  FileMailslotQueryInformation,
  FileMailslotSetInformation,
  FileCompressionInformation,
  FileObjectIdInformation,
  FileCompletionInformation,
  FileMoveClusterInformation,
  FileQuotaInformation,
  FileReparsePointInformation,
  FileNetworkOpenInformation,
  FileAttributeTagInformation,
  FileTrackingInformation,
  FileIdBothDirectoryInformation,
  FileIdFullDirectoryInformation,
  FileValidDataLengthInformation,
  FileShortNameInformation,
  FileIoCompletionNotificationInformation,
  FileIoStatusBlockRangeInformation,
  FileIoPriorityHintInformation,
  FileSfioReserveInformation,
  FileSfioVolumeInformation,
  FileHardLinkInformation,
  FileProcessIdsUsingFileInformation,
  FileNormalizedNameInformation,
  FileNetworkPhysicalNameInformation,
  FileIdGlobalTxDirectoryInformation,
  FileIsRemoteDeviceInformation,
  FileAttributeCacheInformation,
  FileNumaNodeInformation,
  FileStandardLinkInformation,
  FileRemoteProtocolInformation,
  FileRenameInformationBypassAccessCheck,
  FileLinkInformationBypassAccessCheck,
  FileVolumeNameInformation,
  FileIdInformation,
  FileIdExtdDirectoryInformation,
  FileReplaceCompletionInformation,
  FileHardLinkFullIdInformation,
  FileIdExtdBothDirectoryInformation,
  FileDispositionInformationEx, /* based on https://learn.microsoft.com/en-us/windows-hardware/drivers/ddi/wdm/ne-wdm-_file_information_class */
  FileMaximumInformation
} FILE_INFORMATION_CLASS, *PFILE_INFORMATION_CLASS;

typedef struct _FILE_DIRECTORY_INFORMATION {
  ULONG NextEntryOffset;
  ULONG FileIndex;
  LARGE_INTEGER CreationTime;
  LARGE_INTEGER LastAccessTime;
  LARGE_INTEGER LastWriteTime;
  LARGE_INTEGER ChangeTime;
  LARGE_INTEGER EndOfFile;
  LARGE_INTEGER AllocationSize;
  ULONG FileAttributes;
  ULONG FileNameLength;
  WCHAR FileName[1];
} FILE_DIRECTORY_INFORMATION, *PFILE_DIRECTORY_INFORMATION;

typedef struct _FILE_BOTH_DIR_INFORMATION {
  ULONG NextEntryOffset;
  ULONG FileIndex;
  LARGE_INTEGER CreationTime;
  LARGE_INTEGER LastAccessTime;
  LARGE_INTEGER LastWriteTime;
  LARGE_INTEGER ChangeTime;
  LARGE_INTEGER EndOfFile;
  LARGE_INTEGER AllocationSize;
  ULONG FileAttributes;
  ULONG FileNameLength;
  ULONG EaSize;
  CCHAR ShortNameLength;
  WCHAR ShortName[12];
  WCHAR FileName[1];
} FILE_BOTH_DIR_INFORMATION, *PFILE_BOTH_DIR_INFORMATION;

typedef struct _FILE_ID_FULL_DIR_INFORMATION {
  ULONG         NextEntryOffset;
  ULONG         FileIndex;
  LARGE_INTEGER CreationTime;
  LARGE_INTEGER LastAccessTime;
  LARGE_INTEGER LastWriteTime;
  LARGE_INTEGER ChangeTime;
  LARGE_INTEGER EndOfFile;
  LARGE_INTEGER AllocationSize;
  ULONG         FileAttributes;
  ULONG         FileNameLength;
  ULONG         EaSize;
  LARGE_INTEGER FileId;
  WCHAR         FileName[1];
} FILE_ID_FULL_DIR_INFORMATION, *PFILE_ID_FULL_DIR_INFORMATION;

typedef struct _FILE_BASIC_INFORMATION {
  LARGE_INTEGER CreationTime;
  LARGE_INTEGER LastAccessTime;
  LARGE_INTEGER LastWriteTime;
  LARGE_INTEGER ChangeTime;
  DWORD FileAttributes;
} FILE_BASIC_INFORMATION, *PFILE_BASIC_INFORMATION;

typedef struct _FILE_STANDARD_INFORMATION {
  LARGE_INTEGER AllocationSize;
  LARGE_INTEGER EndOfFile;
  ULONG         NumberOfLinks;
  BOOLEAN       DeletePending;
  BOOLEAN       Directory;
} FILE_STANDARD_INFORMATION, *PFILE_STANDARD_INFORMATION;

typedef struct _FILE_INTERNAL_INFORMATION {
  LARGE_INTEGER IndexNumber;
} FILE_INTERNAL_INFORMATION, *PFILE_INTERNAL_INFORMATION;

typedef struct _FILE_EA_INFORMATION {
  ULONG EaSize;
} FILE_EA_INFORMATION, *PFILE_EA_INFORMATION;

typedef struct _FILE_ACCESS_INFORMATION {
  ACCESS_MASK AccessFlags;
} FILE_ACCESS_INFORMATION, *PFILE_ACCESS_INFORMATION;

typedef struct _FILE_POSITION_INFORMATION {
  LARGE_INTEGER CurrentByteOffset;
} FILE_POSITION_INFORMATION, *PFILE_POSITION_INFORMATION;

typedef struct _FILE_MODE_INFORMATION {
  ULONG Mode;
} FILE_MODE_INFORMATION, *PFILE_MODE_INFORMATION;

typedef struct _FILE_ALIGNMENT_INFORMATION {
  ULONG AlignmentRequirement;
} FILE_ALIGNMENT_INFORMATION, *PFILE_ALIGNMENT_INFORMATION;

typedef struct _FILE_NAME_INFORMATION {
  ULONG FileNameLength;
  WCHAR FileName[1];
} FILE_NAME_INFORMATION, *PFILE_NAME_INFORMATION;

typedef struct _FILE_END_OF_FILE_INFORMATION {
  LARGE_INTEGER  EndOfFile;
} FILE_END_OF_FILE_INFORMATION, *PFILE_END_OF_FILE_INFORMATION;

typedef struct _FILE_ALL_INFORMATION {
  FILE_BASIC_INFORMATION     BasicInformation;
  FILE_STANDARD_INFORMATION  StandardInformation;
  FILE_INTERNAL_INFORMATION  InternalInformation;
  FILE_EA_INFORMATION        EaInformation;
  FILE_ACCESS_INFORMATION    AccessInformation;
  FILE_POSITION_INFORMATION  PositionInformation;
  FILE_MODE_INFORMATION      ModeInformation;
  FILE_ALIGNMENT_INFORMATION AlignmentInformation;
  FILE_NAME_INFORMATION      NameInformation;
} FILE_ALL_INFORMATION, *PFILE_ALL_INFORMATION;

typedef struct _FILE_DISPOSITION_INFORMATION {
  BOOLEAN DeleteFile;
} FILE_DISPOSITION_INFORMATION, *PFILE_DISPOSITION_INFORMATION;

typedef struct _FILE_DISPOSITION_INFORMATION_EX {
  DWORD Flags;
} FILE_DISPOSITION_INFORMATION_EX, *PFILE_DISPOSITION_INFORMATION_EX;

typedef struct _FILE_PIPE_LOCAL_INFORMATION {
  ULONG NamedPipeType;
  ULONG NamedPipeConfiguration;
  ULONG MaximumInstances;
  ULONG CurrentInstances;
  ULONG InboundQuota;
  ULONG ReadDataAvailable;
  ULONG OutboundQuota;
  ULONG WriteQuotaAvailable;
  ULONG NamedPipeState;
  ULONG NamedPipeEnd;
} FILE_PIPE_LOCAL_INFORMATION, *PFILE_PIPE_LOCAL_INFORMATION;

#define FILE_SYNCHRONOUS_IO_ALERT               0x00000010
#define FILE_SYNCHRONOUS_IO_NONALERT            0x00000020

typedef enum _FS_INFORMATION_CLASS {
  FileFsVolumeInformation       = 1,
  FileFsLabelInformation        = 2,
  FileFsSizeInformation         = 3,
  FileFsDeviceInformation       = 4,
  FileFsAttributeInformation    = 5,
  FileFsControlInformation      = 6,
  FileFsFullSizeInformation     = 7,
  FileFsObjectIdInformation     = 8,
  FileFsDriverPathInformation   = 9,
  FileFsVolumeFlagsInformation  = 10,
  FileFsSectorSizeInformation   = 11
} FS_INFORMATION_CLASS, *PFS_INFORMATION_CLASS;

typedef struct _FILE_FS_VOLUME_INFORMATION {
  LARGE_INTEGER VolumeCreationTime;
  ULONG         VolumeSerialNumber;
  ULONG         VolumeLabelLength;
  BOOLEAN       SupportsObjects;
  WCHAR         VolumeLabel[1];
} FILE_FS_VOLUME_INFORMATION, *PFILE_FS_VOLUME_INFORMATION;

typedef struct _FILE_FS_LABEL_INFORMATION {
  ULONG VolumeLabelLength;
  WCHAR VolumeLabel[1];
} FILE_FS_LABEL_INFORMATION, *PFILE_FS_LABEL_INFORMATION;

typedef struct _FILE_FS_SIZE_INFORMATION {
  LARGE_INTEGER TotalAllocationUnits;
  LARGE_INTEGER AvailableAllocationUnits;
  ULONG         SectorsPerAllocationUnit;
  ULONG         BytesPerSector;
} FILE_FS_SIZE_INFORMATION, *PFILE_FS_SIZE_INFORMATION;

typedef struct _FILE_FS_DEVICE_INFORMATION {
  DEVICE_TYPE DeviceType;
  ULONG       Characteristics;
} FILE_FS_DEVICE_INFORMATION, *PFILE_FS_DEVICE_INFORMATION;

typedef struct _FILE_FS_ATTRIBUTE_INFORMATION {
  ULONG FileSystemAttributes;
  LONG  MaximumComponentNameLength;
  ULONG FileSystemNameLength;
  WCHAR FileSystemName[1];
} FILE_FS_ATTRIBUTE_INFORMATION, *PFILE_FS_ATTRIBUTE_INFORMATION;

typedef struct _FILE_FS_CONTROL_INFORMATION {
  LARGE_INTEGER FreeSpaceStartFiltering;
  LARGE_INTEGER FreeSpaceThreshold;
  LARGE_INTEGER FreeSpaceStopFiltering;
  LARGE_INTEGER DefaultQuotaThreshold;
  LARGE_INTEGER DefaultQuotaLimit;
  ULONG         FileSystemControlFlags;
} FILE_FS_CONTROL_INFORMATION, *PFILE_FS_CONTROL_INFORMATION;

typedef struct _FILE_FS_FULL_SIZE_INFORMATION {
  LARGE_INTEGER TotalAllocationUnits;
  LARGE_INTEGER CallerAvailableAllocationUnits;
  LARGE_INTEGER ActualAvailableAllocationUnits;
  ULONG         SectorsPerAllocationUnit;
  ULONG         BytesPerSector;
} FILE_FS_FULL_SIZE_INFORMATION, *PFILE_FS_FULL_SIZE_INFORMATION;

typedef struct _FILE_FS_OBJECTID_INFORMATION {
  UCHAR ObjectId[16];
  UCHAR ExtendedInfo[48];
} FILE_FS_OBJECTID_INFORMATION, *PFILE_FS_OBJECTID_INFORMATION;

typedef struct _FILE_FS_DRIVER_PATH_INFORMATION {
  BOOLEAN DriverInPath;
  ULONG   DriverNameLength;
  WCHAR   DriverName[1];
} FILE_FS_DRIVER_PATH_INFORMATION, *PFILE_FS_DRIVER_PATH_INFORMATION;

typedef struct _FILE_FS_VOLUME_FLAGS_INFORMATION {
  ULONG Flags;
} FILE_FS_VOLUME_FLAGS_INFORMATION, *PFILE_FS_VOLUME_FLAGS_INFORMATION;

typedef struct _FILE_FS_SECTOR_SIZE_INFORMATION {
  ULONG LogicalBytesPerSector;
  ULONG PhysicalBytesPerSectorForAtomicity;
  ULONG PhysicalBytesPerSectorForPerformance;
  ULONG FileSystemEffectivePhysicalBytesPerSectorForAtomicity;
  ULONG Flags;
  ULONG ByteOffsetForSectorAlignment;
  ULONG ByteOffsetForPartitionAlignment;
} FILE_FS_SECTOR_SIZE_INFORMATION, *PFILE_FS_SECTOR_SIZE_INFORMATION;

typedef struct _PROCESS_BASIC_INFORMATION {
  PVOID Reserved1;
  PVOID PebBaseAddress;
  PVOID Reserved2[2];
  ULONG_PTR UniqueProcessId;
  ULONG_PTR InheritedFromUniqueProcessId;
} PROCESS_BASIC_INFORMATION, *PPROCESS_BASIC_INFORMATION;

typedef struct _SYSTEM_PROCESSOR_PERFORMANCE_INFORMATION {
    LARGE_INTEGER IdleTime;
    LARGE_INTEGER KernelTime;
    LARGE_INTEGER UserTime;
    LARGE_INTEGER DpcTime;
    LARGE_INTEGER InterruptTime;
    ULONG InterruptCount;
} SYSTEM_PROCESSOR_PERFORMANCE_INFORMATION, *PSYSTEM_PROCESSOR_PERFORMANCE_INFORMATION;

#ifndef SystemProcessorPerformanceInformation
# define SystemProcessorPerformanceInformation 8
#endif

#ifndef ProcessBasicInformation
# define ProcessBasicInformation 0
#endif

#ifndef ProcessConsoleHostProcess
# define ProcessConsoleHostProcess 49
#endif

#ifndef FILE_DEVICE_FILE_SYSTEM
# define FILE_DEVICE_FILE_SYSTEM 0x00000009
#endif

#ifndef FILE_DEVICE_NETWORK
# define FILE_DEVICE_NETWORK 0x00000012
#endif

#ifndef METHOD_BUFFERED
# define METHOD_BUFFERED 0
#endif

#ifndef METHOD_IN_DIRECT
# define METHOD_IN_DIRECT 1
#endif

#ifndef METHOD_OUT_DIRECT
# define METHOD_OUT_DIRECT 2
#endif

#ifndef METHOD_NEITHER
#define METHOD_NEITHER 3
#endif

#ifndef METHOD_DIRECT_TO_HARDWARE
# define METHOD_DIRECT_TO_HARDWARE METHOD_IN_DIRECT
#endif

#ifndef METHOD_DIRECT_FROM_HARDWARE
# define METHOD_DIRECT_FROM_HARDWARE METHOD_OUT_DIRECT
#endif

#ifndef FILE_ANY_ACCESS
# define FILE_ANY_ACCESS 0
#endif

#ifndef FILE_SPECIAL_ACCESS
# define FILE_SPECIAL_ACCESS (FILE_ANY_ACCESS)
#endif

#ifndef FILE_READ_ACCESS
# define FILE_READ_ACCESS 0x0001
#endif

#ifndef FILE_WRITE_ACCESS
# define FILE_WRITE_ACCESS 0x0002
#endif

#ifndef CTL_CODE
# define CTL_CODE(device_type, function, method, access)                      \
    (((device_type) << 16) | ((access) << 14) | ((function) << 2) | (method))
#endif

#ifndef FSCTL_SET_REPARSE_POINT
# define FSCTL_SET_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,            \
                                          41,                                 \
                                          METHOD_BUFFERED,                    \
                                          FILE_SPECIAL_ACCESS)
#endif

#ifndef FSCTL_GET_REPARSE_POINT
# define FSCTL_GET_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,            \
                                          42,                                 \
                                          METHOD_BUFFERED,                    \
                                          FILE_ANY_ACCESS)
#endif

#ifndef FSCTL_DELETE_REPARSE_POINT
# define FSCTL_DELETE_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,         \
                                             43,                              \
                                             METHOD_BUFFERED,                 \
                                             FILE_SPECIAL_ACCESS)
#endif

#ifndef IO_REPARSE_TAG_SYMLINK
# define IO_REPARSE_TAG_SYMLINK (0xA000000CL)
#endif
#ifndef IO_REPARSE_TAG_APPEXECLINK
# define IO_REPARSE_TAG_APPEXECLINK (0x8000001BL)
#endif

typedef VOID (NTAPI *PIO_APC_ROUTINE)
             (PVOID ApcContext,
              PIO_STATUS_BLOCK IoStatusBlock,
              ULONG Reserved);

typedef NTSTATUS (NTAPI *sRtlGetVersion)
                 (PRTL_OSVERSIONINFOW lpVersionInformation);

typedef ULONG (NTAPI *sRtlNtStatusToDosError)
              (NTSTATUS Status);

typedef NTSTATUS (NTAPI *sNtDeviceIoControlFile)
                 (HANDLE FileHandle,
                  HANDLE Event,
                  PIO_APC_ROUTINE ApcRoutine,
                  PVOID ApcContext,
                  PIO_STATUS_BLOCK IoStatusBlock,
                  ULONG IoControlCode,
                  PVOID InputBuffer,
                  ULONG InputBufferLength,
                  PVOID OutputBuffer,
                  ULONG OutputBufferLength);

typedef NTSTATUS (NTAPI *sNtQueryInformationFile)
                 (HANDLE FileHandle,
                  PIO_STATUS_BLOCK IoStatusBlock,
                  PVOID FileInformation,
                  ULONG Length,
                  FILE_INFORMATION_CLASS FileInformationClass);

typedef NTSTATUS (NTAPI *sNtSetInformationFile)
                 (HANDLE FileHandle,
                  PIO_STATUS_BLOCK IoStatusBlock,
                  PVOID FileInformation,
                  ULONG Length,
                  FILE_INFORMATION_CLASS FileInformationClass);

typedef NTSTATUS (NTAPI *sNtQueryVolumeInformationFile)
                 (HANDLE FileHandle,
                  PIO_STATUS_BLOCK IoStatusBlock,
                  PVOID FsInformation,
                  ULONG Length,
                  FS_INFORMATION_CLASS FsInformationClass);

typedef NTSTATUS (NTAPI *sNtQuerySystemInformation)
                 (UINT SystemInformationClass,
                  PVOID SystemInformation,
                  ULONG SystemInformationLength,
                  PULONG ReturnLength);

typedef NTSTATUS (NTAPI *sNtQueryDirectoryFile)
                 (HANDLE FileHandle,
                  HANDLE Event,
                  PIO_APC_ROUTINE ApcRoutine,
                  PVOID ApcContext,
                  PIO_STATUS_BLOCK IoStatusBlock,
                  PVOID FileInformation,
                  ULONG Length,
                  FILE_INFORMATION_CLASS FileInformationClass,
                  BOOLEAN ReturnSingleEntry,
                  PUNICODE_STRING FileName,
                  BOOLEAN RestartScan
                );

typedef NTSTATUS (NTAPI *sNtQueryInformationProcess)
                 (HANDLE ProcessHandle,
                  UINT ProcessInformationClass,
                  PVOID ProcessInformation,
                  ULONG Length,
                  PULONG ReturnLength);

/*
 * Kernel32 headers
 */
#ifndef FILE_SKIP_COMPLETION_PORT_ON_SUCCESS
# define FILE_SKIP_COMPLETION_PORT_ON_SUCCESS 0x1
#endif

#ifndef FILE_SKIP_SET_EVENT_ON_HANDLE
# define FILE_SKIP_SET_EVENT_ON_HANDLE 0x2
#endif

#ifndef SYMBOLIC_LINK_FLAG_DIRECTORY
# define SYMBOLIC_LINK_FLAG_DIRECTORY 0x1
#endif

/* from wincon.h */
#ifndef ENABLE_INSERT_MODE
# define ENABLE_INSERT_MODE 0x20
#endif

#ifndef ENABLE_QUICK_EDIT_MODE
# define ENABLE_QUICK_EDIT_MODE 0x40
#endif

#ifndef ENABLE_EXTENDED_FLAGS
# define ENABLE_EXTENDED_FLAGS 0x80
#endif

/* from winerror.h */
#ifndef ERROR_ELEVATION_REQUIRED
# define ERROR_ELEVATION_REQUIRED 740
#endif

#ifndef ERROR_SYMLINK_NOT_SUPPORTED
# define ERROR_SYMLINK_NOT_SUPPORTED 1464
#endif

#ifndef ERROR_MUI_FILE_NOT_FOUND
# define ERROR_MUI_FILE_NOT_FOUND 15100
#endif

#ifndef ERROR_MUI_INVALID_FILE
# define ERROR_MUI_INVALID_FILE 15101
#endif

#ifndef ERROR_MUI_INVALID_RC_CONFIG
# define ERROR_MUI_INVALID_RC_CONFIG 15102
#endif

#ifndef ERROR_MUI_INVALID_LOCALE_NAME
# define ERROR_MUI_INVALID_LOCALE_NAME 15103
#endif

#ifndef ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME
# define ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME 15104
#endif

#ifndef ERROR_MUI_FILE_NOT_LOADED
# define ERROR_MUI_FILE_NOT_LOADED 15105
#endif

/* from powerbase.h */
#ifndef DEVICE_NOTIFY_CALLBACK
# define DEVICE_NOTIFY_CALLBACK 2
#endif

#ifndef PBT_APMRESUMEAUTOMATIC
# define PBT_APMRESUMEAUTOMATIC 18
#endif

#ifndef PBT_APMRESUMESUSPEND
# define PBT_APMRESUMESUSPEND 7
#endif

typedef ULONG CALLBACK _DEVICE_NOTIFY_CALLBACK_ROUTINE(
  PVOID Context,
  ULONG Type,
  PVOID Setting
);
typedef _DEVICE_NOTIFY_CALLBACK_ROUTINE* _PDEVICE_NOTIFY_CALLBACK_ROUTINE;

typedef struct _DEVICE_NOTIFY_SUBSCRIBE_PARAMETERS {
  _PDEVICE_NOTIFY_CALLBACK_ROUTINE Callback;
  PVOID Context;
} _DEVICE_NOTIFY_SUBSCRIBE_PARAMETERS, *_PDEVICE_NOTIFY_SUBSCRIBE_PARAMETERS;

typedef PVOID _HPOWERNOTIFY;
typedef _HPOWERNOTIFY *_PHPOWERNOTIFY;

typedef DWORD (WINAPI *sPowerRegisterSuspendResumeNotification)
              (DWORD         Flags,
               HANDLE        Recipient,
               _PHPOWERNOTIFY RegistrationHandle);

typedef BOOL (WINAPI *sProcessPrng)(/*_Out_*/PBYTE pbData, SIZE_T cbData);

/* from Winuser.h */
typedef VOID (CALLBACK* WINEVENTPROC)
             (HWINEVENTHOOK hWinEventHook,
              DWORD         event,
              HWND          hwnd,
              LONG          idObject,
              LONG          idChild,
              DWORD         idEventThread,
              DWORD         dwmsEventTime);

typedef HWINEVENTHOOK (WINAPI *sSetWinEventHook)
                      (UINT         eventMin,
                       UINT         eventMax,
                       HMODULE      hmodWinEventProc,
                       WINEVENTPROC lpfnWinEventProc,
                       DWORD        idProcess,
                       DWORD        idThread,
                       UINT         dwflags);

/* From mstcpip.h */
typedef struct _TCP_INITIAL_RTO_PARAMETERS {
  USHORT Rtt;
  UCHAR  MaxSynRetransmissions;
} TCP_INITIAL_RTO_PARAMETERS, *PTCP_INITIAL_RTO_PARAMETERS;

#ifndef TCP_INITIAL_RTO_NO_SYN_RETRANSMISSIONS
# define TCP_INITIAL_RTO_NO_SYN_RETRANSMISSIONS ((UCHAR) -2)
#endif
#ifndef SIO_TCP_INITIAL_RTO
# define  SIO_TCP_INITIAL_RTO _WSAIOW(IOC_VENDOR,17)
#endif

/* from winnt.h */
/* API is defined in newer SDKS */
#if (NTDDI_VERSION < NTDDI_WIN11_ZN)
typedef enum _FILE_INFO_BY_NAME_CLASS {
  FileStatByNameInfo,
  FileStatLxByNameInfo,
  FileCaseSensitiveByNameInfo,
  FileStatBasicByNameInfo,
  MaximumFileInfoByNameClass
} FILE_INFO_BY_NAME_CLASS;
#endif

typedef BOOL(WINAPI* sGetFileInformationByName)(
    PCWSTR FileName,
    FILE_INFO_BY_NAME_CLASS FileInformationClass,
    PVOID FileInfoBuffer,
    ULONG FileInfoBufferSize);

/* Ntdll function pointers */
extern sRtlGetVersion pRtlGetVersion;
extern sRtlNtStatusToDosError pRtlNtStatusToDosError;
extern sNtDeviceIoControlFile pNtDeviceIoControlFile;
extern sNtQueryInformationFile pNtQueryInformationFile;
extern sNtSetInformationFile pNtSetInformationFile;
extern sNtQueryVolumeInformationFile pNtQueryVolumeInformationFile;
extern sNtQueryDirectoryFile pNtQueryDirectoryFile;
extern sNtQuerySystemInformation pNtQuerySystemInformation;
extern sNtQueryInformationProcess pNtQueryInformationProcess;

/* Powrprof.dll function pointer */
extern sPowerRegisterSuspendResumeNotification pPowerRegisterSuspendResumeNotification;

/* bcryptprimitives.dll function pointer */
extern sProcessPrng pProcessPrng;

/* User32.dll function pointer */
extern sSetWinEventHook pSetWinEventHook;

/* api-ms-win-core-file-l2-1-4.dll function pointers */
extern sGetFileInformationByName pGetFileInformationByName;

/* ws2_32.dll function pointer */
/* mingw doesn't have this definition, so let's declare it here locally */
typedef int (WINAPI *uv_sGetHostNameW)
            (PWSTR,
             int);
extern uv_sGetHostNameW pGetHostNameW;

#endif /* UV_WIN_WINAPI_H_ */
