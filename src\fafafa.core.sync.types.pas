unit fafafa.core.sync.types;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base;

type
  // 同步原语异常类型
  ESyncPrimitive = class(ECore);
  EMutexError = class(ESyncPrimitive);
  EEventError = class(ESyncPrimitive);
  ESemaphoreError = class(ESyncPrimitive);
  ERWLockError = class(ESyncPrimitive);
  EConditionError = class(ESyncPrimitive);
  EAtomicError = class(ESyncPrimitive);

  // 等待结果枚举
  TWaitResult = (
    wrSignaled,    // 信号状态
    wrTimeout,     // 超时
    wrAbandoned,   // 被放弃
    wrError        // 错误
  );

  // 锁接口
  ILock = interface(IInterface)
  ['{SYNC-LOCK-INTERFACE-GUID}']
    procedure Acquire;
    procedure Release;
    function TryAcquire(aTimeoutMs: Cardinal = 0): Boolean;
  end;

  // 读写锁接口
  IRWLock = interface(IInterface)
  ['{SYNC-RWLOCK-INTERFACE-GUID}']
    procedure AcquireRead;
    procedure ReleaseRead;
    function TryAcquireRead(aTimeoutMs: Cardinal = 0): Boolean;
    
    procedure AcquireWrite;
    procedure ReleaseWrite;
    function TryAcquireWrite(aTimeoutMs: Cardinal = 0): Boolean;
  end;

  // 事件接口
  IEvent = interface(IInterface)
  ['{SYNC-EVENT-INTERFACE-GUID}']
    procedure SetEvent;
    procedure ResetEvent;
    function WaitFor(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
  end;

  // 信号量接口
  ISemaphore = interface(IInterface)
  ['{SYNC-SEMAPHORE-INTERFACE-GUID}']
    function Acquire(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
    function Release(aReleaseCount: Integer = 1): Integer;
    function GetCount: Integer;
  end;

  // 条件变量接口
  ICondition = interface(IInterface)
  ['{SYNC-CONDITION-INTERFACE-GUID}']
    procedure Wait(const aLock: ILock; aTimeoutMs: Cardinal = INFINITE);
    procedure Signal;
    procedure Broadcast;
  end;

  // 线程安全队列接口
  generic IThreadSafeQueue<T> = interface(IInterface)
  ['{SYNC-THREADSAFE-QUEUE-INTERFACE-GUID}']
    procedure Enqueue(const aItem: T);
    function TryEnqueue(const aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function Dequeue: T;
    function TryDequeue(out aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function IsEmpty: Boolean;
    function Count: Integer;
    procedure Shutdown;
  end;

  // RAII自动锁
  TAutoLock = record
  private
    FLock: ILock;
    FLocked: Boolean;
  public
    constructor Create(const aLock: ILock);
    destructor Destroy;
    
    procedure Unlock;
    class operator Initialize(var aAutoLock: TAutoLock);
    class operator Finalize(var aAutoLock: TAutoLock);
  end;

  // RAII自动读锁
  TAutoReadLock = record
  private
    FRWLock: IRWLock;
    FLocked: Boolean;
  public
    constructor Create(const aRWLock: IRWLock);
    destructor Destroy;
    
    procedure Unlock;
    class operator Initialize(var aAutoLock: TAutoReadLock);
    class operator Finalize(var aAutoLock: TAutoReadLock);
  end;

  // RAII自动写锁
  TAutoWriteLock = record
  private
    FRWLock: IRWLock;
    FLocked: Boolean;
  public
    constructor Create(const aRWLock: IRWLock);
    destructor Destroy;
    
    procedure Unlock;
    class operator Initialize(var aAutoLock: TAutoWriteLock);
    class operator Finalize(var aAutoLock: TAutoWriteLock);
  end;

  // 原子操作结果
  TAtomicResult = record
    Success: Boolean;
    OldValue: Pointer;
    NewValue: Pointer;
  end;

  // 内存序枚举（为将来扩展预留）
  TMemoryOrder = (
    moRelaxed,    // 宽松序
    moAcquire,    // 获取序
    moRelease,    // 释放序
    moAcqRel,     // 获取-释放序
    moSeqCst      // 顺序一致性序
  );

implementation

// TAutoLock 实现

constructor TAutoLock.Create(const aLock: ILock);
begin
  FLock := aLock;
  FLocked := False;
  if Assigned(FLock) then
  begin
    FLock.Acquire;
    FLocked := True;
  end;
end;

destructor TAutoLock.Destroy;
begin
  Unlock;
end;

procedure TAutoLock.Unlock;
begin
  if FLocked and Assigned(FLock) then
  begin
    FLock.Release;
    FLocked := False;
  end;
end;

class operator TAutoLock.Initialize(var aAutoLock: TAutoLock);
begin
  aAutoLock.FLock := nil;
  aAutoLock.FLocked := False;
end;

class operator TAutoLock.Finalize(var aAutoLock: TAutoLock);
begin
  aAutoLock.Unlock;
  aAutoLock.FLock := nil;
end;

// TAutoReadLock 实现

constructor TAutoReadLock.Create(const aRWLock: IRWLock);
begin
  FRWLock := aRWLock;
  FLocked := False;
  if Assigned(FRWLock) then
  begin
    FRWLock.AcquireRead;
    FLocked := True;
  end;
end;

destructor TAutoReadLock.Destroy;
begin
  Unlock;
end;

procedure TAutoReadLock.Unlock;
begin
  if FLocked and Assigned(FRWLock) then
  begin
    FRWLock.ReleaseRead;
    FLocked := False;
  end;
end;

class operator TAutoReadLock.Initialize(var aAutoLock: TAutoReadLock);
begin
  aAutoLock.FRWLock := nil;
  aAutoLock.FLocked := False;
end;

class operator TAutoReadLock.Finalize(var aAutoLock: TAutoReadLock);
begin
  aAutoLock.Unlock;
  aAutoLock.FRWLock := nil;
end;

// TAutoWriteLock 实现

constructor TAutoWriteLock.Create(const aRWLock: IRWLock);
begin
  FRWLock := aRWLock;
  FLocked := False;
  if Assigned(FRWLock) then
  begin
    FRWLock.AcquireWrite;
    FLocked := True;
  end;
end;

destructor TAutoWriteLock.Destroy;
begin
  Unlock;
end;

procedure TAutoWriteLock.Unlock;
begin
  if FLocked and Assigned(FRWLock) then
  begin
    FRWLock.ReleaseWrite;
    FLocked := False;
  end;
end;

class operator TAutoWriteLock.Initialize(var aAutoLock: TAutoWriteLock);
begin
  aAutoLock.FRWLock := nil;
  aAutoLock.FLocked := False;
end;

class operator TAutoWriteLock.Finalize(var aAutoLock: TAutoWriteLock);
begin
  aAutoLock.Unlock;
  aAutoLock.FRWLock := nil;
end;

end.
