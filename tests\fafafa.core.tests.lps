<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <PathDelim Value="\"/>
    <Version Value="12"/>
    <BuildModes Active="Debug"/>
    <Units>
      <Unit>
        <Filename Value="fafafa.core.tests.lpr"/>
        <IsPartOfProject Value="True"/>
        <CursorPos X="41" Y="7"/>
        <UsageCount Value="22"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="test_math.pas"/>
        <IsPartOfProject Value="True"/>
        <UsageCount Value="22"/>
      </Unit>
      <Unit>
        <Filename Value="test_base.pas"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="-1"/>
        <CursorPos X="18" Y="21"/>
        <UsageCount Value="22"/>
      </Unit>
      <Unit>
        <Filename Value="test_mem_utils.pas"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="3"/>
        <TopLine Value="58"/>
        <CursorPos X="26" Y="77"/>
        <UsageCount Value="22"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.mem.utils.pas"/>
        <EditorIndex Value="1"/>
        <TopLine Value="789"/>
        <CursorPos Y="825"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.base.pas"/>
        <EditorIndex Value="2"/>
        <TopLine Value="48"/>
        <CursorPos X="20" Y="109"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="test_collections.arr.pas"/>
        <EditorIndex Value="4"/>
        <TopLine Value="247"/>
        <CursorPos Y="267"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.mem.allocator.pas"/>
        <EditorIndex Value="7"/>
        <TopLine Value="306"/>
        <CursorPos Y="355"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <EditorIndex Value="5"/>
        <TopLine Value="3631"/>
        <CursorPos X="19" Y="3661"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <IsVisibleTab Value="True"/>
        <EditorIndex Value="6"/>
        <TopLine Value="2464"/>
        <CursorPos X="36" Y="2471"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.core.collections.elementManager.pas"/>
        <EditorIndex Value="8"/>
        <TopLine Value="860"/>
        <CursorPos Y="896"/>
        <UsageCount Value="11"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="fafafa.core.tests.lps"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="24"/>
        <CursorPos X="93" Y="224"/>
        <UsageCount Value="10"/>
      </Unit>
    </Units>
    <JumpHistory HistoryIndex="29">
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1201" Column="17" TopLine="1165"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2621" Column="3" TopLine="2618"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1200" Column="17" TopLine="1166"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2616" Column="42" TopLine="2613"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1202" Column="17" TopLine="1165"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="2809" Column="34" TopLine="2774"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3640" Column="3" TopLine="3637"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1199" Column="17" TopLine="1164"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2469" Column="3" TopLine="2464"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="567" Column="17" TopLine="531"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1823" Column="3" TopLine="1821"/>
      </Position>
      <Position>
        <Filename Value="test_collections.arr.pas"/>
        <Caret Line="267" TopLine="247"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3668" TopLine="3637"/>
      </Position>
      <Position>
        <Filename Value="test_collections.arr.pas"/>
        <Caret Line="267" TopLine="247"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2615" TopLine="2579"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2616" TopLine="2579"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.mem.allocator.pas"/>
        <Caret Line="354" TopLine="306"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.mem.allocator.pas"/>
        <Caret Line="355" TopLine="306"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2616" TopLine="2579"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2625" TopLine="2579"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="2626" TopLine="2579"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3639" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3640" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3654" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3655" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3656" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3657" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3659" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.arr.pas"/>
        <Caret Line="3661" Column="19" TopLine="3631"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.core.collections.base.pas"/>
        <Caret Line="1199" Column="17" TopLine="1164"/>
      </Position>
    </JumpHistory>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
  <Debugging>
    <BreakPoints>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="test_collections.arr.pas"/>
        <Line Value="267"/>
      </Item>
    </BreakPoints>
  </Debugging>
</CONFIG>
