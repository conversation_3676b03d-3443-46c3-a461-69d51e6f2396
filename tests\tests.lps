<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <PathDelim Value="\"/>
    <Version Value="12"/>
    <BuildModes Active="Debug"/>
    <Units>
      <Unit>
        <Filename Value="tests.lpr"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="3"/>
        <CursorPos Y="31"/>
        <UsageCount Value="284"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_array.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_Array"/>
        <EditorIndex Value="2"/>
        <TopLine Value="4194"/>
        <CursorPos X="41" Y="4216"/>
        <UsageCount Value="274"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_vec.pas"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="5"/>
        <TopLine Value="885"/>
        <CursorPos Y="927"/>
        <UsageCount Value="274"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_memallocator.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_memAllocator"/>
        <EditorIndex Value="1"/>
        <TopLine Value="176"/>
        <CursorPos X="6" Y="192"/>
        <UsageCount Value="250"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_elementallocator.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_elementAllocator"/>
        <IsVisibleTab Value="True"/>
        <WindowIndex Value="1"/>
        <TopLine Value="541"/>
        <CursorPos X="35" Y="537"/>
        <UsageCount Value="250"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <IsVisibleTab Value="True"/>
        <TopLine Value="6937"/>
        <CursorPos X="102" Y="6962"/>
        <UsageCount Value="142"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="..\src\fafafa.collections.settings.inc"/>
        <EditorIndex Value="4"/>
        <CursorPos Y="11"/>
        <UsageCount Value="107"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="D:\devtools\lazarus\nextgen\fpcsrc\rtl\inc\rttih.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="16"/>
        <CursorPos X="11" Y="83"/>
        <UsageCount Value="69"/>
      </Unit>
      <Unit>
        <Filename Value="D:\devtools\lazarus\nextgen\config_lazarus\onlinepackagemanager\packages\mORMot\SQLite3\Samples\ThirdPartyDemos\George\REST-tester\generics-collections\src\generics.collections.pas"/>
        <UnitName Value="Generics.Collections"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="202"/>
        <CursorPos X="26" Y="226"/>
        <UsageCount Value="40"/>
      </Unit>
      <Unit>
        <Filename Value="D:\devtools\lazarus\trunk\fpc\bin\x86_64-win64\fpc.cfg"/>
        <EditorIndex Value="1"/>
        <WindowIndex Value="1"/>
        <TopLine Value="248"/>
        <CursorPos X="26" Y="298"/>
        <UsageCount Value="72"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="D:\devtools\lazarus\trunk\fpcsrc\rtl\inc\heaph.inc"/>
        <EditorIndex Value="-1"/>
        <TopLine Value="46"/>
        <CursorPos X="10" Y="100"/>
        <UsageCount Value="8"/>
      </Unit>
    </Units>
    <JumpHistory HistoryIndex="29">
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7095" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7098" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7103" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7104" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6937" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6939" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6946" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6926" Column="63" TopLine="6907"/>
      </Position>
      <Position>
        <Filename Value="testcase_elementallocator.pas"/>
        <Caret Line="536" TopLine="512"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="1310" TopLine="1277"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7085" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7086" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7089" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7092" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7095" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7098" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7102" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7103" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7079" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7080" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7081" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7082" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="7104" TopLine="7054"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6937" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6939" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6946" TopLine="6906"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6947" TopLine="6921"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6948" TopLine="6921"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6949" TopLine="6921"/>
      </Position>
      <Position>
        <Filename Value="..\src\fafafa.collections.pas"/>
        <Caret Line="6951" TopLine="6921"/>
      </Position>
    </JumpHistory>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
  <Debugging>
    <BreakPoints>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_iarray.pas"/>
        <Line Value="546"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_vec.pas"/>
        <Line Value="25"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_vec.pas"/>
        <Line Value="6603"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_vec.pas"/>
        <Line Value="927"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="..\src\fafafa.collections.pas"/>
        <Line Value="11585"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_array.pas"/>
        <Line Value="8887"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_array.pas"/>
        <Line Value="4226"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_memallocator.pas"/>
        <Line Value="238"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_memallocator.pas"/>
        <Line Value="249"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_memallocator.pas"/>
        <Line Value="141"/>
      </Item>
      <Item>
        <Kind Value="bpkSource"/>
        <WatchScope Value="wpsLocal"/>
        <WatchKind Value="wpkWrite"/>
        <Source Value="testcase_elementallocator.pas"/>
        <Line Value="741"/>
      </Item>
    </BreakPoints>
    <Watches>
      <Item>
        <Expression Value="PCustomCompareData(@aLeft)"/>
      </Item>
      <Item>
        <Expression Value="PCustomCompareData(@aRight)"/>
      </Item>
      <Item>
        <Expression Value="High(UInt64)"/>
      </Item>
      <Item>
        <Expression Value="LPString[0]"/>
        <DisplayFormat ArrayLenPrefixMaxNest="99"/>
      </Item>
      <Item>
        <Expression Value="LPString[1023]"/>
      </Item>
      <Item>
        <Expression Value="LPString[1022]"/>
      </Item>
      <Item>
        <Expression Value="LPString[1021]"/>
      </Item>
      <Item>
        <Expression Value="LPS[1023]"/>
      </Item>
      <Item>
        <Expression Value="LPS[1022]"/>
      </Item>
      <Item>
        <Expression Value="(LPDstTail - 2)^"/>
      </Item>
      <Item>
        <Expression Value="aSrc^"/>
      </Item>
      <Item>
        <Expression Value="aDst^"/>
      </Item>
      <Item>
        <Expression Value="LPS[1020]"/>
      </Item>
      <Item>
        <Expression Value="lps[1021]"/>
      </Item>
    </Watches>
  </Debugging>
</CONFIG>
