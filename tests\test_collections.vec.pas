unit test_collections.vec;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  fafafa.core.base,
  fafafa.core.collections.base,
  fafafa.core.collections.arr,
  fafafa.core.collections.vec,
  fafafa.core.mem.allocator;

type

  { TTestCase_Vec - TVec向量容器测试 }
  TTestCase_Vec = class(TTestCase)
  published
    { ===== 构造函数测试 ===== }
    procedure Test_Create;
    procedure Test_Create_Capacity;
    procedure Test_Create_Allocator;

    { ===== IVec<T> 容量管理测试 ===== }
    procedure Test_GetCapacity;
    procedure Test_SetCapacity;
    procedure Test_Reserve;
    procedure Test_ShrinkToFit;

    { ===== IVec<T> 栈操作测试 ===== }
    procedure Test_Push;
    procedure Test_Pop;
    procedure Test_Peek;
    procedure Test_TryPop;
    procedure Test_TryPeek;

    { ===== IArray<T> 接口方法测试 ===== }
    procedure Test_Get;
    procedure Test_Put;
    procedure Test_Resize;
    procedure Test_Clear;

    { ===== 内存管理测试 ===== }
    procedure Test_Memory_Management;
    procedure Test_Growth_Strategy;
  end;

implementation

{ TTestCase_Vec }

procedure TTestCase_Vec.Test_Create;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    AssertEquals('Initial count should be 0', Int64(0), Int64(LVec.GetCount));
    AssertTrue('Should be empty', LVec.IsEmpty);
    AssertEquals('Initial capacity should be 0', Int64(0), Int64(LVec.GetCapacity));
    AssertNotNull('Should have allocator', LVec.GetAllocator);
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Create_Capacity;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create(10);
  try
    AssertEquals('Initial count should be 0', Int64(0), Int64(LVec.GetCount));
    AssertTrue('Should be empty', LVec.IsEmpty);
    AssertEquals('Initial capacity should be 10', Int64(10), Int64(LVec.GetCapacity));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Create_Allocator;
var
  LVec: specialize TVec<Integer>;
  LAllocator: TAllocator;
begin
  LAllocator := GetRtlAllocator;
  LVec := specialize TVec<Integer>.Create(LAllocator);
  try
    AssertEquals('Initial count should be 0', Int64(0), Int64(LVec.GetCount));
    AssertTrue('Should be empty', LVec.IsEmpty);
    AssertEquals('Initial capacity should be 0', Int64(0), Int64(LVec.GetCapacity));
    AssertSame('Should use provided allocator', LAllocator, LVec.GetAllocator);
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_GetCapacity;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    AssertEquals('Initial capacity should be 0', Int64(0), Int64(LVec.GetCapacity));

    LVec.SetCapacity(5);
    AssertEquals('Capacity should be 5', Int64(5), Int64(LVec.GetCapacity));

    LVec.SetCapacity(10);
    AssertEquals('Capacity should be 10', Int64(10), Int64(LVec.GetCapacity));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_SetCapacity;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.SetCapacity(20);
    AssertEquals('Capacity should be 20', Int64(20), Int64(LVec.GetCapacity));
    AssertEquals('Count should still be 0', Int64(0), Int64(LVec.GetCount));

    // 缩小容量
    LVec.SetCapacity(10);
    AssertEquals('Capacity should be 10', Int64(10), Int64(LVec.GetCapacity));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Reserve;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Reserve(5);
    AssertTrue('Capacity should be >= 5', LVec.GetCapacity >= 5);
    AssertEquals('Count should still be 0', Int64(0), Int64(LVec.GetCount));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_ShrinkToFit;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.SetCapacity(100);
    LVec.Push(42);
    LVec.Push(84);
    AssertEquals('Count should be 2', Int64(2), Int64(LVec.GetCount));
    AssertEquals('Capacity should be 100', Int64(100), Int64(LVec.GetCapacity));

    LVec.Shrink;
    AssertEquals('Count should still be 2', Int64(2), Int64(LVec.GetCount));
    AssertEquals('Capacity should be 2', Int64(2), Int64(LVec.GetCapacity));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Push;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(42);
    AssertEquals('Count should be 1', Int64(1), Int64(LVec.GetCount));
    AssertFalse('Should not be empty', LVec.IsEmpty);
    AssertEquals('Value should be 42', 42, LVec.Get(0));

    LVec.Push(84);
    AssertEquals('Count should be 2', Int64(2), Int64(LVec.GetCount));
    AssertEquals('First value should be 42', 42, LVec.Get(0));
    AssertEquals('Second value should be 84', 84, LVec.Get(1));

    LVec.Push(168);
    AssertEquals('Count should be 3', Int64(3), Int64(LVec.GetCount));
    AssertEquals('Third value should be 168', 168, LVec.Get(2));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Pop;
var
  LVec: specialize TVec<Integer>;
  LValue: Integer;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(42);
    LVec.Push(84);
    LVec.Push(168);
    AssertEquals('Count should be 3', Int64(3), Int64(LVec.GetCount));

    LValue := LVec.Pop;
    AssertEquals('Popped value should be 168', 168, LValue);
    AssertEquals('Count should be 2', Int64(2), Int64(LVec.GetCount));

    LValue := LVec.Pop;
    AssertEquals('Popped value should be 84', 84, LValue);
    AssertEquals('Count should be 1', Int64(1), Int64(LVec.GetCount));

    LValue := LVec.Pop;
    AssertEquals('Popped value should be 42', 42, LValue);
    AssertEquals('Count should be 0', Int64(0), Int64(LVec.GetCount));
    AssertTrue('Should be empty', LVec.IsEmpty);
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Peek;
var
  LVec: specialize TVec<Integer>;
  LValue: Integer;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(42);
    LVec.Push(84);
    LVec.Push(168);

    LValue := LVec.Peek;
    AssertEquals('Peeked value should be 168', 168, LValue);
    AssertEquals('Count should still be 3', Int64(3), Int64(LVec.GetCount));

    // Peek again to ensure it doesn't modify the vector
    LValue := LVec.Peek;
    AssertEquals('Peeked value should still be 168', 168, LValue);
    AssertEquals('Count should still be 3', Int64(3), Int64(LVec.GetCount));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_TryPop;
var
  LVec: specialize TVec<Integer>;
  LValue: Integer;
  LResult: Boolean;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    // Test TryPop on empty vector
    LResult := LVec.TryPop(LValue);
    AssertFalse('TryPop should fail on empty vector', LResult);

    // Add elements and test TryPop
    LVec.Push(42);
    LVec.Push(84);

    LResult := LVec.TryPop(LValue);
    AssertTrue('TryPop should succeed', LResult);
    AssertEquals('Popped value should be 84', 84, LValue);
    AssertEquals('Count should be 1', Int64(1), Int64(LVec.GetCount));

    LResult := LVec.TryPop(LValue);
    AssertTrue('TryPop should succeed', LResult);
    AssertEquals('Popped value should be 42', 42, LValue);
    AssertEquals('Count should be 0', Int64(0), Int64(LVec.GetCount));

    // Test TryPop on empty vector again
    LResult := LVec.TryPop(LValue);
    AssertFalse('TryPop should fail on empty vector', LResult);
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_TryPeek;
var
  LVec: specialize TVec<Integer>;
  LValue: Integer;
  LResult: Boolean;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    // Test TryPeek on empty vector
    LResult := LVec.TryPeek(LValue);
    AssertFalse('TryPeek should fail on empty vector', LResult);

    // Add elements and test TryPeek
    LVec.Push(42);
    LVec.Push(84);

    LResult := LVec.TryPeek(LValue);
    AssertTrue('TryPeek should succeed', LResult);
    AssertEquals('Peeked value should be 84', 84, LValue);
    AssertEquals('Count should still be 2', Int64(2), Int64(LVec.GetCount));

    // TryPeek again to ensure it doesn't modify the vector
    LResult := LVec.TryPeek(LValue);
    AssertTrue('TryPeek should succeed again', LResult);
    AssertEquals('Peeked value should still be 84', 84, LValue);
    AssertEquals('Count should still be 2', Int64(2), Int64(LVec.GetCount));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Get;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(100);
    LVec.Push(200);
    LVec.Push(300);

    AssertEquals('Get(0) should return 100', 100, LVec.Get(0));
    AssertEquals('Get(1) should return 200', 200, LVec.Get(1));
    AssertEquals('Get(2) should return 300', 300, LVec.Get(2));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Put;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(100);
    LVec.Push(200);
    LVec.Push(300);

    LVec.Put(1, 999);
    AssertEquals('Put should change value', 999, LVec.Get(1));
    AssertEquals('Other values should remain', 100, LVec.Get(0));
    AssertEquals('Other values should remain', 300, LVec.Get(2));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Resize;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Resize(3);
    AssertEquals('Count should be 3', Int64(3), Int64(LVec.GetCount));

    LVec.Put(0, 100);
    LVec.Put(1, 200);
    LVec.Put(2, 300);

    AssertEquals('Values should be set correctly', 100, LVec.Get(0));
    AssertEquals('Values should be set correctly', 200, LVec.Get(1));
    AssertEquals('Values should be set correctly', 300, LVec.Get(2));

    LVec.Resize(5);
    AssertEquals('Count should be 5', Int64(5), Int64(LVec.GetCount));
    AssertEquals('Existing values should remain', 100, LVec.Get(0));
    AssertEquals('Existing values should remain', 200, LVec.Get(1));
    AssertEquals('Existing values should remain', 300, LVec.Get(2));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Clear;
var
  LVec: specialize TVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.Push(1);
    LVec.Push(2);
    LVec.Push(3);
    AssertEquals('Count should be 3', Int64(3), Int64(LVec.GetCount));

    LVec.Clear;
    AssertEquals('Count should be 0 after clear', Int64(0), Int64(LVec.GetCount));
    AssertTrue('Should be empty after clear', LVec.IsEmpty);
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Memory_Management;
var
  LVec: specialize TVec<Integer>;
  LAllocator: TAllocator;
  i: Integer;
begin
  LAllocator := GetRtlAllocator;
  LVec := specialize TVec<Integer>.Create(LAllocator);
  try
    // Test multiple allocations and deallocations
    for i := 0 to 99 do
      LVec.Push(i);

    AssertEquals('Count should be 100', Int64(100), Int64(LVec.GetCount));

    // Clear and refill
    LVec.Clear;
    AssertEquals('Count should be 0', Int64(0), Int64(LVec.GetCount));

    for i := 0 to 49 do
      LVec.Push(i * 2);

    AssertEquals('Count should be 50', Int64(50), Int64(LVec.GetCount));

    // Verify values
    for i := 0 to 49 do
      AssertEquals('Value should match', i * 2, LVec.Get(i));
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_Vec.Test_Growth_Strategy;
var
  LVec: specialize TVec<Integer>;
  LInitialCapacity, LNewCapacity: SizeUInt;
  i: Integer;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    LVec.SetCapacity(2);
    LInitialCapacity := LVec.GetCapacity;
    AssertEquals('Initial capacity should be 2', Int64(2), Int64(LInitialCapacity));

    // Push elements to trigger growth
    for i := 0 to 4 do
      LVec.Push(i);

    LNewCapacity := LVec.GetCapacity;
    AssertTrue('Capacity should have grown', LNewCapacity > LInitialCapacity);
    AssertEquals('Count should be 5', Int64(5), Int64(LVec.GetCount));

    // Verify all values are correct
    for i := 0 to 4 do
      AssertEquals('Value should match', i, LVec.Get(i));
  finally
    LVec.Free;
  end;
end;

initialization
  RegisterTest(TTestCase_Vec);

end.
