<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <PathDelim Value="\"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="tests"/>
      <UseAppBundle Value="False"/>
      <ResourceType Value="res"/>
    </General>
    <BuildModes>
      <Item Name="Default" Default="True"/>
      <Item Name="Debug">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="..\bin\tests"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value="..\src"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <Parsing>
            <SyntaxOptions>
              <IncludeAssertionCode Value="True"/>
            </SyntaxOptions>
          </Parsing>
          <CodeGeneration>
            <Checks>
              <IOChecks Value="True"/>
              <RangeChecks Value="True"/>
              <OverflowChecks Value="True"/>
              <StackChecks Value="True"/>
            </Checks>
            <VerifyObjMethodCallValidity Value="True"/>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <DebugInfoType Value="dsDwarf2Set"/>
              <UseHeaptrc Value="True"/>
              <TrashVariables Value="True"/>
            </Debugging>
          </Linking>
        </CompilerOptions>
      </Item>
      <Item Name="Release">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="..\bin\tests"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value="..\src"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <CodeGeneration>
            <SmartLinkUnit Value="True"/>
            <Optimizations>
              <OptimizationLevel Value="3"/>
            </Optimizations>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <GenerateDebugInfo Value="False"/>
              <RunWithoutDebug Value="True"/>
              <StripSymbols Value="True"/>
            </Debugging>
            <LinkSmart Value="True"/>
          </Linking>
        </CompilerOptions>
      </Item>
      <Item Name="linux_debug">
        <CompilerOptions>
          <Version Value="11"/>
          <PathDelim Value="\"/>
          <Target>
            <Filename Value="..\bin\$(TargetCPU)-$(TargetOS)-tests"/>
          </Target>
          <SearchPaths>
            <IncludeFiles Value="$(ProjOutDir)"/>
            <OtherUnitFiles Value="..\src"/>
            <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
          </SearchPaths>
          <Parsing>
            <SyntaxOptions>
              <IncludeAssertionCode Value="True"/>
            </SyntaxOptions>
          </Parsing>
          <CodeGeneration>
            <Checks>
              <IOChecks Value="True"/>
              <RangeChecks Value="True"/>
              <OverflowChecks Value="True"/>
              <StackChecks Value="True"/>
            </Checks>
            <VerifyObjMethodCallValidity Value="True"/>
            <TargetCPU Value="x86_64"/>
            <TargetOS Value="linux"/>
          </CodeGeneration>
          <Linking>
            <Debugging>
              <DebugInfoType Value="dsDwarf2Set"/>
              <UseHeaptrc Value="True"/>
              <TrashVariables Value="True"/>
            </Debugging>
          </Linking>
        </CompilerOptions>
      </Item>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
      <UseFileFilters Value="True"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <RequiredPackages>
      <Item>
        <PackageName Value="FCL"/>
      </Item>
    </RequiredPackages>
    <Units>
      <Unit>
        <Filename Value="tests.lpr"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_array.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_Array"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_vec.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_memallocator.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_memAllocator"/>
      </Unit>
      <Unit>
        <Filename Value="testcase_elementallocator.pas"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="testcase_elementAllocator"/>
      </Unit>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="..\bin\tests"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="$(ProjOutDir)"/>
      <OtherUnitFiles Value="..\src"/>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Linking>
      <Debugging>
        <DebugInfoType Value="dsDwarf3"/>
        <UseExternalDbgSyms Value="True"/>
      </Debugging>
    </Linking>
  </CompilerOptions>
  <Debugging>
    <Exceptions>
      <Item>
        <Name Value="EAbort"/>
      </Item>
      <Item>
        <Name Value="ECodetoolError"/>
      </Item>
      <Item>
        <Name Value="EFOpenError"/>
      </Item>
      <Item>
        <Name Value="Exception"/>
      </Item>
    </Exceptions>
  </Debugging>
</CONFIG>
