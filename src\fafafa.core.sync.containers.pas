unit fafafa.core.sync.containers;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base,
  fafafa.core.sync.types,
  fafafa.core.sync.atomic;

type
  // 无锁队列节点
  generic PLockFreeQueueNode<T> = ^TLockFreeQueueNode;
  generic TLockFreeQueueNode<T> = record
    Data: T;
    Next: specialize PLockFreeQueueNode<T>;
  end;

  // 无锁队列实现（<PERSON> & <PERSON>算法）
  generic TLockFreeQueue<T> = record
  private
    type
      PNode = specialize PLockFreeQueueNode<T>;
      TNode = specialize TLockFreeQueueNode<T>;
  private
    FHead: TAtomicPointer;
    FTail: TAtomicPointer;
    FCount: TAtomicInt32;
    
    function AllocateNode: PNode;
    procedure FreeNode(aNode: PNode);
  public
    procedure Initialize;
    procedure Finalize;
    
    function TryEnqueue(const aItem: T): Boolean;
    function TryDequeue(out aItem: T): Boolean;
    function IsEmpty: Boolean; inline;
    function Count: Integer; inline;
    
    class operator Initialize(var aQueue: specialize TLockFreeQueue<T>);
    class operator Finalize(var aQueue: specialize TLockFreeQueue<T>);
  end;

  // 无锁栈节点
  generic PLockFreeStackNode<T> = ^TLockFreeStackNode;
  generic TLockFreeStackNode<T> = record
    Data: T;
    Next: specialize PLockFreeStackNode<T>;
  end;

  // 无锁栈实现
  generic TLockFreeStack<T> = record
  private
    type
      PNode = specialize PLockFreeStackNode<T>;
      TNode = specialize TLockFreeStackNode<T>;
  private
    FTop: TAtomicPointer;
    FCount: TAtomicInt32;
    
    function AllocateNode: PNode;
    procedure FreeNode(aNode: PNode);
  public
    procedure Initialize;
    procedure Finalize;
    
    function TryPush(const aItem: T): Boolean;
    function TryPop(out aItem: T): Boolean;
    function IsEmpty: Boolean; inline;
    function Count: Integer; inline;
    
    class operator Initialize(var aStack: specialize TLockFreeStack<T>);
    class operator Finalize(var aStack: specialize TLockFreeStack<T>);
  end;

  // 线程安全的简单队列（基于锁）
  generic TThreadSafeQueue<T> = class(TInterfacedObject, specialize IThreadSafeQueue<T>)
  private
    type
      TItemArray = array of T;
  private
    FItems: TItemArray;
    FHead: Integer;
    FTail: Integer;
    FCount: Integer;
    FCapacity: Integer;
    FLock: ILock;
    FShutdown: TAtomicBoolean;
    
    procedure Grow;
  public
    constructor Create(aInitialCapacity: Integer = 16);
    destructor Destroy; override;
    
    // IThreadSafeQueue<T> 接口实现
    procedure Enqueue(const aItem: T);
    function TryEnqueue(const aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function Dequeue: T;
    function TryDequeue(out aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function IsEmpty: Boolean;
    function Count: Integer;
    procedure Shutdown;
  end;

  // 线程安全的简单栈（基于锁）
  generic TThreadSafeStack<T> = class(TInterfacedObject)
  private
    type
      TItemArray = array of T;
  private
    FItems: TItemArray;
    FCount: Integer;
    FCapacity: Integer;
    FLock: ILock;
    
    procedure Grow;
  public
    constructor Create(aInitialCapacity: Integer = 16);
    destructor Destroy; override;
    
    procedure Push(const aItem: T);
    function TryPush(const aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function Pop: T;
    function TryPop(out aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function IsEmpty: Boolean;
    function Count: Integer;
  end;

  // 原子计数器
  TAtomicCounter = record
  private
    FValue: TAtomicInt32;
  public
    function Increment: Integer; inline;
    function Decrement: Integer; inline;
    function Add(aValue: Integer): Integer; inline;
    function GetValue: Integer; inline;
    procedure SetValue(aValue: Integer); inline;
    
    property Value: Integer read GetValue write SetValue;
    
    class operator Initialize(var aCounter: TAtomicCounter);
    class operator Implicit(const aCounter: TAtomicCounter): Integer;
  end;

// 工厂函数
generic function CreateLockFreeQueue<T>: specialize TLockFreeQueue<T>;
generic function CreateLockFreeStack<T>: specialize TLockFreeStack<T>;
generic function CreateThreadSafeQueue<T>(aInitialCapacity: Integer = 16): specialize TThreadSafeQueue<T>;
generic function CreateThreadSafeStack<T>(aInitialCapacity: Integer = 16): specialize TThreadSafeStack<T>;

implementation

uses
  fafafa.core.sync.primitives; // 前向声明，稍后实现

// TLockFreeQueue<T> 实现

generic function TLockFreeQueue<T>.AllocateNode: PNode;
begin
  GetMem(Result, SizeOf(TNode));
  Result^.Next := nil;
end;

generic procedure TLockFreeQueue<T>.FreeNode(aNode: PNode);
begin
  FreeMem(aNode);
end;

generic procedure TLockFreeQueue<T>.Initialize;
var
  DummyNode: PNode;
begin
  DummyNode := AllocateNode;
  FHead.Store(DummyNode);
  FTail.Store(DummyNode);
  FCount.Store(0);
end;

generic procedure TLockFreeQueue<T>.Finalize;
var
  Current, Next: PNode;
begin
  Current := PNode(FHead.Load);
  while Current <> nil do
  begin
    Next := Current^.Next;
    FreeNode(Current);
    Current := Next;
  end;
end;

generic function TLockFreeQueue<T>.TryEnqueue(const aItem: T): Boolean;
var
  NewNode, Tail, Next: PNode;
begin
  Result := False;
  NewNode := AllocateNode;
  if NewNode = nil then
    Exit;
  
  NewNode^.Data := aItem;
  NewNode^.Next := nil;
  
  while True do
  begin
    Tail := PNode(FTail.Load);
    Next := Tail^.Next;
    
    if Tail = PNode(FTail.Load) then // 确保tail没有被其他线程修改
    begin
      if Next = nil then
      begin
        // tail指向最后一个节点，尝试链接新节点
        if atomic_cas_pointer(Pointer(Tail^.Next), nil, NewNode) then
        begin
          // 成功链接，尝试移动tail
          atomic_cas_pointer(Pointer(FTail.FValue), Tail, NewNode);
          FCount.Increment;
          Result := True;
          Break;
        end;
      end
      else
      begin
        // tail没有指向最后一个节点，尝试移动tail
        atomic_cas_pointer(Pointer(FTail.FValue), Tail, Next);
      end;
    end;
  end;
end;

generic function TLockFreeQueue<T>.TryDequeue(out aItem: T): Boolean;
var
  Head, Tail, Next: PNode;
begin
  Result := False;
  
  while True do
  begin
    Head := PNode(FHead.Load);
    Tail := PNode(FTail.Load);
    Next := Head^.Next;
    
    if Head = PNode(FHead.Load) then // 确保head没有被其他线程修改
    begin
      if Head = Tail then
      begin
        if Next = nil then
        begin
          // 队列为空
          Exit;
        end;
        // tail落后，尝试移动tail
        atomic_cas_pointer(Pointer(FTail.FValue), Tail, Next);
      end
      else
      begin
        if Next = nil then
          Continue; // 不应该发生，重试
        
        // 读取数据
        aItem := Next^.Data;
        
        // 尝试移动head
        if atomic_cas_pointer(Pointer(FHead.FValue), Head, Next) then
        begin
          FreeNode(Head);
          FCount.Decrement;
          Result := True;
          Break;
        end;
      end;
    end;
  end;
end;

generic function TLockFreeQueue<T>.IsEmpty: Boolean;
begin
  Result := FCount.Load = 0;
end;

generic function TLockFreeQueue<T>.Count: Integer;
begin
  Result := FCount.Load;
end;

generic class operator TLockFreeQueue<T>.Initialize(var aQueue: specialize TLockFreeQueue<T>);
begin
  aQueue.Initialize;
end;

generic class operator TLockFreeQueue<T>.Finalize(var aQueue: specialize TLockFreeQueue<T>);
begin
  aQueue.Finalize;
end;

// TLockFreeStack<T> 实现

generic function TLockFreeStack<T>.AllocateNode: PNode;
begin
  GetMem(Result, SizeOf(TNode));
  Result^.Next := nil;
end;

generic procedure TLockFreeStack<T>.FreeNode(aNode: PNode);
begin
  FreeMem(aNode);
end;

generic procedure TLockFreeStack<T>.Initialize;
begin
  FTop.Store(nil);
  FCount.Store(0);
end;

generic procedure TLockFreeStack<T>.Finalize;
var
  Current, Next: PNode;
begin
  Current := PNode(FTop.Load);
  while Current <> nil do
  begin
    Next := Current^.Next;
    FreeNode(Current);
    Current := Next;
  end;
end;

generic function TLockFreeStack<T>.TryPush(const aItem: T): Boolean;
var
  NewNode, Top: PNode;
begin
  Result := False;
  NewNode := AllocateNode;
  if NewNode = nil then
    Exit;
  
  NewNode^.Data := aItem;
  
  repeat
    Top := PNode(FTop.Load);
    NewNode^.Next := Top;
  until atomic_cas_pointer(Pointer(FTop.FValue), Top, NewNode);
  
  FCount.Increment;
  Result := True;
end;

generic function TLockFreeStack<T>.TryPop(out aItem: T): Boolean;
var
  Top, Next: PNode;
begin
  Result := False;
  
  repeat
    Top := PNode(FTop.Load);
    if Top = nil then
      Exit; // 栈为空
    
    Next := Top^.Next;
  until atomic_cas_pointer(Pointer(FTop.FValue), Top, Next);
  
  aItem := Top^.Data;
  FreeNode(Top);
  FCount.Decrement;
  Result := True;
end;

generic function TLockFreeStack<T>.IsEmpty: Boolean;
begin
  Result := FTop.Load = nil;
end;

generic function TLockFreeStack<T>.Count: Integer;
begin
  Result := FCount.Load;
end;

generic class operator TLockFreeStack<T>.Initialize(var aStack: specialize TLockFreeStack<T>);
begin
  aStack.Initialize;
end;

generic class operator TLockFreeStack<T>.Finalize(var aStack: specialize TLockFreeStack<T>);
begin
  aStack.Finalize;
end;

// TThreadSafeQueue<T> 实现

generic constructor TThreadSafeQueue<T>.Create(aInitialCapacity: Integer);
begin
  inherited Create;
  FCapacity := aInitialCapacity;
  SetLength(FItems, FCapacity);
  FHead := 0;
  FTail := 0;
  FCount := 0;
  FLock := CreateMutex; // 前向声明，稍后实现
  FShutdown.Store(False);
end;

generic destructor TThreadSafeQueue<T>.Destroy;
begin
  Shutdown;
  FLock := nil;
  inherited Destroy;
end;

generic procedure TThreadSafeQueue<T>.Grow;
var
  NewCapacity, i, OldTail: Integer;
  NewItems: TItemArray;
begin
  NewCapacity := FCapacity * 2;
  SetLength(NewItems, NewCapacity);

  // 复制现有元素
  i := 0;
  OldTail := FHead;
  while i < FCount do
  begin
    NewItems[i] := FItems[OldTail];
    OldTail := (OldTail + 1) mod FCapacity;
    Inc(i);
  end;

  FItems := NewItems;
  FHead := 0;
  FTail := FCount;
  FCapacity := NewCapacity;
end;

generic procedure TThreadSafeQueue<T>.Enqueue(const aItem: T);
var
  Guard: TAutoLock;
begin
  if FShutdown.Load then
    raise ESyncPrimitive.Create('Queue is shutdown');

  Guard := TAutoLock.Create(FLock);

  if FCount = FCapacity then
    Grow;

  FItems[FTail] := aItem;
  FTail := (FTail + 1) mod FCapacity;
  Inc(FCount);
end;

generic function TThreadSafeQueue<T>.TryEnqueue(const aItem: T; aTimeoutMs: Cardinal): Boolean;
var
  Guard: TAutoLock;
begin
  Result := False;

  if FShutdown.Load then
    Exit;

  if not FLock.TryAcquire(aTimeoutMs) then
    Exit;

  try
    if FShutdown.Load then
      Exit;

    if FCount = FCapacity then
      Grow;

    FItems[FTail] := aItem;
    FTail := (FTail + 1) mod FCapacity;
    Inc(FCount);
    Result := True;
  finally
    FLock.Release;
  end;
end;

generic function TThreadSafeQueue<T>.Dequeue: T;
var
  Guard: TAutoLock;
begin
  if FShutdown.Load then
    raise ESyncPrimitive.Create('Queue is shutdown');

  Guard := TAutoLock.Create(FLock);

  if FCount = 0 then
    raise ESyncPrimitive.Create('Queue is empty');

  Result := FItems[FHead];
  FHead := (FHead + 1) mod FCapacity;
  Dec(FCount);
end;

generic function TThreadSafeQueue<T>.TryDequeue(out aItem: T; aTimeoutMs: Cardinal): Boolean;
var
  Guard: TAutoLock;
begin
  Result := False;

  if FShutdown.Load then
    Exit;

  if not FLock.TryAcquire(aTimeoutMs) then
    Exit;

  try
    if (FCount = 0) or FShutdown.Load then
      Exit;

    aItem := FItems[FHead];
    FHead := (FHead + 1) mod FCapacity;
    Dec(FCount);
    Result := True;
  finally
    FLock.Release;
  end;
end;

generic function TThreadSafeQueue<T>.IsEmpty: Boolean;
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);
  Result := FCount = 0;
end;

generic function TThreadSafeQueue<T>.Count: Integer;
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);
  Result := FCount;
end;

generic procedure TThreadSafeQueue<T>.Shutdown;
begin
  FShutdown.Store(True);
end;

// TThreadSafeStack<T> 实现

generic constructor TThreadSafeStack<T>.Create(aInitialCapacity: Integer);
begin
  inherited Create;
  FCapacity := aInitialCapacity;
  SetLength(FItems, FCapacity);
  FCount := 0;
  FLock := CreateMutex; // 前向声明，稍后实现
end;

generic destructor TThreadSafeStack<T>.Destroy;
begin
  FLock := nil;
  inherited Destroy;
end;

generic procedure TThreadSafeStack<T>.Grow;
begin
  FCapacity := FCapacity * 2;
  SetLength(FItems, FCapacity);
end;

generic procedure TThreadSafeStack<T>.Push(const aItem: T);
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);

  if FCount = FCapacity then
    Grow;

  FItems[FCount] := aItem;
  Inc(FCount);
end;

generic function TThreadSafeStack<T>.TryPush(const aItem: T; aTimeoutMs: Cardinal): Boolean;
begin
  Result := False;

  if not FLock.TryAcquire(aTimeoutMs) then
    Exit;

  try
    if FCount = FCapacity then
      Grow;

    FItems[FCount] := aItem;
    Inc(FCount);
    Result := True;
  finally
    FLock.Release;
  end;
end;

generic function TThreadSafeStack<T>.Pop: T;
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);

  if FCount = 0 then
    raise ESyncPrimitive.Create('Stack is empty');

  Dec(FCount);
  Result := FItems[FCount];
end;

generic function TThreadSafeStack<T>.TryPop(out aItem: T; aTimeoutMs: Cardinal): Boolean;
begin
  Result := False;

  if not FLock.TryAcquire(aTimeoutMs) then
    Exit;

  try
    if FCount = 0 then
      Exit;

    Dec(FCount);
    aItem := FItems[FCount];
    Result := True;
  finally
    FLock.Release;
  end;
end;

generic function TThreadSafeStack<T>.IsEmpty: Boolean;
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);
  Result := FCount = 0;
end;

generic function TThreadSafeStack<T>.Count: Integer;
var
  Guard: TAutoLock;
begin
  Guard := TAutoLock.Create(FLock);
  Result := FCount;
end;

// TAtomicCounter 实现

function TAtomicCounter.Increment: Integer;
begin
  Result := FValue.Increment;
end;

function TAtomicCounter.Decrement: Integer;
begin
  Result := FValue.Decrement;
end;

function TAtomicCounter.Add(aValue: Integer): Integer;
begin
  Result := FValue.Add(aValue);
end;

function TAtomicCounter.GetValue: Integer;
begin
  Result := FValue.Load;
end;

procedure TAtomicCounter.SetValue(aValue: Integer);
begin
  FValue.Store(aValue);
end;

class operator TAtomicCounter.Initialize(var aCounter: TAtomicCounter);
begin
  aCounter.FValue.Store(0);
end;

class operator TAtomicCounter.Implicit(const aCounter: TAtomicCounter): Integer;
begin
  Result := aCounter.GetValue;
end;

// 工厂函数实现

generic function CreateLockFreeQueue<T>: specialize TLockFreeQueue<T>;
begin
  Result.Initialize;
end;

generic function CreateLockFreeStack<T>: specialize TLockFreeStack<T>;
begin
  Result.Initialize;
end;

generic function CreateThreadSafeQueue<T>(aInitialCapacity: Integer): specialize TThreadSafeQueue<T>;
begin
  Result := specialize TThreadSafeQueue<T>.Create(aInitialCapacity);
end;

generic function CreateThreadSafeStack<T>(aInitialCapacity: Integer): specialize TThreadSafeStack<T>;
begin
  Result := specialize TThreadSafeStack<T>.Create(aInitialCapacity);
end;

end.
