unit fafafa.collections;

{

```text
   ______   ______     ______   ______     ______   ______
  /\  ___\ /\  __ \   /\  ___\ /\  __ \   /\  ___\ /\  __ \
  \ \  __\ \ \  __ \  \ \  __\ \ \  __ \  \ \  __\ \ \  __ \
   \ \_\    \ \_\ \_\  \ \_\    \ \_\ \_\  \ \_\    \ \_\ \_\
    \/_/     \/_/\/_/   \/_/     \/_/\/_/   \/_/     \/_/\/_/  Studio

```
# fafafa.collections

## 概述

fafafa.collections 是 fafafa 框架的泛型容器库,提供了大量的泛型容器.
所有容器都支持泛型接口使用,并且提供了内存分配器接口,可以方便的替换默认的内存分配器,这让用户自定义高性能或者专用的内存分配器成为可能.
容器的实现目标以高性能和丰富灵活高泛化能力为主,同时兼顾易用性.

## 源码注释

源码中,我添加了大量的注释,这些注释可以帮助你理解代码的实现和使用,好的习惯是多阅读源码中好的注释,而不是依赖文档.

## 结构树

- IMemAllocator: 内存分配器接口
- TMemAllocator 内存分配器实现默认基类

- ICollection: 基础容器接口
  - IArray  数组
  - IVec  向量数组
  - IVecDeque 双端队列
  - IStack 栈
  - IQueue 队列
  - IMap 映射
  - ISet 集合
  - IList 列表
  - IHash 哈希
  - IBitSet 位集

## todo

  以实现MVP为主,逐步完善.

## 声明

转发或者用于自己项目请保留本项目的版权声明,谢谢.

fafafaStudio
Email:<EMAIL>
QQ群:685403987  QQ:179033731

}


{$mode objfpc}{$H+}
{$I fafafa.collections.settings.inc}

interface


uses
  sysutils, typinfo, variants;


///
/// 异常
///

type

  { 惊讶 }
  EWow              = class(Exception) end;

  { 空容器 }
  EEmpty            = class(Exception) end;

  { nil }
  ENil              = class(Exception) end;

  { 自己 }
  ESelf             = class(Exception) end;

  { 为 0 }
  EIsZero           = class(Exception) end;

  { 索引越界 }
  ERangeOutOfIndex  = class(Exception) end;

  { 范围越界 }
  ERangeOutOfBounds = class(Exception) end;

  { 相同的索引 }
  ESame             = class(Exception) end;

  { 重叠 }
  EOverlap          = class(Exception) end;

  { 加载异常 }
  ELoad             = class(Exception) end;

  { 写异常 }
  EWrite            = class(Exception) end;

  { 读异常 }
  ERead             = class(Exception) end;
  
  { 分配异常 }
  EAlloc            = class(Exception) end;

  { Resize 异常 }
  EResize           = class(Exception) end;

  { 不兼容 }
  ECompatible    = class(Exception) end;


///
/// 内存分配器
///

type

  { 
    通过在内存分配器中注入自定义的内存分配器,可以提高内存分配效率
    例如: mimalloc/jemalloc/tcmalloc等
    这在服务器应用开发中,可以带来显著的性能提升.
  }

  { IMemAllocator 内存分配器接口 }
  IMemAllocator = interface
  ['{b2fa99a8-9232-4ae4-a019-4b0f6af25fbf}']

    {**
     * GetMem
     *
     * @desc 分配一块指定大小的内存区域.
     *
     * @params
     *   aSize 要分配的内存大小 (以字节为单位).
     *
     * @return 返回指向新分配内存的指针. 如果分配失败, 则返回 `nil`.
     *
     * @remark 返回的内存块内容是未定义的 (不是零初始化的).
     *}
    function GetMem(aSize: SizeUInt): Pointer;

    {**
     * AllocMem
     *
     * @desc 分配一块指定大小的内存区域, 并将其所有字节初始化为零.
     * 
     * @params
     *   aSize 要分配的内存大小 (以字节为单位).
     *
     * @return 返回指向新分配并清零的内存的指针. 如果分配失败, 则返回 `nil`.
     *
     * @remark 由于需要执行清零操作, 此函数通常比 `GetMem` 稍慢.
     *}
    function AllocMem(aSize: SizeUInt): Pointer;

    {**
     * ReallocMem
     *
     * @desc 调整一块已分配内存区域的大小.此函数可以扩大或缩小内存块,并可能移动内存块到新的位置.
     *
     * @params
     *   aDst  指向要调整大小的现有内存块的指针
     *   aSize 请求的新内存大小 (以字节为单位)
     *
     * @return 返回指向新分配内存区域的指针.如果操作失败,则返回 `nil`
     *
     * @remark 
     *   当 `aSize` 为 0 时,其行为等同于 `FreeMem`,并返回 `nil`
     *   当 `aDst` 为 `nil` 时,其行为等同于 `GetMem`
     *   内存分配器不保证一定分配成功,考虑极端情况,分配失败会返回 `nil`
     *}
    function ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;

    {**
     * FreeMem
     *
     * @desc 释放一块先前通过 `GetMem`, `AllocMem` 或 `ReallocMem` 分配的内存区域.
     *
     * @params
     *   aDst 指向要释放的内存块的指针.
     *
     * @remark 
     *   调用者必须确保 `aDst` 是一个有效的、非 `nil` 的指针. 传递 `nil` 或无效指针将导致未定义行为.
     *   同一块内存不能被释放两次, 否则可能导致堆损坏.
     *   内存被释放后, 不应再访问该指针指向的区域,否则会导致未定义行为.
     *}
    procedure FreeMem(aDst: Pointer);

    {**
     * IsOverlap
     *
     * @desc 检查两块内存区域是否重叠.
     *
     * @params
     *   aPtr1  指向第一块内存区域的指针.
     *   aSize1 第一块内存区域的大小.
     *   aPtr2  指向第二块内存区域的指针.
     *   aSize2 第二块内存区域的大小.
     *
     * @return 如果两块内存区域重叠, 则返回 `True`, 否则返回 `False`.
     *}
    function IsOverlap(aPtr1: Pointer; aSize1: SizeUInt; aPtr2: Pointer; aSize2: SizeUInt): Boolean;

    {**
     * Copy
     *
     * @desc 从源地址向目标地址复制一块内存, 此函数能安全地处理源和目标内存区域重叠的情况.
     *
     * @params
     *   aSrc  指向源内存区域的指针.
     *   aDst  指向目标内存区域的指针.
     *   aSize 要复制的字节数.
     *
     * @remark 
     *   此函数的行为等价于标准库的 `Move` 过程或 C 语言的 `memmove` 函数.
     *   调用者必须确保 `aSrc` 和 `aDst` 指向有效的、已分配的内存区域.
     *   目标内存区域 (`aDst`) 必须足够大, 能够容纳 `aSize` 字节, 以防止缓冲区溢出.
     *
     * @exceptions
     *   ENil    如果 aSrc 或 aDst 为 nil
     *   EIsZero 如果 aSize 为 0
     *}
    procedure Copy(aSrc, aDst: Pointer; aSize: SizeUInt);

    {**
     * CopyUnChecked
     *
     * @desc `Copy` 的快速,无检查版本. 它能安全处理重叠内存, 但为了最大化速度而省略了所有参数验证.
     *
     * @params
     *   aSrc  指向源内存区域的指针.
     *   aDst  指向目标内存区域的指针.
     *   aSize 要复制的字节数.
     *
     * @remark 
     *   调用者必须自行确保所有参数的有效性：
     *     - aSrc 和 aDst 指针必须有效且非 nil。
     *     - aSize 必须大于 0。
     *     - 源和目标内存区域必须已正确分配。
     *   任何无效参数都将导致未定义行为（如访问冲突、数据损坏）。
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure CopyUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt);

    {**
     * CopyNonOverlap
     *
     * @desc 从源地址向目标地址复制一块内存. 此函数假定源和目标内存区域没有重叠, 在此前提下通常比 `Copy` 更快.
     *
     * @params
     *   aSrc  指向源内存区域的指针.
     *   aDst  指向目标内存区域的指针.
     *   aSize 要复制的字节数.
     *
     * @remark 
     *   此函数的行为等价于 C 语言的 `memcpy` 函数.
     *   调用者必须确保目标内存区域 (`aDst`) 足够大以容纳 `aSize` 字节.
     *
     * @exceptions
     *   ENil    如果 aSrc 或 aDst 为 nil
     *   EIsZero 如果 aSize 为 0
     *}
    procedure CopyNonOverlap(aSrc, aDst: Pointer; aSize: SizeUInt);

    {**
     * CopyNonOverlapUnChecked
     *
     * @desc `CopyNonOverlap` 的快速,无检查版本. 它假定源和目标内存区域没有重叠, 并且为了最大化速度省略了所有参数验证.
     *
     * @params
     *   aSrc  指向源内存区域的指针.
     *   aDst  指向目标内存区域的指针.
     *   aSize 要复制的字节数.
     *
     * @remark 
     *   违反任何这些条件都将导致未定义行为 (如数据损坏、访问冲突或崩溃).
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure CopyNonOverlapUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt);

    {**
     * Fill
     *
     * @desc 用指定的字节值填充一块内存区域. 这是一个别名, 等同于调用 `Fill8`.
     *
     * @params
     *   aDst   指向目标内存区域的指针.
     *   aCount 要填充的字节数.
     *   aValue 填充的值.
     *
     * @remark
     *   调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,
     *   其大小至少为 `aCount` 字节, 以避免缓冲区溢出.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aCount 为 0
     *}
    procedure Fill(aDst: Pointer; aCount: SizeUInt; aValue: UInt8);

    {**
     * Fill8
     *
     * @desc 用指定的字节值填充一块内存区域.
     *
     * @params
     *   aDst   指向目标内存区域的指针.
     *   aCount 要填充的字节数.
     *   aValue 用于填充的 8-位 (byte) 值.
     *
     * @remark 调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,其大小至少为 `aCount` 字节, 以避免缓冲区溢出.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aCount 为 0
     *}
    procedure Fill8(aDst: Pointer; aCount: SizeUInt; aValue: UInt8);

    {**
     * Fill16
     *
     * @desc 用指定的 16-位 (Word) 值填充一块内存区域.
     *
     * @params
     *   aDst   指向要填充的内存块的指针.
     *   aCount 要写入的 16-位单元的数量.
     *   aValue 用于填充的 16-位 (Word) 值.
     *
     * @remark
     *   总共将写入 `aCount * 2` 个字节.
     *   调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,其大小至少为 `aCount * 2` 字节.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aCount 为 0
     *}
    procedure Fill16(aDst: Pointer; aCount: SizeUInt; aValue: UInt16);

    {**
     * Fill32
     *
     * @desc 用指定的 32-位 (DWord) 值填充一块内存区域.
     *
     * @params
     *   aDst   指向要填充的内存块的指针.
     *   aCount 要写入的 32-位单元的数量.
     *   aValue 用于填充的 32-位 (DWord) 值.
     *
     * @remark
     *   总共将写入 `aCount * 4` 个字节.
     *   调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,其大小至少为 `aCount * 4` 字节.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aCount 为 0
     *}
    procedure Fill32(aDst: Pointer; aCount: SizeUInt; aValue: UInt32);

    {**
     * Fill64
     *
     * @desc 用指定的 64-位 (QWord) 值填充一块内存区域.
     *
     * @params
     *   aDst   指向要填充的内存块的指针.
     *   aCount 要写入的 64-位单元的数量.
     *   aValue 用于填充的 64-位 (QWord) 值.
     *
     * @remark
     *   总共将写入 `aCount * 8` 个字节.
     *   调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,其大小至少为 `aCount * 8` 字节.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aCount 为 0
     *}
    procedure Fill64(aDst: Pointer; aCount: SizeUInt; const aValue: UInt64);

    {**
     * Zero
     *
     * @desc 将一块内存区域的所有字节清零.
     *
     * @params
     *   aDst  指向要清零的内存块的指针.
     *   aSize  要清零的字节数.
     *
     * @remark
     *   此过程在功能上等同于 `Fill8(aDst, aSize, 0)`, 但其实现可能经过特殊优化.
     *   调用者必须确保 `aDst` 指向一个有效的、已分配的内存区域,其大小至少为 `aSize` 字节.
     *
     * @exceptions
     *   ENil    如果 aDst 为 nil
     *   EIsZero 如果 aSize 为 0
     *}
    procedure Zero(aDst: Pointer; aSize: SizeUInt);
  end;

  { 分配器回调 }

  allocator_GetMem_t     = function(aSize: SizeUInt): Pointer;
  allocator_AllocMem_t   = function(aSize: SizeUInt): Pointer;
  allocator_ReallocMem_t = function(aPtr: Pointer; aSize: SizeUInt): Pointer;
  allocator_FreeMem_t    = procedure(aPtr: Pointer);


  { TMemAllocator 内存分配器实现 }

  TMemAllocator = class(TInterfacedObject, IMemAllocator)
  private
    FGetMem:     allocator_GetMem_t;
    FAllocMem:   allocator_AllocMem_t;
    FReallocMem: allocator_ReallocMem_t;
    FFreeMem:    allocator_FreeMem_t;
  public
    constructor Create(aGetMem: allocator_GetMem_t; aAllocMem: allocator_AllocMem_t; aReallocMem: allocator_ReallocMem_t; aFreeMem: allocator_FreeMem_t); virtual;

    function  GetMem(aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  AllocMem(aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure FreeMem(aDst: Pointer); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    function  IsOverlap(aPtr1: Pointer; aSize1: SizeUInt; aPtr2: Pointer; aSize2: SizeUInt): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Copy(aSrc, aDst: Pointer; aSize: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure CopyUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure CopyNonOverlap(aSrc, aDst: Pointer; aSize: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure CopyNonOverlapUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Fill(aDst: Pointer; aCount: SizeUInt; aValue: UInt8); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Fill8(aDst: Pointer; aCount: SizeUInt; aValue: UInt8); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Fill16(aDst: Pointer; aCount: SizeUInt; aValue: UInt16); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Fill32(aDst: Pointer; aCount: SizeUInt; aValue: UInt32); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Fill64(aDst: Pointer; aCount: SizeUInt; const aValue: UInt64); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Zero(aDst: Pointer; aSize: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  end;

  {**
   * RtlMemAllocator
   *
   * @desc 获取全局默认的内存分配器(RTL)
   *
   * @return 内存分配器
   *}
  function RtlMemAllocator: TMemAllocator; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}


///
/// 基础容器
///

type

  TCollection = class;

  generic TGenericArray<T> = array of T;

  { ICollection 基础容器接口 }
  ICollection = interface
  ['{3ab40ac3-d1b8-4777-9d80-219337e99a7d}']

  {**
   * GetAllocator
   *
   * @desc 获取容器使用的内存分配器.
   *
   * @return 此容器的 TMemAllocator 实例.
   *
   * @remark
   *   容器内的所有内存操作都由此分配器处理, 它是容器生命周期的基础.
   *   此容器永远不会nil,默认它是 rtl 的内存分配器.
   *}
  function GetAllocator: TMemAllocator;

  {**
   * GetCount
   *
   * @desc 获取容器中当前元素的数量.
   *
   * @return 元素的数量.
   *}
  function GetCount: SizeUInt;

  {**
   * IsEmpty
   *
   * @desc 检查容器是否为空 (不包含任何元素).
   *
   * @return 如果容器为空, 则返回 `True`, 否则返回 `False`.
   *
   * @remark 此方法等同 `GetCount = 0`.
   *}
  function IsEmpty: Boolean;

  {**
   * GetData
   *
   * @desc 获取与容器关联的用户自定义数据指针.
   *
   * @return 用户自定义的指针.
   *
   * @remark 此属性允许用户将任意数据与容器实例关联.容器本身不管理或解释此数据.
   *}
  function GetData: Pointer;

  {**
   * SetData
   *
   * @desc 设置与容器关联的用户自定义数据指针.
   *
   * @params
   *   aData 指向自定义数据的指针.
   *
   * @remark 此属性允许用户将任意数据与容器实例关联.容器本身不管理或解释此数据.
   *}
  procedure SetData(aData: Pointer);

  {**
   * Clear
   *
   * @desc 从容器中移除所有元素.
   *
   * @remark  `Clear` 的具体行为 (例如, 是否释放内存) 取决于具体的容器实现.请参考相应类的文档以获取详细信息.
   *}
  procedure Clear;

  {**
   * WriteToArrayMemory
   *
   * @desc 将容器中的元素复制到原始内存缓冲区.
   *
   * @params
   *   aDst   指向目标内存缓冲区的指针.
   *   aCount 要复制的元素数量.
   *
   * @remark 
   *   此操作从容器的起始位置开始复制 `aCount` 个元素.
   *   **警告:** 调用者必须确保 `aDst` 指向的目标缓冲区足够大,以容纳 `aCount` 个元素, 以防止缓冲区溢出.
   *   此接口 输入 `nil` 和 `0` 数量时 不会抛出异常,仅仅返回 `false`
   *
   * @exceptions
   *   ENil              aDst = nil 时 这是非法操作,会抛出异常
   *   EOverlap          源指针与当前容器内存范围重叠,这是严重的灾难性错误
   *   ERangeOutOfBounds 范围越界
   *}
  procedure WriteToArrayMemory(aDst: Pointer; aCount: SizeUInt);

  {**
   * Clone
   *
   * @desc 创建容器的一个新的深层副本.
   *
   * @return 一个包含元素副本的新的 TCollection 实例.
   *
   * @remark 
   *   新容器将与原始容器共享相同的内存分配器和其他配置.
   *   克隆的“深度” (即如何处理作为指针或接口的元素) 取决于具体的容器实现.
   *}
  function Clone: TCollection;

  {**
   * IsCompatible
   *
   * @desc 检查当前容器是否兼容指定容器
   *
   * @params
   *   aDst 目标容器
   *
   * @return 如果兼容, 则返回 `True`.
   *
   * @remark 
   *   此方法用于检查当前容器是否兼容指定类型的容器.
   *}
  function IsCompatible(aDst: TCollection): Boolean;

  {**
   * LoadFrom
   *
   * @desc 清空当前容器, 并从原始内存缓冲区复制指定数量的元素.
   *
   * @params
   *   aSrc          指向源内存缓冲区的指针.
   *   aElementCount 要从缓冲区复制的元素数量.
   *
   * @remark 
   *   此接口 输入 aElementCount = `0` 数量时  视作 Clear
   *
   * @exceptions
   *   ENil     aSrc = nil 时 这是非法操作,会抛出异常
   *   EOverlap 源指针与当前容器内存范围重叠,这是严重的灾难性错误
   *   EAlloc   内存分配失败
   *}
  procedure LoadFrom(const aSrc: Pointer; aElementCount: SizeUInt); overload;

  {**
   * LoadFromUnChecked
   *
   * @desc 清空当前容器, 并从原始内存缓冲区复制指定数量的元素(快速,无检查版本).
   *
   * @params
   *   aSrc          指向源内存缓冲区的指针.
   *   aElementCount 要从缓冲区复制的元素数量.
   *
   * @remark 
   *   不再接受 aElementCount = 0 的参数, 这会造成灾难,因为不会有检查
   *   此过程不执行检查. 调用者必须自行保证安全
   *
   * @exceptions
   *   EAlloc   内存分配失败
   *}
  procedure LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); overload;
  
  {**
   * Append
   *
   * @desc 从原始内存缓冲区将指定数量的元素追加到容器末尾(拷贝).
   *
   * @params
   *   aSrc          指向源内存缓冲区的指针.
   *   aElementCount 要从缓冲区追加的元素数量.
   *
   * @remark 
   *   此操作会增加容器的大小.
   *   源指针指向的内存空间必须有效.
   *   此接口 输入 aElementCount = `0` 数量时什么都不会做
   *
   * @exceptions
   *   ENil              源指针为 nil
   *   ERangeOutOfBounds 范围越界
   *   EWow              源指针未对齐
   *   EAlloc            内存分配失败
   *}
  procedure Append(const aSrc: Pointer; aElementCount: SizeUInt); overload;

  {**
   * AppendUnChecked
   *
   * @desc 从原始内存缓冲区将指定数量的元素追加到容器末尾(拷贝)(快速,无检查版本).
   *
   * @params
   *   aSrc          指向源内存缓冲区的指针.
   *   aElementCount 要从缓冲区追加的元素数量.
   *
   * @remark 
   *   此操作会增加容器的大小.
   *   源指针指向的内存空间必须有效.
   *   此接口 输入 aElementCount = `0` 数量时什么都不会做
   *   此接口不做任何检查, 调用者必须自行保证安全
   *
   * @exceptions
   *   ERangeOutOfBounds 范围越界
   *}
  procedure AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); overload;

  {**
   * LoadFrom
   *
   * @desc 清空当前容器, 并从源容器复制所有元素.
   *
   * @params
   *   aSrc 从中加载元素的源容器.
   *
   * @remark 
   *   此操作在复制前会先清空当前容器.
   *   如果源容器为空, 则仅清空当前容器.
   *
   * @exceptions
   *   ENil           源容器为 nil
   *   ESelf          源容器为自身
   *   ECompatible 源容器与当前容器不兼容,不是当前容器的子类
   *   EAlloc         内存分配失败
   *}
  procedure LoadFrom(const aSrc: TCollection); overload;

  {**
   * LoadFromUnChecked
   *
   * @desc 清空当前容器, 并从源容器复制所有元素(快速,无检查版本).
   *
   * @params
   *   aSrc 从中加载元素的源容器.
   *
   * @remark 
   *   此操作在复制前会先清空当前容器.
   *   如果源容器为空, 则仅清空当前容器(与 `Clear` 等价).
   *   此过程不执行任何检查. 调用者必须自行保证安全
   *
   * @exceptions
   *   EAlloc         内存分配失败
   *}
  procedure LoadFromUnChecked(const aSrc: TCollection); overload;

  {**
   * SaveTo
   *
   * @desc 清空目标容器, 并将当前容器的所有元素复制到其中.
   *
   * @params
   *   aDst 目标容器.
   *
   * @remark 
   *   此操作在复制前会先清空目标容器.
   *   如果当前容器为空, 则仅清空目标容器.
   *
   * @exceptions
   *   ENil           目标容器为 nil
   *   ESelf          目标容器为自身
   *   ECompatible 目标容器与当前容器不兼容,不是当前容器的子类
   *}
  procedure SaveTo(aDst: TCollection); overload;

  {**
   * SaveToUnChecked
   *
   * @desc 清空目标容器, 并将当前容器的所有元素复制到其中(快速,无检查版本).
   *
   * @params
   *   aDst 目标容器.
   *
   * @remark 
   *   此操作在复制前会先清空目标容器.
   *   如果当前容器为空, 则仅清空目标容器(与 `aDst.Clear` 等价).
   *   此过程不执行任何检查. 调用者必须自行保证安全
   *}
  procedure SaveToUnChecked(aDst: TCollection);

  {**
   * Append
   *
   * @desc 将源容器的所有元素追加到当前容器的末尾(拷贝).
   *
   * @params
   *   aSrc 源容器
   *
   * @remark 
   *   此操作会增加容器的大小.
   *   如果源容器元素数量为0,则不执行任何操作,直接返回 `True`.
   *
   * @exceptions
   *   ENil           源容器为 nil
   *   ESelf          源容器为自身
   *   ECompatible 源容器与当前容器不兼容,不是当前容器的子类
   *   EAlloc         内存分配失败
   *}
  procedure Append(const aSrc: TCollection); overload;

  {**
   * AppendUnChecked
   *
   * @desc 将源容器的所有元素追加到当前容器的末尾(拷贝)(快速,无检查版本).
   *
   * @params
   *   aSrc 源容器
   *
   * @remark 
   *   此操作会增加容器的大小.
   *   如果源容器元素数量为0,则不执行任何操作,直接返回 `True`.
   *   此过程不执行任何检查. 调用者必须自行保证安全
   *
   * @exceptions
   *   EAlloc         内存分配失败
   *}
  procedure AppendUnChecked(const aSrc: TCollection); overload;

  {**
   * AppendTo
   *
   * @desc 将当前容器的所有元素追加到目标容器的末尾(拷贝).
   *
   * @params
   *   aDst 目标容器
   *
   * @remark 
   *   此操作会增加目标容器的大小.
   *   如果自身容器元素数量为0,则不执行任何操作,直接返回 `True`.
   *
   * @exceptions
   *   ENil           目标容器为 nil
   *   ESelf          目标容器为自身
   *   ECompatible 目标容器与当前容器不兼容,不是当前容器的子类
   *   EAlloc         内存分配失败
   *}
  procedure AppendTo(const aDst: TCollection); overload;

  {**
   * AppendToUnChecked
   *
   * @desc 将当前容器的所有元素追加到目标容器的末尾(拷贝)(快速,无检查版本).
   *
   * @params
   *   aDst 目标容器
   *
   * @remark 
   *   此操作会增加目标容器的大小.
   *   如果自身容器元素数量为0,则不执行任何操作,直接返回 `True`.
   *   此过程不执行任何检查. 调用者必须自行保证安全
   *
   * @exceptions
   *   EAlloc         内存分配失败
   *}
  procedure AppendToUnChecked(const aDst: TCollection); overload;

  property Count: SizeUInt read GetCount;
  property Data:  Pointer read GetData write SetData;
  property Allocator: TMemAllocator read GetAllocator;
  end;


  { TCollection 容器基类 }
  TCollection = class(TInterfacedObject, ICollection)
  private
    FData:      Pointer;
  protected
    FAllocator: TMemAllocator;
    function IsOverlap(const aSrc: Pointer; aElementCount: SizeUInt): Boolean; virtual; abstract;
  public
    constructor Create; overload;
    constructor Create(aAllocator: TMemAllocator); virtual; overload;
    constructor Create(const aSrc: TCollection); overload;
    constructor Create(const aSrc: TCollection; aAllocator: TMemAllocator); overload;
    constructor Create(aSrc: Pointer; aElementCount: SizeUInt); overload;
    constructor Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator); overload;

    function  GetAllocator: TMemAllocator; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetCount: SizeUInt; virtual; abstract;
    function  IsEmpty: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetData: Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure SetData(aData: Pointer); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Clear; virtual; abstract;

    procedure WriteToArrayMemory(aDst: Pointer; aCount: SizeUInt); virtual; abstract;

    function  Clone: TCollection; virtual;
    function  IsCompatible(aDst: TCollection): Boolean; virtual;

    procedure LoadFrom(const aSrc: Pointer; aElementCount: SizeUInt); virtual; overload;
    procedure LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); virtual; abstract; overload;
    procedure Append(const aSrc: Pointer; aElementCount: SizeUInt); virtual; overload;
    procedure AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); virtual; abstract; overload;

    procedure LoadFrom(const aSrc: TCollection); overload;
    procedure LoadFromUnChecked(const aSrc: TCollection); virtual; overload;

    procedure Append(const aSrc: TCollection); overload;
    procedure AppendUnChecked(const aSrc: TCollection); virtual;

    procedure AppendTo(const aDst: TCollection);
    procedure AppendToUnChecked(const aDst: TCollection); virtual; abstract;

    procedure SaveTo(aDst: TCollection); overload;
    procedure SaveToUnChecked(aDst: TCollection); virtual;

    property  Count:     SizeUInt      read GetCount;
    property  Data:      Pointer       read GetData write SetData;
    property  Allocator: TMemAllocator read GetAllocator;
  end;

  TCollectionClass = class of TCollection;

type

  { TGenericHelper 泛型辅助类 主要用于突破当前fpc泛型语法限制 }
  generic TGenericHelper<T> = class
  type
    PGenericPtr = ^T;
  end;


type

  // { IEnumerator 通用泛型迭代器接口 }
  // generic IEnumerator<T> = interface
  // ['{97194911-8007-4904-8688-665614062857}']

  //   {**
  //   * GetStarted
  //   *
  //   * @desc 判断迭代器是否已经开始
  //   *
  //   * @return 开始返回True,否则返回False
  //   *}
  //   function GetStarted: Boolean;

  //   {**
  //   * GetCurrent
  //   *
  //   * @desc 获取当前迭代器指向的元素
  //   *
  //   * @return 元素
  //   *}
  //   function GetCurrent: T;
    
  //   {**
  //   * MoveNext
  //   *
  //   * @desc 移动迭代器到下一个元素
  //   *
  //   * @return 成功返回True,否则返回False
  //   *}
  //   function MoveNext: Boolean;

  //   {**
  //   * Reset
  //   *
  //   * @desc 重置迭代器
  //   *}
  //   procedure Reset;

  //   property Current: T read GetCurrent;
  //   property Started: Boolean read GetStarted;
  // end;

  { TEnumerator 泛型迭代器基类 }
  generic TEnumerator<T> = class(TInterfacedObject, specialize IEnumerator<T>)
  private
    FStarted: Boolean;
  public
    function    GetStarted: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    GetCurrent: T;virtual; abstract;
    function    MoveNext: Boolean; virtual; abstract;
    procedure   Reset; virtual; abstract;

    property    Current: T read GetCurrent;
    property    Started: Boolean read GetStarted;
  end;

type

  { IElementAllocator 泛型元素分配器接口 }
  generic IElementAllocator<T> = interface
  ['{8C517B2F-A197-4665-80F0-D7C131247171}']

    {**
    * GetMemAllocator
    *
    * @desc 获取此元素分配器所使用的底层原始内存分配器.
    *
    * @return TMemAllocator 实例.
    *}
    function GetMemAllocator: TMemAllocator;

    {**
     * GetElementSize
     *
     * @desc 获取泛型类型 T 的单个元素所占用的内存大小 (以字节为单位).
     *
     * @return 单个元素的大小.
     *}
    function GetElementSize: SizeUInt;

    {**
     * GetIsManagedType
     *
     * @desc 检查泛型类型 T 是否为托管类型.
     *
     * @return 如果是托管类型 (如 `string`, `interface`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 托管类型拥有自动的生命周期管理 (如引用计数). 此分配器会为它们提供特殊的初始化和释放处理.
     *}
    function GetIsManagedType: Boolean;

    {**
     * GetTypeInfo
     *
     * @desc 获取泛型类型 T 的运行时类型信息 (RTTI).
     *
     * @return 指向 TTypeInfo 记录的指针.
     *}
    function GetTypeInfo: PTypeInfo;

    {**
     * InitializeElements
     *
     * @desc 对一块内存区域中的所有元素进行初始化.
     *
     * @params
     *   aPtr          指向元素数组的指针.
     *   aElementCount 要初始化的元素数量.
     *
     * @remark 
     *   **此方法仅对托管类型有效**, 它会将 string 初始化为空字符串, 接口初始化为 nil 等.
     *   对于非托管类型, 此方法不执行任何操作.
     *   通常由容器在内部分配新内存后自动调用, 用户很少需要直接调用.
     *
     * @exceptions
     *   ENil    `aPtr` 为 nil
     *   EIsZero `aElementCount` 为 0
     *}
    procedure InitializeElements(aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * InitializeElementsUnChecked
     *
     * @desc 对一块内存区域中的所有元素进行初始化(快速,无检查版本).
     *
     * @params
     *   aPtr          指向元素数组的指针.
     *   aElementCount 要初始化的元素数量.
     *
     * @remark 
     *   **此方法仅对托管类型有效**, 它会将 string 初始化为空字符串, 接口初始化为 nil 等.
     *   对于非托管类型, 此方法不执行任何操作.
     *   通常由容器在内部分配新内存后自动调用, 用户很少需要直接调用.
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure InitializeElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * FinalizeManagedElements
     *
     * @desc 对一块内存区域中的所有托管类型元素进行终结 (Finalize).
     *
     * @params
     *   aPtr          指向元素数组的指针.
     *   aElementCount 要终结的元素数量.
     *
     * @remark 
     *   **此方法仅对托管类型有效**, 它会减少 string 或 interface 的引用计数.
     *   对于非托管类型, 此方法不执行任何操作.
     *   通常由容器在释放内存前自动调用, 用户很少需要直接调用.
     *
     * @exceptions
     *   ENil    `aPtr` 为 nil
     *   EIsZero `aElementCount` 为 0
     *}
    procedure FinalizeManagedElements(aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * FinalizeManagedElementsUnChecked
     *
     * @desc 对一块内存区域中的所有托管类型元素进行终结 (Finalize)(快速,无检查版本).
     *
     * @params
     *   aPtr          指向元素数组的指针.
     *   aElementCount 要终结的元素数量.
     *
     * @remark 
     *   **此方法仅对托管类型有效**, 它会减少 string 或 interface 的引用计数.
     *   对于非托管类型, 此方法不执行任何操作.
     *   通常由容器在释放内存前自动调用, 用户很少需要直接调用.
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure FinalizeManagedElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * AllocElements
     *
     * @desc 分配足够容纳指定数量元素的内存, 并进行适当的初始化.
     *
     * @params
     *   aCount 要分配的元素数量.
     *
     * @return 指向新分配的元素数组的类型安全指针. 如果分配失败, 则返回 `nil`.
     *
     * @remark 如果 T 是托管类型, 分配后的内存区域会通过 `InitializeElements` 进行初始化.
     *}
    function AllocElements(aCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr;

    {**
     * AllocElement
     *
     * @desc 分配并初始化单个元素的内存.
     *
     * @return 指向新分配的单个元素的类型安全指针. 如果分配失败, 则返回 `nil`.
     *
     * @remark 这是 `AllocElements(1)` 的便捷方法.
     *}
    function AllocElement: specialize TGenericHelper<T>.PGenericPtr;

    {**
     * ReallocElements
     *
     * @desc 重新分配一块元素内存区域, 调整其可容纳的元素数量.
     *
     * @params
     *   aDst             指向当前元素内存块的指针.
     *   aElementCount    内存块中当前的元素数量.
     *   aNewElementCount 调整后期望的元素数量.
     *
     * @return 指向新分配的元素数组的类型安全指针. 如果分配失败, 则返回 `nil`.
     *
     * @remark
     *   当 `aDst` 为 `nil` 时, 视作分配新的内存(与 `GetMem` 等价).
     *   此过程会智能处理托管类型:
     *   分配器会对托管类型进行特定处理
     *   此接口针对连续内存,重分配意味着整块内存变化,不可对局部内存进行重分配
     *   - 如果缩减容量, 多余的托管元素会被正确终结.
     *   - 如果增加容量, 新增的托管元素会被正确初始化.
     *
     * @exceptions
     *   EWow 当 指针`aDst` 为 `nil` 被视作分配新的内存,但 `aNewElementCount` 为 0,这是一个恶意输入, 会抛出 `EWow` 异常
     *}
    function ReallocElements(aDst: Pointer; aElementCount, aNewElementCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr;

    {**
     * FreeElements
     *
     * @desc 释放一块包含指定数量元素的内存.
     *
     * @params
     *   aDst   指向要释放的元素内存块的指针.
     *   aCount 内存块中的元素数量.
     *
     * @remark 如果 T 是托管类型, 在释放内存前, 所有元素会先通过 `FinalizeManagedElements` 进行终结.
     *
     * @exceptions
     *   ENil    `aDst` 为 nil
     *   EIsZero `aCount` 为 0
     *}
    procedure FreeElements(aDst: Pointer; aCount: SizeUInt);

    {**
     * FreeElement
     *
     * @desc 释放由 `AllocElement` 分配的单个元素内存.
     *
     * @remark 这是 `FreeElements(aDst, 1)` 的便捷方法.
     *
     * @exceptions
     *   ENil    `aDst` 为 nil
     *}
    procedure FreeElement(aDst: Pointer);

    {**
     * IsMemoryOverlap
     *
     * @desc 检查两个元素数组的内存区域是否重叠.
     *
     * @params
     *   aSrc          指向第一个元素数组的指针.
     *   aDst          指向第二个元素数组的指针.
     *   aElementCount 数组中的元素数量.
     *
     * @return 如果内存区域有重叠, 则返回 `True`; 否则返回 `False`.
     *}
    function IsMemoryOverlap(aSrc, aDst: Pointer; aElementCount: SizeUInt): Boolean;

    {**
     * CopyElements
     *
     * @desc 复制元素, 能安全处理源和目标内存区域重叠的情况.
     *
     * @params
     *   aSrc          指向源元素数组的指针.
     *   aDst          指向目标元素数组的指针.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   此操作对于托管类型是安全的 (会正确处理引用计数).
     *   行为等价于对元素进行 `Move` 操作.
     *   **警告:** 调用者必须确保指针有效且目标区域足够大.
     *
     * @exceptions
     *   ENil    `aSrc` 为 nil
     *   EIsZero `aElementCount` 为 0
     *   ESame   `aSrc` 和 `aDst` 指向相同的内存区域
     *}
    procedure CopyElements(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);

    {**
     * CopyElementsUnChecked
     *
     * @desc 复制元素, 能安全处理源和目标内存区域重叠的情况(快速,无检查版本).
     *
     * @params
     *   aSrc          指向源元素数组的指针.
     *   aDst          指向目标元素数组的指针.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   此操作对于托管类型是安全的 (会正确处理引用计数).
     *   行为等价于对元素进行 `Move` 操作.
     *   **警告:** 调用者必须确保指针有效且目标区域足够大.
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure CopyElementsUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);

    {**
     * CopyElementsNonOverlap
     *
     * @desc 复制元素, 它假定源和目标内存区域没有重叠.
     *
     * @params
     *   aSrc          指向第一个源元素的指针.
     *   aDst          指向第一个目标元素的指针.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   **警告: 如果源和目标内存区域有任何重叠, 结果是未定义的.**
     *   对于托管类型 (如 string, interface), 此过程会正确增加其引用计数.
     *   调用者必须确保 `aSrc` 和 `aDst` 指针有效, 且目标内存空间足以容纳 `aElementCount` 个元素.
     *
     * @exceptions
     *   ENil    `aSrc` 为 nil
     *   EIsZero `aElementCount` 为 0
     *   ESame   `aSrc` 和 `aDst` 指向相同的内存区域
     *}
    procedure CopyElementsNonOverlap(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);

    {**
     * CopyElementsNonOverlapUnChecked
     *
     * @desc 复制元素, 它假定源和目标内存区域没有重叠(快速,无检查版本).
     *
     * @params
     *   aSrc          指向第一个源元素的指针.
     *   aDst          指向第一个目标元素的指针.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   **警告: 如果源和目标内存区域有任何重叠, 结果是未定义的.**
     *   对于托管类型 (如 string, interface), 此过程会正确增加其引用计数.
     *   调用者必须确保 `aSrc` 和 `aDst` 指针有效, 且目标内存空间足以容纳 `aElementCount` 个元素.
     *   此过程不执行任何检查. 调用者必须自行保证安全
     *}
    procedure CopyElementsNonOverlapUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);

    {**
     * FillElements
     *
     * @desc 使用一个“模板”元素, 填充一块内存区域(泛型Fill).
     *
     * @params
     *   aDst          指向要填充的第一个目标元素的指针.
     *   aValue        用作填充源的模板元素.
     *   aElementCount 要填充的元素数量.
     *
     * @remark
     *   对于托管类型, 此过程会将 `aValue` 的引用计数增加 `aElementCount` 次.
     *   调用者必须确保 `aDst` 指向的内存空间足以容纳 `aElementCount` 个元素.
     *
     * @exceptions
     *   ENil    `aDst` 为 nil
     *   EIsZero `aElementCount` 为 0
     *}
    procedure FillElements(aDst: Pointer; aValue: T; aElementCount: SizeUInt);

    {**
     * ZeroElements
     *
     * @desc 将一块内存区域中的所有元素初始化为其类型的“零等价值”.
     *
     * @params
     *   aDst          指向要清零的第一个元素的指针.
     *   aElementCount 要清零的元素数量.
     *
     * @remark
     *   “零等价值”的含义取决于元素类型:
     *   - **对于托管类型 (string, interface):** 意味着释放它们 (将其设为 `nil`, 减少引用计数).
     *   - **对于非托管类型 (record, integer):** 意味着将内存按位清零 (binary zero).
     *   调用者必须确保 `aDst` 指向的内存空间足以容纳 `aElementCount` 个元素.
     *
     * @exceptions
     *   ENil    `aDst` 为 nil
     *   EIsZero `aElementCount` 为 0
     *}
    procedure ZeroElements(aDst: Pointer; aElementCount: SizeUInt);

    property ElementSize:   SizeUInt      read GetElementSize;
    property IsManagedType: Boolean       read GetIsManagedType;
    property TypeInfo:      PTypeInfo     read GetTypeInfo;
    property MemAllocator:  TMemAllocator read GetMemAllocator;

  end;

  { TElementAllocator 泛型元素分配器实现 }

  generic TElementAllocator<T> = class(TInterfacedObject, specialize IElementAllocator<T>)
  private
    FMemAllocator:  TMemAllocator;
    FElementSize:   SizeUInt;
    FIsManagedType: Boolean;
    FTypeInfo:      PTypeInfo;
  private
      procedure CopyElementsUnCheckedInternal(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  public
    constructor Create(aMemAllocator: TMemAllocator); overload;
    constructor Create; overload;

    function  GetMemAllocator: TMemAllocator; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetElementSize: SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetIsManagedType: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetTypeInfo: PTypeInfo; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure InitializeElements(aPtr: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure InitializeElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure FinalizeManagedElements(aPtr: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure FinalizeManagedElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    function  AllocElements(aElementCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  AllocElement: specialize TGenericHelper<T>.PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  ReallocElements(aDst: Pointer; aElementCount, aNewElementCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure FreeElements(aDst: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure FreeElement(aDst: Pointer); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    function  IsMemoryOverlap(aSrc, aDst: Pointer; aElementCount: SizeUInt): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure CopyElements(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure CopyElementsUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure CopyElementsNonOverlap(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure CopyElementsNonOverlapUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure FillElements(aDst: Pointer; aValue: T; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure ZeroElements(aDst: Pointer; aElementCount: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    property  ElementSize:   SizeUInt      read GetElementSize;
    property  IsManagedType: Boolean       read GetIsManagedType;
    property  TypeInfo:      PTypeInfo     read GetTypeInfo;
    property  MemAllocator:  TMemAllocator read GetMemAllocator;
  end;



type

  { 泛型遍历回调函数 }

  generic TForEachFunc<T>    = function (const aValue: T; aData: Pointer): Boolean;
  generic TForEachMethod<T>  = function (const aValue: T; aData: Pointer): Boolean of Object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  generic TForEachRefFunc<T> = reference to function (const aValue: T): Boolean;
  {$ENDIF}

  { 泛型比较回调函数 }
  generic TCompareFunc<T>    = function (const aLeft, aRight: T; aData: Pointer): SizeInt;
  generic TCompareMethod<T>  = function (const aLeft, aRight: T; aData: Pointer): SizeInt of Object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  generic TCompareRefFunc<T> = reference to function (const aLeft, aRight: T): SizeInt;
  {$ENDIF}

  { 泛型相等回调函数 }
  generic TEqualsFunc<T>    = function (const aLeft, aRight: T; aData: Pointer): Boolean;
  generic TEqualsMethod<T>  = function (const aLeft, aRight: T; aData: Pointer): Boolean of Object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  generic TEqualsRefFunc<T> = reference to function (const aLeft, aRight: T): Boolean;
  {$ENDIF}

type

  { IGenericCollection 泛型容器接口 }
  generic IGenericCollection<T> = interface(ICollection)
  ['{89825945-5184-4554-8618-465913072857}']

    function GetTypeInfo: PTypeInfo;

    {**
      * GetEnumerator
      *
      * @desc 获取容器的迭代器
      *
      * @return 迭代器
      *}
    function GetEnumerator: specialize TEnumerator<T>;

    {*
      * GetElementSize
      *
      * @desc 获取容器中元素的大小(字节)
      *
      * @return 元素大小
      *}
    function GetElementSize: SizeUInt;

    {**
    * GetIsManagedType
    *
    * @desc 判断容器中元素是否为托管类型
    *
    * @return 托管类型返回True,否则返回False
    *
    * @remark 托管类型包括 string、IInterface（接口）、以及包含托管字段的结构体等。
    *         容器会自动对托管类型进行初始化和释放，这可能带来一定的性能开销。
    *}
    function GetIsManagedType: Boolean;

    {**
     * GetElementAllocator
     *
     * @desc 获取元素分配器
     *
     * @return 元素分配器
     *
     * @remark 元素分配器用于在容器内部管理元素的内存分配和释放。
     *}
    function GetElementAllocator: specialize TElementAllocator<T>;

    {**
     * LoadFrom
     *
     * @desc 从指定数组加载元素到容器(拷贝)。
     *
     * @params
     *   aSrc 用于初始化的源数组
     *
     * @remark 此操作将重置容器大小，并从指定数组加载元素到容器
     *         如果源数组为空,则容器将清空
     *}
    procedure LoadFrom(const aSrc: array of T); overload;

    {**
     * Append
     *
     * @desc 从指定数组追加元素到容器(拷贝)
     *
     * @params
     *   aSrc 用于追加的源数组。
     *
     * @remark 此操作会扩充容器大小以包含追加的元素到尾部
     *         如果源数组为空,则什么都没发生,直接返回 True
     *}
    procedure Append(const aSrc: array of T); overload;

    {**
     * ToArray
     *
     * @desc 将容器内元素转换为数组(拷贝)
     *
     * @return 数组
     *
     * @remark 此操作会创建一个新的数组,并复制容器中的元素到新数组中
     *}
    function ToArray: specialize TGenericArray<T>;

    {**
     * ForEach
     *
     * @desc 遍历容器中的元素
     *
     * @params
     *   aForEach 遍历函数回调
     *   aData    用户自定义数据
     *
     * @return 完整遍历返回 True,否则返回 False
     *
     * @remark 
     *   此操作会遍历容器中的所有元素,并依次调用回调函数
     *   如果容器为空,则立即返回 True
     *   在回调函数中返回 False 时,遍历会立即停止,并返回 False 表示未完全遍历
     *   在回调函数中返回 True 时,遍历会继续,并返回 True 表示完全遍历
     *}
    function ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * ForEach
     *
     * @desc 遍历容器中的元素
     *
     * @params
     *   aForEach 遍历函数对象方法回调
     *   aData    用户自定义数据
     *
     * @return 完整遍历返回 True,否则返回 False
     *
     * @remark 
     *   此操作会遍历容器中的所有元素,并依次调用回调函数
     *   如果容器为空,则立即返回 True
     *   在回调函数中返回 False 时,遍历会立即停止,并返回 False 表示未完全遍历
     *   在回调函数中返回 True 时,遍历会继续,并返回 True 表示完全遍历
     *}
    function ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * ForEach
     *
     * @desc 遍历容器中的元素(匿名引用函数版本)
     *
     * @params
     *   aForEach 遍历函数回调
     *
     * @return 完整遍历返回 True,否则返回 False
     *
     * @remark 
     *   此操作会遍历容器中的所有元素,并依次调用回调函数
     *   如果容器为空,则立即返回 True
     *   在回调函数中返回 False 时,遍历会立即停止,并返回 False 表示未完全遍历
     *   在回调函数中返回 True 时,遍历会继续,并返回 True 表示完全遍历
     *   使用此接口需要在 fpc 3.3.1 及以上并且开启宏 FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES
     *}
    function ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    {**
     * Contains
     *
     * @desc 检查容器中是否包含指定的元素(默认版本)
     *
     * @params
     *   aValue 要检查的元素值
     *
     * @return 如果包含返回 True 否则返回 False
     *
     * @remark 
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object 类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   若需要自定义比较逻辑，请使用 Contains 方法的回调版本
     *   当容器为空时,立即返回 False
     *}
    function Contains(const aValue: T): Boolean; overload;

    {**
     * Contains
     *
     * @desc 检查容器中是否包含指定的元素(回调函数版本)
     *
     * @params
     *   aValue  要检查的元素值
     *   aEquals 相等回调函数
     *   aData   用户自定义数据
     *
     * @return 如果包含返回 True 否则返回 False
     *
     * @remark 
     *   回调函数中应该遵守相等返回值规则:
     *    返回 True  表示相等
     *    返回 False 表示不相等
     *
     *   当容器为空时,立即返回 False
     *}
    function Contains(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * Contains
     *
     * @desc 检查容器中是否包含指定的元素(对象方法版本)
     *
     * @params
     *   aValue  要检查的元素值
     *   aEquals 相等回调函数
     *   aData   用户自定义数据
     *
     * @return 如果包含返回 True 否则返回 False
     *
     * @remark 
     *   回调函数中应该遵守相等返回值规则:
     *    返回 True  表示相等
     *    返回 False 表示不相等
     *
     *   当容器为空时,立即返回 False
     *}
    function Contains(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Contains
     *
     * @desc 检查容器中是否包含指定的元素(匿名引用函数版本)
     *
     * @params
     *   aValue    要检查的元素值
     *   aComparer 比较回调函数
     *   aData     用户自定义数据
     *
     * @return 如果包含返回 True 否则返回 False
     *
     * @remark 
     *   回调函数中应该遵守相等返回值规则:
     *    返回 True  表示相等
     *    返回 False 表示不相等
     *
     *   当容器为空时,立即返回 False
     *   使用此接口需要在 fpc 3.3.1 及以上并且开启宏 FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES
     *}
    function Contains(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    {**
     * Reverse
     *
     * @desc 反转容器中的元素
     *}
    procedure Reverse;

    property Enumerator:       specialize TEnumerator<T>        read GetEnumerator; 
    property ElementSize:      SizeUInt                         read GetElementSize;
    property IsManagedType:    Boolean                          read GetIsManagedType;
    property ElementAllocator: specialize TElementAllocator<T>  read GetElementAllocator;
    property TypeInfo:         PTypeInfo                        read GetTypeInfo;

  end;

  { TGenericCollection 泛型容器基类 }

  generic TGenericCollection<T> = class(TCollection, specialize IGenericCollection<T>)
  type
    PGenericPtr                 = ^T;
    TCollectionElementAllocator = specialize TElementAllocator<T>;

    TEqualsFunc   = specialize TEqualsFunc<T>;
    TEqualsMethod = specialize TEqualsMethod<T>;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    TEqualsRefFunc = specialize TEqualsRefFunc<T>;
    {$ENDIF}

    TInternalEqualsMethod     = function (const aLeft, aRight: T): Boolean of object;

    TInternalEqualsMethodData = record
      success:  Boolean;
      equals: TInternalEqualsMethod;
      value:    T;
    end;
    PInternalEqualsMethodData = ^TInternalEqualsMethodData;

    TContainsFuncData = record
      success: Boolean;
      equals:  TEqualsFunc;
      value:   T;
      data:    Pointer;
    end;
    PContainsFuncData = ^TContainsFuncData;

    TContainsMethodData = record
      success: Boolean;
      equals:  TEqualsMethod;
      value:   T;
      data:    Pointer;
    end;
    PContainsMethodData = ^TContainsMethodData;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    TContainsRefFuncData = record
      success: Boolean;
      equals:  TEqualsRefFunc;
      value:   T;
    end;
    PContainsRefFuncData = ^TContainsRefFuncData;
    {$ENDIF}

    TInternalCompareMethod = function (const aLeft, aRight: T): SizeInt of object;

  protected
    FElementAllocator: TCollectionElementAllocator;
    
  { compare 内部比较回调 }
  protected
    FInternalComparer: TInternalCompareMethod;
    FElementSizeCache: SizeUInt; // 元素大小缓存
  protected
    function    DoInternalContainsForEachFunc(const aValue: T; aData: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoContainsForEachFunc(const aValue: T; aData: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoContainsForEachMethod(const aValue: T; aData: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function    DoContainsForEachRefFunc(const aValue: T; aData: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function    DoCompareBool(const aLeft, aRight: Boolean): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareChar(const aLeft, aRight: Char): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareWChar(const aLeft, aRight: WideChar): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareI8(const aLeft, aRight: Int8): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareI16(const aLeft, aRight: Int16): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareI32(const aLeft, aRight: Int32): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareI64(const aLeft, aRight: Int64): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareU8(const aLeft, aRight: UInt8): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareU16(const aLeft, aRight: UInt16): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareU32(const aLeft, aRight: UInt32): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareU64(const aLeft, aRight: UInt64): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareSingle(const aLeft, aRight: Single): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareDouble(const aLeft, aRight: Double): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareExtended(const aLeft, aRight: Extended): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareCurrency(const aLeft, aRight: Currency): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareComp(const aLeft, aRight: Comp): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareShortString(const aLeft, aRight: ShortString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareAnsiString(const aLeft, aRight: AnsiString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareWideString(const aLeft, aRight: WideString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareUnicodeString(const aLeft, aRight: UnicodeString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoComparePointer(const aLeft, aRight: Pointer): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareVariant(const aLeft, aRight: Variant): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareStr(const aLeft, aRight: String): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareMethod(const aLeft, aRight: TMethod): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareBin(const aLeft, aRight: T): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    DoCompareDynArray(const aLeft, aRight: Pointer): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  { equals 内部相等回调 }
  protected
    FInternalEquals: TInternalEqualsMethod;
  protected
    function  DoEqualsBool(const aLeft, aRight: Boolean): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsChar(const aLeft, aRight: Char): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsWChar(const aLeft, aRight: WideChar): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsI8(const aLeft, aRight: Int8): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsI16(const aLeft, aRight: Int16): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsI32(const aLeft, aRight: Int32): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsI64(const aLeft, aRight: Int64): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsU8(const aLeft, aRight: UInt8): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsU16(const aLeft, aRight: UInt16): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsU32(const aLeft, aRight: UInt32): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsU64(const aLeft, aRight: UInt64): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsSingle(const aLeft, aRight: Single): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsDouble(const aLeft, aRight: Double): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsExtended(const aLeft, aRight: Extended): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsCurrency(const aLeft, aRight: Currency): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsComp(const aLeft, aRight: Comp): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsShortString(const aLeft, aRight: ShortString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsAnsiString(const aLeft, aRight: AnsiString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsWideString(const aLeft, aRight: WideString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsUnicodeString(const aLeft, aRight: UnicodeString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsPointer(const aLeft, aRight: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsVariant(const aLeft, aRight: Variant): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsStr(const aLeft, aRight: String): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsMethod(const aLeft, aRight: TMethod): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsBin(const aLeft, aRight: T): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  DoEqualsDynArray(const aLeft, aRight: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    { hash 内部哈希回调 }

  public
    constructor Create(aAllocator: TMemAllocator); override; overload;
    constructor Create(const aSrc: array of T); overload;
    constructor Create(const aSrc: array of T; aAllocator: TMemAllocator); overload;

    destructor  Destroy; override;

    function    GetEnumerator: specialize TEnumerator<T>; virtual; abstract;
    function    GetElementSize: SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    GetIsManagedType: Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    GetTypeInfo: PTypeInfo; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function    GetElementAllocator: specialize TElementAllocator<T>; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    function    IsCompatible(aDst: TCollection): Boolean; override;
    procedure   LoadFrom(const aSrc: array of T); overload;
    procedure   Append(const aSrc: array of T); overload;

    function    ToArray: specialize TGenericArray<T>; virtual;

    function    ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; virtual; abstract; overload;
    function    ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; virtual; abstract; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function    ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean; virtual; abstract; overload;
    {$ENDIF}

    { Contains 默认情况下使用 ForEach 实现,后代容器会有不同的实现 }
    function    Contains(const aValue: T): Boolean; overload; virtual;
    function    Contains(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; virtual; overload;
    function    Contains(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; virtual; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function    Contains(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): Boolean; virtual; overload;
    {$ENDIF}

    procedure Reverse; virtual; abstract;

    property Enumerator:       specialize TEnumerator<T>        read GetEnumerator;
    property ElementSize:      SizeUInt                         read GetElementSize;
    property IsManagedType:    Boolean                          read GetIsManagedType;
    property ElementAllocator: specialize TElementAllocator<T>  read GetElementAllocator;
    property TypeInfo:         PTypeInfo                        read GetTypeInfo;
  end;

type

  generic TSortProc<T>    = procedure(const aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer) of object;
  generic TSortMethod<T>  = procedure(const aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer) of object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  generic TSortRefProc<T> = reference to procedure(const aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
  {$ENDIF}


  { IArray 泛型数组接口 提供对线性、连续元素序列进行索引访问的泛型接口 }
  generic IArray<T> = interface(specialize IGenericCollection<T>)
  ['{fe46e638-8e5f-41be-b83e-71e5e9b479c6}']

    {**
     * Get
     *
     * @desc 获取指定索引处的元素 (安全版本).
     *
     * @params
     *   aIndex 要获取的元素的索引 (0-based)
     *
     * @return 位于指定索引的元素.
     *
     * @remark 
     *   此方法进行索引越界检查.
     *   为追求极致性能, 可使用 `GetUnChecked` 或通过 `GetMemory` 直接访问指针.
     *   示例: `LValue := LArray.GetMemory[aIndex];`
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function Get(aIndex: SizeUInt): T;

    {**
     * GetUnChecked
     *
     * @desc 获取指定索引处的元素 (不安全快速版本)
     *
     * @params
     *   aIndex 要获取的元素的索引 (0-based).
     *
     * @return 位于指定索引的元素.
     *
     * @remark 
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不执行任何边界检查. 调用者必须自行确保 `aIndex` 在有效范围内.
     *   传递无效索引将导致未定义行为 (如访问冲突、数据损坏).
     *}
    function GetUnChecked(aIndex: SizeUInt): T;

    {**
     * Put
     *
     * @desc 设置指定索引处的元素 (安全版本).
     *
     * @params
     *   aIndex 要设置的元素的索引 (0-based).
     *   aValue 要设置的新元素值.
     *
     * @remark 
     *   此方法进行索引越界检查.
     *   对于托管类型, 此过程会正确处理旧元素的释放和新元素的引用计数.
     *   为追求极致性能, 可使用 `PutUnChecked` 或通过 `GetMemory` 直接访问指针.
     *   示例: `LArray.GetMemory[aIndex] := aValue;`
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    procedure Put(aIndex: SizeUInt; const aValue: T);

    {**
     * PutUnChecked
     *
     * @desc 设置指定索引处的元素(不安全快速版本)
     *
     * @params
     *   aIndex 要设置的元素的索引 (0-based).
     *   aValue 要设置的新元素值.
     *
     * @remark 
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不执行任何边界检查. 调用者必须自行确保 `aIndex` 在有效范围内.
     *   传递无效索引将导致未定义行为.
     *}
    procedure PutUnChecked(aIndex: SizeUInt; const aValue: T);

    {**
     * GetMemory
     *
     * @desc 获取指向容器内部存储区头部的直接指针.
     *
     * @return 指向第一个元素的指针. 如果容器为空, 结果是未定义的.
     *
     * @remark
     *   **警告: 返回的指针是易变的, 绝不能长期持有.**
     *   任何可能导致内存重分配的操作 (如 `Resize`, `Add`, `Insert`)都会使此指针失效.
     *   仅用于在小范围内进行高性能的连续操作.
     *}
    function GetMemory: specialize TGenericCollection<T>.PGenericPtr;

    {**
     * GetPtr
     *
     * @desc 获取指向指定索引处元素的指针 (安全版本).
     *
     * @params
     *   aIndex 目标元素的索引 (0-based).
     *
     * @return 指向指定元素的指针.
     *
     * @remark 
     *   **警告: 返回的指针是易变的, 绝不能长期持有.** (原因同 `GetMemory`).
     *   此方法进行严格的边界检查.
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function GetPtr(aIndex: SizeUInt): specialize TGenericCollection<T>.PGenericPtr;

    {**
     * GetPtrUnChecked
     *
     * @desc 获取指向指定索引处元素的指针 (不安全快速版本)
     *
     * @params
     *   aIndex 目标元素的索引 (0-based).
     *
     * @return 指向指定元素的指针.
     *
     * @remark
     *   **警告: 返回的指针是易变的, 且此方法不进行边界检查.**
     *   调用者必须自行确保 `aIndex` 有效, 且不能长期持有返回的指针.
     *}
    function GetPtrUnChecked(aIndex: SizeUInt): specialize TGenericCollection<T>.PGenericPtr;

    {**
     * Resize
     *
     * @desc 调整容器大小, 使其恰好包含指定数量的元素.
     *
     * @params
     *   aNewSize 容器的新元素数量.
     *
     * @remark
     *   若 `aNewSize < GetCount`, 多余元素将被截断 (托管类型会被正确终结).
     *   若 `aNewSize > GetCount`, 新增元素将被零初始化 (托管类型会被正确初始化).
     *   若 `aNewSize = GetCount`, 不执行任何操作.
     *   若 `aNewSize = 0`, 容器将被清空.
     *
     * @exceptions
     *   EAlloc 内存分配失败.
     *}
    procedure Resize(aNewSize: SizeUInt);

    {**
     * Ensure
     *
     * @desc 确保容器内部容量至少能容纳指定数量的元素.
     *
     * @params
     *   aCount 需要确保的最小容量 (以元素数量计).
     *
     * @remark
     *   这是一个性能优化工具, 用于在大量添加元素前一次性分配足够内存,
     *   避免连续的、小规模的内存重分配.
     *
     * @exceptions
     *   EAlloc 内存分配失败.
     *}
    procedure Ensure(aCount: SizeUInt);

    {**
     * OverWrite
     *
     * @desc 在容器内指定位置覆写一段来自外部指针的元素.
     *
     * @params
     *   aIndex        容器内开始覆写的目标索引 (0-based).
     *   aSrc          指向源数据的外部内存指针.
     *   aElementCount 要复制的元素数量
     *
     * @remark
     *   此操作严格遵守“覆写”语义, 绝不会改变容器的 `Count`.
     *   对于托管类型, 会正确处理被覆写元素的生命周期.
     *   调用者必须确保目标范围 `aIndex` 到 `aElementCount` 在容器的有效范围内.
     *   当 `aElementCount` = 0 什么也不会发生
     *
     * @exceptions
     *   ENil              `aSrc` 为 `nil` 且 `aElementCount` > 0.
     *   ERangeOutOfIndex  索引越界
     *   ERangeOutOfBounds 范围越界
     *}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * OverWriteUnChecked
     *
     * @desc 在容器内指定位置覆写一段来自外部指针的元素 (不安全快速版本).
     *
     * @params
     *   aIndex        容器内开始覆写的目标索引 (0-based).
     *   aSrc          指向源数据的外部内存指针.
     *   aElementCount 要复制的元素数量
     *
     * @remark
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不执行任何边界检查. 调用者必须自行确保 `aIndex` 和源数据长度 在有效范围内.
     *   传递无效索引将导致未定义行为.
     *}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * OverWrite
     *
     * @desc 在容器内指定位置覆写一个动态数组的全部内容.
     *
     * @params
     *   aIndex 容器内开始覆写的目标索引 (0-based).
     *   aSrc 包含源数据的动态数组.
     *
     * @remark
     *   此操作严格遵守“覆写”语义, 绝不会改变容器的 `Count`.
     *   覆写的元素数量由 `Length(aSrc)` 决定.
     *   如果 `aSrc` 为空, 此操作不产生任何效果.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引越界
     *   ERangeOutOfBounds 范围越界
     *}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: array of T); overload;

    {**
     * OverWriteUnChecked
     *
     * @desc 在容器内指定位置覆写一个动态数组的全部内容 (不安全快速版本).
     *
     * @params
     *   aIndex 容器内开始覆写的目标索引 (0-based).
     *   aSrc 包含源数据的动态数组.
     *
     * @remark
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不执行任何边界检查. 调用者必须自行确保 `aIndex` 和源数据长度 在有效范围内.
     *   传递无效索引将导致未定义行为.
     *}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T); overload; 

    {**
     * OverWrite
     *
     * @desc 在容器内指定位置覆写另一个容器的全部内容.
     *
     * @params
     *   aIndex 容器内开始覆写的目标索引 (0-based).
     *   aSrc   提供源数据的容器.
     *
     * @remark 
     *   此操作严格遵守“覆写”语义, 绝不会改变容器的 `Count`.
     *   覆写的元素数量由 `aSrc.GetCount` 决定.
     *   如果 `aSrc` 为空, 此操作不产生任何效果.
     *   aSrc 应为向兼容的容器, 否则会抛出 `ECompatible` 异常.
     *
     * @exceptions
     *   ENil              源容器为 `nil`.
     *   ECompatible    源容器与目标容器不兼容.
     *   ERangeOutOfIndex  索引越界.
     *   ERangeOutOfBounds 范围越界.
     *}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection); overload;

    {**
     * OverWrite
     *
     * @desc 在容器内指定位置覆写另一个集合的部分内容.
     *
     * @params
     *   aIndex      容器内开始覆写的目标索引 (0-based).
     *   aSrc 提供源数据的容器.
     *   aCount      要从 `aSrc` 复制并覆写的元素数量.
     *
     * @remark
     *   此操作严格遵守“覆写”语义, 绝不会改变容器的 `Count`.
     *
     * @exceptions
     *   ENil              源容器为nil.
     *   ECompatible    源容器与目标容器不兼容.
     *   ERangeOutOfIndex  索引越界.
     *   ERangeOutOfBounds 范围越界.
     *}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload;

    {**
     * OverWriteUnChecked
     *
     * @desc 在容器内指定位置覆写另一个容器的部分内容 (不安全快速版本).
     *
     * @params
     *   aIndex 容器内开始覆写的目标索引 (0-based).
     *   aSrc   提供源数据的容器.
     *   aCount 要从 `aSrc` 复制并覆写的元素数量.
     *
     * @remark
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不执行任何边界检查. 调用者必须自行确保 `aIndex` 和源数据长度 在有效范围内.
     *   传递无效索引将导致未定义行为.
     *}
    procedure OverWriteUnChecked(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; 

    {**
     * Read
     *
     * @desc 将容器内指定范围的元素复制到外部内存.
     *
     * @params
     *   aIndex        容器内开始读取的源索引 (0-based).
     *   aPtr          用于接收数据的外部内存指针.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   **警告: 调用者必须确保 `aPtr` 指向的内存区域已分配且足够大,**
     *   以容纳 `aElementCount` 个元素, 否则将导致缓冲区溢出.
     *
     * @exceptions
     *   ENil              `aPtr` 为 `nil`.
     *   ERangeOutOfIndex  索引越界.
     *   ERangeOutOfBounds 范围越界.
     *}
    procedure Read(aIndex: SizeUInt; aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * Read
     *
     * @desc 将容器内指定范围的元素读取到一个动态数组中.
     *
     * @params
     *   aIndex        容器内开始读取的源索引 (0-based).
     *   aElements     (var) 用于接收数据的动态数组..
     *   aElementCount 要读取的元素数量.
     *
     * @remark
     *   `aElements` 数组将被自动调整大小以容纳读取的数据.
     *
     * @exceptions
     *   ENil              `aElements` 为 `nil`.
     *   ERangeOutOfIndex  索引越界.
     *   ERangeOutOfBounds 范围越界.
     *}
    procedure Read(aIndex: SizeUInt; var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt); overload;

    {**
     * Fill
     *
     * @desc 使用指定值填充容器内的指定范围.
     *
     * @params
     *   aIndex        填充操作的起始索引 (0-based).
     *   aElementCount 要填充的元素数量.
     *   aValue        用于填充的元素值.
     *
     * @remark
     *   此操作会覆写范围内的现有元素.
     *   对于托管类型, 会正确处理被覆写元素的释放和新元素的引用计数.
     *
     * @exceptions
     *   EIsZero           填充数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Fill(aIndex, aElementCount: SizeUInt; const aValue: T); overload;

    {**
     * Fill
     *
     * @desc 使用指定值填充容器内的指定范围
     *
     * @params
     *   aIndex 填充操作的起始索引 (0-based).
     *   aValue 用于填充的元素值.
     *
     * @remark
     *   此操作会覆写范围内的现有元素.
     *   对于托管类型, 会正确处理被覆写元素的释放和新元素的引用计数.
     *
     * @exceptions
     *   EIsZero           填充数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Fill(aIndex: SizeUInt; const aValue: T); overload;

    {**
     * Fill
     *
     * @desc 使用指定值填充容器中的所有元素.
     *
     * @params
     *   aValue 用于填充的元素值.
     *
     * @remark
     *   此操作会覆写范围内的现有元素.
     *   对于托管类型, 会正确处理被覆写元素的释放和新元素的引用计数.
     *
     * @exceptions
     *   EEmpty            容器为空.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Fill(const aValue: T); overload;

    {**
     * Zero
     *
     * @desc 将容器内指定范围的元素写零.
     *
     * @params
     *   aIndex        写零操作的起始索引 (0-based).
     *   aElementCount 元素数量
     *
     * @remark
     *   对托管类型意味着释放 (设为 `nil`), 对值类型意味着内存按位清零.
     *
     * @exceptions
     *   EIsZero           写零数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Zero(aIndex, aElementCount: SizeUInt); overload;
    
    {**
     * Zero
     *
     * @desc 将容器内指定范围的元素写零.
     *
     * @params
     *   aIndex 写零操作的起始索引 (0-based).
     *
     * @remark
     *   对托管类型意味着释放 (设为 `nil`), 对值类型意味着内存按位清零.
     *
     * @exceptions
     *   EIsZero           写零数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Zero(aIndex: SizeUInt); overload;

    {**
     * Zero
     *
     * @desc 将容器中的所有元素写零.
     *
     * @remark
     *   对托管类型意味着释放 (设为 `nil`), 对值类型意味着内存按位清零.
     *
     * @exceptions
     *   EEmpty            容器为空.
     *}
    procedure Zero; overload;

    {**
     * Swap
     *
     * @desc 交换两个指定索引处的元素 
     *
     * @params
     *   aIndex1 第一个元素的索引 (0-based).
     *   aIndex2 第二个元素的索引 (0-based).
     *
     * @remark
     *   为追求极致性能, 可使用 `SwapUnChecked`.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ESame             索引相同.
     *}
    procedure Swap(aIndex1, aIndex2: SizeUInt); overload;

    {**
     * SwapUnChecked
     *
     * @desc 交换两个指定索引处的元素(不安全快速版本)
     *
     * @params
     *   aIndex1 第一个元素的索引 (0-based).
     *   aIndex2 第二个元素的索引 (0-based).
     *
     * @remark
     *   **警告: 仅用于性能极其关键的路径.**
     *   此方法不进行任何索引检查. 调用者必须自行确保索引有效且不相等.
     *   传递无效参数将导致未定义行为.
     *}
    procedure SwapUnChecked(aIndex1, aIndex2: SizeUInt);

    {**
     * Swap
     *
     * @desc 交换两个不重叠的、等长的元素范围
     *
     * @params
     *   aIndex1 第一个元素的索引 (0-based).
     *   aIndex2 第二个元素的索引 (0-based).
     *   aElementCount 每个范围的元素数量.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ESame             索引相同.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *   EAlloc            分配内存失败.
     *}
    procedure Swap(aIndex1, aIndex2, aElementCount: SizeUInt); overload;

    {**
     * Swap
     *
     * @desc 交换两个不重叠的、等长的元素范围
     *
     * @params
     *   aIndex1         第一个元素的索引 (0-based).
     *   aIndex2         第二个元素的索引 (0-based).
     *   aElementCount   每个范围的元素数量.
     *   aSwapBufferSize 内部交换操作使用的临时缓冲区大小 (字节).
     *
     * @remark
     *   提供更大的 `aSwapBufferSize` 通常可以提升性能, 但会增加内存消耗.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ESame             索引相同.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *   EAlloc            分配失败.
     *}
    procedure Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt); overload;

    {**
     * Copy
     *
     * @desc 在容器内部复制元素, 安全处理重叠区域.
     *
     * @params
     *   aSrcIndex     源范围的起始索引.
     *   aDstIndex     目标范围的起始索引.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   此函数的行为类似于 C 语言的 `memmove`, 能正确处理源和目标范围重叠的情况.
     *   对于托管类型, 会正确处理相关的引用计数.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ESame             源索引和目标索引相同.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);

    {**
     * CopyUnChecked
     *
     * @desc 在容器内部复制元素, 不安全快速版本
     *
     * @params
     *   aSrcIndex     源范围的起始索引.
     *   aDstIndex     目标范围的起始索引.
     *   aElementCount 要复制的元素数量.
     *
     * @remark
     *   此函数的行为类似于 C 语言的 `memmove`, 能正确处理源和目标范围重叠的情况.
     *   对于托管类型, 会正确处理相关的引用计数.
     *   此函数不进行任何索引检查. 调用者必须自行确保索引有效.
     *   传递无效参数将导致未定义行为.
     *}
    procedure CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);

    {**
     * Reverse
     *
     * @desc 从指定索引位置开始反转容器中的元素
     *
     * @params
     *   aStartIndex 反转操作的起始索引.
     *
     * @exceptions
     *   EEmpty            容器为空.
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Reverse(aStartIndex: SizeUInt); overload;

    {**
     * Reverse
     *
     * @desc 反转容器内指定范围的元素.
     *
     * @params
     *   aStartIndex 反转操作的起始索引.
     *   aCount      要反转的元素数量.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Reverse(aStartIndex, aCount: SizeUInt); overload;

    {**
     * ForEach
     *
     * @desc 从指定索引开始, 对容器的每个后续元素执行一个回调 (过程指针版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aForEach    要对每个元素执行的回调过程.
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 
     *   回调函数 `aForEach` 返回 `False` 将立即中断遍历过程.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * ForEach
     *
     * @desc 从指定索引开始, 对容器的每个后续元素执行一个回调 (对象方法版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aForEach    要对每个元素执行的回调过程.
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 
     *   回调函数 `aForEach` 返回 `False` 将立即中断遍历过程.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * ForEach
     *
     * @desc 从指定索引开始, 对容器的每个后续元素执行一个回调 (匿名方法版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aForEach    要对每个元素执行的匿名方法.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`. 
     *
     * @remark 
     *   匿名方法 `aForEach` 返回 `False` 将立即中断遍历过程.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    {**
     * ForEach
     *
     * @desc 在指定范围内, 对容器的每个元素执行一个回调 (过程指针版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aCount      要遍历的元素数量.
     *   aForEach    要对每个元素执行的回调过程.
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 
     *   如果 `aCount` 为 0, 此函数不执行任何操作并立即返回 `True`.
     *   回调过程 `aForEach` 返回 `False` 将立即中断遍历过程.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * ForEach
     *
     * @desc 在指定范围内, 对容器的每个元素执行一个回调 (对象方法版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aCount      要遍历的元素数量.
     *   aForEach    要对每个元素执行的回调过程.
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 
     *   如果 `aCount` 为 0, 此函数不执行任何操作并立即返回 `True`.
     *   回调过程 `aForEach` 返回 `False` 将立即中断遍历过程.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * ForEach
     *
     * @desc 在指定范围内, 对容器的每个元素执行一个回调 (匿名方法版本).
     *
     * @params
     *   aStartIndex 遍历的起始索引 (0-based).
     *   aCount      要遍历的元素数量.
     *   aForEach    要对每个元素执行的匿名方法.
     *
     * @return 若遍历完成 (回调从未返回 `False`), 则返回 `True`; 否则返回 `False`.
     *
     * @remark 
     *   如果 `aCount` 为 0, 此函数不执行任何操作并立即返回 `True`.
     *   匿名方法 `aForEach` 返回 `False` 将立即中断遍历过程.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    {**
     * Contains
     *
     * @desc 检查元素是否存在, 从指定索引搜索到末尾 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Contains(const aValue: T; aStartIndex: SizeUInt): Boolean; overload;

    {**
     * Contains
     *
     * @desc 检查元素是否存在, 从指定索引搜索到末尾 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * Contains
     *
     * @desc 检查元素是否存在, 从指定索引搜索到末尾 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Contains
     *
     * @desc 检查元素是否存在, 从指定索引搜索到末尾 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (匿名方法版本).
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    {**
     * Contains
     *
     * @desc 在指定范围内检查元素是否存在 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean; overload;

    {**
     * Contains
     *
     * @desc 在指定范围内检查元素是否存在 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;

    {**
     * Contains
     *
     * @desc 在指定范围内检查元素是否存在 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Contains
     *
     * @desc 在指定范围内检查元素是否存在 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (匿名方法版本).
     *
     * @return 如果找到元素, 返回 `True`; 否则返回 `False`.
     *
     * @remark
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    { 查找 }

    {**
     * Find
     *
     * @desc  从头开始搜索指定元素, 并返回其首次出现的索引 (使用默认比较器).  
     *
     * @params
     *   aValue 要搜索的元素.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *}
    function Find(const aValue: T): SizeInt; overload;

    {**
     * Find
     *
     * @desc 从头开始搜索指定元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue  要搜索的元素.
     *   aEquals 用于判断元素是否相等的自定义回调 (过程指针版本).
     *   aData   传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *}
    function Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * Find
     *
     * @desc 从头开始搜索指定元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aEquals     用于判断元素是否相等的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *}
    function Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Find
     *
     * @desc 从头开始搜索指定元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aEquals     用于判断元素是否相等的自定义回调 (匿名方法版本).
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *}
    function Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * Find
     *
     * @desc 从指定索引开始搜索元素, 并返回其首次出现的索引 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Find(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;

    {**
     * Find
     *
     * @desc 从指定索引开始搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * Find
     *
     * @desc 从指定索引开始搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Find
     *
     * @desc 从指定索引开始搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aEquals     用于判断元素是否相等的自定义回调 (匿名方法版本).
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * Find
     *
     * @desc 在指定范围内搜索元素, 并返回其首次出现的索引 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;

    {**
     * Find
     *
     * @desc 在指定范围内搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * Find
     *
     * @desc 在指定范围内搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Find
     *
     * @desc 在指定范围内搜索元素, 并返回其首次出现的索引 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aEquals     用于判断元素是否相等的自定义回调 (匿名方法版本).
     *
     * @return 如果找到, 返回元素的索引 (0-based); 否则返回 -1.
     *
     * @remark
     *   如果容器为空, 此函数不执行搜索并立即返回 -1.
     *   回调 `aEquals` 应在两元素相等时返回 `True`.
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    { 排序 }

    {**
     * Sort
     *
     * @desc 对容器中的所有元素进行排序 (使用默认比较器).
     *
     * @remark
     *   排序算法为内省排序 (Introsort), 结合了快速排序、堆排序和插入排序的优点.
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *}
    procedure Sort; overload;

    {**
     * Sort
     *
     * @desc 对容器中的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aComparer 用于比较两个元素的自定义回调 (过程指针版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    procedure Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;

    {**
     * Sort
     *
     * @desc 对容器中的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aComparer 用于比较两个元素的自定义回调 (对象方法版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    procedure Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Sort
     *
     * @desc 对容器中的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aComparer 用于比较两个元素的自定义回调 (匿名方法版本).
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *}
    procedure Sort(aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    {**
     * Sort
     *
     * @desc 对从指定索引到末尾的所有元素进行排序 (使用默认比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Sort(aStartIndex: SizeUInt); overload;

    {**
     * Sort
     *
     * @desc 对从指定索引到末尾的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;

    {**
     * Sort
     *
     * @desc 对从指定索引到末尾的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Sort
     *
     * @desc 对从指定索引到末尾的所有元素进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    {**
     * Sort
     *
     * @desc 对容器内的指定范围进行排序 (使用默认比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aCount      要排序的元素数量.
     *
     * @remark
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Sort(aStartIndex, aCount: SizeUInt); overload;

    {**
     * Sort
     *
     * @desc 对容器内的指定范围进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aCount      要排序的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;

    {**
     * Sort
     *
     * @desc 对容器内的指定范围进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aCount      要排序的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * Sort
     *
     * @desc 对容器内的指定范围进行排序 (使用自定义比较器).
     *
     * @params
     *   aStartIndex 排序范围的起始索引 (0-based).
     *   aCount      要排序的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *
     * @remark
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   EIsZero           元素数量为 0.
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    { 二分查找 }

    {**
     * BinarySearch
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue 要搜索的元素.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.** 在未排序的容器上调用, 结果是未定义的.
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *}
    function BinarySearch(const aValue: T): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (过程指针版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.** 在未排序的容器上调用, 结果是未定义的.
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    function BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (对象方法版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.** 在未排序的容器上调用, 结果是未定义的.
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    function BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearch
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (匿名方法版本).
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.** 在未排序的容器上调用, 结果是未定义的.
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *}
    function BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素 (使用自定义比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearch
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素 (使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 搜索范围的起始索引 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return 如果找到匹配元素, 返回其索引. 如果未找到, 返回-1.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   此重载版本需要在fpc 3.3.1 及以上 并启用 `FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES` 编译指令.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器大小.
     *}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    { 二分查找后端版本 }

    {**
     * BinarySearchInsert
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素, 并返回其位置或插入点.
     *
     * @params
     *   aValue 要搜索的元素.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.** 在未排序的容器上调用, 结果是未定义的.
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *}
    function BinarySearchInsert(const aValue: T): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素, 并返回其位置或插入点 (使用自定义比较器)..
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (过程指针版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素, 并返回其位置或插入点 (使用自定义比较器).
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (对象方法版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *}
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearchInsert
     *
     * @desc 在整个(有序)序列中, 使用二分搜索算法查找元素, 并返回其位置或插入点 (使用自定义比较器).
     *
     * @params
     *   aValue    要搜索的元素.
     *   aComparer 用于比较两个元素的自定义回调 (匿名方法版本).
     *   aData     传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`.
     *
     * @remark
     *   **警告: 此函数要求整个容器必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   使用此接口需要在fpc 3.3.1 及以上并且开启宏 FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES
     *}
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点(使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点(使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点(使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定后续范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点(使用默认比较器).
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求从 `aStartIndex` 到末尾的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   使用此接口需要在fpc 3.3.1 及以上并且开启宏 FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES
     *
     * @exceptions
     *   ERangeOutOfIndex 索引超出范围.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点.
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aCount      要搜索的元素数量.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求指定的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   此接口内部会根据元素类型自动选择合适的默认比较器（如数值比较、字符串比较或内存比较）
     *   对于自定义的 record 或 object类型，若未提供自定义比较器，默认行为是进行内存比较，这可能不是预期的逻辑
     *   对于复杂类型, 强烈建议使用提供自定义比较器的重载版本.
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点.
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (过程指针版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求指定的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;

    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点.
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (对象方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求指定的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    {**
     * BinarySearchInsert
     *
     * @desc 在(有序)序列的指定范围中, 使用二分搜索算法查找元素, 并返回其位置或插入点.
     *
     * @params
     *   aValue      要搜索的元素.
     *   aStartIndex 要开始查找的索引位置 (0-based).
     *   aCount      要搜索的元素数量.
     *   aComparer   用于比较两个元素的自定义回调 (匿名方法版本).
     *   aData       传递给回调过程的用户自定义数据.
     *
     * @return
     *   如果找到匹配元素, 返回其索引 (一个 `>= 0` 的值).
     *   如果未找到, 返回 `(-(插入点) - 1)`. 插入点是第一个大于 `aValue` 的元素的索引.
     *
     * @remark
     *   **警告: 此函数要求指定的范围必须是已排序的.**
     *   **返回值契约**:
     *     返回值 `>= 0` 表示找到了元素.
     *     返回值 `< 0` 表示未找到. 可以通过 `Abs(Result) - 1` 计算出正确的插入点.
     *
     *   回调函数中应该遵守比较返回值规则:
     *    * 小于0表示左值小于右值
     *    * 等于0表示左值等于右值
     *    * 大于0表示左值大于右值
     *
     *   使用此接口需要在fpc 3.3.1 及以上并且开启宏 FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES
     *
     * @exceptions
     *   ERangeOutOfIndex  索引超出范围.
     *   ERangeOutOfBounds 范围超出容器.
     *}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}


    property Items[aIndex: SizeUInt]: T                                            read Get write Put; default;
    property Ptr[aIndex: SizeUInt]:   specialize TGenericCollection<T>.PGenericPtr read GetPtr;
    property Memory:                  specialize TGenericCollection<T>.PGenericPtr read GetMemory;
  end;

const
  ARRAY_DEFAULT_SWAP_BUFFER_SIZE = 4096; // 默认交换缓冲区最大用量(字节)

type

  TSwapMethod  = procedure(const aIndex1, aIndex2: SizeUInt) of object;

  { TArray 泛型数组实现 }
  generic TArray<T> = class(specialize TGenericCollection<T>, specialize IArray<T>)
  type
    { TArrayEnumerator 迭代器 }
    TArrayEnumerator = class(specialize TEnumerator<T>, specialize IEnumerator<T>)
    private
      FArray:        specialize TArray<T>;
      FCurrentIndex: SizeUInt;
    public
      constructor Create(const aArray: specialize TArray<T>);
      function    GetCurrent: T; override;
      function    MoveNext: Boolean; override;
      procedure   Reset; override;
    end;

  const
    INSERTION_SORT_THRESHOLD = 16;
  private
    FMemory: Pointer;
    FCount:  SizeUInt;
  protected
    FSwapBufferCache:  Pointer;     // 交换内存缓冲区
    FSwapValueCache:   T;           // 交换值缓冲区
    FSwapPointerCache: PtrUInt;     // 交换指针缓冲区
    FSwapMethod:       TSwapMethod; // 交换回调
    procedure DoSwapRaw(const aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure DoSwapPtrUInt(const aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure DoSwapMove(const aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure DoQuickSort(aLeft, aRight: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure DoQuickSortFunc(aLeft, aRight: SizeUInt; aComparer: specialize TCompareFunc<T>;   aData: Pointer); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure DoQuickSortMethod(aLeft, aRight: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure DoQuickSortRefFunc(aLeft, aRight: SizeUInt; aComparer: specialize TCompareRefFunc<T>); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  protected
    function  IsOverlap(const aSrc: Pointer; aElementCount: SizeUInt): Boolean; override;
  public
    constructor Create(aCount: SizeUInt); overload;
    constructor Create(aAllocator: TMemAllocator); override; overload;
    constructor Create(aAllocator: TMemAllocator; aCount: SizeUInt); overload;
    destructor  Destroy; override;

    { 实现 ICollection<T> 接口 }
    function  GetCount: SizeUInt; override;
    procedure Clear; override;
    procedure WriteToArrayMemory(aDst: Pointer; aCount: SizeUInt); override;
    procedure LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); override; overload;
    procedure AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); override;
    procedure AppendToUnChecked(const aDst: TCollection); override;

    { 实现 IGenericCollection<T> 接口 }
    function  GetEnumerator: specialize TEnumerator<T>; override;
    procedure SaveToUnChecked(aDst: TCollection); override;
    procedure Reverse; override;
    
    function  ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; override;
    function  ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; override;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean; override; overload;
    {$ENDIF}

    function Contains(const aValue: T): Boolean; override; overload;
    function Contains(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; override; overload;
    function Contains(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; override; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Contains(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): Boolean; override; overload;
    {$ENDIF}
    

    { 实现 IArray<T> 接口 }
    function  GetMemory: PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  Get(aIndex: SizeUInt): T; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetUnChecked(aIndex: SizeUInt): T; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Put(aIndex: SizeUInt; const aValue: T); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure PutUnChecked(aIndex: SizeUInt; const aValue: T); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetPtr(aIndex: SizeUInt): PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function  GetPtrUnChecked(aIndex: SizeUInt): PGenericPtr; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Resize(aNewSize: SizeUInt);
    procedure Ensure(aCount: SizeUInt);

    procedure Fill(const aValue: T); overload;
    procedure Fill(aIndex: SizeUInt; const aValue: T); overload;
    procedure Fill(aIndex, aElementCount: SizeUInt; const aValue: T); overload;
    
    procedure Zero; overload;
    procedure Zero(aIndex: SizeUInt); overload;
    procedure Zero(aIndex, aElementCount: SizeUInt); overload;

    procedure Swap(aIndex1, aIndex2: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure SwapUnChecked(aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Swap(aIndex1, aIndex2, aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
    procedure CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);

    procedure Reverse(aStartIndex: SizeUInt); overload;
    procedure Reverse(aStartIndex, aCount: SizeUInt); virtual; overload;

    function  ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
    function  ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    function  ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
    function  ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}


    function  Contains(const aValue: T; aStartIndex: SizeUInt): Boolean; overload;
    function  Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
    function  Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    function  Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean; overload;
    function  Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
    function  Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    function Find(const aValue: T): SizeInt; overload;
    function Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function Find(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    procedure Sort; overload;
    procedure Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    procedure Sort(aStartIndex: SizeUInt); overload;
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    procedure Sort(aStartIndex, aCount: SizeUInt); overload;
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    function BinarySearch(const aValue: T): SizeInt; overload;
    function BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    procedure OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    
    procedure Read(aIndex: SizeUInt; aDst: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Read(aIndex: SizeUInt; var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    property Items[aIndex: SizeUInt]: T           read Get write Put; default;
    property Ptr[aIndex: SizeUInt]:   PGenericPtr read GetPtr;
    property Memory:                  PGenericPtr read GetMemory;
  end;

///
/// 泛型数组常用辅助函数
/// 提供常用的泛型数组辅助函数，以简化 FPC 泛型使用的繁琐性帮助提升开发效率。
///


  { makeArray 泛型数组工厂函数 }

  generic function makeArray<T>: specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeArray<T>(aCount: SizeUInt): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeArray<T>(aAllocator: TMemAllocator): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeArray<T>(aAllocator: TMemAllocator; aCount: SizeUInt): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeArray<T>(const aSrc: array of T): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeArray<T>(const aSrc: array of T; aAllocator: TMemAllocator): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeArray<T>(const aSrc: TCollection): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeArray<T>(const aSrc: TCollection; aAllocator: TMemAllocator): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeArray<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeArray<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator): specialize IArray<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}


type

  { IStack 泛型栈接口 }
  generic IStack<T> = interface(specialize IGenericCollection<T>)
  ['{b2d0130d-760b-4369-86c8-4ccd5ddac18c}']

    {**
     * Push
     *
     * @desc 从内存指针压入多个元素(拷贝)
     *
     * @params
     *   aSrc          指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 指针内存应为泛型元素数组内存
     *}
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 从数组压入元素
     *
     * @params
     *   aSrc 要压入的元素数组
     *}
    procedure Push(const aSrc: array of T); overload;

    {**
     * Push
     *
     * @desc 在末尾添加容器指定数量的元素(拷贝)
     *
     * @params
     *   aSrc          要添加的源容器
     *   aElementCount 要添加的元素数量
     *
     *
     * @remark 如果容器为空会抛出异常
     *}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 压入元素
     *
     * @params
     *   aElement 要压入栈顶的元素
     *
     * @remark 该方法将一个元素压入栈顶
     *}
    procedure Push(const aElement: T); overload;


    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素到指定内存指针处
     *
     * @params
     *   aDst          目标内存指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark
     *   需确保指针内存足够容纳指定数量的元素
     *   如果栈为空，将返回 False 且不修改 aDst
     *}
    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素拷贝到指定数组
     *
     * @params
     *   aDst          目标数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark
     *   数组会被修改长度为指定大小
     *   如果栈为空，将返回 False 且不修改 aDst
     *}
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出栈顶元素到指定变量
     *
     * @params
     *   aDst 用于存储弹出元素的变量
     *
     * @return 返回是否成功
     *
     * @remark 如果栈为空，将返回 False 且不修改 aElement
     *}
    function TryPop(var aDst: T): Boolean; overload;

    {**
     * Pop
     *
     * @desc 弹出栈顶元素
     *
     * @return 返回弹出的栈顶元素
     *
     * @remark 如果栈为空，将抛出异常
     *}
    function  Pop: T; overload;


    {**
     * TryPeekCopy
     *
     * @desc 尝试拷贝栈顶多个元素到指定内存
     *
     * @params
     *   aDst          指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 需确保指针内存足够容纳指定数量的元素
     *}
    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 获取栈顶多个元素到指定数组
     *
     * @params
     *   aDst     元素数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 数组会被修改长度为指定大小
     *}
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * Peek
     *
     * @desc 尝试获取栈顶元素但不弹出
     *
     * @params
     *  - aElement 用于存储栈顶元素的变量
     *
     * @return 返回是否成功
     *
     * @remark 如果栈为空,将返回 False 且不修改 aElement
     *}
    function  TryPeek(var aElement: T): Boolean; overload;

    {**
     * PeekRange
     *
     * @desc 获取从栈顶开始指定数量的元素的指针(容器内的指针)
     *
     * @params
     *   aElementCount 元素数量
     *
     * @return 返回指针
     *
     * @remark 如果容器为空或者元素数量大于容器数量会返回 nil
     *}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PGenericPtr; overload;

    {**
     * Peek
     *
     * @desc 获取栈顶元素但不弹出
     *
     * @return 返回栈顶元素
     *
     * @remark 如果栈为空，将抛出异常
     *}
    function  Peek: T; overload;
  end;


  { IQueue 泛型队列接口 }
  generic IQueue<T> = interface(specialize IGenericCollection<T>)
  ['{8D2A4A2F-3C7C-4E94-A763-6E2E7D6C5D37}']
    
    {**
     * Enqueue
     *
     * @desc 将元素添加到队列尾部 (入队)
     * 
     * @params
     *   aElement 要入队的元素
     *
     * @remark 该方法将一个元素添加到队列尾部。
     *}
    procedure Enqueue(const aElement: T);

    {**
     * Push (别名)
     *
     * @desc 别名: 等同于 Enqueue
     *}
    procedure Push(const aElement: T);

    {**
     * Dequeue
     *
     * @desc 移除并返回队列头部的元素 (出队)
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法移除队列头部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function  Dequeue: T; overload;

    {**
     * Pop (别名)
     *
     * @desc 别名: 等同于 Dequeue
     *}
    function  Pop: T; overload;

    {**
     * Dequeue
     *
     * @desc 尝试移除队列头部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试移除队列头部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function  Dequeue(var aElement: T): Boolean; overload;

    {**
     * Pop (别名)
     *
     * @desc 别名: 等同于 Dequeue
     *}
    function  Pop(var aElement: T): Boolean; overload; inline;

    {**
     * Peek
     *
     * @desc 获取队列头部元素但不移除
     *
     * @return 返回队列头部元素
     *
     * @remark
     *   该方法返回队列头部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function  Peek: T; overload;

    {**
     * Peek
     *
     * @desc 尝试获取队列头部元素但不移除
     *
     * @params
     *   aElement 用于存储队列头部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列头部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function Peek(var aElement: T): Boolean; overload;

  end;


  { IDeque 泛型双向队列接口 }
  generic IDeque<T> = interface(specialize IQueue<T>)
  ['{F1A2B3C4-D5E6-4F78-9A0B-1C2D3E4F5A6B}']

    {**
     * PushFront
     *
     * @desc 将泛型容器中的元素添加到队列头部(拷贝)
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将泛型容器中的每个元素依次添加到队列的头部。
     *}
    procedure PushFront(const aElements: specialize TGenericCollection<T>); overload;

    {**
     * PushFront
     *
     * @desc 将元素数组添加到队列头部
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将元素数组中的每个元素依次添加到队列的头部。
     *}
    procedure PushFront(const aElements: array of T); overload;

    {**
     * PushFront
     *
     * @desc 将元素添加到队列头部
     *
     * @params
     *   aElement 要添加的元素
     *
     * @remark 该方法将元素添加到队列的头部 (前端)。
     *}
    procedure PushFront(const aElement: T); overload;

    {**
     * PushBack
     *
     * @desc 将泛型容器中的元素添加到队列尾部(拷贝)
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将泛型容器中的每个元素依次添加到队列的尾部。
     *}
    procedure PushBack(const aElements: specialize TGenericCollection<T>); overload;

    {**
     * PushBack
     *
     * @desc 将元素数组添加到队列尾部
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将元素数组中的每个元素依次添加到队列的尾部。
     *}
    procedure PushBack(const aElements: array of T); overload;

    {**
     * PushBack
     *
     * @desc 将元素添加到队列尾部 (等同于 Enqueue)
     *
     * @params
     *  - aElement 要添加的元素
     *
     * @remark
     * 该方法等同于 Enqueue, 将元素添加到队列的尾部。
     *}
    procedure PushBack(const aElement: T); overload;

    {**
     * PopFront
     *
     * @desc 移除并返回队列头部的元素,以数组存储
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法等同于 Dequeue, 移除队列头部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function PopFront(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * PopFront (别名: Dequeue)
     *
     * @desc 尝试移除队列头部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法等同于 Dequeue, 尝试移除队列头部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PopFront(var aElement: T): Boolean; overload;

    {**
     * PopFront (别名: Dequeue)
     *
     * @desc 移除并返回队列头部的元素
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法等同于 Dequeue, 移除队列头部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function PopFront: T; overload;

    {**
     * PopBack
     *
     * @desc 尝试移除队列尾部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试移除队列尾部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PopBack(var aElement: T): Boolean; overload;

    {**
     * PopBack
     *
     * @desc 移除并返回队列尾部的元素
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法移除队列尾部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function PopBack: T; overload;
  

    {**
     * PeekFront (别名: Peek)
     *
     * @desc 获取队列头部元素但不移除
     *
     * @return 返回队列头部元素
     *
     * @remark
     *   该方法等同于 Peek, 返回队列头部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function PeekFront: T; overload;

    {**
     * PeekFront
     *
     * @desc 尝试获取队列头部元素但不移除
     *
     * @params
     *   aElement 用于存储队列头部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列头部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PeekFront(var aElement: T): Boolean; overload;

    {**
     * PeekBack
     *
     * @desc 获取队列尾部元素但不移除
     *
     * @return 返回队列尾部元素
     *
     * @remark
     *   该方法返回队列尾部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function PeekBack: T; overload;

    {**
     * PeekBack
     *
     * @desc 尝试获取队列尾部元素但不移除
     *
     * @params
     *   aElement 用于存储队列尾部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列尾部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PeekBack(var aElement: T): Boolean; overload;

  end;


  { IRingBuffer 泛型环形缓冲区接口 }
  generic IRingBuffer<T> = interface(specialize IGenericCollection<T>)
  ['{F1A2B3C4-D5E6-4F78-9A0B-1C2D3E4F5A6B}']

    {**
     * GetCapacity
     *
     * @desc 获取环形缓冲区的容量
     *
     * @return 返回环形缓冲区的容量
     *}
    function  GetCapacity: SizeUInt;

    {**
     * SetCapacity
     *
     * @desc 设置环形缓冲区的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @remark
     *   如果设置失败会抛出异常
     *   如果设置的容量小于当前元素数量会丢弃当前有效元素
     *   如果设置的容量大于当前元素数量会扩容,这可能带来一定的性能开销(数组实现)
     *}
    procedure SetCapacity(aCapacity: SizeUInt);

    {**
     * WriteMemory
     *
     * @desc 从指定指针写入元素
     *
     * @params
     *   aPtr          指针
     *   aElementCount 元素数量
     *
     * @return 返回是否写入成功
     *}
    function WriteMemory(const aPtr: Pointer; aElementCount: SizeUInt): Boolean;

    {**
     * Write
     *
     * @desc 写入元素
     *
     * @params
     *   aElement 元素
     *
     * @remark
     *   如果写入失败会抛出异常
     *   容量不足会失败
     *}
    procedure Write(const aElement: T); overload;

    {**
     * WriteArray
     *
     * @desc 写入元素数组
     *
     * @params
     *   aElements 元素数组
     *
     * @remark
     *   如果写入失败会抛出异常
     *   容量不足会失败
     *}
    procedure WriteArray(const aElements: array of T); overload;

    {**
     * WriteCollection
     *
     * @desc 写入元素集合
     *
     * @params
     *   aElements 元素集合
     *
     * @remark
     *   如果写入失败会抛出异常
     *   容量不足会失败
     *}
    procedure WriteCollection(const aElements: specialize TGenericCollection<T>); overload;

    {**
     * PeekPtr
     *
     * @desc 从指定指针读取元素但不移除
     *
     * @return 返回是否读取成功
     *
     * @remark
     *   请确保接收元素的内存指针有效并且能够容纳读取的元素数量
     *}
    function  PeekPtr: specialize TGenericCollection<T>.PGenericPtr; overload;

    {**
     * Peek
     *
     * @desc 读取元素但不移除
     *
     * @params
     *   aElement 用于存储读取的元素
     *
     * @return 返回是否读取成功
     *
     * @remark
     *   如果读取的元素数量大于当前元素数量会返回失败
     *}
    function  Peek(var aElement: T): Boolean; overload;

    {**
     * Peek
     *
     * @desc 读取元素但不移除
     *
     * @params
     *   aElements     用于存储读取的元素数组,该数组会被重设未读取的元素数量
     *   aElementCount 要读取的元素数量
     *
     * @return 返回是否读取成功
     *
     * @remark 如果读取的元素数量大于当前元素数量会返回失败
     *}
    function  Peek(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * ReadMemory
     *
     * @desc 从指定指针读取元素
     *
     * @params
     *   aPtr          接收元素的内存指针
     *   aElementCount 要读取的元素数量
     *
     * @return 返回是否读取成功
     *
     * @remark
     *   如果读取的元素数量大于当前元素数量会返回失败
     *   请确保接收元素的内存指针有效并且能够容纳读取的元素数量
     *}
    function ReadMemory(aPtr: Pointer; aElementCount: SizeUInt): Boolean;

    {**
     * Read
     *
     * @desc 读取元素
     *
     * @params
     *   aElement 用于存储读取的元素
     *
     * @return 返回是否读取成功
     *
     * @remark 如果缓冲区元素为空会返回失败
     *}
    function Read(var aElement: T): Boolean; overload;

    {**
     * ReadArray
     *
     * @desc 读取元素数组
     *
     * @params
     *   aElements     用于存储读取的元素数组,该数组会被重设未读取的元素数量
     *   aElementCount 要读取的元素数量
     *
     * @return 返回是否读取成功
     *
     * @remark
     *   如果读取的元素数量大于当前元素数量会返回失败
     *}
    function ReadArray(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * IsFull
     *
     * @desc 判断缓冲区是否满
     *
     * @return 返回是否满
     *}
    function IsFull: Boolean;

    property Capacity: SizeUInt read GetCapacity write SetCapacity;
  end;


  type

  { IGrowthStrategy 增长策略接口 }
  IGrowthStrategy = interface
    ['{CC19F814-5759-4240-8404-873FF5001198}']
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
  end;

  { TGrowthStrategy 增长策略基类 }
  TGrowthStrategy = class(TInterfacedObject, IGrowthStrategy)
  protected
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; virtual; abstract;
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; virtual;
  end;

  TGrowthStrategyClass = class of TGrowthStrategy;

  { 增长策略回调 }
  TGrowFunc    = function (aCurrentSize, aRequiredSize: SizeUInt; aData: Pointer): SizeUInt;
  TGrowMethod  = function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt of object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  TGrowRefFunc = reference to function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
  {$ENDIF}
  TGrowProxyMethod = function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt of object;

  { TCustomGrowthStrategy 自定义回调增长策略 }
  TCustomGrowthStrategy = class(TGrowthStrategy)
  private
    FData:        Pointer;
    FGrowFunc:    TGrowFunc;
    FGrowMethod:  TGrowMethod;
    FGrowRefFunc: TGrowRefFunc;
    FGrowProxy:   TGrowProxyMethod;
    function GetData: Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoGetGrowSizeFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function DoGetGrowSizeMethod(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function DoGetGrowSizeRefFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aGrowFunc: TGrowFunc; aData: Pointer);
    constructor Create(aGrowMethod: TGrowMethod; aData: Pointer);
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    constructor Create(aGrowRefFunc: TGrowRefFunc);
    {$ENDIF}

    property Data:     Pointer read GetData;
  end;

  { TCalcGrowStrategy 计算增长策略(这是个抽象类,不能直接使用) }
  TCalcGrowStrategy = class(TGrowthStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; virtual; abstract;
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  public
  end;

  { TDoublingGrowStrategy 指数增长 }
  TDoublingGrowStrategy = class(TCalcGrowStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  private
    class var FGlobal: TDoublingGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TDoublingGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  end;

  { TFixedGrowStrategy 固定线性增长 }
  TFixedGrowStrategy = class(TCalcGrowStrategy)
  private
    FFixedSize: SizeUInt;
    function GetFixedSize: SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aFixedSize: SizeUInt);

    property FixedSize: SizeUInt read GetFixedSize;
  end;

  { TFactorGrowStrategy 因子增长 }
  TFactorGrowStrategy = class(TCalcGrowStrategy)
  private
    FFactor: Single;
    function GetFactor: Single; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aFactor: Single);

    property Factor: Single read GetFactor;
  end;

  { TPowerOfTwoGrowStrategy 最近的2次幂增长 }
  TPowerOfTwoGrowStrategy = class(TGrowthStrategy)
  private
    class var FGlobal: TPowerOfTwoGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TPowerOfTwoGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  end;

  { TGoldenRatioGrowStrategy 黄金比例增长 }
  TGoldenRatioGrowStrategy = class(TCalcGrowStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  private
    class var FGlobal: TGoldenRatioGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TGoldenRatioGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  end;

  { TAlignedWrapperStrategy 对齐包装增长策略 }
  TAlignedWrapperStrategy = class(TGrowthStrategy)
  private
    FGrowStrategy: TGrowthStrategy;
    FAlignSize: SizeUInt;

    function GetGrowStrategy: TGrowthStrategy;
    function GetAlignSize: SizeUInt;
  public
    const
      DEFAULT_ALIGN_SIZE    = 64;
  public
    constructor Create(aGrowStrategy: TGrowthStrategy; aAlignSize: SizeUInt);
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;

    property GrowStrategy: TGrowthStrategy read GetGrowStrategy;
    property AlignSize: SizeUInt read GetAlignSize;
  end;

  { TExactGrowStrategy 精确增长策略 }
  TExactGrowStrategy = class(TGrowthStrategy)
  private
    class var FGlobal: TExactGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TExactGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  end;


  { IVec 泛型向量数组接口 }
  generic IVec<T> = interface(specialize IArray<T>)
  ['{e2b26f1d-c62b-4e63-b34a-d619df5acbbe}']

    {**
     * GetCapacity
     *
     * @desc 获取向量的容量
     *
     * @return 返回向量的容量
     *}
    function GetCapacity: SizeUint;

    {**
     * SetCapacity
     *
     * @desc 设置向量的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @remark 如果失败会抛出异常
     *}
    procedure SetCapacity(aCapacity: SizeUint);

    {**
     * SetCapacity2
     *
     * @desc 设置向量的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @return 如果预留成功返回 True,否则返回 False
     *}
    function SetCapacity2(aCapacity: SizeUint): Boolean;

    {**
     * GetGrowStrategy
     *
     * @desc 获取容器当前的容量增长策略.
     *
     * @return 返回向量的增长策略
     *
     * @remark
     *   增长策略决定了当容器容量不足需要扩容时, 其内部存储空间应如何扩展.
     *   这是一个影响性能和内存使用效率的关键参数.
     *}
    function GetGrowStrategy: TGrowthStrategy;

    {**
     * SetGrowStrategy
     *
     * @desc 设置容器的容量增长策略.
     *
     * @params
     *   aGrowStrategy 要设置的增长策略.
     *
     * @remark
     *   通过改变增长策略,可以调整容器在自动扩容时的行为,
     *   从而在内存使用和重新分配次数 (影响性能) 之间进行权衡.
     *
     *   如果设置为 nil,容器将恢复使用默认的 `TGoldenRatioGrowStrategy` (黄金比例增长) 增长策略.
     *   默认策略在内存占用与分配性能之间提供了较好的平衡，适用于大多数场景.
     *
     *   用户可创建自定义策略并设置到容器中，但该策略对象的生命周期由用户负责管理.
     *   框架内置的常用增长策略包括:
     *     TCustomGrowthStrategy    自定义回调增长策略.通过回调函数精细控制增长行为.
     *     TDoublingGrowStrategy    指数增长策略(容量 * 2).广泛用于大多数动态容器
     *     TFixedGrowStrategy       固定线性增长(每次 += fixedSize).内存利用率高,适用于定长批量增长场景.
     *     TFactorGrowStrategy      因子增长(容量 *= factor).可调整增长幅度.
     *     TPowerOfTwoGrowStrategy  容量扩展至不小于所需容量的最小 2 的幂次.适用于哈希表、位运算容器
     *     TGoldenRatioGrowStrategy 黄金比例增长(容量 *= 1.618).空间浪费小,适合高增长频率场景.这是框架默认的增长策略.
     *     TAlignedWrapperStrategy  对齐包装策略.可包裹任意增长策略,对齐容量至指定字节边界(需为 2 的幂),提升 CPU 预取效率.
     *     TExactGrowStrategy       精确增长策略.始终将容量扩展到恰好满足需求,不浪费空间.但分配频繁,除非对分配行为有严格控制,否则不推荐使用.
     *}
    procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);

    {**
     * TryReserve
     *
     * @desc 尝试预留额外的容量 (Count + aAdditional)
     *
     * @params
     *   aAdditional 要预留的额外容量(增加的元素数量,该接口用于确保容量足够)
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    function TryReserve(aAdditional: SizeUint): Boolean;

    {**
     * Reserve
     *
     * @desc 预留额外的容量
     *
     * @params
     *   aAdditional 要预留的额外容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    procedure Reserve(aAdditional: SizeUint);

    {**
     * TryReserveExact
     *
     * @desc 尝试预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   向量预留的空间等于请求的空间,不会按照增长策略进行分配
     *}
    function TryReserveExact(aAdditional: SizeUint): Boolean;

    {**
     * ReserveExact
     *
     * @desc 预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   向量预留的容量空间等于请求的空间,不会按照增长策略进行分配
     *}
    procedure ReserveExact(aAdditional: SizeUint);

    {**
     * Shrink
     *
     * @desc 收缩向量容量到实际使用的大小,释放多余的内存空间。
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   收缩后的容量等于当前元素数量
     *}
    procedure Shrink;

    {**
     * ShrinkTo
     *
     * @desc 收缩向量容量到指定大小,释放多余的内存空间。
     *
     * @params
     *   aCapacity 要收缩到的容量大小
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   如果指定的容量小于当前元素数量则会抛出异常(因为会截断元素)
     *   如果指定收缩的容量大于当前容量,什么也不会发生
     *}
    procedure ShrinkTo(aCapacity: SizeUint);

    {**
     * Truncate
     *
     * @desc 截断向量到指定数量,丢弃截断的元素。
     *
     * @params
     *   aCount 要截断到的数量
     *
     * @remark
     *   如果指定的数量大于当前元素数量,不会进行任何操作
     *   不会释放内存空间(不影响容量),只是修改元素数量
     *   如果需要截断元素数量并缩减容量,请使用 Truncate + Shrink
     *}
    procedure Truncate(aCount: SizeUint);

    {**
     * ResizeExact
     *
     * @desc 精确重置向量大小
     *
     * @params
     *   aNewSize 要重置的元素空间和容量大小
     *
     * @remark
     *   此接口精确改变容器大小和容量,如果新大小小于当前大小,会丢弃多余元素
     *   与 Resize 不同, Resize 按照增长策略进行扩容,而 ResizeExact 会严格按照新大小设置容量
     *
     * @exceptions
     *   EAlloc 内存分配/调整失败.
     *}
    procedure ResizeExact(aNewSize: SizeUint);


    { Write 系列接口 }

    {**
     * Write
     *
     * @desc 从指针内存写入多个元素到指定位置
     *
     * @params
     *   aIndex        要写入的元素位置
     *   aSrc          要写入的指针
     *   aElementCount 要写入的元素数量
     *
     * @remark 
     *   超出容量会自动扩容
     *   目标索引边界是Count位置,不得超越
     *
     * @exceptions
     *   ENil              `aSrc` 为 `nil`.
     *   ERangeOutOfIndex  索引越界.
     *}
    procedure Write(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入一个动态数组的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   包含源数据的动态数组.
     *
     * @remark
     *   写入的元素数量由 Length(aSrc) 决定.
     *   如果 aIndex + Length(aSrc) 超出当前 Count, 容器将被扩容.
     *   如果 aSrc 为空, 此操作不产生任何效果.
     *
     * @exceptions
     *   EAlloc           如果内存分配失败.
     *   ERangeOutOfIndex 索引越界
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: array of T); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入另一个容器的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   提供源数据的容器.
     *
     * @remark
     *   写入的元素数量由 `aSrc.GetCount` 决定.
     *   如果 `aIndex + aSrc.GetCount` 超出当前 Count, 容器将被扩容.
     *
     * @exceptions
     *   ENil             `aSrc` 为 `nil`.
     *   ERangeOutOfIndex 索引越界
     *   ESelf            `aSrc` 是容器自身.
     *   ENotCompatible   `aSrc` 与当前容器不兼容.
     *   EAlloc           如果内存分配失败.
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入另一个容器的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   提供源数据的容器.
     *   aCount 要写入的元素数量
     *
     * @remark
     *   写入的元素数量由 `aCount` 决定.
     *   如果 aIndex + aCount 超出当前 Count, 容器将被扩容.
     *   如果 aCount 为 0, 此操作不产生任何效果.
     *
     * @exceptions
     *   ENil           如果 aCollection 为 nil.
     *   ESelf          如果 aCollection 是容器自身.
     *   ENotCompatible 如果 aCollection 与当前容器不兼容.
     *   EAlloc         如果内存分配失败.
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload;

    {**
     * WriteExact
     *
     * @desc 精确将指定内存指针处的多个元素复制到容器中的指定位置(拷贝)。
     *
     * @params
     *   aIndex        要写入的索引
     *   aSrc          要写入的内存指针
     *   aElementCount 要写入的元素数量
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aPtr 必须为有效指针
     *   aElementCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素数组
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aArray 必须为有效数组
     *   aElementCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: array of T);
    
    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素容器
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aCollection 必须为有效容器
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection); overload;

    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素容器
     *   aCount 要写入的元素数量
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aCollection 必须为有效容器
     *   aCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);


    { Insert 系列接口 }

    {**
     * Insert
     *
     * @desc 在指定位置插入多个元素(从指针拷贝)
     *
     * @params
     *   aIndex        要插入的位置
     *   aSrc          要插入的指针
     *   aElementCount 要插入的元素数量
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入一个元素
     *
     * @params
     *   aIndex   要插入的位置
     *   aElement 要插入的元素
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aElement: T); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入多个元素
     *
     * @params
     *   aIndex 要插入的位置
     *   aSrc   要插入的元素数组
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aSrc: array of T); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入集合元素(拷贝)
     *
     * @params
     *   aIndex        要插入的位置
     *   aSrc          要插入的泛型容器
     *   aElementCount 要插入的元素数量
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处原来的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aSrc: TCollection; aElementCount: SizeUInt); overload;


    {**
     * Push
     *
     * @desc 在末尾添加多个元素(从指针拷贝)
     *
     * @params
     *   aSrc          指针
     *   aElementCount 元素数量
     *
     * @remark 指针内存应为泛型元素数组内存
     *}
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 在末尾添加数组元素(拷贝)
     *
     * @params
     *   aElements 元素数组
     *}
    procedure Push(const aSrc: array of T); overload;

    {**
     * Push
     *
     * @desc 在末尾添加容器指定数量的元素(拷贝)
     *
     * @params
     *   aSrc 要添加的集合
     *   aCount      要添加的元素数量
     *
     *
     * @remark 如果容器为空会抛出异常
     *}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 在末尾添加一个元素
     *
     * @params
     *   aElement 元素
     *}
    procedure Push(const aElement: T); overload;


    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出多个元素拷贝到指定指针内存
     *
     * @params
     *   aPtr          指针
     *   aElementCount 元素数量
     *
     * @return 如果成功弹出返回 True,否则返回 False
     *}
    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出多个元素拷贝到指定数组
     *
     * @params
     *   aElements     元素数组
     *   aElementCount 元素数量
     *
     * @return 如果成功移除返回 True,否则返回 False
     *}
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出单个元素拷贝到指定元素内存变量
     *
     * @params
     *   aDst 目标变量
     *
     * @return 如果成功移除返回 True,否则返回 False
      *}
      function TryPop(var aDst: T): Boolean; overload;

    {**
     * Pop
     *
     * @desc 从末尾移除一个元素
     *
     * @return 返回移除的元素
     *
     * @remark 如果容器为空会抛出异常
     *}
    function Pop: T; overload;

    {**
     * TryPeekCopy
     *
     * @desc 尝试拷贝末尾多个元素到指定指针内存
     *
     * @params
     *   aPtr          指针
     *   aElementCount 元素数量
     *
     * @return 如果成功读取返回 True,否则返回 False
     *}
    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 尝试拷贝末尾多个元素到指定数组内存
     *
     * @params
     *   aDst          元素数组
     *   aElementCount 元素数量
     *
     * @return 如果成功获取返回 True,否则返回 False
     *
     * @remark 数组会被修改长度为指定大小
     *}
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 尝试获取末尾一个元素(拷贝到指定元素内存变量)
     *
     * @params
     *   aElement 元素
     *
     * @return 如果成功获取返回 True,否则返回 False
     *}
    function TryPeek(var aElement: T): Boolean; overload;

    {**
     * PeekRange
     *
     * @desc 获取从末尾开始指定数量的元素的指针(容器内的指针)
     *
     * @params
     *   aElementCount 元素数量
     *
     * @return 返回指针
     *
     * @remark 如果容器为空或者元素数量大于容器数量会返回 nil
     *}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PGenericPtr; overload;

    {**
     * Peek
     *
     * @desc 获取末尾一个元素
     *
     * @return 返回末尾元素
     *
     * @remark 如果容器为空会抛出异常
     *}
    function Peek: T; overload;


    {**
     * Delete
     *
     * @desc 删除指定位置的元素(低效操作)
     *
     * @params
     *   aIndex        要删除的元素位置
     *   aElementCount 要删除的元素数量
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 DeleteSwap
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure Delete(aIndex, aElementCount: SizeUInt); overload;

    {**
     * Delete
     *
     * @desc 删除指定位置的元素(低效操作)
     *
     * @params
     *   aIndex 要删除的元素位置
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 DeleteSwap
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure Delete(aIndex: SizeUInt); overload;

    {**
     * DeleteSwap
     *
     * @desc 删除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex        要删除的元素位置
     *   aElementCount 要删除的元素数量
     *
     * @remark
     *   此函数是一种高效的删除操作,可以避免元素的移动,但会破坏元素的顺序,适合不敏感的场合
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure DeleteSwap(aIndex, aElementCount: SizeUInt); overload;

    {**
     * DeleteSwap
     *
     * @desc 删除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要删除的元素位置
     *
     * @remark
     *   此函数是一种高效的删除操作,可以避免元素的移动,但会破坏元素的顺序,适合不敏感的场合
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure DeleteSwap(aIndex: SizeUInt); overload;


    { 关于 Delete 和 Remove 的说明 "擦掉"和"移走" }
    

    {**
     * RemoveMemory
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定指针
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aPtr          保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 RemoveMemorySwap
     *   请确保aPtr指向的内存空间足够容纳aElementCount个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveMemory(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveMemory
     *
     * @desc 移除指定位置的元素并拷贝到指定指针
     *
     * @params
     *   aIndex 要移除的元素位置
     *   aPtr   保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 RemovePtrSwap
     *   请确保aPtr指向的内存空间足够容纳1个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveMemory(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveMemorySwap
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定指针,并用尾部元素填充删除数据的位置(交换)
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aPtr          保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   请确保aPtr指向的内存空间足够容纳aElementCount个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveMemorySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveMemorySwap
     *
     * @desc 移除指定位置的元素并拷贝到指定指针,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要移除的元素位置
     *   aPtr   保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   请确保aPtr指向的内存空间足够容纳1个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveMemorySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveArray
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定数组
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aElements     保存元素的数组变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   aElements指向的数组数量会被自动调整
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;

    {**
     * Remove
     *
     * @desc 移除指定位置的元素并拷贝到指定元素变量
     *
     * @params
     *   aIndex   要移除的元素位置
     *   aElement 保存元素的元素变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function Remove(aIndex: SizeUInt; var aElement: T): Boolean; overload;

    {**
     * Remove
     *
     * @desc 移除指定位置的元素
     *
     * @params
     *   aIndex 要移除的元素位置
     *
     * @return 返回移除的元素
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function Remove(aIndex: SizeUInt): T; overload;

    {**
     * RemoveArraySwap
     *
     * @desc 移除指定位置指定数量的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aElements     保存元素的数组变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   aElements指向的数组数量会被自动调整
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;

    {**
     * RemoveSwap
     *
     * @desc 移除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex   要移除的元素位置 autocompleted
     *   aElement 保存元素的元素变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean; overload;

    {**
     * RemoveSwap
     *
     * @desc 移除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要移除的元素位置
     *
     * @return 返回移除的元素
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveSwap(aIndex: SizeUInt): T; overload;

    property Capacity:     SizeUint        read GetCapacity     write SetCapacity;
    property GrowStrategy: TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;
  end;


type

  { TVec 向量数组实现 }
  generic TVec<T> = class(specialize TGenericCollection<T>, specialize IVec<T>, specialize IStack<T>)
  const
    VEC_DEFAULT_CAPACITY                            = 0;
  type
    TVecBuf = specialize TArray<T>;

    { TVecEnumerator 内联迭代器 }
    TVecEnumerator = class(specialize TEnumerator<T>, specialize IEnumerator<T>)
    private
      FVec:          specialize TVec<T>;
      FCurrentIndex: SizeUInt;
    public
      constructor Create(const aVec: specialize TVec<T>);
      function    GetCurrent: T;  override;
      function    MoveNext: Boolean; override;
      procedure   Reset; override;
    end;


  private
    FBuf:                  TVecBuf;
    FGrowStrategy:         TGrowthStrategy;
    FCount:                SizeUint;
  protected
    function  GetDefaultGrowStrategy: TGrowthStrategy; virtual;
  public
    constructor Create(aCapacity: SizeUint); overload;
    constructor Create(aCapacity: SizeUint; aGrowStrategy: TGrowthStrategy); overload;

    constructor Create(aAllocator: TMemAllocator); override; overload;
    constructor Create(aAllocator: TMemAllocator; aCapacity: SizeUint); overload;
    constructor Create(aAllocator: TMemAllocator; aCapacity: SizeUint; aGrowStrategy: TGrowthStrategy); overload;

    constructor Create(const aCollection: TCollection; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(const aArray: array of T; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(aPtr: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy); overload;

    destructor  Destroy; override;

    { 实现 ICollection<T> 接口 }
    function  GetCount: SizeUint; override;
    { Vec 的 Clear 不会释放已有的容量,如果希望将容量也释放,请使用 ResizeExact(0) }
    procedure Clear; override;
    procedure WriteToArrayMemory(aSrc: Pointer; aCount: SizeUInt); override;
    procedure LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); overload; override;
    procedure AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); override;

    { 实现 IGenericCollection<T> 接口 }
    function  GetEnumerator: specialize TEnumerator<T>; override;

    function  ForEach(aForEach: specialize TForEachFunc<T>;   aData: Pointer): Boolean; override;
    function  ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; override;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function  ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean; override; overload;
    {$ENDIF}

    function  Contains(const aValue: T): Boolean; override; overload;
    procedure Reverse; override;


    { 实现 IArray<T> 接口 }
    function  GetMemory: PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  Get(aIndex: SizeUint): T; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  GetUnChecked(aIndex: SizeUint): T; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Put(aIndex: SizeUint; const aValue: T); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure PutUnChecked(aIndex: SizeUint; const aValue: T); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  GetPtr(aIndex: SizeUint): PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  GetPtrUnChecked(aIndex: SizeUint): PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Resize(aNewSize: SizeUint);
    procedure Ensure(aCount: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Fill(aIndex, aElementCount: SizeUInt; const aValue: T); overload;
    procedure Fill(aIndex: SizeUInt; const aValue: T); overload;
    procedure Fill(const aValue: T); overload;
    procedure Zero(aIndex, aElementCount: SizeUInt); overload;
    procedure Zero(aIndex: SizeUInt); overload;
    procedure Zero; overload;
    procedure Swap(aIndex1, aIndex2: SizeUInt); overload;
    procedure SwapUnChecked(aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Swap(aIndex1, aIndex2, aElementCount: SizeUInt); overload;
    procedure Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt); overload;
    procedure CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt); overload;

    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
    {$ENDIF}

    function Find(const aValue: T): SizeInt; overload;
    function Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function Find(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    procedure Sort; overload;
    procedure Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    procedure Sort(aStartIndex: SizeUInt); overload;
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}

    procedure Sort(aStartIndex, aCount: SizeUInt); overload;
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
    {$ENDIF}


    function BinarySearch(const aValue: T): SizeInt; overload;
    function BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
    {$ENDIF}

    {
      关于 Write 和 OverWrite
      Write 行为会在超出容器大小时自动调整大小
      而 OverWrite 行为不会自动调整大小(超出容器大小时会抛出异常)
    }

    procedure OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Write(aIndex:SizeUInt; const aSrc:Pointer; aElementCount:SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteUnChecked(aIndex:SizeUInt; const aSrc:Pointer; aElementCount:SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Write(aIndex:SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteUnChecked(aIndex:SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteUnChecked(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure WriteExact(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload;  {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExactUnChecked(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload;  {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExact(aIndex: SizeUint; const aSrc: array of T); overload;  {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExactUnChecked(aIndex: SizeUint; const aSrc: array of T); overload;  {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure WriteExactUnChecked(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Read(aIndex:SizeUInt; aDst: Pointer; aElementCount: SizeUInt); overload;
    procedure Read(aIndex:SizeUInt; var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt); overload;

    { 实现 IStack<T> 接口 }
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Push(const aSrc: array of T); overload;
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;
    procedure Push(const aElement: T); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

    function  TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUint): Boolean; overload; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  TryPop(var aDst: T): Boolean; overload; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  Pop: T; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

    function  TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;
    function  TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
    function  TryPeek(var aElement: T): Boolean; overload;
    function  PeekRange(aElementCount: SizeUInt): PGenericPtr; overload;
    function  Peek: T; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

    { 实现 IVec<T> 接口 }
    function  GetCapacity: SizeUint;
    procedure SetCapacity(aCapacity: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  SetCapacity2(aCapacity: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  GetGrowStrategy: TGrowthStrategy; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
    procedure ResizeExact(aNewSize: SizeUint);
    function  TryReserve(aAdditional: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Reserve(aAdditional: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    function  TryReserveExact(aAdditional: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure ReserveExact(aAdditional: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Shrink; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure ShrinkTo(aCapacity: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
    procedure Truncate(aCount: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

    procedure Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Insert(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Insert(aIndex: SizeUInt; const aSrc: TCollection; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    procedure Insert(aIndex: SizeUInt; const aElement: T); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

    procedure Delete(aIndex, aElementCount: SizeUInt); overload;
    procedure Delete(aIndex: SizeUInt); overload;
    procedure DeleteSwap(aIndex, aElementCount: SizeUInt); overload;
    procedure DeleteSwap(aIndex: SizeUInt); overload;

    function  RemoveMemory(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveMemory(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveMemorySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveMemorySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
    function  RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
    function  Remove(aIndex: SizeUInt; var aElement: T): Boolean; overload;
    function  Remove(aIndex: SizeUInt): T; overload;
    function  RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean; overload;
    function  RemoveSwap(aIndex: SizeUInt): T; overload;

    

    procedure Reverse(aStartIndex: SizeUInt); overload;
    procedure Reverse(aStartIndex, aCount: SizeUInt); virtual; overload;

    function Contains(const aValue: T; aStartIndex: SizeUInt): Boolean; overload;
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean; overload;
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
    {$ENDIF}

    property Capacity:                SizeUint        read GetCapacity     write SetCapacity;
    property GrowStrategy:            TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;
    property Items[aIndex: SizeUint]: T               read Get             write Put; default;
    property Ptr[aIndex: SizeUint]:   PGenericPtr     read GetPtr;
    property Memory:                  PGenericPtr     read GetMemory;
  end;

  { makeVec 泛型向量工厂函数 }

  generic function makeVec<T>: specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(aCapacity: SizeUint): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(aCapacity: SizeUint; aGrowStrategy: TGrowthStrategy): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeVec<T>(aAllocator: TMemAllocator): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(aAllocator: TMemAllocator; aCapacity: SizeUint): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(aAllocator: TMemAllocator; aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeVec<T>(const aSrc: array of T): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: array of T; aAllocator: TMemAllocator): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: array of T; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeVec<T>(const aSrc: TCollection): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: TCollection; aAllocator: TMemAllocator): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: TCollection; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

  generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy): specialize IVec<T>; overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}


const
  VEC_DEQUE_DEFAULT_CAPACITY  = 512;
  VEC_DEQUE_DEFAULT_GROW_SIZE = 512;

// type

//   { TVecDeque 向量双向队列实现 }
//   generic TVecDeque<T> = class(specialize TGenericCollection<T>, specialize IVec<T>, specialize IDeque<T>)
//   type
//     TVecDequeBuf = specialize TArray<T>;

//     { TVecDequeEnumerator 内联迭代器 }
//     TVecDequeEnumerator = class(specialize TEnumerator<T>, specialize IEnumerator<T>)
//     private
//       FVecDeque:     specialize TVecDeque<T>;
//       FCurrentIndex: SizeUInt;
//     public
//       constructor Create(const aVecDeque: specialize TVecDeque<T>);
//       function    GetCurrent: T; override;
//       function    MoveNext: Boolean; override;
//       procedure   Reset; override;
//     end;

//   private
//     FBuf:                  TVecDequeBuf;
//     FGrowStrategy:         TGrowthStrategy;
//     FHead:                 SizeUint;
//     FCount:                SizeUint;
//     FGrowSize:             SizeUint;
  
//     function    IsFull: Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function    WrapIndex(aLogicalIndex, aCapacity: SizeUint): SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function    WrapAdd(aIndex, aAddend: SizeUint): SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function    WrapSub(aIndex, aSubtrahend: SizeUint): SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function    GetPhysicalIndex(aLogicalIndex: SizeUint): SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function    GetPhysicalPtr(aLogicalIndex: SizeUint): PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure   HandleCountChange(aNewCount: SizeUint);
//     procedure   HandleCapacityChange(aNewCapacity: SizeUint);
//     procedure   HandleCapacityIncrease(const aOldCapacity: SizeUint);
//   protected
//     function  GetDefaultGrowStrategy: TGrowthStrategy; virtual;
//   public
//     constructor Create(aAllocator: TMemAllocator; aCapacity, aGrowSize: SizeUint); overload;
//     constructor Create(aAllocator: TMemAllocator; aCapacity: SizeUint); overload;
//     constructor Create(aAllocator: TMemAllocator); override; overload;
//     constructor Create(aCapacity, aGrowSize: SizeUint); overload;
//     constructor Create(aCapacity: SizeUint); overload;
//     constructor Create; overload;

//     destructor Destroy; override;

//     function  GetHead: SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

//     { 实现 ICollection<T> 接口 }

//     function  GetCount: SizeUint; override;
//     procedure Clear; override;
//     function  WriteToArrayMemory(aPtr: Pointer; aCount: SizeUInt): Boolean; override;
//     function  LoadFromUnChecked(const aPtr: Pointer; aElementCount: SizeUInt): Boolean; override;
//     function  AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt): Boolean; override;
//     function  SaveToUnChecked(aDst: TCollection): Boolean; override;


//     { 实现 IGenericCollection<T> 接口 }
//     function GetEnumerator: specialize TEnumerator<T>; override;

//     function  ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; override;
//     function  ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; override;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function  ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean; override; overload;
//     {$ENDIF}

//     function Contains(const aValue: T): Boolean; override; overload;
//     function Contains(const aValue: T; aStartIndex: SizeUInt): Boolean; overload;
//     function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
//     function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
//     {$ENDIF}
//     function Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean; overload;
//     function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload;
//     function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload;
//     {$ENDIF}

//     procedure Reverse; override;
//     procedure Reverse(aStartIndex: SizeUInt); overload;
//     procedure Reverse(aStartIndex, aCount: SizeUInt); virtual; overload;


//     { 实现 IArray<T> 接口 }
//     function  Get(aIndex: SizeUint): T; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  GetUnChecked(aIndex: SizeUInt): T; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
//     procedure Put(aIndex: SizeUint; const aValue: T); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure PutUnChecked(aIndex: SizeUInt; const aValue: T); {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
//     function  GetPtr(aIndex: SizeUint): PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  GetPtrUnChecked(aIndex: SizeUint): specialize TGenericCollection<T>.PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  GetMemory: PGenericPtr; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Resize(aNewSize: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Ensure(aCount: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Fill(aIndex, aElementCount: SizeUInt; const aValue: T); overload;
//     procedure Fill(aIndex: SizeUInt; const aValue: T); overload;
//     procedure Fill(const aValue: T); overload;
//     procedure Zero(aIndex, aElementCount: SizeUInt); overload;
//     procedure Zero(aIndex: SizeUInt); overload;
//     procedure Zero; overload;
//     procedure Swap(aIndex1, aIndex2: SizeUInt); overload;
//     procedure SwapUnChecked(aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Swap(aIndex1, aIndex2, aElementCount: SizeUInt); overload;
//     procedure Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt); overload; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
//     procedure Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
//     procedure CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);

//     function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
//     function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
//     {$ENDIF}

//     function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean; overload;
//     function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean; overload;
//     {$ENDIF}

//     function Find(const aValue: T): SizeInt; overload;
//     function Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
//     function Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function Find(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
//     function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
//     function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
//     function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload;
//     function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     procedure Sort; overload;
//     procedure Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
//     procedure Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     procedure Sort(aComparer: specialize TCompareRefFunc<T>); overload;
//     {$ENDIF}

//     procedure Sort(aStartIndex: SizeUInt); overload;
//     procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
//     procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
//     {$ENDIF}

//     procedure Sort(aStartIndex, aCount: SizeUInt); overload;
//     procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload;
//     procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload;
//     {$ENDIF}


//     function BinarySearch(const aValue: T): SizeInt; overload;
//     function BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
//     function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
//     function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function BinarySearchInsert(const aValue: T): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload;
//     function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload;
//     {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
//     function BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload;
//     {$ENDIF}

//     { 实现 IVec<T> 接口 }
//     procedure ResizeExact(aNewSize: SizeUint);
//     function  GetCapacity: SizeUint; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure SetCapacity(aCapacity: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  SetCapacity2(aCapacity: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  GetGrowStrategy: TGrowthStrategy; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
//     function  TryReserve(aAdditional: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Reserve(aAdditional: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     function  TryReserveExact(aAdditional: SizeUint): Boolean; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure ReserveExact(aAdditional: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Shrink; {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure ShrinkTo(aCapacity: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}
//     procedure Truncate(aCount: SizeUint); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

//     function  InsertFromMemory(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  InsertFromMemory(aIndex: SizeUint; const aPtr: Pointer): Boolean; overload;
//     procedure Insert(aIndex: SizeUInt; const aElement: T); overload;
//     procedure InsertFromArray(aIndex: SizeUInt; const aElements: array of T); overload;
//     function  InsertFromCollection(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>): Boolean; overload;
//     function  InsertFromCollection(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean; overload;

//     function  PushFromMemory(const aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PushFromMemory(const aPtr: Pointer): Boolean; overload;
//     procedure PushFromArray(const aElements: array of T); overload;
//     procedure Push(const aElement: T); {$IFDEF FAFAFA_COLLECTION_INLINE}inline;{$ENDIF}

//     function  PopToMemory(aPtr: Pointer; aCount: SizeUInt): Boolean; overload;
//     function  PopToMemory(aPtr: Pointer): Boolean; overload;
//     function  PopToArray(var aArray: specialize TGenericArray<T>; aCount: SizeUint): Boolean; overload;
//     function  PeekMemory(aCount: SizeUInt): PGenericPtr; overload;
//     function  PeekMemory: PGenericPtr; overload;
//     function  PeekReadMemory(aPtr: Pointer; aElementCount: SizeUint): Boolean; overload;
//     function  PeekReadMemory(aPtr: Pointer): Boolean; overload;
//     function  PeekToArray(var aArray: specialize TGenericArray<T>; aCount: SizeUInt): Boolean; overload;

//     procedure Delete(aIndex, aElementCount: SizeUInt); overload;
//     procedure Delete(aIndex: SizeUInt); overload;
//     procedure DeleteSwap(aIndex, aElementCount: SizeUInt); overload;
//     procedure DeleteSwap(aIndex: SizeUInt); overload;

//     function  RemoveMemory(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
//     function  RemoveMemory(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
//     function  RemoveMemorySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
//     function  RemoveMemorySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
//     function  RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
//     function  Remove(aIndex: SizeUInt; var aElement: T): Boolean; overload;
//     function  Remove(aIndex: SizeUInt): T; overload;
//     function  RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
//     function  RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean; overload;
//     function  RemoveSwap(aIndex: SizeUInt): T; overload;

//     function  Write(aIndex:SizeUInt; const aPtr:Pointer; aElementCount:SizeUInt): Boolean;
//     function  WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
//     function  Write(aIndex:SizeUInt; const aElements:array of T): Boolean; overload;
//     function  WriteExact(aIndex: SizeUint; const aArray: array of T): Boolean; overload;
//     function  Write(aIndex:SizeUInt; const aCollection:specialize TGenericCollection<T>): Boolean; overload;
//     function  Write(aIndex:SizeUInt; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean; overload;
//     function  WriteExact(aIndex: SizeUint; const aCollection: specialize TGenericCollection<T>): Boolean; overload;
//     function  WriteExact(aIndex: SizeUint; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean; overload;
//     function  Read(aIndex:SizeUInt; aPtr:Pointer; aElementCount:SizeUInt): Boolean; overload;
//     function  Read(aIndex:SizeUInt; var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

//     { 实现 IQueue<T> 接口 }

//     procedure Enqueue(const aElement: T);
//     function  Dequeue: T; overload;
//     function  Dequeue(var aElement: T): Boolean; overload;
//     function  Pop: T; overload; inline;
//     function  Pop(var aElement: T): Boolean; overload; inline;
//     function  Peek: T; overload;
//     function  Peek(var aElement: T): Boolean; overload;
//     function  PushFromCollection(const aCollection: specialize TGenericCollection<T>): Boolean; overload;
//     function  PushFromCollection(const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean; overload;

//     { 实现 IDeque<T> 接口 }
//     function  PushFrontPtr(const aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PushFrontPtr(const aPtr: Pointer): Boolean; overload;
//     procedure PushFront(const aElements: specialize TGenericCollection<T>); overload;
//     procedure PushFront(const aElements: array of T); overload;
//     procedure PushFront(const aElement: T); overload;

//     function  PushBackPtr(const aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PushBackPtr(const aPtr: Pointer): Boolean; overload;
//     procedure PushBack(const aElements: specialize TGenericCollection<T>); overload;
//     procedure PushBack(const aElements: array of T); overload;
//     procedure PushBack(const aElement: T); overload;

//     function  PopFrontPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PopFrontPtr(aPtr: Pointer): Boolean; overload;
//     function  PopFront(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
//     function  PopFront(var aElement: T): Boolean; overload;
//     function  PopFront: T; overload;

//     function  PopBackPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PopBackPtr(aPtr: Pointer): Boolean; overload;
//     function  PopBack(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
//     function  PopBack(var aElement: T): Boolean; overload;
//     function  PopBack: T; overload;

//     function  PeekFrontPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean; overload;
//     function  PeekFrontPtr(aPtr: Pointer): Boolean; overload;
//     function  PeekFront: T; overload;
//     function  PeekFront(var aElement: T): Boolean; overload;
//     function  PeekFront(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

//     function  PeekBackPtr(aPtr: Pointer; aElementCount: SizeUInt): Pointer; overload;
//     function  PeekBackPtr(aPtr: Pointer): Pointer; overload;
//     function  PeekBack: T; overload;
//     function  PeekBack(var aElement: T): Boolean; overload;
//     function  PeekBack(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

//     property Capacity:                SizeUint        read GetCapacity     write SetCapacity;
//     property GrowStrategy:            TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;
//     property Items[aIndex: SizeUint]: T               read Get             write Put; default;
//     property Ptr[aIndex: SizeUint]:   PGenericPtr     read GetPtr;
//     property Memory:                  PGenericPtr     read GetMemory;
//     property Head:                    SizeUint        read GetHead;
//   end;

  { IRingBuffer 环形缓冲区接口 }

  { TRingBuffer 环形缓冲区实现 }


  { linked_node_t 单向链表节点 }

  { ILinkedList 单向链表 }

  { TLinkedList 单向链表实现 }


  { dlinked_node_t 双向链表节点 }

  { IDLinkedList 双向链表 }

  { TDLinkedList 双向链表实现 }


  { ILinkedQueue 链表队列接口 }

  { TLinkedQueue 链表队列实现 }


  { avl_node_t AVL树节点 }

  { IAVLTree AVL树接口 }

  { TAVLTree AVL树实现 }

  { IAVLTreeMap AVL树映射接口 }

  { TAVLTreeMap AVL树映射实现 }

  { IAVLTreeSet AVL树集合接口 }

  { TAVLTreeSet AVL树集合实现 }
  

  { rb_node_t 红黑树节点 }

  { IRBTree 红黑树接口 }

  { TRBTree 红黑树实现 }

  { IRBTreeMap 红黑树映射接口 }

  { TRBTreeMap 红黑树映射实现 }

  { IRBTreeSet 红黑树集合接口 }

  { TRBTreeSet 红黑树集合实现 }


  { hashmap_node_t 哈希表节点 }

  { IHashMap 哈希表 }

  { THashMap 哈希表实现 }

  { IHashSet 哈希集合接口 }

  { THashSet 哈希集合实现 }
  
 
  { IBinaryHeap 二叉堆接口 }
  
  { TBinaryHeap 二叉堆实现 }


  { btree_node_t 二叉树节点 }

  { IBTree 二叉树接口 }

  { TBTree 二叉树实现 }

  { IBTreeMap 二叉树映射接口 }

  { TBTreeMap 二叉树映射实现 }

  { IBTreeSet 二叉树集合接口 }

  { TBTreeSet 二叉树集合实现 }


function Min(a, b: SizeUInt): SizeUInt; inline;
function Max(a, b: SizeUInt): SizeUInt; inline;
function ceil(x: single): Integer; inline;
function ceil64(x: single): Int64; inline;


{ 内置比较函数 }
function compare_bool(const aLeft, aRight: Boolean): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_char(const aLeft, aRight: Char): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_wchar(const aLeft, aRight: WideChar): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_i8(const aLeft, aRight: Int8): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_i16(const aLeft, aRight: Int16): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_i32(const aLeft, aRight: Int32): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_i64(const aLeft, aRight: Int64): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_u8(const aLeft, aRight: UInt8): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_u16(const aLeft, aRight: UInt16): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_u32(const aLeft, aRight: UInt32): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_u64(const aLeft, aRight: UInt64): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_single(const aLeft, aRight: Single): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_double(const aLeft, aRight: Double): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_extended(const aLeft, aRight: Extended): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_currency(const aLeft, aRight: Currency): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_comp(const aLeft, aRight: Comp): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_shortstring(const aLeft, aRight: ShortString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_ansistring(const aLeft, aRight: AnsiString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_widestring(const aLeft, aRight: WideString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_unicodestring(const aLeft, aRight: UnicodeString): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_pointer(const aLeft, aRight: Pointer): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_bin(const aLeft, aRight: Pointer; aSize: SizeUInt): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_variant(const aLeft, aRight: Variant): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_string(const aLeft, aRight: string): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_method(const aLeft, aRight: TMethod): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function compare_dynarray(const aLeft, aRight: Pointer; aElementSize: SizeUInt): SizeInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}

{ 内置相等函数 }
function equals_bool(const aLeft, aRight: Boolean): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_char(const aLeft, aRight: Char): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_wchar(const aLeft, aRight: WideChar): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_i8(const aLeft, aRight: Int8): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_i16(const aLeft, aRight: Int16): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_i32(const aLeft, aRight: Int32): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_i64(const aLeft, aRight: Int64): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_u8(const aLeft, aRight: UInt8): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_u16(const aLeft, aRight: UInt16): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_u32(const aLeft, aRight: UInt32): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_u64(const aLeft, aRight: UInt64): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_single(const aLeft, aRight: Single): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_double(const aLeft, aRight: Double): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_extended(const aLeft, aRight: Extended): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_currency(const aLeft, aRight: Currency): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_comp(const aLeft, aRight: Comp): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_shortstring(const aLeft, aRight: ShortString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_ansistring(const aLeft, aRight: AnsiString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_widestring(const aLeft, aRight: WideString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_unicodestring(const aLeft, aRight: UnicodeString): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_pointer(const aLeft, aRight: Pointer): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_bin(const aLeft, aRight: Pointer; aSize: SizeUInt): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_variant(const aLeft, aRight: Variant): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_string(const aLeft, aRight: string): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_method(const aLeft, aRight: TMethod): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
function equals_dynarray(const aLeft, aRight: Pointer; aElementSize: SizeUInt): Boolean; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}


const
  SIZE_PTR = SizeOf(Pointer);

implementation

var
  _RTLAllocator: TMemAllocator;

{ crt }
{$IFDEF FAFAFA_COLLECTIONS_CRT}
function memcpy(aDst, aSrc : pointer; aSize : SizeUInt): Pointer; cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'memcpy';
function memmove(aDst, aSrc : pointer; aSize : SizeUInt): Pointer; cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'memmove';
{$ENDIF}

function Min(a, b: SizeUInt): SizeUInt;
begin
  if a < b then
    Result := a
  else
    Result := b;
end;

function Max(a, b: SizeUInt): SizeUInt;
begin
  if a > b then
    Result := a
  else
    Result := b;
end;

function ceil(x : single) : integer;
begin
  Result := Trunc(x)+ord(Frac(x)>0);
end;

function ceil64(x: single): Int64;
begin
  Result := Trunc(x)+ord(Frac(x)>0);
end;


{ TMemAllocator }

constructor TMemAllocator.Create(aGetMem: allocator_GetMem_t; 
  aAllocMem: allocator_AllocMem_t; aReallocMem: allocator_ReallocMem_t; 
  aFreeMem: allocator_FreeMem_t);
begin
  inherited Create;
  FGetMem     := aGetMem;
  FAllocMem   := aAllocMem;
  FReallocMem := aReallocMem;
  FFreeMem    := aFreeMem;
end;

function TMemAllocator.GetMem(aSize: SizeUInt): Pointer;
begin
  Result := FGetMem(aSize);
end;

function TMemAllocator.AllocMem(aSize: SizeUInt): Pointer;
begin
  Result := FAllocMem(aSize);
end;

function TMemAllocator.ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;
begin
  if (aDst = nil) then
  begin
    if aSize > 0 then
      exit(Self.GetMem(aSize))
    else
      exit(nil);
  end;

  if aSize = 0 then
  begin
    Self.FreeMem(aDst);
    exit(nil);
  end;

  Result := FReallocMem(aDst, aSize);
end;

procedure TMemAllocator.FreeMem(aDst: Pointer);
begin
  FFreeMem(aDst);
end;

function TMemAllocator.IsOverlap(aPtr1: Pointer; aSize1: SizeUInt; aPtr2: Pointer; aSize2: SizeUInt): Boolean;
begin
  if (aPtr1 = nil) or (aPtr2 = nil) or (aSize1 = 0) or (aSize2 = 0) then
    Exit(False);

  Result := (aPtr1 < aPtr2 + aSize2) and (aPtr2 < aPtr1 + aSize1);
end;

procedure TMemAllocator.Copy(aSrc, aDst: Pointer; aSize: SizeUInt);
begin
  if (aSrc = nil) then
    raise ENil.Create('TMemAllocator.Copy: aSrc is nil');

  if (aDst = nil) then
    raise ENil.Create('TMemAllocator.Copy: aDst is nil');

  if (aSize = 0) then
    raise EIsZero.Create('TMemAllocator.Copy: aSize is 0');

  CopyUnChecked(aSrc, aDst, aSize);
end;

procedure TMemAllocator.CopyUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt);
begin
  {$IF DEFINED(FAFAFA_COLLECTIONS_CRT)}
    memmove(aDst, aSrc, aSize);
  {$ELSE}
    Move(aSrc^, aDst^, SizeInt(aSize));
  {$IFEND}
end;

procedure TMemAllocator.CopyNonOverlap(aSrc, aDst: Pointer; aSize: SizeUInt);
begin
  if aSrc = nil then
    raise ENil.Create('TMemAllocator.CopyNonOverlap: aSrc is nil');

  if aDst = nil then
    raise ENil.Create('TMemAllocator.CopyNonOverlap: aDst is nil');

  if aSize = 0 then
    raise EIsZero.Create('TMemAllocator.CopyNonOverlap: aSize is 0');

  CopyNonOverlapUnChecked(aSrc, aDst, aSize);
end;

procedure TMemAllocator.CopyNonOverlapUnChecked(aSrc, aDst: Pointer; aSize: SizeUInt);
begin
  {$IF DEFINED(FAFAFA_COLLECTIONS_CRT)}
    memcpy(aDst, aSrc, aSize);
  {$ELSE}
    Move(aSrc^, aDst^, aSize);
  {$IFEND}
end;

procedure TMemAllocator.Fill(aDst: Pointer; aCount: SizeUInt; aValue: UInt8);
begin
  Fill8(aDst, aCount, aValue);
end;

procedure TMemAllocator.Fill8(aDst: Pointer; aCount: SizeUInt; aValue: UInt8);
begin
  if (aDst = nil) then
    raise ENil.Create('TMemAllocator.Fill8: aDst is nil');

  if (aCount = 0) then
    raise EIsZero.Create('TMemAllocator.Fill8: aCount is 0');

  FillChar(aDst^, aCount, aValue);
end;

procedure TMemAllocator.Fill16(aDst: Pointer; aCount: SizeUInt; aValue: UInt16);
begin
  if (aDst = nil) then
    raise ENil.Create('TMemAllocator.Fill16: aDst is nil');

  if (aCount = 0) then
    raise EIsZero.Create('TMemAllocator.Fill16: aCount is 0');

  FillWord(aDst^, aCount, aValue);
end;

procedure TMemAllocator.Fill32(aDst: Pointer; aCount: SizeUInt; aValue: UInt32);
begin
  if (aDst = nil) then
    raise ENil.Create('TMemAllocator.Fill32: aDst is nil');

  if (aCount = 0) then
    raise EIsZero.Create('TMemAllocator.Fill32: aCount is 0');

  FillDWord(aDst^, aCount, aValue);
end;

procedure TMemAllocator.Fill64(aDst: Pointer; aCount: SizeUInt; const aValue: UInt64);
begin
  if (aDst = nil) then
    raise ENil.Create('TMemAllocator.Fill64: aDst is nil');

  if (aCount = 0) then
    raise EIsZero.Create('TMemAllocator.Fill64: aCount is 0');

  FillQWord(aDst^, aCount, aValue);
end;

procedure TMemAllocator.Zero(aDst: Pointer; aSize: SizeUInt);
begin
  Fill8(aDst, aSize, 0);
end;


{ TElementAllocator }

constructor TElementAllocator.Create(aMemAllocator: TMemAllocator);
begin
  inherited Create;
  FMemAllocator  := aMemAllocator;
  FElementSize   := SizeOf(T);
  FIsManagedType := system.IsManagedType(T);
  FTypeInfo      := system.TypeInfo(T);
end;

constructor TElementAllocator.Create;
begin
  Create(RtlMemAllocator);
end;

function TElementAllocator.GetMemAllocator: TMemAllocator;
begin
  Result := FMemAllocator;
end;

procedure TElementAllocator.InitializeElements(aPtr: Pointer; aElementCount: SizeUInt);
begin
  if aPtr = nil then
    raise ENil.Create('TElementAllocator.InitializeElements: aPtr is nil');

  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.InitializeElements: aElementCount is 0');

  InitializeElementsUnChecked(aPtr, aElementCount);
end;

procedure TElementAllocator.InitializeElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 1 then
    Initialize(T(aPtr^))
  else
    InitializeArray(aPtr,FTypeInfo,aElementCount); // FillChar(aPtr^, aElementCount * FElementSize, 0);
end;

procedure TElementAllocator.FinalizeManagedElements(aPtr: Pointer; aElementCount: SizeUInt);
begin
  if aPtr = nil then
    raise ENil.Create('TElementAllocator.FinalizeManagedElements: aPtr is nil');

  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.FinalizeManagedElements: aElementCount is 0');

  FinalizeManagedElementsUnChecked(aPtr, aElementCount);
end;

procedure TElementAllocator.FinalizeManagedElementsUnChecked(aPtr: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 1 then
    Finalize(T(aPtr^))
  else
    FinalizeArray(aPtr, FTypeInfo, aElementCount);
end;

procedure TElementAllocator.CopyElementsNonOverlapUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);
begin
  case aElementCount of
    1:aDst[0] := aSrc[0];
    2:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
    end;
    3:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
    end;
    4:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
      aDst[3] := aSrc[3];
    end;
    5:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
      aDst[3] := aSrc[3];
      aDst[4] := aSrc[4];
    end;
    6:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
      aDst[3] := aSrc[3];
      aDst[4] := aSrc[4];
      aDst[5] := aSrc[5];
    end;
    7:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
      aDst[3] := aSrc[3];
      aDst[4] := aSrc[4];
      aDst[5] := aSrc[5];
      aDst[6] := aSrc[6];
    end;
    8:begin
      aDst[0] := aSrc[0];
      aDst[1] := aSrc[1];
      aDst[2] := aSrc[2];
      aDst[3] := aSrc[3];
      aDst[4] := aSrc[4];
      aDst[5] := aSrc[5];
      aDst[6] := aSrc[6];
      aDst[7] := aSrc[7];
    end;
  else
    if FIsManagedType then
      CopyArray(aDst, aSrc, FTypeInfo, aElementCount)
    else
      FMemAllocator.CopyUnChecked(aSrc, aDst, aElementCount * FElementSize);
  end;
end;

procedure TElementAllocator.CopyElementsUnCheckedInternal(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);
var
  LPSrcTail:        ^T;
  LPDstTail:        ^T;
  LIsReverse:       boolean;
  LOverlapCount:    SizeUInt;
  LNonOverlapCount: SizeUInt;
begin
  { 非托管类型,直接拷贝 }
  if not FIsManagedType then
  begin
    FMemAllocator.CopyUnChecked(aSrc, aDst, aElementCount * FElementSize);
    exit;
  end;

  { 托管类型 }
  LPSrcTail  := aSrc + aElementCount;
  LPDstTail  := aDst + aElementCount ;
  LIsReverse := (aSrc > aDst);

  if LIsReverse then
    LOverlapCount := LPDstTail - aSrc
    else
    LOverlapCount := LPSrcTail - aDst;

  LNonOverlapCount := aElementCount - LOverlapCount;

  { 小重叠 直接拷贝两手 }
  if LNonOverlapCount >= LOverlapCount then
  begin
    if LIsReverse then
    begin
      CopyArray(aDst, aSrc, FTypeInfo, LOverlapCount);
      CopyArray(aDst + LOverlapCount, aSrc + LOverlapCount, FTypeInfo, LNonOverlapCount);
    end
    else
    begin
      CopyArray(LPDstTail - LOverlapCount, LPSrcTail - LOverlapCount, FTypeInfo, LOverlapCount);
      CopyArray(aDst, aSrc, FTypeInfo, LNonOverlapCount);
    end;
  end
  else
  begin
    { 大重叠 }
    if LIsReverse then
    begin
      FinalizeManagedElementsUnChecked(aDst, LOverlapCount);
      FMemAllocator.CopyUnChecked(aSrc, aDst, LOverlapCount * FElementSize);
      FMemAllocator.Zero(LPDstTail - LNonOverlapCount, LNonOverlapCount * FElementSize);
      CopyArray(LPDstTail - LNonOverlapCount, LPSrcTail - LNonOverlapCount, FTypeInfo, LNonOverlapCount);
    end
    else
    begin
      FinalizeManagedElementsUnChecked(LPDstTail - LNonOverlapCount, LNonOverlapCount);
      FMemAllocator.CopyUnChecked(aSrc + LNonOverlapCount, aDst + LNonOverlapCount, LOverlapCount * FElementSize);
      FMemAllocator.Zero(aDst, LNonOverlapCount * FElementSize);
      CopyArray(aDst, aSrc, FTypeInfo, LNonOverlapCount);
    end;
  end;
end;

function TElementAllocator.GetElementSize: SizeUInt;
begin
  Result := FElementSize;
end;

function TElementAllocator.GetIsManagedType: Boolean;
begin
  Result := FIsManagedType;
end;

function TElementAllocator.GetTypeInfo: PTypeInfo;
begin
  Result := FTypeInfo;
end;

function TElementAllocator.AllocElements(aElementCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr;
begin
  if aElementCount = 0 then
    Exit(nil);

  Result := FMemAllocator.GetMem(aElementCount * FElementSize);

  if (Result <> nil) and FIsManagedType then
    InitializeElementsUnChecked(Result, aElementCount);
end;

function TElementAllocator.AllocElement: specialize TGenericHelper<T>.PGenericPtr;
begin
  Result := AllocElements(1);
end;

function TElementAllocator.ReallocElements(aDst: Pointer; aElementCount, aNewElementCount: SizeUInt): specialize TGenericHelper<T>.PGenericPtr;
begin
  { 分配 }
  if aDst = nil then
  begin
    if aNewElementCount > 0 then
      Result := AllocElements(aNewElementCount)
    else
      raise EWow.Create('TElementAllocator.ReallocElements: Where did you come from? My friend'); // 非法调用

    exit;
  end;

  { 释放 }
  if aNewElementCount = 0 then
  begin
    FreeElements(aDst, aElementCount);
    Exit(nil);
  end;
  
  { 调整 }

  { 托管元素缩小前,反初始化因缩小而放弃的内存 }
  if FIsManagedType and (aNewElementCount < aElementCount) then
    FinalizeManagedElements(Pointer(aDst + (aNewElementCount * FElementSize)), aElementCount - aNewElementCount);

  { 调整内存 }
  Result := FMemAllocator.ReallocMem(aDst, aNewElementCount * FElementSize);

  { 托管元素扩大时,初始化新扩大的内存 }
  if FIsManagedType and (aNewElementCount > aElementCount) then
    InitializeElements(Pointer(Result + aElementCount), aNewElementCount - aElementCount);
end;

procedure TElementAllocator.FreeElements(aDst: Pointer; aElementCount: SizeUInt);
begin
  if aDst = nil then
    raise ENil.Create('TElementAllocator.FreeElements: aDst is nil');

  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.FreeElements: aElementCount is 0');

  if FIsManagedType then
    FinalizeManagedElements(aDst, aElementCount);

  FMemAllocator.FreeMem(aDst);
end;

procedure TElementAllocator.FreeElement(aDst: Pointer);
begin
  FreeElements(aDst, 1);
end;

function TElementAllocator.IsMemoryOverlap(aSrc, aDst: Pointer; aElementCount: SizeUInt): Boolean;
var
  LSize: SizeUInt;
begin
  LSize  := aElementCount * FElementSize;
  Result := FMemAllocator.IsOverlap(aSrc, LSize, aDst, LSize);
end;

procedure TElementAllocator.CopyElements(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.CopyElements: aElementCount is 0');

  if aSrc = nil then
    raise ENil.Create('TElementAllocator.CopyElements: aSrc is nil');

  if aDst = nil then
    raise ENil.Create('TElementAllocator.CopyElements: aDst is nil');

  if aSrc = aDst then
    raise ESame.Create('TElementAllocator.CopyElements: aSrc and aDst are the same');

  CopyElementsUnChecked(aSrc, aDst, aElementCount);
end;

procedure TElementAllocator.CopyElementsUnChecked(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);
begin
  if (aElementCount > 1) and IsMemoryOverlap(aSrc, aDst, aElementCount) then
    CopyElementsUnCheckedInternal(aSrc, aDst, aElementCount)
  else
    CopyElementsNonOverlapUnChecked(aSrc, aDst, aElementCount); // 不重叠,直接拷贝
end;

procedure TElementAllocator.CopyElementsNonOverlap(aSrc, aDst: specialize TGenericHelper<T>.PGenericPtr; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.CopyElementsNonOverlap: aElementCount is 0');

  if aSrc = nil then
    raise ENil.Create('TElementAllocator.CopyElementsNonOverlap: aSrc is nil');

  if aDst = nil then
    raise ENil.Create('TElementAllocator.CopyElementsNonOverlap: aDst is nil');

  if aSrc = aDst then
    raise ESame.Create('TElementAllocator.CopyElementsNonOverlap: aSrc and aDst are the same');

  CopyElementsNonOverlapUnChecked(aSrc, aDst, aElementCount);
end;

procedure TElementAllocator.FillElements(aDst: Pointer; aValue: T; aElementCount: SizeUInt);
var
  LPDst:      ^T;
  LBlockSize: SizeUInt;
  LSize:      SizeUInt;
begin
  if aDst = nil then
    raise ENil.Create('TElementAllocator.FillElements: aDst is nil');

  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.FillElements: aElementCount is 0');

  if FIsManagedType or (FElementSize > 8) then
  begin
    LPDst  := aDst;
    LPDst^ := aValue;

    if aElementCount > 1 then
    begin
      LBlockSize := 1;

      while LBlockSize < aElementCount do
      begin
        LSize := Min(LBlockSize, aElementCount - LBlockSize);
        CopyElementsNonOverlapUnChecked(LPDst, Pointer(LPDst + LBlockSize), LSize);
        inc(LBlockSize, LSize);
      end;
    end;

  end
  else
  begin
    case FElementSize of
      1: FMemAllocator.Fill8(aDst, aElementCount,  PUInt8(@aValue)^);
      2: FMemAllocator.Fill16(aDst, aElementCount, PUInt16(@aValue)^);
      4: FMemAllocator.Fill32(aDst, aElementCount, PUInt32(@aValue)^);
      8: FMemAllocator.Fill64(aDst, aElementCount, PUInt64(@aValue)^);
    end;
  end;
end;

procedure TElementAllocator.ZeroElements(aDst: Pointer; aElementCount: SizeUInt);
begin
  if aDst = nil then
    raise ENil.Create('TElementAllocator.ZeroElements: aDst is nil');

  if aElementCount = 0 then
    raise EIsZero.Create('TElementAllocator.ZeroElements: aElementCount is 0');

  if FIsManagedType then
    FinalizeManagedElementsUnChecked(aDst, aElementCount)
  else
  begin
    case FElementSize of
      1: FMemAllocator.Fill8(aDst,  aElementCount, 0);
      2: FMemAllocator.Fill16(aDst, aElementCount, 0);
      4: FMemAllocator.Fill32(aDst, aElementCount, 0);
      8: FMemAllocator.Fill64(aDst, aElementCount, 0);
    else
      FMemAllocator.Zero(aDst, aElementCount * FElementSize);
    end;
  end;
end;

function rtl_getMem(aSize: PtrUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function rtl_allocMem(aSize: PtrUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function rtl_reallocMem(aPtr: Pointer; aSize: PtrUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure rtl_freeMem(aPtr: Pointer);
begin
  FreeMem(aPtr);
end;

function RtlMemAllocator: TMemAllocator;
begin
  if _RTLAllocator = nil then
    _RTLAllocator := TMemAllocator.Create(@rtl_getMem, @rtl_allocMem, @rtl_reallocMem, @rtl_freeMem);

  Result := _RTLAllocator;
end;


constructor TCollection.Create(aAllocator: TMemAllocator);
begin
  inherited Create;
  FData := nil;

  if aAllocator = nil then
    FAllocator := RtlMemAllocator
  else
    FAllocator := aAllocator;
end;

constructor TCollection.Create(const aSrc: TCollection);
begin
  Create(aSrc, aSrc.GetAllocator);
end;

constructor TCollection.Create(const aSrc: TCollection; aAllocator: TMemAllocator);
begin
  Create(aAllocator);
  LoadFrom(aSrc);
end;

constructor TCollection.Create(aSrc: Pointer; aElementCount: SizeUInt);
begin
  Create(aSrc, aElementCount, RtlMemAllocator);
end;

constructor TCollection.Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator);
begin
  Create(aAllocator);
  LoadFrom(aSrc, aElementCount);
end;

constructor TCollection.Create;
begin
  Create(RtlMemAllocator);
end;

function TCollection.GetAllocator: TMemAllocator;
begin
  Result := FAllocator;
end;

function TCollection.IsEmpty: Boolean;
begin
  Result := GetCount = 0;
end;

function TCollection.GetData: Pointer;
begin
  Result := FData;
end;

procedure TCollection.SetData(aData: Pointer);
begin
  FData := aData;
end;

function TCollection.Clone: TCollection;
begin
  Result := TCollectionClass(Self.ClassType).Create(FAllocator);
  Result.LoadFrom(Self);
end;

function TCollection.IsCompatible(aDst: TCollection): Boolean;
begin
  Result := (aDst is TCollection);
end;

procedure TCollection.LoadFrom(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if aSrc = nil then
    raise ENil.Create('TCollection.LoadFrom: Failed to load: aSrc is nil');

  if IsOverlap(aSrc, aElementCount) then
    raise EOverlap.Create('TCollection.LoadFrom: Failed to load: aSrc overlaps with current container');

  if aElementCount = 0 then
  begin
    Clear;
    exit;
  end;

  LoadFromUnChecked(aSrc, aElementCount);
end;

procedure TCollection.Append(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if aSrc = nil then
    raise ENil.Create('TCollection.Append: Failed to append: aSrc is nil');

  if aElementCount = 0 then
    Exit;

  AppendUnChecked(aSrc, aElementCount);
end;

procedure TCollection.LoadFrom(const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TCollection.LoadFrom: Failed to load: aCollection is nil');

  if aSrc = Self then
    raise ESelf.Create('TCollection.LoadFrom: Failed to load: aCollection is self');

  if not IsCompatible(aSrc) then
    raise ECompatible.Create('TCollection.LoadFrom: Failed to load: aCollection is not compatible');

  LoadFromUnChecked(aSrc);
end;

procedure TCollection.LoadFromUnChecked(const aSrc: TCollection);
begin
  aSrc.SaveToUnChecked(Self);
end;

procedure TCollection.SaveTo(aDst: TCollection);
begin
  if aDst = nil then
    raise ENil.Create('TCollection.SaveTo: Failed to save: aCollection is nil');

  if aDst = Self then
    raise ESelf.Create('TCollection.SaveTo: Failed to save: aCollection is self');

  if not IsCompatible(aDst) then
    raise ECompatible.Create('TCollection.SaveTo: Failed to save: aCollection is not compatible');

  SaveToUnChecked(aDst);
end;

procedure TCollection.SaveToUnChecked(aDst: TCollection);
begin
  aDst.Clear;

  if GetCount = 0 then
    exit;

  aDst.AppendUnChecked(Self);
end;

procedure TCollection.Append(const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TCollection.Append: Failed to append: aCollection is nil');

  if aSrc = Self then
    raise ESelf.Create('TCollection.Append: Failed to append: aCollection is self');

  if not IsCompatible(aSrc) then
    raise ECompatible.Create('TCollection.Append: Failed to append: aCollection is not compatible');

  AppendUnChecked(aSrc);
end;

procedure TCollection.AppendUnChecked(const aSrc: TCollection);
begin
  if GetCount = 0 then
    exit;

  aSrc.AppendToUnChecked(Self);
end;

procedure TCollection.AppendTo(const aDst: TCollection);
begin
  if not IsCompatible(aDst) then
    raise ECompatible.Create('TCollection.AppendTo: Failed to append: aCollection is not compatible');

  AppendToUnChecked(aDst);
end;


function TEnumerator.GetStarted: Boolean;
begin
  Result := FStarted;
end;

function compare_bool(const aLeft, aRight: Boolean): SizeInt;
begin
  if aLeft = aRight then
    Result := 0
  else if aLeft then
    Result := 1
  else
    Result := -1;
end;

function compare_char(const aLeft, aRight: Char): SizeInt;
begin
  Result := compare_u8(Ord(aLeft), Ord(aRight));
end;

function compare_wchar(const aLeft, aRight: WideChar): SizeInt;
begin
  Result := compare_u16(Ord(aLeft), Ord(aRight));
end;

function compare_i8(const aLeft, aRight: Int8): SizeInt;
begin
  Result := aLeft - aRight;
end;

function compare_i16(const aLeft, aRight: Int16): SizeInt;
begin
  Result := aLeft - aRight;
end;

function compare_i32(const aLeft, aRight: Int32): SizeInt;
begin
  {$IFDEF CPU64}
  Result := aLeft - aRight;
  {$ELSE}
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
  {$ENDIF}
end;

function compare_i64(const aLeft, aRight: Int64): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_u8(const aLeft, aRight: UInt8): SizeInt;
begin
  Result := aLeft - aRight;
end;

function compare_u16(const aLeft, aRight: UInt16): SizeInt;
begin
  Result := aLeft - aRight;
end;

function compare_u32(const aLeft, aRight: UInt32): SizeInt;
begin
  {$IFDEF CPU64}
  Result := aLeft - aRight;
  {$ELSE}
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
  {$ENDIF}
end;

function compare_u64(const aLeft, aRight: UInt64): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_single(const aLeft, aRight: Single): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_double(const aLeft, aRight: Double): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_extended(const aLeft, aRight: Extended): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_currency(const aLeft, aRight: Currency): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_comp(const aLeft, aRight: Comp): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;


function compare_variant(const aLeft, aRight: Variant): SizeInt;
var
  LLeftString, LRightString: string;
begin
  try
    case VarCompareValue(aLeft, aRight) of
      vrGreaterThan:
        Exit(1);
      vrLessThan:
        Exit(-1);
      vrEqual:
        Exit(0);
      vrNotEqual:
        if VarIsEmpty(aLeft) or VarIsNull(aLeft) then
          Exit(1)
        else
          Exit(-1);
    end;
  except
    try
      LLeftString  := aLeft;
      LRightString := aRight;
      Result := CompareStr(LLeftString, LRightString);
    except
      Result := CompareMemRange(@aLeft, @aRight, SizeOf(System.Variant));
    end;
  end;
end;

function compare_string(const aLeft, aRight: string): SizeInt;
begin
  Result := CompareStr(aLeft, aRight);
end;

function compare_method(const aLeft, aRight: TMethod): SizeInt;
begin
  Result := CompareMemRange(@aLeft, @aRight, SizeOf(TMethod));
end;

function compare_dynarray(const aLeft, aRight: Pointer; aElementSize: SizeUInt): SizeInt;
var
  LLeftLen:  SizeInt;
  LRightLen: SizeInt;
  LLen:      SizeInt;
begin
  LLeftLen := DynArraySize(aLeft);
  LRightLen := DynArraySize(aRight);

  if LLeftLen > LRightLen then
    LLen := LRightLen
  else
    LLen := LLeftLen;

  Result := CompareMemRange(aLeft, aRight, LLen * aElementSize);

  if Result = 0 then
    Result := LLeftLen - LRightLen;
end;

function compare_shortstring(const aLeft, aRight: ShortString): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_ansistring(const aLeft, aRight: AnsiString): SizeInt;
begin
  Result := AnsiCompareStr(aLeft, aRight);
end;

function compare_widestring(const aLeft, aRight: WideString): SizeInt;
begin
  Result := WideCompareStr(aLeft, aRight);
end;

function compare_unicodestring(const aLeft, aRight: UnicodeString): SizeInt;
begin
  Result := UnicodeCompareStr(aLeft, aRight);
end;

function compare_pointer(const aLeft, aRight: Pointer): SizeInt;
begin
  if aLeft > aRight then
    Result := 1
  else if aLeft < aRight then
    Result := -1
  else
    Result := 0;
end;

function compare_bin(const aLeft, aRight: Pointer; aSize: SizeUInt): SizeInt;
begin
  Result := CompareMemRange(aLeft, aRight, aSize);
end;

function equals_bool(const aLeft, aRight: Boolean): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_char(const aLeft, aRight: Char): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_wchar(const aLeft, aRight: WideChar): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_i8(const aLeft, aRight: Int8): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_i16(const aLeft, aRight: Int16): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_i32(const aLeft, aRight: Int32): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_i64(const aLeft, aRight: Int64): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_u8(const aLeft, aRight: UInt8): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_u16(const aLeft, aRight: UInt16): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_u32(const aLeft, aRight: UInt32): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_u64(const aLeft, aRight: UInt64): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_single(const aLeft, aRight: Single): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_double(const aLeft, aRight: Double): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_extended(const aLeft, aRight: Extended): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_currency(const aLeft, aRight: Currency): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_comp(const aLeft, aRight: Comp): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_shortstring(const aLeft, aRight: ShortString): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_ansistring(const aLeft, aRight: AnsiString): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_widestring(const aLeft, aRight: WideString): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_unicodestring(const aLeft, aRight: UnicodeString): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_pointer(const aLeft, aRight: Pointer): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_bin(const aLeft, aRight: Pointer; aSize: SizeUInt): Boolean;
begin
  Result := (compare_bin(aLeft, aRight, aSize) = 0);
end;

function equals_variant(const aLeft, aRight: Variant): Boolean;
begin
  Result := (VarCompareValue(aLeft, aRight) = vrEqual);
end;

function equals_string(const aLeft, aRight: string): Boolean;
begin
  Result := (aLeft = aRight);
end;

function equals_method(const aLeft, aRight: TMethod): Boolean;
begin
  Result := (aLeft.Code = aRight.Code) and (aLeft.Data = aRight.Data);
end;

function equals_dynarray(const aLeft, aRight: Pointer; aElementSize: SizeUInt): Boolean;
var
  LLen: SizeInt;
begin
  LLen := DynArraySize(aLeft);
  Result := (LLen = DynArraySize(aRight)) and (compare_bin(aLeft, aRight, LLen * aElementSize) = 0);
end;


function TGenericCollection.DoCompareBool(const aLeft, aRight: Boolean): SizeInt;
begin
  Result := compare_bool(aLeft, aRight);
end;

function TGenericCollection.DoCompareChar(const aLeft, aRight: Char): SizeInt;
begin
  Result := compare_char(aLeft, aRight);
end;

function TGenericCollection.DoCompareWChar(const aLeft, aRight: WideChar): SizeInt;
begin
  Result := compare_wchar(aLeft, aRight);
end;

function TGenericCollection.DoCompareI8(const aLeft, aRight: Int8): SizeInt;
begin
  Result := compare_i8(aLeft, aRight);
end;

function TGenericCollection.DoCompareI16(const aLeft, aRight: Int16): SizeInt;
begin
  Result := compare_i16(aLeft, aRight);
end;

function TGenericCollection.DoCompareI32(const aLeft, aRight: Int32): SizeInt;
begin
  Result := compare_i32(aLeft, aRight);
end;

function TGenericCollection.DoCompareI64(const aLeft, aRight: Int64): SizeInt;
begin
  Result := compare_i64(aLeft, aRight);
end;

function TGenericCollection.DoCompareU8(const aLeft, aRight: UInt8): SizeInt;
begin
  Result := compare_u8(aLeft, aRight);
end;

function TGenericCollection.DoCompareU16(const aLeft, aRight: UInt16): SizeInt;
begin
  Result := compare_u16(aLeft, aRight);
end;

function TGenericCollection.DoCompareU32(const aLeft, aRight: UInt32): SizeInt;
begin
  Result := compare_u32(aLeft, aRight);
end;

function TGenericCollection.DoCompareU64(const aLeft, aRight: UInt64): SizeInt;
begin
  Result := compare_u64(aLeft, aRight);
end;

function TGenericCollection.DoCompareSingle(const aLeft, aRight: Single): SizeInt;
begin
  Result := compare_single(aLeft, aRight);
end;

function TGenericCollection.DoCompareDouble(const aLeft, aRight: Double): SizeInt;
begin
  Result := compare_double(aLeft, aRight);
end;

function TGenericCollection.DoCompareExtended(const aLeft, aRight: Extended): SizeInt;
begin
  Result := compare_extended(aLeft, aRight);
end;

function TGenericCollection.DoCompareCurrency(const aLeft, aRight: Currency): SizeInt;
begin
  Result := compare_currency(aLeft, aRight);
end;

function TGenericCollection.DoCompareComp(const aLeft, aRight: Comp): SizeInt;
begin
  Result := compare_comp(aLeft, aRight);
end;

function TGenericCollection.DoCompareShortString(const aLeft, aRight: ShortString): SizeInt;
begin
  Result := compare_shortstring(aLeft, aRight);
end;

function TGenericCollection.DoCompareAnsiString(const aLeft, aRight: AnsiString): SizeInt;
begin
  Result := compare_ansistring(aLeft, aRight);
end;

function TGenericCollection.DoCompareWideString(const aLeft, aRight: WideString): SizeInt;
begin
  Result := compare_widestring(aLeft, aRight);
end;

function TGenericCollection.DoCompareUnicodeString(const aLeft, aRight: UnicodeString): SizeInt;
begin
  Result := compare_unicodestring(aLeft, aRight);
end;

function TGenericCollection.DoComparePointer(const aLeft, aRight: Pointer): SizeInt;
begin
  Result := compare_pointer(aLeft, aRight);
end;

function TGenericCollection.DoCompareVariant(const aLeft, aRight: Variant): SizeInt;
begin
  Result := compare_variant(aLeft, aRight);
end;

function TGenericCollection.DoCompareBin(const aLeft, aRight: T): SizeInt;
begin
  Result := compare_bin(@aLeft, @aRight, FElementSizeCache);
end;

function TGenericCollection.DoCompareDynArray(const aLeft, aRight: Pointer): SizeInt;
begin
  Result := compare_dynarray(aLeft, aRight, FElementSizeCache);
end;

function TGenericCollection.DoCompareStr(const aLeft, aRight: String): SizeInt;
begin
  Result := compare_string(aLeft, aRight);
end;

function TGenericCollection.DoCompareMethod(const aLeft, aRight: TMethod): SizeInt;
begin
  Result := compare_method(aLeft, aRight);
end;

function TGenericCollection.DoEqualsBool(const aLeft, aRight: Boolean): Boolean;
begin
  Result := equals_bool(aLeft, aRight);
end;

function TGenericCollection.DoEqualsChar(const aLeft, aRight: Char): Boolean;
begin
  Result := equals_char(aLeft, aRight);
end;

function TGenericCollection.DoEqualsWChar(const aLeft, aRight: WideChar): Boolean;
begin
  Result := equals_wchar(aLeft, aRight);
end;

function TGenericCollection.DoEqualsI8(const aLeft, aRight: Int8): Boolean;
begin
  Result := equals_i8(aLeft, aRight);
end;

function TGenericCollection.DoEqualsI16(const aLeft, aRight: Int16): Boolean;
begin
  Result := equals_i16(aLeft, aRight);
end;

function TGenericCollection.DoEqualsI32(const aLeft, aRight: Int32): Boolean;
begin
  Result := equals_i32(aLeft, aRight);
end;

function TGenericCollection.DoEqualsI64(const aLeft, aRight: Int64): Boolean;
begin
  Result := equals_i64(aLeft, aRight);
end;

function TGenericCollection.DoEqualsU8(const aLeft, aRight: UInt8): Boolean;
begin
  Result := equals_u8(aLeft, aRight);
end;

function TGenericCollection.DoEqualsU16(const aLeft, aRight: UInt16): Boolean;
begin
  Result := equals_u16(aLeft, aRight);
end;

function TGenericCollection.DoEqualsU32(const aLeft, aRight: UInt32): Boolean;
begin
  Result := equals_u32(aLeft, aRight);
end;

function TGenericCollection.DoEqualsU64(const aLeft, aRight: UInt64): Boolean;
begin
  Result := equals_u64(aLeft, aRight);
end;

function TGenericCollection.DoEqualsSingle(const aLeft, aRight: Single): Boolean;
begin
  Result := equals_single(aLeft, aRight);
end;

function TGenericCollection.DoEqualsDouble(const aLeft, aRight: Double): Boolean;
begin
  Result := equals_double(aLeft, aRight);
end;

function TGenericCollection.DoEqualsExtended(const aLeft, aRight: Extended): Boolean;
begin
  Result := equals_extended(aLeft, aRight);
end;

function TGenericCollection.DoEqualsCurrency(const aLeft, aRight: Currency): Boolean;
begin
  Result := equals_currency(aLeft, aRight);
end;

function TGenericCollection.DoEqualsComp(const aLeft, aRight: Comp): Boolean;
begin
  Result := equals_comp(aLeft, aRight);
end;

function TGenericCollection.DoEqualsShortString(const aLeft, aRight: ShortString): Boolean;
begin
  Result := equals_shortstring(aLeft, aRight);
end;

function TGenericCollection.DoEqualsAnsiString(const aLeft, aRight: AnsiString): Boolean;
begin
  Result := equals_ansistring(aLeft, aRight);
end;

function TGenericCollection.DoEqualsWideString(const aLeft, aRight: WideString): Boolean;
begin
  Result := equals_widestring(aLeft, aRight);
end;

function TGenericCollection.DoEqualsUnicodeString(const aLeft, aRight: UnicodeString): Boolean;
begin
  Result := equals_unicodestring(aLeft, aRight);
end;

function TGenericCollection.DoEqualsPointer(const aLeft, aRight: Pointer): Boolean;
begin
  Result := equals_pointer(aLeft, aRight);
end;

function TGenericCollection.DoEqualsVariant(const aLeft, aRight: Variant): Boolean;
begin
  Result := equals_variant(aLeft, aRight);
end;

function TGenericCollection.DoEqualsStr(const aLeft, aRight: String): Boolean;
begin
  Result := equals_string(aLeft, aRight);
end;

function TGenericCollection.DoEqualsMethod(const aLeft, aRight: TMethod): Boolean;
begin
  Result := equals_method(aLeft, aRight);
end;

function TGenericCollection.DoEqualsBin(const aLeft, aRight: T): Boolean;
begin
  Result := equals_bin(@aLeft, @aRight, FElementSizeCache);
end;

function TGenericCollection.DoEqualsDynArray(const aLeft, aRight: Pointer): Boolean;
begin
  Result := equals_dynarray(aLeft, aRight, FElementSizeCache);
end;


constructor TGenericCollection.Create(aAllocator: TMemAllocator);
var
  LTypeInfo: PTypeInfo;
begin
  inherited Create(aAllocator);
  FElementAllocator := TCollectionElementAllocator.Create(GetAllocator);
  FElementSizeCache := GetElementSize;

  LTypeInfo := GetTypeInfo;

  case LTypeInfo^.Kind of
    tkBool:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareBool);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsBool);
    end;
    tkChar:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareChar);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsChar);
    end;
    tkWChar:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareWChar);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsWChar);
    end;
    tkInteger:
    begin
      if LTypeInfo = system.typeinfo(Int8) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareI8);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsI8);
      end
      else if LTypeInfo = system.typeinfo(Int16) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareI16);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsI16);
      end
      else if LTypeInfo = system.typeinfo(Int32) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareI32);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsI32);
      end
      else if LTypeInfo = system.typeinfo(UInt8) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareU8);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsU8);
      end
      else if LTypeInfo = system.typeinfo(UInt16) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareU16);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsU16);
      end
      else if LTypeInfo = system.typeinfo(UInt32) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareU32);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsU32);
      end
    end;
    tkInt64:
    begin
      if LTypeInfo = system.typeinfo(Int64) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareI64);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsI64);
      end
      else if LTypeInfo = system.typeinfo(Comp) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareComp);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsComp);
      end;
    end;
    tkQWord:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareU64);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsU64);
    end;
    tkFloat:
    begin
      if LTypeInfo = system.typeinfo(Single) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareSingle);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsSingle);
      end
      else if LTypeInfo = system.typeinfo(Double) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareDouble);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsDouble);
      end
      else if LTypeInfo = system.typeinfo(Extended) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareExtended);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsExtended);
      end
      else if LTypeInfo = system.typeinfo(Currency) then
      begin
        FInternalComparer := TInternalCompareMethod(@DoCompareCurrency);
        FInternalEquals   := TInternalEqualsMethod(@DoEqualsCurrency);
      end;
    end;
    tkSString:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareShortString);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsShortString);
    end;
    tkLString,tkAString:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareAnsiString);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsAnsiString);
    end;
    tkUString:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareUnicodeString);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsUnicodeString);
    end;
    tkWString:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareWideString);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsWideString);
    end;
    tkVariant:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareVariant);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsVariant);
    end;
    tkMethod:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareMethod);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsMethod);
    end;
    tkPointer:
    begin
      FInternalComparer := TInternalCompareMethod(@DoComparePointer);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsPointer);
    end;
    tkDynArray:
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareDynArray);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsDynArray);
    end;
    else
    begin
      FInternalComparer := TInternalCompareMethod(@DoCompareBin);
      FInternalEquals   := TInternalEqualsMethod(@DoEqualsBin);
    end;
  end;
end;

constructor TGenericCollection.Create(const aSrc: array of T);
begin
  Create(aSrc, RtlMemAllocator);
end;

constructor TGenericCollection.Create(const aSrc: array of T; aAllocator: TMemAllocator);
begin
  Create(aAllocator);
  LoadFrom(aSrc);
end;

destructor TGenericCollection.Destroy;
begin
  FElementAllocator.Free;
  inherited Destroy;
end;

function TGenericCollection.GetElementSize: SizeUInt;
begin
  Result := FElementAllocator.GetElementSize();
end;

function TGenericCollection.GetIsManagedType: Boolean;
begin
  Result := FElementAllocator.GetIsManagedType();
end;

function TGenericCollection.GetTypeInfo: PTypeInfo;
begin
  Result := FElementAllocator.GetTypeInfo();
end;

function TGenericCollection.GetElementAllocator: specialize TElementAllocator<T>;
begin
  Result := FElementAllocator;
end;

function TGenericCollection.IsCompatible(aDst: TCollection): Boolean;
begin
  Result := (inherited IsCompatible(aDst)) and (aDst is (specialize TGenericCollection<T>));
end;

procedure TGenericCollection.LoadFrom(const aSrc: array of T);
var
  LLen: SizeInt;
begin
  LLen := Length(aSrc);
  if LLen = 0 then
  begin
    Clear;
    Exit;
  end;

  LoadFrom(@aSrc[0], LLen);
end;

procedure TGenericCollection.Append(const aSrc: array of T);
begin
  Append(@aSrc[0], Length(aSrc));
end;

function TGenericCollection.ToArray: specialize TGenericArray<T>;
var
  LCount: SizeUInt;
begin
  Initialize(Result);
  LCount := GetCount;

  if LCount > 0 then
  begin
    SetLength(Result, LCount);
    WriteToArrayMemory(@Result[0], LCount);
  end;
end;

{ 
  以下这些Contains方法的实现，都是基于ForEach方法的,其实后代都会有优化的实现
  这些实现最终是不会被采用的,但为了伦理,还是实现了(伦理上提供可用的基础实现,但实际不会被采用)
}

function TGenericCollection.DoInternalContainsForEachFunc(const aValue: T; aData: Pointer): Boolean;
var
  LP: PInternalEqualsMethodData;
begin
  LP          := PInternalEqualsMethodData(aData);
  LP^.success := LP^.equals(aValue, LP^.value);
  Result      := not LP^.success;
end;

function TGenericCollection.Contains(const aValue: T): Boolean;
var
  LData: TInternalEqualsMethodData;
begin
  if GetCount = 0 then
    Exit(False);

  LData.success  := False;
  LData.equals   := FInternalEquals;
  LData.value    := aValue;
  ForEach(@DoInternalContainsForEachFunc, @LData);
  Result := LData.success;
end;

function TGenericCollection.DoContainsForEachFunc(const aValue: T; aData: Pointer): Boolean;
var
  LP: PContainsFuncData;
begin
  LP          := PContainsFuncData(aData);
  LP^.success := LP^.equals(aValue, LP^.value, LP^.data);
  Result      := not LP^.success;
end;

function TGenericCollection.Contains(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
var
  LData: TContainsFuncData;
begin
  if GetCount = 0 then
    Exit(False);

  LData.success  := False;
  LData.equals   := aEquals;
  LData.value    := aValue;
  LData.data     := aData;
  ForEach(@DoContainsForEachFunc, @LData);
  Result := LData.success;
end;

function TGenericCollection.DoContainsForEachMethod(const aValue: T; aData: Pointer): Boolean;
var
  LP: PContainsMethodData;
begin
  LP          := PContainsMethodData(aData);
  LP^.success := LP^.equals(aValue, LP^.value, LP^.data);
  Result      := not LP^.success;
end;

function TGenericCollection.Contains(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
var
  LData: TContainsMethodData;
begin
  if GetCount = 0 then
    Exit(False);

  LData.success := False;
  LData.equals  := aEquals;
  LData.value   := aValue;
  LData.data    := aData;
  ForEach(@DoContainsForEachMethod, @LData);
  Result := LData.success;
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TGenericCollection.DoContainsForEachRefFunc(const aValue: T; aData: Pointer): Boolean;
var
  LP: PContainsRefFuncData;
begin
  LP          := PContainsRefFuncData(aData);
  LP^.success := LP^.equals(aValue, LP^.value);
  Result      := not LP^.success;
end;
{$ENDIF}

function TGenericCollection.Contains(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): Boolean;
var
  LData: TContainsRefFuncData;
begin
  if GetCount = 0 then
    Exit(False);

  LData.success := False;
  LData.equals  := aEquals;
  LData.value   := aValue;
  ForEach(@DoContainsForEachRefFunc, @LData);
  Result := LData.success;
end;

function TGrowthStrategy.GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  if aCurrentSize = 0 then
    Exit(aRequiredSize);

  Result := DoGetGrowSize(aCurrentSize, aRequiredSize);
end;

function TCustomGrowthStrategy.GetData: Pointer;
begin
  Result := FData;
end;

function TCustomGrowthStrategy.DoGetGrowSizeFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := FGrowFunc(aCurrentSize, aRequiredSize, FData);
end;

function TCustomGrowthStrategy.DoGetGrowSizeMethod(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := FGrowMethod(aCurrentSize, aRequiredSize);
end;

function TCustomGrowthStrategy.DoGetGrowSizeRefFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := FGrowRefFunc(aCurrentSize, aRequiredSize);
end;

function TCustomGrowthStrategy.DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := FGrowProxy(aCurrentSize, aRequiredSize);
end;

constructor TCustomGrowthStrategy.Create(aGrowFunc: TGrowFunc; aData: Pointer);
begin
  inherited Create;

  if aGrowFunc = nil then
    raise ENil.Create('TCustomGrowthStrategy.Create: aGrowFunc is nil');

  FGrowFunc  := aGrowFunc;
  FData      := aData;

  FGrowProxy := @DoGetGrowSizeFunc;
end;

constructor TCustomGrowthStrategy.Create(aGrowMethod: TGrowMethod; aData: Pointer);
begin
  inherited Create;

  if aGrowMethod = nil then
    raise ENil.Create('TCustomGrowthStrategy.Create: aGrowMethod is nil');

  FGrowMethod := aGrowMethod;
  FData       := aData;

  FGrowProxy  := @DoGetGrowSizeMethod;
end;

constructor TCustomGrowthStrategy.Create(aGrowRefFunc: TGrowRefFunc);
begin
  inherited Create;

  if aGrowRefFunc = nil then
    raise ENil.Create('TCustomGrowthStrategy.Create: aGrowRefFunc is nil');

  FGrowRefFunc := aGrowRefFunc;
  FData        := nil;

  FGrowProxy   := @DoGetGrowSizeRefFunc;
end;

function TCalcGrowStrategy.DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := aCurrentSize;

  while Result < aRequiredSize do
    Result := DoCalc(Result);
end;

function TDoublingGrowStrategy.DoCalc(aCurrentSize: SizeUInt): SizeUInt;
begin
  Result := aCurrentSize * 2;
end;

class destructor TDoublingGrowStrategy.Destroy;
begin
  if FGlobal <> nil then
  begin
    FGlobal.Free;
    FGlobal := nil;
  end;
end;

class function TDoublingGrowStrategy.GetGlobal: TDoublingGrowStrategy;
begin
  if FGlobal = nil then
    FGlobal := TDoublingGrowStrategy.Create;

  Result := FGlobal;
end;

function TFixedGrowStrategy.GetFixedSize: SizeUInt;
begin
  Result := FFixedSize;
end;

function TFixedGrowStrategy.DoCalc(aCurrentSize: SizeUInt): SizeUInt;
begin
  Result := aCurrentSize + FFixedSize;
end;

constructor TFixedGrowStrategy.Create(aFixedSize: SizeUInt);
begin
  inherited Create;

  if aFixedSize = 0 then
    raise EIsZero.Create('TFixedGrowStrategy.Create: aFixedSize is 0');

  FFixedSize := aFixedSize;
end;

function TFactorGrowStrategy.GetFactor: Single;
begin
  Result := FFactor;
end;

function TFactorGrowStrategy.DoCalc(aCurrentSize: SizeUInt): SizeUInt;
begin
  Result := ceil(aCurrentSize * FFactor);
end;

constructor TFactorGrowStrategy.Create(aFactor: Single);
begin
  inherited Create;

  if aFactor <= 0 then
    raise EIsZero.Create('TFactorGrowStrategy.Create: aFactor is 0');

  FFactor := aFactor;
end;

class destructor TPowerOfTwoGrowStrategy.Destroy;
begin
  if FGlobal <> nil then
  begin
    FGlobal.Free;
    FGlobal := nil;
  end;
end;

class function TPowerOfTwoGrowStrategy.GetGlobal: TPowerOfTwoGrowStrategy;
begin
  if FGlobal = nil then
    FGlobal := TPowerOfTwoGrowStrategy.Create;

  Result := FGlobal;
end;

function TPowerOfTwoGrowStrategy.GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  if aRequiredSize = 0 then
    Exit(0);

  Result := 1;

  while Result < aRequiredSize do
    Result := Result shl 1;
end;

function TGoldenRatioGrowStrategy.DoCalc(aCurrentSize: SizeUInt): SizeUInt;
const
  GOLDEN_RATIO: Single = 1.61803398875;
begin
  Result := ceil(aCurrentSize * GOLDEN_RATIO);
end;

class destructor TGoldenRatioGrowStrategy.Destroy;
begin
  if FGlobal <> nil then
  begin
    FGlobal.Free;
    FGlobal := nil;
  end;
end;

class function TGoldenRatioGrowStrategy.GetGlobal: TGoldenRatioGrowStrategy;
begin
  if FGlobal = nil then
    FGlobal := TGoldenRatioGrowStrategy.Create;

  Result := FGlobal;
end;

function TAlignedWrapperStrategy.GetGrowStrategy: TGrowthStrategy;
begin
  Result := FGrowStrategy;
end;

function TAlignedWrapperStrategy.GetAlignSize: SizeUInt;
begin
  Result := FAlignSize;
end;

constructor TAlignedWrapperStrategy.Create(aGrowStrategy: TGrowthStrategy; aAlignSize: SizeUInt);
begin
  inherited Create;

  if (aAlignSize = 0) then
    raise EIsZero.Create('TAlignedWrapperStrategy.Create: aAlignSize is 0');

  if ((aAlignSize and (aAlignSize - 1)) <> 0) then
    raise Exception.Create('TAlignedWrapperStrategy.Create: aAlignSize must be power of two');

  FGrowStrategy := aGrowStrategy;
  FAlignSize    := aAlignSize;
end;

function TAlignedWrapperStrategy.GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := FGrowStrategy.GetGrowSize(aCurrentSize, aRequiredSize);
  Result :=  ((Result + FAlignSize - 1) div FAlignSize) * FAlignSize;
end;

class destructor TExactGrowStrategy.Destroy;
begin
  if FGlobal <> nil then
  begin
    FGlobal.Free;
    FGlobal := nil;
  end;
end;

class function TExactGrowStrategy.GetGlobal: TExactGrowStrategy;
begin
  if FGlobal = nil then
    FGlobal := TExactGrowStrategy.Create;

  Result := FGlobal;
end;

function TExactGrowStrategy.GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
begin
  Result := aRequiredSize;
end;

procedure TArray.DoSwapPtrUInt(const aIndex1, aIndex2: SizeUInt);
var
  LP1: PPtrUInt;
  LP2: PPtrUInt;
begin
  LP1               := PPtrUInt(GetPtrUnChecked(aIndex1));
  LP2               := PPtrUInt(GetPtrUnChecked(aIndex2));
  FSwapPointerCache := LP1^;
  LP1^              := LP2^;
  LP2^              := FSwapPointerCache;
end;

procedure TArray.DoSwapMove(const aIndex1, aIndex2: SizeUInt);
var
  LP1: PGenericPtr;
  LP2: PGenericPtr;
  LAllocator: TMemAllocator;
begin
  LP1 := GetPtrUnChecked(aIndex1);
  LP2 := GetPtrUnChecked(aIndex2);
  LAllocator := GetAllocator;

  LAllocator.CopyUnChecked(LP1, FSwapBufferCache, FElementSizeCache);
  LAllocator.CopyUnChecked(LP2, LP1, FElementSizeCache);
  LAllocator.CopyUnChecked(FSwapBufferCache, LP2, FElementSizeCache);
end;

procedure TArray.DoSwapRaw(const aIndex1, aIndex2: SizeUInt);
begin
  FSwapValueCache := GetUnChecked(aIndex1);
  PutUnChecked(aIndex1, GetUnChecked(aIndex2));
  PutUnChecked(aIndex2, FSwapValueCache);
end;

procedure TArray.DoQuickSort(aLeft, aRight: SizeUInt);

  function MedianOfThree(const A, B, C: T): T; inline;
  begin
    if FInternalComparer(A, B) < 0 then
    begin
      if FInternalComparer(B, C) < 0 then
        Exit(B)
      else if FInternalComparer(A, C) < 0 then
        Exit(C)
      else
        Exit(A);
    end
    else
    begin
      if FInternalComparer(A, C) < 0 then
        Exit(A)
      else if FInternalComparer(B, C) < 0 then
        Exit(C)
      else
        Exit(B);
    end;
  end;

  procedure InsertionSort(aLeft, aRight: SizeUInt); inline;
  var
    LP: PGenericPtr;
    i, j: SizeUInt;
    temp: T;
  begin
    LP := FMemory;

    for i := aLeft + 1 to aRight do
    begin
      temp := LP[i];
      j := i;

      while (j > aLeft) and (FInternalComparer(LP[j - 1], temp) > 0) do
      begin
        LP[j] := LP[j - 1];
        Dec(j);
      end;

      LP[j] := temp;
    end;
  end;

var
  LP: PGenericPtr;
  i, j: SizeUInt;
  pivot: T;
begin
  LP := FMemory;

  while aLeft < aRight do
  begin
    if aRight - aLeft <= INSERTION_SORT_THRESHOLD then
    begin
      InsertionSort(aLeft, aRight);
      Exit;
    end;

    i := aLeft;
    j := aRight;

    pivot := MedianOfThree(
      LP[aLeft],
      LP[(aLeft + aRight) div 2],
      LP[aRight]
    );

    repeat
      while FInternalComparer(LP[i], pivot) < 0 do Inc(i);
      while FInternalComparer(LP[j], pivot) > 0 do Dec(j);

      if i <= j then
      begin
        if i <> j then
          SwapUnChecked(i, j);
        Inc(i);
        Dec(j);
      end;
    until i > j;

    // 尾递归优化：先处理小段，尾递归处理大段
    if (j - aLeft) < (aRight - i) then
    begin
      DoQuickSort(aLeft, j);
      aLeft := i;
    end
    else
    begin
      DoQuickSort(i, aRight);
      aRight := j;
    end;
  end;
end;

procedure TArray.DoQuickSortFunc(aLeft, aRight: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);

  function MedianOfThree(const A, B, C: T): T; inline;
  begin
    if aComparer(A, B, aData) < 0 then
    begin
      if aComparer(B, C, aData) < 0 then
        Exit(B)
      else if aComparer(A, C, aData) < 0 then
        Exit(C)
      else
        Exit(A);
    end
    else
    begin
      if aComparer(A, C, aData) < 0 then
        Exit(A)
      else if aComparer(B, C, aData) < 0 then
        Exit(C)
      else
        Exit(B);
    end;
  end;

  procedure InsertionSort(aLeft, aRight: SizeUInt); inline;
  var
    LP: PGenericPtr;
    i, j: SizeUInt;
    temp: T;
  begin
    LP := FMemory;

    for i := aLeft + 1 to aRight do
    begin
      temp := LP[i];
      j := i;

      while (j > aLeft) and (aComparer(LP[j - 1], temp, aData) > 0) do
      begin
        LP[j] := LP[j - 1];
        Dec(j);
      end;

      LP[j] := temp;
    end;
  end;

var
  LP: PGenericPtr;
  i, j: SizeUInt;
  pivot: T;
begin
  LP := FMemory;

  while aLeft < aRight do
  begin
    if aRight - aLeft <= INSERTION_SORT_THRESHOLD then
    begin
      InsertionSort(aLeft, aRight);
      Exit;
    end;

    i := aLeft;
    j := aRight;

    pivot := MedianOfThree(
      LP[aLeft],
      LP[(aLeft + aRight) div 2],
      LP[aRight]
    );

    repeat
      while aComparer(LP[i], pivot, aData) < 0 do Inc(i);
      while aComparer(LP[j], pivot, aData) > 0 do Dec(j);

      if i <= j then
      begin
        if i <> j then
          SwapUnChecked(i, j);
        Inc(i);
        Dec(j);
      end;
    until i > j;

    // 尾递归优化：先处理小段，尾递归处理大段
    if (j - aLeft) < (aRight - i) then
    begin
      DoQuickSortFunc(aLeft, j, aComparer, aData);
      aLeft := i;
    end
    else
    begin
      DoQuickSortFunc(i, aRight, aComparer, aData);
      aRight := j;
    end;
  end;
end;

procedure TArray.DoQuickSortMethod(aLeft, aRight: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);

  function MedianOfThree(const A, B, C: T): T; inline;
  begin
    if aComparer(A, B, aData) < 0 then
    begin
      if aComparer(B, C, aData) < 0 then
        Exit(B)
      else if aComparer(A, C, aData) < 0 then
        Exit(C)
      else
        Exit(A);
    end
    else
    begin
      if aComparer(A, C, aData) < 0 then
        Exit(A)
      else if aComparer(B, C, aData) < 0 then
        Exit(C)
      else
        Exit(B);
    end;
  end;

  procedure InsertionSort(aLeft, aRight: SizeUInt); inline;
  var
    LP: PGenericPtr;
    i, j: SizeUInt;
    temp: T;
  begin
    LP := FMemory;

    for i := aLeft + 1 to aRight do
    begin
      temp := LP[i];
      j := i;

      while (j > aLeft) and (aComparer(LP[j - 1], temp, aData) > 0) do
      begin
        LP[j] := LP[j - 1];
        Dec(j);
      end;

      LP[j] := temp;
    end;
  end;

var
  LP: PGenericPtr;
  i, j: SizeUInt;
  pivot: T;
begin
  LP := FMemory;

  while aLeft < aRight do
  begin
    if aRight - aLeft <= INSERTION_SORT_THRESHOLD then
    begin
      InsertionSort(aLeft, aRight);
      Exit;
    end;

    i := aLeft;
    j := aRight;

    pivot := MedianOfThree(
      LP[aLeft],
      LP[(aLeft + aRight) div 2],
      LP[aRight]
    );

    repeat
      while aComparer(LP[i], pivot, aData) < 0 do Inc(i);
      while aComparer(LP[j], pivot, aData) > 0 do Dec(j);

      if i <= j then
      begin
        if i <> j then
          SwapUnChecked(i, j);
        Inc(i);
        Dec(j);
      end;
    until i > j;

    // 尾递归优化：先处理小段，尾递归处理大段
    if (j - aLeft) < (aRight - i) then
    begin
      DoQuickSortMethod(aLeft, j, aComparer, aData);
      aLeft := i;
    end
    else
    begin
      DoQuickSortMethod(i, aRight, aComparer, aData);
      aRight := j;
    end;
  end;
end;

procedure TArray.DoQuickSortRefFunc(aLeft, aRight: SizeUInt; aComparer: specialize TCompareRefFunc<T>);

  function MedianOfThree(const A, B, C: T): T; inline;
  begin
    if aComparer(A, B) < 0 then
    begin
      if aComparer(B, C) < 0 then
        Exit(B)
      else if aComparer(A, C) < 0 then
        Exit(C)
      else
        Exit(A);
    end
    else
    begin
      if aComparer(A, C) < 0 then
        Exit(A)
      else if aComparer(B, C) < 0 then
        Exit(C)
      else
        Exit(B);
    end;
  end;

  procedure InsertionSort(aLeft, aRight: SizeUInt); inline;
  var
    LP: PGenericPtr;
    i, j: SizeUInt;
    temp: T;
  begin
    LP := FMemory;

    for i := aLeft + 1 to aRight do
    begin
      temp := LP[i];
      j := i;

      while (j > aLeft) and (aComparer(LP[j - 1], temp) > 0) do
      begin
        LP[j] := LP[j - 1];
        Dec(j);
      end;

      LP[j] := temp;
    end;
  end;

var
  LP: PGenericPtr;
  i, j: SizeUInt;
  pivot: T;
begin
  LP := FMemory;

  while aLeft < aRight do
  begin
    if aRight - aLeft <= INSERTION_SORT_THRESHOLD then
    begin
      InsertionSort(aLeft, aRight);
      Exit;
    end;

    i := aLeft;
    j := aRight;

    pivot := MedianOfThree(
      LP[aLeft],
      LP[(aLeft + aRight) div 2],
      LP[aRight]
    );

    repeat
      while aComparer(LP[i], pivot) < 0 do Inc(i);
      while aComparer(LP[j], pivot) > 0 do Dec(j);

      if i <= j then
      begin
        if i <> j then
          SwapUnChecked(i, j);
        Inc(i);
        Dec(j);
      end;
    until i > j;

    // 尾递归优化：先处理小段，尾递归处理大段
    if (j - aLeft) < (aRight - i) then
    begin
      DoQuickSortRefFunc(aLeft, j, aComparer);
      aLeft := i;
    end
    else
    begin
      DoQuickSortRefFunc(i, aRight, aComparer);
      aRight := j;
    end;
  end;

end;

function TArray.IsOverlap(const aSrc: Pointer; aElementCount: SizeUInt): Boolean;
begin
  Result := FElementAllocator.IsMemoryOverlap(FMemory, aSrc, aElementCount);
end;

function TArray.GetPtrUnChecked(aIndex: SizeUInt): PGenericPtr;
begin
  Result := PGenericPtr(FMemory) + aIndex;
end;

constructor TArray.Create(aAllocator: TMemAllocator; aCount: SizeUInt);
begin
  inherited Create(aAllocator);
  FMemory := nil;
  FCount  := 0;

  if GetIsManagedType then
  begin
    if FElementSizeCache = SIZE_PTR then
      FSwapMethod := @DoSwapPtrUInt
    else
    begin
      FSwapBufferCache := GetAllocator.GetMem(FElementSizeCache);

      if FSwapBufferCache = nil then
        raise EAlloc.Create('TArray.Create: Failed to allocate swap buffer cache.');

      FSwapMethod := @DoSwapMove;
    end;
  end
  else
    FSwapMethod := @DoSwapRaw;

  if (aCount > 0) then
    Resize(aCount);
end;

constructor TArray.Create(aAllocator: TMemAllocator);
begin
  Create(aAllocator, 0);
end;

constructor TArray.Create(aCount: SizeUInt);
begin
  Create(RtlMemAllocator, aCount);
end;

destructor TArray.Destroy;
begin
  Clear;

  if GetIsManagedType and (FElementSizeCache <> SIZE_PTR) then
    GetAllocator.FreeMem(FSwapBufferCache);

  inherited Destroy;
end;

function TArray.GetCount: SizeUInt;
begin
  Result := FCount;
end;

procedure TArray.Clear;
begin
  if FCount > 0 then
    Resize(0);
end;

procedure TArray.WriteToArrayMemory(aDst: Pointer; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aDst = nil then
    raise ENil.Create('TArray.WriteToArrayMemory: aDst is nil');

  if IsOverlap(aDst, aCount) then
    raise EOverlap.Create('TArray.WriteToArrayMemory: aDst is overlapped');

  if aCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.WriteToArrayMemory: aCount out of bounds');

   FElementAllocator.CopyElementsUnChecked(FMemory, aDst, aCount);
end;

procedure TArray.LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  Resize(aElementCount);
  OverWriteUnChecked(0, aSrc, aElementCount);
end;

procedure TArray.AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt);
var
  LCount:       SizeUInt;
  LIndex:       SizeUInt;
  LElementSize: SizeUInt;
begin
  LCount := FCount;

  { 如果源内存与目标内存重叠, 属于从自身容器内存源追加 }
  if IsOverlap(aSrc, aElementCount) then
  begin
    LElementSize := GetElementSize;

    { 检查源指针是否与元素大小对齐 }
    if (PtrUInt(aSrc) mod LElementSize) <> 0 then
      raise EWow.Create('TArray.AppendUnChecked: aSrc is not aligned');

    LIndex := (PtrUInt(aSrc) - PtrUInt(FMemory)) div LElementSize;

    if LIndex + aElementCount > LCount then
      raise ERangeOutOfBounds.Create('TArray.AppendUnChecked: bounds out of range');
    
    Resize(LCount + aElementCount);
    CopyUnChecked(LIndex, LCount, aElementCount);
  end
  else
  begin
    Resize(LCount + aElementCount);
    OverWriteUnChecked(LCount, aSrc, aElementCount);
  end;
end;

procedure TArray.AppendToUnChecked(const aDst: TCollection);
begin
  if (FCount = 0) then
    exit;

  aDst.AppendUnChecked(FMemory, FCount);
end;

function TArray.Get(aIndex: SizeUInt): T;
begin
  Result := GetPtr(aIndex)^;
end;

function TArray.GetUnChecked(aIndex: SizeUInt): T;
begin
  Result := GetPtrUnChecked(aIndex)^;
end;

procedure TArray.Put(aIndex: SizeUInt; const aValue: T);
begin
  GetPtr(aIndex)^ := aValue;
end;

procedure TArray.PutUnChecked(aIndex: SizeUInt; const aValue: T);
begin
  GetPtrUnChecked(aIndex)^ := aValue;
end;

function TArray.GetMemory:PGenericPtr;
begin
  Result := PGenericPtr(FMemory);
end;

function TArray.GetPtr(aIndex:SizeUInt):PGenericPtr;
begin
  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.GetPtr: index out of range');

  Result := GetPtrUnChecked(aIndex);
end;

procedure TArray.Resize(aNewSize: SizeUInt);
var
  LMemory: Pointer;
begin
  if aNewSize = FCount then
    exit;

  LMemory := FElementAllocator.ReallocElements(FMemory, FCount, aNewSize);

  if (aNewSize > 0) and (LMemory = nil) then
    raise EAlloc.Create('TArray.Resize: out of memory');

  FMemory := LMemory;
  FCount  := aNewSize;
end;

procedure TArray.Ensure(aCount: SizeUInt);
begin
  if FCount < aCount then
    Resize(aCount);
end;

procedure TArray.OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TArray.OverWrite: aSrc is nil');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.OverWrite: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.OverWrite: bounds out of range');

  OverWriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TArray.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
begin
  FElementAllocator.CopyElementsUnChecked(aSrc, GetPtrUnChecked(aIndex), aElementCount);
end;

procedure TArray.OverWrite(aIndex: SizeUInt; const aSrc: array of T);
begin
  OverWrite(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TArray.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T);
begin
  OverWriteUnChecked(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TArray.OverWrite(aIndex: SizeUInt; const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TArray.OverWrite: aSrc is nil');

  OverWrite(aIndex, aSrc, aSrc.GetCount);
end;

procedure TArray.OverWrite(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TArray.OverWrite: aSrc is nil');

  if not IsCompatible(aSrc) then
    raise ECompatible.Create('TArray.OverWrite: aSrc is not compatible');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.OverWrite: index out of range');

  if aIndex + aCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.OverWrite: bounds out of range');

  OverWriteUnChecked(aIndex, aSrc, aCount);
end;

procedure TArray.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  aSrc.WriteToArrayMemory(GetPtrUnChecked(aIndex), aCount);
end;

procedure TArray.Read(aIndex: SizeUInt; aDst: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    exit;

  if aDst = nil then
    raise ENil.Create('TArray.Read: aDst is nil');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Read: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.Read: bounds out of range');

  FElementAllocator.CopyElementsUnChecked(GetPtrUnChecked(aIndex), aDst, aElementCount);
end;

procedure TArray.Read(aIndex: SizeUInt; var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt);
var
  LLen: SizeUInt;
begin
  if aElementCount = 0 then
    exit;

  LLen := Length(aDst);

  if LLen <> aElementCount then
    SetLength(aDst, aElementCount);

  Read(aIndex, @aDst[0], aElementCount);
end;

procedure TArray.Fill(aIndex, aElementCount: SizeUInt; const aValue: T);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TArray.Fill: aElementCount is 0');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Fill: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.Fill: bounds out of range');

  FElementAllocator.FillElements(GetPtrUnChecked(aIndex), aValue, aElementCount);
end;

procedure TArray.Fill(aIndex: SizeUInt; const aValue: T);
begin
  if FCount = 0 then
    raise EEmpty.Create('TArray.Fill: empty array');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Fill: index out of range');

  Fill(aIndex, FCount - aIndex, aValue);
end;

procedure TArray.Fill(const aValue: T);
begin
  if FCount = 0 then
    raise EEmpty.Create('TArray.Fill: empty array');

  Fill(0, FCount, aValue);
end;

procedure TArray.Zero(aIndex, aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TArray.Zero: aElementCount is 0');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Zero: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.Zero: bounds out of range');

  FElementAllocator.ZeroElements(GetPtrUnChecked(aIndex), aElementCount);
end;

procedure TArray.Zero(aIndex: SizeUInt);
begin
  if FCount = 0 then
    raise EEmpty.Create('TArray.Zero: empty array');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Zero: index out of range');

  Zero(aIndex, FCount - aIndex);
end;

procedure TArray.Zero;
begin
  if FCount = 0 then
    raise EEmpty.Create('TArray.Zero: empty array');

  Zero(0);
end;

procedure TArray.Swap(aIndex1, aIndex2: SizeUInt);
begin
  if (aIndex1 >= FCount) or (aIndex2 >= FCount) then
    raise ERangeOutOfIndex.Create('TArray.Swap: index out of range');

  if aIndex1 = aIndex2 then
    raise ESame.Create('TArray.Swap: same index');

  SwapUnChecked(aIndex1, aIndex2);
end;

procedure TArray.SwapUnChecked(aIndex1, aIndex2: SizeUInt);
begin
  FSwapMethod(aIndex1, aIndex2);
end;

procedure TArray.Swap(aIndex1, aIndex2, aElementCount: SizeUInt);
begin
  Swap(aIndex1, aIndex2, aElementCount, ARRAY_DEFAULT_SWAP_BUFFER_SIZE);
end;

procedure TArray.Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt);
var
  LBuffer:      Pointer;
  LP1:          PByte;
  LP2:          PByte;
  LBufferSize:  SizeUInt;
  LElementSize: SizeUInt;
  LRemainSize:  SizeUInt;
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TArray.Swap: element count is 0');

  if aIndex1 = aIndex2 then
    raise ESame.Create('TArray.Swap: same index');

  if (aIndex1 >= FCount) or (aIndex2 >= FCount) then
    raise ERangeOutOfIndex.Create('TArray.Swap: index out of range');

  if (aIndex1 + aElementCount > FCount) or (aIndex2 + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Swap: bounds out of range');

  { 如果元素数量大于 1，则需要检查范围是否越界和是否重叠并且用临时缓冲区交换元素 }
  if aElementCount > 1 then
  begin
    if (aIndex1 + aElementCount > aIndex2) and (aIndex2 + aElementCount > aIndex1) then
      raise EOverlap.Create('TArray.Swap: overlap');

    LElementSize := GetElementSize;
    LRemainSize  := aElementCount * LElementSize;
    
    if LRemainSize > aSwapBufferSize then
      LBufferSize := aSwapBufferSize
    else
      LBufferSize := LRemainSize;

    LBuffer := Allocator.GetMem(LBufferSize);

    if LBuffer = nil then
      raise EAlloc.Create('TArray.Swap: out of memory');

    try
      { 分块交换元素 }
      LP1 := PByte(GetPtrUnChecked(aIndex1));
      LP2 := PByte(GetPtrUnChecked(aIndex2));

      while LRemainSize > 0 do
      begin
        if LBufferSize > LRemainSize then
          LBufferSize  := LRemainSize;

        Allocator.CopyUnChecked(LP1,     LBuffer, LBufferSize);
        Allocator.CopyUnChecked(LP2,     LP1,     LBufferSize);
        Allocator.CopyUnChecked(LBuffer, LP2,     LBufferSize);

        Inc(LP1, LBufferSize);
        Inc(LP2, LBufferSize);
        Dec(LRemainSize, LBufferSize);
      end;

    finally
      Allocator.FreeMem(LBuffer);
    end;
  end
  else
    SwapUnChecked(aIndex1, aIndex2);
end;

procedure TArray.Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TArray.Copy: element count is 0');

  if aSrcIndex = aDstIndex then
    raise ESame.Create('TArray.Copy: same index');

  if aSrcIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Copy: src index out of range');

  if aDstIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Copy: dst index out of range');

  if (aSrcIndex + aElementCount > FCount) or (aDstIndex + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Copy: bounds out of range');

  CopyUnChecked(aSrcIndex, aDstIndex, aElementCount);
end;

procedure TArray.CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
begin
  FElementAllocator.CopyElementsUnChecked(GetPtrUnChecked(aSrcIndex), GetPtrUnChecked(aDstIndex), aElementCount);
end;

procedure TArray.SaveToUnChecked(aDst: TCollection);
begin
  if FCount = 0 then
    exit;

  aDst.LoadFromUnChecked(FMemory, FCount);
end;

function TArray.ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
begin
  if FCount = 0 then
    exit(True);

  Result := ForEach(0, FCount, aForEach, aData);
end;

function TArray.ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
begin
  if FCount = 0 then
    exit(True);

  Result := ForEach(0, FCount, aForEach, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean;
begin
  if FCount = 0 then
    exit(True);

  Result := ForEach(0, FCount, aForEach);
end;
{$ENDIF}

function TArray.Contains(const aValue: T): Boolean;
begin
  if FCount = 0 then
    exit(False);

  Result := Contains(aValue, 0, FCount);
end;

function TArray.Contains(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin
  if FCount = 0 then
    exit(False);

  Result := Contains(aValue, 0, FCount, aEquals, aData);
end;

function TArray.Contains(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin
  if FCount = 0 then
    exit(False);

  Result := Contains(aValue, 0, FCount, aEquals, aData);
end;

function TArray.Contains(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin
  if FCount = 0 then
    exit(False);

  Result := Contains(aValue, 0, FCount, aEquals);
end;

function TArray.Contains(const aValue: T; aStartIndex: SizeUInt): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Contains: index out of range');

  Result := Contains(aValue, aStartIndex, FCount - aStartIndex);
end;

function TArray.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Contains: index out of range');

  Result := Contains(aValue, aStartIndex, FCount - aStartIndex, aEquals, aData);
end;

function TArray.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Contains: index out of range');

  Result := Contains(aValue, aStartIndex, FCount - aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Contains: index out of range');

  Result := Contains(aValue, aStartIndex, FCount - aStartIndex, aEquals);
end;
{$ENDIF}

function TArray.Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean;
begin
  Result := (Find(aValue, aStartIndex, aCount) >= 0);
end;

function TArray.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin
  Result := (Find(aValue, aStartIndex, aCount, aEquals, aData) >= 0);
end;

function TArray.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin
  Result := (Find(aValue, aStartIndex, aCount, aEquals, aData) >= 0);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin
  Result := (Find(aValue, aStartIndex, aCount, aEquals) >= 0);
end;
{$ENDIF}

function TArray.Find(const aValue: T): SizeInt;
begin
  if FCount = 0 then
    Exit(-1);

  Result := Find(aValue, 0, FCount);
end;

function TArray.Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  if FCount = 0 then
    Exit(-1);

  Result := Find(aValue, 0, FCount, aEquals, aData);
end;

function TArray.Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  if FCount = 0 then
    Exit(-1);

  Result := Find(aValue, 0, FCount, aEquals, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  if FCount = 0 then
    Exit(-1);

  Result := Find(aValue, 0, FCount, aEquals);
end;
{$ENDIF}

function TArray.Find(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  Result := Find(aValue, aStartIndex, FCount - aStartIndex);
end;

function TArray.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  Result := Find(aValue, aStartIndex, FCount - aStartIndex, aEquals, aData);
end;

function TArray.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  Result := Find(aValue, aStartIndex, FCount - aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  Result := Find(aValue, aStartIndex, FCount - aStartIndex, aEquals);
end;
{$ENDIF}

function TArray.Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    raise EIsZero.Create('TArray.Find: count is 0');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  if aStartIndex + aCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.Find: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if FInternalEquals(GetPtrUnChecked(i)^, aValue) then
      exit(i);

  Result := -1;
end;

function TArray.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    raise EIsZero.Create('TArray.Find: count is 0');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Find: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if aEquals(GetPtrUnChecked(i)^, aValue, aData) then
      exit(i);

  Result := -1;
end;

function TArray.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    raise EIsZero.Create('TArray.Find: count is 0');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Find: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if aEquals(GetPtrUnChecked(i)^, aValue, aData) then
      exit(i);

  Result := -1;
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    raise EIsZero.Create('TArray.Find: count is 0');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Find: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Find: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if aEquals(GetPtrUnChecked(i)^, aValue) then
      exit(i);

  Result := -1;
end;
{$ENDIF}

procedure TArray.Sort;
begin
  if FCount < 2 then
    exit;

  Sort(0);
end;

procedure TArray.Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  if FCount < 2 then
    exit;

  Sort(0, aComparer, aData);
end;

procedure TArray.Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  if FCount < 2 then
    exit;

  Sort(0, aComparer, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TArray.Sort(aComparer: specialize TCompareRefFunc<T>);
begin
  if FCount < 2 then
    exit;

  Sort(0, aComparer);
end;
{$ENDIF}

procedure TArray.Sort(aStartIndex: SizeUInt);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  Sort(aStartIndex, FCount - aStartIndex);
end;

procedure TArray.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  Sort(aStartIndex, FCount - aStartIndex, aComparer, aData);
end;

procedure TArray.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  Sort(aStartIndex, FCount - aStartIndex, aComparer, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TArray.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  Sort(aStartIndex, FCount - aStartIndex, aComparer);
end;
{$ENDIF}

procedure TArray.Sort(aStartIndex, aCount: SizeUInt);
begin
  if aCount = 0 then
    raise EIsZero.Create('TArray.Sort: count is 0');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  if aStartIndex + aCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.Sort: bounds out of range');

  if aCount < 2 then
    exit;

  DoQuickSort(aStartIndex, aStartIndex + aCount - 1);
end;

procedure TArray.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Sort: bounds out of range');

  if aCount < 2 then
    exit;

  DoQuickSortFunc(aStartIndex, aStartIndex + aCount - 1, aComparer, aData);
end;

procedure TArray.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Sort: bounds out of range');


  if aCount < 2 then
    exit;

  DoQuickSortMethod(aStartIndex, aStartIndex + aCount - 1, aComparer, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TArray.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Sort: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Sort: bounds out of range');


  if aCount < 2 then
    exit;

  DoQuickSortRefFunc(aStartIndex, aStartIndex + aCount - 1, aComparer);
end;
{$ENDIF}

function TArray.BinarySearch(const aValue: T): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TArray.BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TArray.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TArray.BinarySearchInsert(const aValue: T): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TArray.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TArray.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TArray.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}


procedure TArray.Reverse;
begin
  Reverse(0, FCount);
end;

procedure TArray.Reverse(aStartIndex: SizeUInt);
begin
  if FCount = 0 then
    raise EEmpty.Create('TArray.Reverse: empty array');

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.Reverse: index out of range');

  Reverse(aStartIndex, FCount - aStartIndex);
end;

procedure TArray.Reverse(aStartIndex, aCount: SizeUInt);
var
  LSwapCount:    SizeUInt;
  LLeft:         SizeUInt;
  LRight:        SizeUInt;
  i:             SizeUInt;
begin
  if aCount <= 1 then
    exit;

  if (aStartIndex >= FCount) then
    raise ERangeOutOfIndex.Create('TArray.Reverse: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.Reverse: bounds out of range');

  LSwapCount := aCount div 2;
  LLeft      := aStartIndex;
  LRight     := aStartIndex + aCount - 1;

  for i := 0 to LSwapCount - 1 do
  begin
    SwapUnChecked(LLeft, LRight);
    Inc(LLeft);
    Dec(LRight);
  end;
end;

function TArray.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  Result := ForEach(aStartIndex, FCount - aStartIndex, aForEach, aData);
end;

function TArray.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  Result := ForEach(aStartIndex, FCount - aStartIndex, aForEach, aData);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
begin
  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  Result := ForEach(aStartIndex, FCount - aStartIndex, aForEach);
end;
{$ENDIF}

function TArray.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    exit(True);

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.ForEach: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if not aForEach(GetUnChecked(i), aData) then
      exit(False);

  Result := True;
end;

function TArray.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    exit(True);

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.ForEach: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if not aForEach(GetUnChecked(i), aData) then
      exit(False);

  Result := True;
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TArray.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
var
  i: SizeUInt;
begin
  if aCount = 0 then
    exit(True);

  if aStartIndex >= FCount then
    raise ERangeOutOfIndex.Create('TArray.ForEach: index out of range');

  if (aStartIndex + aCount > FCount) then
    raise ERangeOutOfBounds.Create('TArray.ForEach: bounds out of range');

  for i := aStartIndex to aStartIndex + aCount - 1 do
    if not aForEach(GetUnChecked(i)) then
      exit(False);

  Result := True;
end;
{$ENDIF}


function TArray.GetEnumerator: specialize TEnumerator<T>;
begin
  Result := TArrayEnumerator.Create(Self);
end;

constructor TArray.TArrayEnumerator.Create(const aArray: specialize TArray<T>);
begin
  inherited Create;
  FArray := aArray;
  Reset;
end;

function TArray.TArrayEnumerator.GetCurrent: T;
begin
  Result := FArray.GetUnChecked(FCurrentIndex);
end;

function TArray.TArrayEnumerator.MoveNext: Boolean;
begin
  if not FStarted then
  begin
    FStarted      := True;
    FCurrentIndex := 0;
    Result := (FCurrentIndex < FArray.FCount);
  end
  else 
  begin
    Inc(FCurrentIndex);
    Result := (FCurrentIndex < FArray.FCount);
  end;
end;

procedure TArray.TArrayEnumerator.Reset;
begin
  FStarted      := False;
  FCurrentIndex := 0;
end;

function TVec.GetDefaultGrowStrategy: TGrowthStrategy;
begin
  Result := TGoldenRatioGrowStrategy.GetGlobal;
end;

constructor TVec.Create(aAllocator: TMemAllocator; aCapacity: SizeUint);
begin
  Create(aAllocator, aCapacity, GetDefaultGrowStrategy);
end;

constructor TVec.Create(aAllocator: TMemAllocator; aCapacity: SizeUint; aGrowStrategy: TGrowthStrategy);
begin
  inherited Create(aAllocator);
  try
    FBuf := TVecBuf.Create(aAllocator, aCapacity);
  except
    on E: Exception do
      raise EAlloc.CreateFmt('TVec.Create: Failed to create internal buffer. Original error: %s', [E.Message]);
  end;

  FCount := 0;
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(const aCollection: TCollection; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aAllocator, 0, aGrowStrategy);
  LoadFrom(aCollection)
end;

constructor TVec.Create(const aArray: array of T; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aAllocator, 0, aGrowStrategy);
  LoadFrom(aArray);
end;

constructor TVec.Create(aPtr: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aAllocator, 0, aGrowStrategy);
  LoadFrom(aPtr, aElementCount);
end;

constructor TVec.Create(aAllocator: TMemAllocator);
begin
  Create(aAllocator, VEC_DEFAULT_CAPACITY, GetDefaultGrowStrategy);
end;

constructor TVec.Create(aCapacity: SizeUint);
begin
  Create(RtlMemAllocator, aCapacity, GetDefaultGrowStrategy);
end;

constructor TVec.Create(aCapacity: SizeUint; aGrowStrategy: TGrowthStrategy);
begin
  Create(RtlMemAllocator, aCapacity, aGrowStrategy);
end;

destructor TVec.Destroy;
begin
  FBuf.Free;
  FCount := 0;
  inherited Destroy;
end;

function TVec.GetCount: SizeUint;
begin
  Result := FCount;
end;

procedure TVec.LoadFromUnChecked(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  FBuf.LoadFromUnChecked(aSrc, aElementCount);
  FCount := aElementCount;
end;

procedure TVec.AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt);
var
  LIndex:       SizeUInt;
  LElementSize: SizeUInt;
begin
  { 如果源内存与目标内存重叠, 属于从自身容器内存源追加 }
  if IsOverlap(aSrc, aElementCount) then
  begin
    LElementSize := GetElementSize;

    { 检查源指针是否与元素大小对齐 }
    if (PtrUInt(aSrc) mod LElementSize) <> 0 then
      raise EWow.Create('TVec.AppendUnChecked: aSrc is not aligned');

    LIndex := (PtrUInt(aSrc) - PtrUInt(GetMemory())) div LElementSize;

    if LIndex + aElementCount > FCount then
      raise ERangeOutOfBounds.Create('TVec.AppendUnChecked: bounds out of range');
    
    Reserve(aElementCount);
    FBuf.CopyUnChecked(LIndex, FCount, aElementCount);
  end
  else
  begin
    Reserve(aElementCount);
    FBuf.OverWriteUnChecked(FCount, aSrc, aElementCount);
  end;

  Inc(FCount, aElementCount);
end;

procedure TVec.Clear;
begin
  Resize(0);
end;

procedure TVec.WriteToArrayMemory(aSrc: Pointer; aCount: SizeUInt);
begin
  if aCount > FCount then
    raise ERangeOutOfBounds.Create('TVec.WriteToArrayMemory: count out of bounds');

  FBuf.WriteToArrayMemory(aSrc, aCount);
end;

function TVec.GetEnumerator: specialize TEnumerator<T>;
begin
  Result := TVecEnumerator.Create(Self);
end;

function TVec.ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
begin

end;

function TVec.ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean;
begin

end;
{$ENDIF}


function TVec.Contains(const aValue: T): Boolean;
begin
end;

function TVec.Contains(const aValue: T; aStartIndex: SizeUInt): Boolean;
begin

end;

function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin

end;

function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin

end;
{$ENDIF}

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean;
begin

end;

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin

end;

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin

end;
{$ENDIF}

function TVec.Find(const aValue: T): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.Find(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

procedure TVec.Sort;
begin

end;

procedure TVec.Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin

end;

procedure TVec.Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aComparer: specialize TCompareRefFunc<T>);
begin

end;
{$ENDIF}

procedure TVec.Sort(aStartIndex: SizeUInt);
begin

end;

procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin

end;

procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin

end;
{$ENDIF}

procedure TVec.Sort(aStartIndex, aCount: SizeUInt);
begin

end;

procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin

end;

procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin

end;
{$ENDIF}

function TVec.BinarySearch(const aValue: T): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.BinarySearchInsert(const aValue: T): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

function TVec.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin

end;

function TVec.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin

end;
{$ENDIF}

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TVec.OverWrite: aSrc is nil');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.OverWrite: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TVec.OverWrite: bounds out of range');

  OverWriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
begin
  FElementAllocator.CopyElementsUnChecked(aSrc, GetPtrUnChecked(aIndex), aElementCount);
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: array of T);
begin
  OverWrite(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T);
begin
  OverWriteUnChecked(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TArray.OverWrite: aSrc is nil');

  OverWrite(aIndex, aSrc, aSrc.GetCount);
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TArray.OverWrite: aSrc is nil');

  if not IsCompatible(aSrc) then
    raise ECompatible.Create('TArray.OverWrite: aSrc is not compatible');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TArray.OverWrite: index out of range');

  if aIndex + aCount > FCount then
    raise ERangeOutOfBounds.Create('TArray.OverWrite: bounds out of range');

  OverWriteUnChecked(aIndex, aSrc, aCount);
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  aSrc.WriteToArrayMemory(GetPtrUnChecked(aIndex), aCount);
end;

procedure TVec.Reverse;
begin

end;

procedure TVec.Reverse(aStartIndex: SizeUInt);
begin

end;

procedure TVec.Reverse(aStartIndex, aCount: SizeUInt);
begin

end;


function TVec.Get(aIndex: SizeUint): T;
begin
  Result := GetPtr(aIndex)^;
end;

function TVec.GetUnChecked(aIndex: SizeUint): T;
begin
  Result := GetPtrUnChecked(aIndex)^;
end;

procedure TVec.Put(aIndex: SizeUint; const aValue: T);
begin
  GetPtr(aIndex)^ := aValue;
end;

procedure TVec.PutUnChecked(aIndex: SizeUint; const aValue: T);
begin
  GetPtrUnChecked(aIndex)^ := aValue;
end;

function TVec.GetPtr(aIndex: SizeUint): PGenericPtr;
begin
  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.GetPtr: index out of bounds');

  Result := FBuf.GetPtrUnChecked(aIndex);
end;

function TVec.GetPtrUnChecked(aIndex: SizeUint): PGenericPtr;
begin
  Result := FBuf.GetPtrUnChecked(aIndex);
end;

function TVec.GetMemory: PGenericPtr;
begin
  Result := PGenericPtr(FBuf.GetMemory);
end;

procedure TVec.Resize(aNewSize: SizeUint);
begin
  if aNewSize = FCount then
    exit;

  if (aNewSize < FCount) and (GetIsManagedType) then
    FElementAllocator.FinalizeManagedElements(FBuf.GetPtrUnChecked(aNewSize), FCount - aNewSize)
  else if not TryReserve(aNewSize - FCount) then
    raise EAlloc.Create('TVec.Resize: TryReserve failed');

  FCount := aNewSize;
end;

procedure TVec.ResizeExact(aNewSize: SizeUint);
begin
  FBuf.Resize(aNewSize);
  FCount := aNewSize;
end;

procedure TVec.Ensure(aCount: SizeUint);
begin
  if FCount < aCount then
    Resize(aCount);
end;

procedure TVec.Fill(aIndex, aElementCount: SizeUInt; const aValue: T);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TVec.Fill: count is 0');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.Fill: index out of range');

  if (aIndex + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TVec.Fill: bounds out of range');

  FBuf.Fill(aIndex, aElementCount, aValue);
end;

procedure TVec.Fill(aIndex: SizeUInt; const aValue: T);
begin
  if FCount = 0 then
    raise EIsZero.Create('TVec.Fill: array is empty');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.Fill: index out of range');

  Fill(aIndex, FCount - aIndex, aValue);
end;

procedure TVec.Fill(const aValue: T);
begin
  Fill(0, FCount, aValue);
end;

procedure TVec.Zero(aIndex, aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TVec.Zero: aElementCount is 0');

  if aIndex >= FCount  then
    raise ERangeOutOfIndex.Create('TVec.Zero: index out of range');

  if (aIndex + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TVec.Zero: bounds out of range');

  FBuf.Zero(aIndex, aElementCount);
end;

procedure TVec.Zero(aIndex: SizeUInt);
begin
  if FCount = 0 then
    raise EEmpty.Create('TVec.Zero: array is empty');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.Zero: index out of range');

  Zero(aIndex, FCount - aIndex);
end;

procedure TVec.Zero;
begin
  Zero(0);
end;

procedure TVec.Swap(aIndex1, aIndex2: SizeUInt);
begin
  Swap(aIndex1, aIndex2, 1, ARRAY_DEFAULT_SWAP_BUFFER_SIZE);
end;

procedure TVec.SwapUnChecked(aIndex1, aIndex2: SizeUInt);
begin

end;

procedure TVec.Swap(aIndex1, aIndex2, aElementCount: SizeUInt);
begin
  Swap(aIndex1, aIndex2, aElementCount, ARRAY_DEFAULT_SWAP_BUFFER_SIZE);
end;

procedure TVec.Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt);
begin
  if (aIndex1 >= FCount) or (aIndex2 >= FCount) then
    raise ERangeOutOfIndex.Create('TVec.Swap: index out of range');

  if (aIndex1 + aElementCount > FCount) or (aIndex2 + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TVec.Swap: bounds out of range');

  if aIndex1 = aIndex2 then
    raise ESame.Create('TVec.Swap: same index');

  FBuf.Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize);
end;

procedure TVec.Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
begin
  if (aSrcIndex >= FCount) or (aDstIndex >= FCount) then
    raise ERangeOutOfIndex.Create('TVec.Copy: index out of range');

  if (aSrcIndex + aElementCount > FCount) or (aDstIndex + aElementCount > FCount) then
    raise ERangeOutOfBounds.Create('TVec.Copy: bounds out of range');

  FBuf.Copy(aSrcIndex, aDstIndex, aElementCount);
end;

procedure TVec.CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
begin

end;

function TVec.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
begin

end;

function TVec.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
begin

end;
{$ENDIF}

function TVec.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
begin

end;

function TVec.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
begin

end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
function TVec.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
begin

end;
{$ENDIF}



function TVec.GetCapacity: SizeUint;
begin
  Result := FBuf.GetCount;
end;

procedure TVec.SetCapacity(aCapacity: SizeUint);
begin
  if not SetCapacity2(aCapacity) then
    raise EResize.Create('TVec.SetCapacity: failed to set capacity');
end;

function TVec.SetCapacity2(aCapacity: SizeUint): Boolean;
begin
  FBuf.Resize(aCapacity);

  if (aCapacity < FCount) then
    FCount := aCapacity;
end;

function TVec.GetGrowStrategy: TGrowthStrategy;
begin
  Result := FGrowStrategy;
end;

procedure TVec.SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
begin
  if aGrowStrategy = nil then
    FGrowStrategy := GetDefaultGrowStrategy
  else
    FGrowStrategy := aGrowStrategy;
end;

function TVec.TryReserve(aAdditional: SizeUint): Boolean;
var
  LCapacity: SizeUint;
  LTarget:   SizeUint;
  LExpect:   SizeUint;
begin
  if aAdditional = 0 then
    Exit(True);

  LCapacity := GetCapacity;
  LTarget   := FCount + aAdditional;
  Result    := (LCapacity >= LTarget);

  if not Result then
  begin
    LExpect := FGrowStrategy.GetGrowSize(LCapacity, LTarget);
    Assert(LExpect >= LCapacity, 'TVec.TryReserve: failed to reserve');
    Result := SetCapacity2(LExpect);
  end;
end;

procedure TVec.Reserve(aAdditional: SizeUint);
begin
  if not TryReserve(aAdditional) then
    raise EResize.Create('TVec.Reserve: failed to reserve');
end;

function TVec.TryReserveExact(aAdditional: SizeUint): Boolean;
var
  LExpect: SizeUInt;
begin
  LExpect := FCount + aAdditional;
  Result  := (GetCapacity >= LExpect) or SetCapacity2(LExpect);
end;

procedure TVec.ReserveExact(aAdditional: SizeUint);
begin
  if not TryReserveExact(aAdditional) then
    raise EResize.Create('TVec.ReserveExact: failed to reserve exact');
end;

procedure TVec.Shrink;
begin
  if not SetCapacity2(FCount) then
    raise EResize.Create('TVec.Shrink: failed to shrink');
end;

procedure TVec.ShrinkTo(aCapacity: SizeUint);
begin
  if aCapacity < FCount then
    raise EResize.Create('TVec.ShrinkTo: failed to shrink to (less than count)');

  if aCapacity >= GetCapacity then
    exit;

  if not SetCapacity2(aCapacity) then
    raise EResize.Create('TVec.ShrinkTo: failed to shrink to');
end;

procedure TVec.Truncate(aCount: SizeUint);
begin
  if FCount > aCount then
    Resize(aCount);
end;

procedure TVec.Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    exit;
  
  if aSrc = nil then
    raise EInvalid.Create('TVec.Insert: aSrc is nil');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TVec.Insert: index out of range');


  Resize(FCount + aElementCount);

  if (aIndex < FCount) then
    Copy(aIndex, aIndex + aElementCount, FCount - aIndex);

  Result := Write(aIndex, aPtr, aElementCount);
end;

procedure TVec.Insert(aIndex: SizeUInt; const aElement: T);
begin
  if not Insert(aIndex, @aElement, 1) then
    raise EWrite.Create('TVec.Insert: failed to insert');
end;

procedure TVec.InsertFromArray(aIndex: SizeUInt; const aElements: array of T);
var
  LLen: SizeUint;
begin
  LLen := Length(aElements);

  if LLen = 0 then
    exit;

  if not InsertFromMemory(aIndex, @aElements[0], LLen) then
    raise EWrite.Create('TVec.InsertFromArray: failed to insert');
end;

function TVec.InsertFromCollection(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean;
begin
  Result := (aCollection <> nil) and (aCollection <> Self) and (aCount > 0);

  if Result then
  begin
    Result := (aIndex <= FCount);

    if Result then
    begin
      Resize(FCount + aCount);
  
      if aIndex < FCount then
        Copy(aIndex, aIndex + aCount, FCount - aIndex);

      Result := aCollection.WriteToArrayMemory(FBuf.GetPtrUnChecked(aIndex), aCount);
    end;
  end;
end;


procedure TVec.Push(const aElement: T);
begin
  Push(@aElement, 1);
end;

procedure TVec.Push(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  Insert(FCount, aSrc, aElementCount);
end;

procedure TVec.Push(const aSrc: array of T);
begin
  Push(@aSrc[0], Length(aSrc));
end;

procedure TVec.Push(const aSrc: TCollection; aElementCount: SizeUInt);
begin
  Insert(FCount, aSrc, aElementCount);
end;


function TVec.TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean;
var
  LIndex: SizeUint;
begin
  assert(aElementCount > 0, 'TVec.TryPop: element count is 0');
  assert(aDst <> nil, 'TVec.TryPop: aPtr is nil');
  assert(FCount >= aElementCount, 'TVec.TryPop: count is less than element count');

  Result := (aElementCount > 0) and (FCount >= aElementCount) and (aDst <> nil);

  if Result then
  begin
    LIndex := FCount - aElementCount;
    Read(LIndex, aDst, aElementCount);
    Resize(LIndex);
  end;
end;

function TVec.TryPop(var aElements: specialize TGenericArray<T>; aElementCount: SizeUint): Boolean;
var
  LLen: SizeUint;
begin
  if aElementCount = 0 then
    exit(False);

  LLen := Length(aElements);

  if LLen <> aElementCount then
    SetLength(aElements, aElementCount);

  Result := TryPop(@aElements[0], aElementCount);
end;

function TVec.TryPop(var aElement: T): Boolean;
begin
  Result := TryPop(@aElement, 1);
end;

function TVec.Pop: T;
begin
  if not TryPop(@Result, 1) then
    raise EFail.Create('TVec.Pop: failed to pop');

  // {$HINTS OFF}
  // {$WARN 5093 OFF}
  // if not Pop(Result) then{$WARN 5093 ON}{$HINTS ON}
  //   raise ERead.Create('TVec.Pop: failed to pop');
end;

function TVec.PeekRange(aElementCount: SizeUInt): PGenericPtr;
begin
  if (aElementCount = 0) or (aElementCount > FCount) then
    exit(nil);

  Result := GetPtrUnChecked(FCount - aElementCount);
end;

function TVec.TryPeek(var aElement: T): Boolean;
var
  LP: Pointer;
begin
  LP     := PeekRange(1);
  Result := (LP <> nil);

  if Result then
    aElement := PGenericPtr(LP)^;
end;

function TVec.Peek: T;
begin
  if not TryPeek(Result) then
    raise ERead.Create('TVec.Peek: failed to peek');
  // {$HINTS OFF}
  // {$WARN 5093 OFF}
  // if not Peek(Result) then{$WARN 5093 ON}{$HINTS ON}
  //   raise ERead.Create('TVec.Peek: failed to peek');
end;

function TVec.TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean;
var
  LPtr: PGenericPtr;
begin
  Assert(aElementCount > 0, 'TVec.TryPeekCopy: element count is 0');
  Assert(aDst <> nil, 'TVec.TryPeekCopy: aDst is nil');

  if (aElementCount = 0) or (aDst = nil) then
    exit(False);

  LPtr := PeekRange(aElementCount);
  Result := (LPtr <> nil);

  if Result then
    FElementAllocator.CopyElementsNonOverlapUnChecked(LPtr, aDst, aElementCount);
end;

function TVec.TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
var
  LLen: SizeUint;
begin
  if aElementCount = 0 then
    exit(False);

  LLen := Length(aDst);

  if LLen <> aElementCount then
    SetLength(aDst, aElementCount);

  Result := TryPeekCopy(@aDst[0], aElementCount);
end;

procedure TVec.Delete(aIndex, aElementCount: SizeUInt);
var
  LRight:      SizeUint;
  LRightCount: SizeUint;
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TVec.Delete: element count is 0');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.Delete: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TVec.Delete: bounds out of range');

  if (aIndex = 0) and (aElementCount = FCount) then
    Clear
  else
  begin
    LRight      := aIndex + aElementCount;
    LRightCount := FCount - LRight;

    if LRightCount > 0 then
      Copy(LRight, aIndex, LRightCount);

    Resize(FCount - aElementCount);
  end;
end;

procedure TVec.Delete(aIndex: SizeUInt);
begin
  Delete(aIndex, 1);
end;

procedure TVec.DeleteSwap(aIndex, aElementCount: SizeUInt);
var
  LRight:      SizeUint;
  LRightCount: SizeUint;
begin
  if aElementCount = 0 then
    raise EIsZero.Create('TVec.DeleteSwap: element count is 0');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TVec.DeleteSwap: index out of bounds');

  if (aIndex = 0) and (aElementCount = FCount) then
    Clear
  else
  begin
    LRight      := aIndex + aElementCount;
    LRightCount := FCount - LRight;
  
    if LRightCount > 0 then
    begin
      if LRightCount > aElementCount then
        Copy(FCount - aElementCount, aIndex, aElementCount)
      else
        Copy(LRight, aIndex, LRightCount);
    end;

  Resize(FCount - aElementCount);
  end;
end;

procedure TVec.DeleteSwap(aIndex: SizeUInt);
begin
  DeleteSwap(aIndex, 1);
end;

function TVec.RemoveMemory(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
begin
  if (aElementCount = 0) or (aIndex + aElementCount > FCount) or (aPtr = nil) then
    exit(False);

  Result := Read(aIndex, aPtr, aElementCount);

  if Result then
    Delete(aIndex, aElementCount);
end;

function TVec.RemoveMemory(aIndex: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := RemoveMemory(aIndex, 1, aPtr);
end;

function TVec.RemoveMemorySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
begin
  if (aElementCount = 0) or (aIndex + aElementCount > FCount) or (aPtr = nil) then
    exit(False);

  Result := Read(aIndex, aPtr, aElementCount);

  if Result then
    DeleteSwap(aIndex, aElementCount);
end;

function TVec.RemoveMemorySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := RemoveMemorySwap(aIndex, 1, aPtr);
end;

function TVec.RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
var
  LLen: SizeUint;
begin
  if aElementCount = 0 then
    exit(False);

  LLen := Length(aElements);

  if LLen <> aElementCount then
    SetLength(aElements, aElementCount);

  Result := RemoveMemory(aIndex, aElementCount, @aElements[0]);
end;

function TVec.Remove(aIndex: SizeUInt; var aElement: T): Boolean;
begin
  Result := RemoveMemory(aIndex, 1, @aElement);
end;

function TVec.Remove(aIndex: SizeUInt): T;
begin
  {$HINTS OFF}
  {$WARN 5093 OFF}
  if not Remove(aIndex, Result) then{$WARN 5093 ON}{$HINTS ON}
    raise ERead.Create('TVec.Remove: failed to remove');
end;

function TVec.RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
var
  LLen: SizeUint;
begin
  if aElementCount = 0 then
    exit(False);

  LLen := Length(aElements);

  if LLen <> aElementCount then
    SetLength(aElements, aElementCount);

  Result := RemoveMemorySwap(aIndex, aElementCount, @aElements[0]);
end;

function TVec.RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean;
begin
  Result := RemoveMemorySwap(aIndex, 1, @aElement);
end;

function TVec.RemoveSwap(aIndex: SizeUInt): T;
begin
  {$HINTS OFF}
  {$WARN 5093 OFF}
  if not RemoveSwap(aIndex, Result) then{$WARN 5093 ON}{$HINTS ON}
    raise ERead.Create('TVec.RemoveSwap: failed to remove swap');
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LCapacity: SizeUint;
  LEnd:      SizeUint;
begin
  if aElementCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TVec.Write: aSrc is nil');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TVec.Write: index out of range');

  WriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.WriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LEnd:      SizeUInt;
  LCapacity: SizeUInt;
begin
  LEnd      := aIndex + aElementCount;
  LCapacity := GetCapacity;

  if LCapacity < LEnd then
    Reserve(LEnd - FCount);

  FBuf.OverWriteUnChecked(aIndex, aSrc, aElementCount);

  if LEnd > FCount then
    FCount := LEnd;
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: array of T);
begin
  WriteUnChecked(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.WriteUnChecked(aIndex: SizeUInt; const aSrc: array of T);
begin
  WriteUnChecked(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TVec.Write: aSrc is nil');

  Write(aIndex, aSrc, aSrc.GetCount);
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TVec.Write: aSrc is nil');

  if not IsCompatible(aSrc) then
    raise EIncompatible.Create('TVec.Write: aSrc is not compatible');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TVec.Write: index out of range');

  WriteUnChecked(aIndex, aSrc, aCount);
end;

procedure TVec.WriteUnChecked(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  aSrc.WriteToArrayMemory(GetPtrUnChecked(aIndex), aCount);
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LCapacity: SizeUint;
  LEnd:      SizeUint;
begin
  if aElementCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TVec.WriteExact: aSrc is nil');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TVec.WriteExact: index out of range');

  WriteExactUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.WriteExactUnChecked(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LCapacity: SizeUint;
  LEnd:      SizeUInt;
begin
  LEnd      := aIndex + aElementCount;
  LCapacity := GetCapacity;

  if LCapacity < LEnd then
    ResizeExact(LEnd);

  FBuf.OverWriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aArray: array of T);
begin
  WriteExact(aIndex, @aArray[0], LLen);
end;

procedure TVec.WriteExactUnChecked(aIndex: SizeUint; const aArray: array of T);
begin
  WriteExactUnChecked(aIndex, @aArray[0], Length(aArray));
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: TCollection);
begin
  if aSrc = nil then
    raise ENil.Create('TVec.WriteExact: aSrc is nil');

  WriteExact(aIndex, aSrc, aSrc.GetCount);
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aSrc = nil then
    raise ENil.Create('TVec.WriteExact: aSrc is nil');

  if not IsCompatible(aSrc) then
    raise EIncompatible.Create('TVec.WriteExact: aSrc is not compatible');

  if aIndex > FCount then
    raise ERangeOutOfIndex.Create('TVec.WriteExact: index out of range');

  WriteExactUnChecked(aIndex, aSrc, aCount);
end;

procedure TVec.WriteExactUnChecked(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);
var
  LEnd: SizeUint;
begin
  LEnd := aIndex + aCount;

  if (LEnd > FCount) then
    ResizeExact(LEnd);

  aSrc.WriteToArrayMemory(FBuf.GetPtrUnChecked(aIndex), aCount);
end;

procedure TVec.Read(aIndex: SizeUInt; aDst: Pointer; aElementCount: SizeUInt);
begin
  if aElementCount = 0 then
    exit;
  
  if aDst = nil then
    raise ENil.Create('TVec.Read: aDst is nil');

  if aIndex >= FCount then
    raise ERangeOutOfIndex.Create('TVec.Read: index out of range');

  if aIndex + aElementCount > FCount then
    raise ERangeOutOfBounds.Create('TVec.Read: bounds out of range');

  FElementAllocator.CopyElementsNonOverlapUnChecked(GetPtrUnChecked(aIndex), aDst, aElementCount);
end;

procedure TVec.Read(aIndex: SizeUInt; var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt);
var
  LLen:   SizeUint;
begin
  if aElementCount = 0 then
    exit;

  LLen := Length(aDst);

  if LLen <> aElementCount then
    SetLength(aDst, aElementCount);

  Read(aIndex, @aDst[0], aElementCount);
end;

constructor TVec.TVecEnumerator.Create(const aVec: specialize TVec<T>);
begin
  inherited Create;
  FVec := aVec;
  Reset;
end;

function TVec.TVecEnumerator.GetCurrent: T;
begin
  Result := FVec.GetUnChecked(FCurrentIndex);
end;

function TVec.TVecEnumerator.MoveNext: Boolean;
begin
  if not FStarted then
  begin
    FStarted      := True;
    FCurrentIndex := 0;
    Result := (FCurrentIndex < FVec.Count);
  end
  else 
  begin
    Inc(FCurrentIndex);
    Result := (FCurrentIndex < FVec.Count);
  end;
end;

procedure TVec.TVecEnumerator.Reset;
begin
  FStarted      := False;
  FCurrentIndex := 0;
end;


generic function makeArray<T>: specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(RtlMemAllocator);
end;

generic function makeArray<T>(aCount: SizeUInt): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(RtlMemAllocator, aCount);
end;



generic function makeArray<T>(aAllocator: TMemAllocator): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aAllocator);
end;

generic function makeArray<T>(aAllocator: TMemAllocator; aCount: SizeUInt): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aAllocator, aCount);
end;

generic function makeArray<T>(const aSrc: array of T): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc);
end;

generic function makeArray<T>(const aSrc: array of T; aAllocator: TMemAllocator): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc, aAllocator);
end;

generic function makeArray<T>(const aSrc: TCollection): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc);
end;

generic function makeArray<T>(const aSrc: TCollection; aAllocator: TMemAllocator): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc, aAllocator);
end;

generic function makeArray<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc, aElementCount);
end;

generic function makeArray<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator): specialize IArray<T>;
begin
  Result := specialize TArray<T>.Create(aSrc, aElementCount, aAllocator);
end;


generic function makeVec<T>: specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create;
end;

generic function makeVec<T>(aCapacity: SizeUint): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aCapacity);
end;

generic function makeVec<T>(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aCapacity, aGrowStrategy);
end;


generic function makeVec<T>(aAllocator: TMemAllocator): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aAllocator);
end;

generic function makeVec<T>(aAllocator: TMemAllocator; aCapacity: SizeUint): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aAllocator, aCapacity);
end;

generic function makeVec<T>(aAllocator: TMemAllocator; aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aAllocator, aCapacity, aGrowStrategy);
end;


generic function makeVec<T>(const aSrc: array of T): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc);
end;

generic function makeVec<T>(const aSrc: array of T; aAllocator: TMemAllocator): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aAllocator);
end;

generic function makeVec<T>(const aSrc: array of T; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aAllocator, aGrowStrategy);
end;


generic function makeVec<T>(const aSrc: TCollection): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc);
end;

generic function makeVec<T>(const aSrc: TCollection; aAllocator: TMemAllocator ): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aAllocator);
end;

generic function makeVec<T>(const aSrc: TCollection; aAllocator: TMemAllocator;  aGrowStrategy: TGrowthStrategy): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aAllocator, aGrowStrategy);
end;

generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aElementCount);
end;

generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aElementCount, aAllocator);
end;

generic function makeVec<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TMemAllocator; aGrowStrategy: TGrowthStrategy): specialize IVec<T>;
begin
  Result := specialize TVec<T>.Create(aSrc, aElementCount, aAllocator, aGrowStrategy);
end;


// function TVecDeque.IsFull: Boolean;
// begin
//   Result := (FCount = GetCapacity);
// end;

// function TVecDeque.WrapIndex(aLogicalIndex, aCapacity: SizeUint): SizeUint;
// begin
//   if aLogicalIndex >= aCapacity then
//     Result := aLogicalIndex - aCapacity
//   else
//     Result := aLogicalIndex;
// end;

// function TVecDeque.WrapAdd(aIndex, aAddend: SizeUint): SizeUint;
// begin
//   Result := {$Q-}WrapIndex(aIndex + aAddend, GetCapacity){$Q+};
// end;

// function TVecDeque.WrapSub(aIndex, aSubtrahend: SizeUint): SizeUint;
// var
//   LCapacity: SizeUint;
// begin
//   LCapacity := GetCapacity;
//   Result    := {$Q-}WrapIndex(aIndex - aSubtrahend + LCapacity, LCapacity){$Q+};
// end;

// function TVecDeque.GetPhysicalIndex(aLogicalIndex: SizeUint): SizeUint;
// begin
//   Result := WrapAdd(FHead, aLogicalIndex);
// end;

// function TVecDeque.GetPhysicalPtr(aLogicalIndex: SizeUint): PGenericPtr;
// begin
//   Result := FBuf.GetPtr(GetPhysicalIndex(aLogicalIndex));
// end;

// procedure TVecDeque.HandleCountChange(aNewCount: SizeUint);
// begin

// end;

// procedure TVecDeque.HandleCapacityChange(aNewCapacity: SizeUint);
// begin

// end;

// procedure TVecDeque.HandleCapacityIncrease(const aOldCapacity: SizeUint);
// var
//   LNewCapacity: SizeUInt;
//   LHeadLen:     SizeUInt;
//   LTailLen:     SizeUInt;
//   LNewHead:     SizeUInt;
// begin
//   LNewCapacity := GetCapacity;
//   assert(LNewCapacity >= aOldCapacity, 'New capacity must be greater than or equal to old capacity');

//   // H := head
//   // L := last element (`self.to_physical_idx(self.len - 1)`)
//   //
//   //    H             L
//   //   [o o o o o o o o ]
//   //    H             L
//   // A [o o o o o o o o . . . . . . . . ]
//   //        L H
//   //   [o o o o o o o o ]
//   //          H             L
//   // B [. . . o o o o o o o o . . . . . ]
//   //              L H
//   //   [o o o o o o o o ]
//   //              L                 H
//   // C [o o o o o o . . . . . . . . o o ]

//   { case A }
//   if FHead <= aOldCapacity - FCount then
//     exit;

//   LHeadLen := aOldCapacity - FHead;
//   LTailLen := FCount - LHeadLen;

//   if (LHeadLen > LTailLen) and (LNewCapacity >= LHeadLen) then
//   begin
//     { case B }
    
//   end
//   else
//   begin
//     { case C }
//     LNewHead := LNewCapacity - LHeadLen;

//     { copy }
//     FHead := LNewHead;
//   end;
// end;

// function TVecDeque.GetDefaultGrowStrategy: TGrowthStrategy;
// begin
//   Result := TGoldenRatioGrowStrategy.GetGlobal;
// end;

// constructor TVecDeque.Create(aAllocator: TMemAllocator; aCapacity, aGrowSize: SizeUint);
// begin
//   inherited Create(aAllocator);
//   try
//     FBuf      := TVecDequeBuf.Create(aAllocator, aCapacity);
//   except
//     on E: Exception do
//       raise EAlloc.CreateFmt('TVecDeque.Create: Failed to create internal buffer. Original error: %s', [E.Message]);
//   end;

//   SetGrowStrategy(GetDefaultGrowStrategy);
//   FHead     := 0;
//   FCount    := 0;
// end;

// constructor TVecDeque.Create(aAllocator: TMemAllocator; aCapacity: SizeUint);
// begin
//   Create(aAllocator, aCapacity, VEC_DEQUE_DEFAULT_GROW_SIZE);
// end;

// constructor TVecDeque.Create(aAllocator: TMemAllocator);
// begin
//     Create(aAllocator, VEC_DEQUE_DEFAULT_CAPACITY, VEC_DEQUE_DEFAULT_GROW_SIZE);
// end;

// constructor TVecDeque.Create(aCapacity, aGrowSize: SizeUint);
// begin
//   Create(RtlMemAllocator, aCapacity, aGrowSize);
// end;

// constructor TVecDeque.Create(aCapacity: SizeUint);
// begin
//   Create(aCapacity, VEC_DEQUE_DEFAULT_GROW_SIZE);
// end;

// constructor TVecDeque.Create;
// begin
//   Create(VEC_DEQUE_DEFAULT_CAPACITY, VEC_DEQUE_DEFAULT_GROW_SIZE);
// end;

// destructor TVecDeque.Destroy;
// begin
//   FBuf.Free;
//   inherited Destroy;
// end;

// function TVecDeque.GetHead: SizeUint;
// begin
//   Result := FHead;
// end;

// function TVecDeque.GetCount: SizeUint;
// begin
//   Result := FCount;
// end;

// procedure TVecDeque.Clear;
// begin
//   FBuf.Clear;
//   FHead  := 0;
//   FCount := 0;
// end;

// function TVecDeque.WriteToArrayMemory(aPtr: Pointer; aCount: SizeUInt): Boolean;
// var
//   LPhysicalIndex: SizeUInt;
//   LFirstPartLen:  SizeUInt;
// begin
//   Result := (aPtr <> nil) and (aCount > 0) and (aCount <= FCount);
  
//   if Result then
//   begin
//     LPhysicalIndex := GetPhysicalIndex(0); // 从逻辑索引0开始的物理索引
//     LFirstPartLen  := Min(aCount, FBuf.GetCount - LPhysicalIndex); // 第一部分的长度
      
//     { 复制第一部分 }
//     FElementAllocator.CopyElementsNonOverlap(FBuf.GetPtr(LPhysicalIndex), aPtr, LFirstPartLen);

//     { 如果有第二部分（环绕情况） }
//     if LFirstPartLen < aCount then
//       FElementAllocator.CopyElementsNonOverlap(FBuf.GetPtr(0), Pointer(PByte(aPtr) + LFirstPartLen * GetElementSize), aCount - LFirstPartLen);
//   end;
// end;

// function TVecDeque.GetEnumerator: specialize TEnumerator<T>;
// begin
//   Result := TVecDequeEnumerator.Create(Self);
// end;

// function TVecDeque.LoadFromUnChecked(const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.SaveToUnChecked(aDst: TCollection): Boolean;
// begin

// end;

// function TVecDeque.ForEach(aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
// begin
//   Result := True;
// end;

// function TVecDeque.ForEach(aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
// begin
//   Result := True;
// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.ForEach(aForEach: specialize TForEachRefFunc<T>): Boolean;
// begin
//   Result := True;
// end;
// {$ENDIF}

// function TVecDeque.Contains(const aValue: T): Boolean;
// begin
//   Result:=inherited Contains(aValue);
// end;

// function TVecDeque.Contains(const aValue: T; aStartIndex: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
// begin

// end;

// function TVecDeque.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
// begin

// end;
// {$ENDIF}

// function TVecDeque.Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
// begin

// end;

// function TVecDeque.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
// begin

// end;
// {$ENDIF}

// procedure TVecDeque.Reverse;
// begin

// end;

// procedure TVecDeque.Reverse(aStartIndex: SizeUInt);
// begin

// end;

// procedure TVecDeque.Reverse(aStartIndex, aCount: SizeUInt);
// begin

// end;


// function TVecDeque.Get(aIndex: SizeUint): T;
// var
//   LPtr: PGenericPtr;
// begin
//   LPtr := GetPtr(aIndex);

//   if LPtr = nil then
//     raise ENil.Create('Get: Ptr is nil');

//   Result := LPtr^;
// end;

// function TVecDeque.GetUnChecked(aIndex: SizeUInt): T;
// begin

// end;

// procedure TVecDeque.Put(aIndex: SizeUint; const aValue: T);
// var
//   LPtr: PGenericPtr;
// begin
//   LPtr := GetPtr(aIndex);

//   if LPtr = nil then
//     raise ENil.Create('Put: Ptr is nil');

//   LPtr^ := aValue;
// end;

// procedure TVecDeque.PutUnChecked(aIndex: SizeUInt; const aValue: T);
// begin

// end;

// function TVecDeque.GetPtr(aIndex: SizeUint): PGenericPtr;
// begin
//   if aIndex >= FCount then
//     exit(nil);

//   Result := GetPhysicalPtr(aIndex);
// end;

// function TVecDeque.GetPtrUnChecked(aIndex: SizeUint): specialize TGenericCollection<T>.PGenericPtr;
// begin

// end;

// function TVecDeque.GetMemory: PGenericPtr;
// begin
//   Result := FBuf.GetMemory;
// end;

// procedure TVecDeque.Resize(aNewSize: SizeUint);
// var
//   LCount: SizeUint;
// begin
//   LCount := GetCount;

//   if aNewSize = LCount then
//     exit;

//   if aNewSize = 0 then
//   begin
//     Clear;
//     exit;
//   end;

//   if aNewSize > LCount then
//   begin
//     // 扩大
//     if not TryReserve(aNewSize - LCount) then
//       exit;

//     {
//       "H" 表示 Head
//       "L" 表示 Last (Head + Count)
//       "." 表示容量内空闲空间
//       "-" 表示新启用的空间,需要初始化
//       "X" 表示需要移动的元素(并擦除)

//       扩大时将面临以下布局

//               . . . .                       ------- . . .  .  .  .  .  .
//       0,1,2,3,4,5,6,7  Resize(8)-> 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
//       H     L                        H             L

//       这是普通数组的场景,没有插入过头部,Head为0,扩大时,Head不变,设置新长度即可.
//       --------------------------------------------------------------------------------

//       . .                           . .            --- .  .  .  .  .  . 
//       0,1,2,3,4,5,6,7  Resize(8)-> 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
//           H         L                  H             L

//       这种布局是在插入过头部元素的场景,Head绕回,移除头部后产生,这跟case1一样(Last没有绕回的场景)
//       --------------------------------------------------------------------------------

//                 .                            - X X . . .  .  .  .  -----
//       0,1,2,3,4,5,6,7  Resize(8)-> 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
//               L   H                          L                     H    

//       Last 已经绕回,Head距离尾部近,移动头部元素到新启用的空间的尾部相对位置
//       --------------------------------------------------------------------------------

//           .                        X X X           ------ .  .  .  .  . 
//       0,1,2,3,4,5,6,7  Resize(8)-> 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15
//         L   H                            H             L                

//       Last 已经绕回,
//       --------------------------------------------------------------------------------
//     }
//     FBuf.Resize(aNewSize);
    
//   end
//   else
//   begin
//     // 缩小
//     HandleCapacityChange(aNewSize);
//   end;

//   // 处理可能的扩容
//   if (aNewSize > LCount) and (not TryReserve(aNewSize - LCount)) then
//     exit;

//   HandleCountChange(aNewSize);
//   FBuf.Resize(aNewSize);
// end;

// procedure TVecDeque.Ensure(aCount: SizeUint);
// begin
// end;

// procedure TVecDeque.Fill(aIndex, aElementCount: SizeUInt; const aValue: T);
// var
//   LCapacity:     SizeUInt;
//   LPhysical:     SizeUInt;
//   LFirstPartLen: SizeUInt;
// begin
//   if aIndex >= FCount then
//     raise ERangeOutOfIndex.Create('Fill: index out of range');

//   if aIndex + aElementCount > FCount then
//     raise ERangeOutOfBounds.Create('Fill: bounds out of range');

//   LCapacity     := FBuf.GetCount;
//   LPhysical     := GetPhysicalIndex(aIndex);
//   LFirstPartLen := Min(aElementCount, LCapacity - LPhysical);

//   { 第一段：从物理索引开始 }
//   FBuf.Fill(aValue, LPhysical, LFirstPartLen);

//   { 第二段（如果发生环绕） }
//   if LFirstPartLen < aElementCount then
//     FBuf.Fill(aValue, 0, aElementCount - LFirstPartLen);
// end;

// procedure TVecDeque.Fill(aIndex: SizeUInt; const aValue: T);
// begin
//   Fill(aIndex, FCount - aIndex, aValue);
// end;

// procedure TVecDeque.Fill(const aValue: T);
// begin
//   Fill(0, FCount, aValue);
// end;

// procedure TVecDeque.Zero(aIndex, aElementCount: SizeUInt);
// var
//   LCapacity:     SizeUInt;
//   LPhysical:     SizeUInt;
//   LFirstPartLen: SizeUInt;
// begin
//   if aIndex >= FCount then
//     raise ERangeOutOfIndex.Create('Zero: index out of range');

//   if aIndex + aElementCount > FCount then
//     raise ERangeOutOfBounds.Create('Zero: bounds out of range');

//   LCapacity     := FBuf.GetCount;
//   LPhysical     := GetPhysicalIndex(aIndex);
//   LFirstPartLen := Min(aElementCount, LCapacity - LPhysical);

//   { 前段清零 }
//   FBuf.Zero(LPhysical, LFirstPartLen);

//   { 环绕后段清零 }
//   if LFirstPartLen < aElementCount then
//     FBuf.Zero(0, aElementCount - LFirstPartLen);
// end;

// procedure TVecDeque.Zero(aIndex: SizeUInt);
// begin
//   Zero(aIndex, FCount - aIndex);
// end;

// procedure TVecDeque.Zero;
// begin
//   Zero(0, FCount);
// end;

// procedure TVecDeque.Swap(aIndex1, aIndex2: SizeUInt);
// begin
//   Swap(aIndex1, aIndex2, 1, ARRAY_DEFAULT_SWAP_BUFFER_SIZE);
// end;

// procedure TVecDeque.SwapUnChecked(aIndex1, aIndex2: SizeUInt);
// begin

// end;

// procedure TVecDeque.Swap(aIndex1, aIndex2, aElementCount: SizeUInt);
// begin
//   Swap(aIndex1, aIndex2, aElementCount, ARRAY_DEFAULT_SWAP_BUFFER_SIZE);
// end;

// procedure TVecDeque.Swap(aIndex1, aIndex2, aElementCount, aSwapBufferSize: SizeUInt);
// begin

// end;

// procedure TVecDeque.Copy(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
// begin

// end;

// procedure TVecDeque.CopyUnChecked(aSrcIndex, aDstIndex, aElementCount: SizeUInt);
// begin

// end;

// function TVecDeque.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
// begin

// end;

// function TVecDeque.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.ForEach(aStartIndex: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
// begin

// end;
// {$ENDIF}

// function TVecDeque.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachFunc<T>; aData: Pointer): Boolean;
// begin

// end;

// function TVecDeque.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachMethod<T>; aData: Pointer): Boolean;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.ForEach(aStartIndex, aCount: SizeUInt; aForEach: specialize TForEachRefFunc<T>): Boolean;
// begin

// end;
// {$ENDIF}

// function TVecDeque.Find(const aValue: T): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.Find(const aValue: T; aStartIndex: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// procedure TVecDeque.Sort;
// begin

// end;

// procedure TVecDeque.Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer);
// begin

// end;

// procedure TVecDeque.Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer);
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// procedure TVecDeque.Sort(aComparer: specialize TCompareRefFunc<T>);
// begin

// end;
// {$ENDIF}

// procedure TVecDeque.Sort(aStartIndex: SizeUInt);
// begin

// end;

// procedure TVecDeque.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
// begin

// end;

// procedure TVecDeque.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// procedure TVecDeque.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
// begin

// end;
// {$ENDIF}

// procedure TVecDeque.Sort(aStartIndex, aCount: SizeUInt);
// begin

// end;

// procedure TVecDeque.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
// begin

// end;

// procedure TVecDeque.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// procedure TVecDeque.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearch(const aValue: T): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearch(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearch(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearch(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearchInsert(const aValue: T): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearchInsert(const aValue: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
// begin

// end;

// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
// begin

// end;

// {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
// function TVecDeque.BinarySearchInsert(const aValue: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
// begin

// end;
// {$ENDIF}


// procedure TVecDeque.ResizeExact(aNewSize: SizeUint);
// begin

// end;

// function TVecDeque.GetCapacity: SizeUint;
// begin
//   Result := FBuf.GetCount;
// end;

// procedure TVecDeque.SetCapacity(aCapacity: SizeUint);
// begin
//   if not SetCapacity2(aCapacity) then
//     raise EResize.Create('VecDeque SetCapacity failed');
// end;

// function TVecDeque.SetCapacity2(aCapacity: SizeUint): Boolean;
// begin
//   FBuf.Resize(aCapacity);
// end;

// function TVecDeque.GetGrowStrategy: TGrowthStrategy;
// begin
//   Result := FGrowStrategy;
// end;

// procedure TVecDeque.SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
// begin
//   if aGrowStrategy = nil then
//     FGrowStrategy := GetDefaultGrowStrategy
//   else
//     FGrowStrategy := aGrowStrategy;
// end;

// function TVecDeque.TryReserve(aAdditional: SizeUint): Boolean;
// var
//   LNeededCapacity: SizeUint;
// begin
//   if aAdditional = 0 then
//     Exit(True);

//   LNeededCapacity := FCount + aAdditional;

//   FBuf.Resize(LNeededCapacity);
// end;

// procedure TVecDeque.Reserve(aAdditional: SizeUint);
// begin
//   if not TryReserve(aAdditional) then
//     raise EResize.Create('VecDeque Reserve failed');
// end;

// function TVecDeque.TryReserveExact(aAdditional: SizeUint): Boolean;
// begin

// end;

// procedure TVecDeque.ReserveExact(aAdditional: SizeUint);
// begin

// end;

// procedure TVecDeque.Shrink;
// begin

// end;

// procedure TVecDeque.ShrinkTo(aCapacity: SizeUint);
// begin

// end;

// procedure TVecDeque.Truncate(aCount: SizeUint);
// begin

// end;

// function TVecDeque.InsertFromMemory(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.InsertFromMemory(aIndex: SizeUint; const aPtr: Pointer): Boolean;
// begin

// end;

// procedure TVecDeque.Insert(aIndex: SizeUInt; const aElement: T);
// begin

// end;

// procedure TVecDeque.InsertFromArray(aIndex: SizeUInt; const aElements: array of T);
// begin

// end;

// function TVecDeque.InsertFromCollection(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>): Boolean;
// begin

// end;

// function TVecDeque.InsertFromCollection(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PushFromMemory(const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin
//   Result := PushBackPtr(aPtr, aElementCount);
// end;

// function TVecDeque.PushFromMemory(const aPtr: Pointer): Boolean;
// begin
//   Result := PushBackPtr(aPtr);
// end;

// procedure TVecDeque.PushFromArray(const aElements: array of T);
// begin
//   PushBack(aElements)
// end;

// function TVecDeque.PopToMemory(aPtr: Pointer; aCount: SizeUInt): Boolean;
// begin
//   Result := PopBackPtr(aPtr, aCount);
// end;

// function TVecDeque.PopToMemory(aPtr: Pointer): Boolean;
// begin
//   Result := PopBackPtr(aPtr);
// end;

// function TVecDeque.PopToArray(var aArray: specialize TGenericArray<T>; aCount: SizeUint): Boolean;
// begin
//   Result := PopBack(aArray, aCount);
// end;

// function TVecDeque.PeekMemory(aCount: SizeUInt): PGenericPtr;
// begin

// end;

// function TVecDeque.PeekMemory: PGenericPtr;
// begin

// end;

// function TVecDeque.PeekReadMemory(aPtr: Pointer; aElementCount: SizeUint): Boolean;
// begin

// end;

// function TVecDeque.PeekReadMemory(aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.PeekToArray(var aArray: specialize TGenericArray<T>; aCount: SizeUInt): Boolean;
// begin

// end;

// procedure TVecDeque.Delete(aIndex, aElementCount: SizeUInt);
// begin

// end;

// procedure TVecDeque.Delete(aIndex: SizeUInt);
// begin

// end;

// procedure TVecDeque.DeleteSwap(aIndex, aElementCount: SizeUInt);
// begin

// end;

// procedure TVecDeque.DeleteSwap(aIndex: SizeUInt);
// begin

// end;

// function TVecDeque.RemoveMemory(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.RemoveMemory(aIndex: SizeUInt; aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.RemoveMemorySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.RemoveMemorySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
// begin

// end;

// function TVecDeque.Remove(aIndex: SizeUInt; var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.Remove(aIndex: SizeUInt): T;
// begin

// end;

// function TVecDeque.RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
// begin

// end;

// function TVecDeque.RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.RemoveSwap(aIndex: SizeUInt): T;
// begin

// end;

// function TVecDeque.Write(aIndex: SizeUInt; const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.Write(aIndex: SizeUInt; const aElements: array of T): Boolean;
// begin

// end;

// function TVecDeque.Write(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>): Boolean;
// begin

// end;

// function TVecDeque.Write(aIndex: SizeUInt; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.Read(aIndex: SizeUInt; aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.WriteExact(aIndex: SizeUint; const aArray: array of T): Boolean;
// begin

// end;

// function TVecDeque.WriteExact(aIndex: SizeUint; const aCollection: specialize TGenericCollection<T>): Boolean;
// begin

// end;

// function TVecDeque.WriteExact(aIndex: SizeUint; const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.Read(aIndex: SizeUInt; var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
// begin

// end;

// procedure TVecDeque.Enqueue(const aElement: T);
// begin

// end;

// procedure TVecDeque.Push(const aElement: T);
// begin

// end;

// function TVecDeque.Dequeue: T;
// begin

// end;

// function TVecDeque.Dequeue(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.Pop: T;
// begin

// end;

// function TVecDeque.Pop(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.Peek: T;
// begin

// end;

// function TVecDeque.Peek(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.PushFromCollection(const aCollection: specialize TGenericCollection<T>): Boolean;
// begin

// end;

// function TVecDeque.PushFromCollection(const aCollection: specialize TGenericCollection<T>; aCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PushFrontPtr(const aPtr: Pointer; aElementCount: SizeUInt
//   ): Boolean;
// begin

// end;

// function TVecDeque.PushFrontPtr(const aPtr: Pointer): Boolean;
// begin

// end;

// procedure TVecDeque.PushFront(const aElements: specialize TGenericCollection<T>);
// begin

// end;

// procedure TVecDeque.PushFront(const aElements: array of T);
// begin

// end;

// procedure TVecDeque.PushFront(const aElement: T);
// begin

// end;

// function TVecDeque.PushBackPtr(const aPtr: Pointer; aElementCount: SizeUInt
//   ): Boolean;
// var
//   LCurrentCount: SizeUInt;
//   LTargetCount:  SizeUInt;
//   LPhysicalIndex: SizeUInt;
//   LFirstPartLen:  SizeUInt;
// begin
//   if (aElementCount = 0) or (aPtr = nil) then
//   begin
//     Result := False;
//     exit;
//   end;

//   LCurrentCount := FCount;
//   LTargetCount  := LCurrentCount + aElementCount;

//   // 确保有足够的容量
//   if not TryReserve(aElementCount) then
//   begin
//     Result := False;
//     exit;
//   end;

//   // 计算写入的物理起始索引
//   LPhysicalIndex := WrapAdd(FHead, LCurrentCount);

//   // 计算第一部分的长度
//   LFirstPartLen := Min(aElementCount, FBuf.GetCount - LPhysicalIndex);

//   // 复制第一部分
//   FElementAllocator.CopyElementsNonOverlap(aPtr, FBuf.GetPtr(LPhysicalIndex), LFirstPartLen);

//   // 如果有第二部分（环绕情况）
//   if LFirstPartLen < aElementCount then
//   begin
//     FElementAllocator.CopyElementsNonOverlap(Pointer(PByte(aPtr) + LFirstPartLen * GetElementSize), FBuf.GetPtr(0), aElementCount - LFirstPartLen);
//   end;

//   FCount := LTargetCount; // 更新元素数量
//   Result := True;
// end;

// function TVecDeque.PushBackPtr(const aPtr: Pointer): Boolean;
// begin

// end;

// procedure TVecDeque.PushBack(const aElements: specialize TGenericCollection<T>);
// begin

// end;

// procedure TVecDeque.PushBack(const aElements: array of T);
// begin

// end;

// procedure TVecDeque.PushBack(const aElement: T);
// begin

// end;

// function TVecDeque.PopFrontPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PopFrontPtr(aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.PopFront(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PopFront(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.PopFront: T;
// begin

// end;

// function TVecDeque.PopBackPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PopBackPtr(aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.PopBack(var aElements: specialize TGenericArray<T>; 
//   aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PopBack(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.PopBack: T;
// begin

// end;

// function TVecDeque.PeekFrontPtr(aPtr: Pointer; aElementCount: SizeUInt): Boolean;
// begin

// end;

// function TVecDeque.PeekFrontPtr(aPtr: Pointer): Boolean;
// begin

// end;

// function TVecDeque.PeekFront: T;
// begin
//   if not PeekFrontPtr(@Result, 1) then
//     raise ERead.Create('VecDeque PeekFront failed');
// end;

// function TVecDeque.PeekFront(var aElement: T): Boolean;
// begin
//   Result := PeekFront(aElement);
// end;

// function TVecDeque.PeekFront(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
// begin
//   if aElementCount > FCount then
//     Exit(False);


// end;

// function TVecDeque.PeekBackPtr(aPtr: Pointer; aElementCount: SizeUInt): Pointer;
// begin
//   if aElementCount > FCount then
//     Exit(nil);

//   { 从尾部开始读取元素,处理可能的绕回 }
// end;

// function TVecDeque.PeekBackPtr(aPtr: Pointer): Pointer;
// begin
//   Result := PeekBackPtr(aPtr, 1);
// end;

// function TVecDeque.PeekBack: T;
// begin

// end;

// function TVecDeque.PeekBack(var aElement: T): Boolean;
// begin

// end;

// function TVecDeque.PeekBack(var aElements: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
// begin

// end;

// constructor TVecDeque.TVecDequeEnumerator.Create(const aVecDeque: specialize TVecDeque<T>);
// begin
//   inherited Create;
//   FVecDeque := aVecDeque;
//   Reset;
// end;

// function TVecDeque.TVecDequeEnumerator.GetCurrent: T;
// begin
//   Result := FVecDeque.FBuf.GetUnChecked(FCurrentIndex);
// end;

// function TVecDeque.TVecDequeEnumerator.MoveNext: Boolean;
// begin
//   if not FStarted then
//   begin
//     FStarted      := True;
//     FCurrentIndex := 0;
//     Result := (FCurrentIndex < FVecDeque.Count);
//   end
//   else 
//   begin
//     Inc(FCurrentIndex);
//     Result := (FCurrentIndex < FVecDeque.Count);
//   end;
// end;

// procedure TVecDeque.TVecDequeEnumerator.Reset;
// begin
//   FStarted      := False;
//   FCurrentIndex := 0;
// end;

finalization
  if _RTLAllocator <> nil then
    _RTLAllocator.Free;

end.
