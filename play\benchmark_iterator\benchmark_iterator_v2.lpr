program benchmark_iterator_v2;

{$mode objfpc}{$H+}
{$O3}

uses
  SysUtils, Windows;

const
  ITERATION_COUNT = 100000000; // 一亿次

type
  // --- 多态 (Polymorphism) 版本 --- 
  IBenchIterator = interface
  ['{1A7E8A5A-5B1A-4D7E-8F2B-2E4A6E1A0E4A}']
    function MoveNext: Boolean;
    function GetCurrent: Integer;
    property Current: Integer read GetCurrent;
  end;

  TDummyState = record
    Counter: Integer;
  end;
  PDummyState = ^TDummyState;

  TPolymorphicIterator = class(TInterfacedObject, IBenchIterator)
  private
    FState: TDummyState;
  public
    constructor Create;
    destructor Destroy; override;
    function MoveNext: Boolean; virtual;
    function GetCurrent: Integer; virtual;
    property Current: Integer read GetCurrent;
  end;

  // --- 回调 (Callback) 版本 ---
  TMoveNextProc = function(aState: PDummyState): Boolean of object;
  TGetCurrentProc = function(const aState: PDummyState): Integer of object;

  TCallbackIterator = class
  private
    FState: TDummyState;
    FMoveNextProc: TMoveNextProc;
    FGetCurrentProc: TGetCurrentProc;
    FOwner: TObject;
  public
    constructor Create(aOwner: TObject; aMoveNextProc: TMoveNextProc; aGetCurrentProc: TGetCurrentProc);
    destructor Destroy; override;
    function MoveNext: Boolean;
    function GetCurrent: Integer;
    property Current: Integer read GetCurrent;
  end;

  // 一个持有具体实现的对象
  TDummyContainer = class
  public
    function MoveNextImplementation(aState: PDummyState): Boolean;
    function GetCurrentImplementation(const aState: PDummyState): Integer;
  end;

{ TPolymorphicIterator }

constructor TPolymorphicIterator.Create;
begin
  inherited Create;
  New(FState);
  FState.Counter := 0;
end;

destructor TPolymorphicIterator.Destroy;
begin
  Dispose(FState);
  inherited Destroy;
end;

function TPolymorphicIterator.MoveNext: Boolean;
begin
  Inc(FState.Counter);
  Result := FState.Counter < ITERATION_COUNT;
end;

function TPolymorphicIterator.GetCurrent: Integer;
begin
  Result := FState.Counter;
end;

{ TCallbackIterator }

constructor TCallbackIterator.Create(aOwner: TObject; aMoveNextProc: TMoveNextProc; aGetCurrentProc: TGetCurrentProc);
begin
  inherited Create;
  New(FState);
  FState.Counter := 0;
  FOwner := aOwner;
  FMoveNextProc := aMoveNextProc;
  FGetCurrentProc := aGetCurrentProc;
end;

destructor TCallbackIterator.Destroy;
begin
  Dispose(FState);
  inherited Destroy;
end;

function TCallbackIterator.MoveNext: Boolean;
begin
  Result := FMoveNextProc(FState);
end;

function TCallbackIterator.GetCurrent: Integer;
begin
  Result := FGetCurrentProc(FState);
end;

{ TDummyContainer }

function TDummyContainer.MoveNextImplementation(aState: PDummyState): Boolean;
begin
  Inc(aState.Counter);
  Result := aState.Counter < ITERATION_COUNT;
end;

function TDummyContainer.GetCurrentImplementation(const aState: PDummyState): Integer;
begin
  Result := aState.Counter;
end;

var
  LPolyIter: IBenchIterator;
  LCallbackIter: TCallbackIterator;
  LContainer: TDummyContainer;
  LStartTime, LEndTime: UInt64;
  LTotal: Int64; // 使用 Int64 避免溢出

begin
  WriteLn('Starting Iterator Benchmark V2 (with GetCurrent)...');
  WriteLn('Iterations per test: ', ITERATION_COUNT);
  WriteLn('------------------------------------');

  // --- 测试多态版本 ---
  LPolyIter := TPolymorphicIterator.Create;
  LTotal := 0;
  LStartTime := GetTickCount64;

  while LPolyIter.MoveNext do
  begin
    LTotal := LTotal + LPolyIter.Current;
  end;

  LEndTime := GetTickCount64;
  WriteLn('Polymorphic (Virtual Call) Time: ', LEndTime - LStartTime, ' ms');
  WriteLn('  (Checksum to prevent optimization: ', LTotal, ')');
  LPolyIter := nil;

  // --- 测试回调版本 ---
  LContainer := TDummyContainer.Create;
  LCallbackIter := TCallbackIterator.Create(LContainer, @LContainer.MoveNextImplementation, @LContainer.GetCurrentImplementation);
  LTotal := 0;
  LStartTime := GetTickCount64;

  while LCallbackIter.MoveNext do
  begin
    LTotal := LTotal + LCallbackIter.Current;
  end;

  LEndTime := GetTickCount64;
  WriteLn('Callback (Method Pointer) Time:  ', LEndTime - LStartTime, ' ms');
  WriteLn('  (Checksum to prevent optimization: ', LTotal, ')');
  LCallbackIter.Free;
  LContainer.Free;

  WriteLn('------------------------------------');
  WriteLn('Benchmark Finished. Press Enter to exit.');
  ReadLn;
end.
