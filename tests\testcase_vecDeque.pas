unit testcase_vecDeque;

{$mode objfpc}{$H+}
{$modeswitch functionreferences}
{$ModeSwitch anonymousfunctions}
interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  //.
  fafafa.collections;

type

  { TTestCase_vecDeque }

  TTestCase_vecDeque= class(TTestCase)
  published
    procedure Test_Create_Allocator_Capacity_GrowSize;
    procedure Test_Create_Allocator_Capacity;
    procedure Test_Create_Allocator;
    procedure Test_Create_Capacity_GrowSize;
    procedure Test_Create_Capacity;
    procedure Test_Create;

    procedure Test_GetHead;
  end;

implementation

function TestGetMem(aSize: SizeUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function TestAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function TestReallocMem(aPtr: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure TestFreeMem(aPtr: Pointer);
begin
  FreeMem(aPtr);
end;

function createAllocator: TMemAllocator;
begin
  Result := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
end;


procedure TTestCase_vecDeque.Test_Create_Allocator_Capacity_GrowSize;
const
  CAPACITY = 10;
  GROWSIZE = 5;
var
  LDeque:     specialize TVecDeque<Integer>;
  LAllocator: TMemAllocator;
begin
  LAllocator := createAllocator;
  try
    LDeque := specialize TVecDeque<Integer>.Create(LAllocator, CAPACITY, GROWSIZE);
    try
      AssertEquals('Head should be equal to 0', 0, LDeque.Head);
      AssertEquals('Count should be equal to 0', 0, LDeque.Count);
      AssertEquals('Capacity should be equal to CAPACITY', CAPACITY, LDeque.Capacity);
      AssertEquals('GrowSize should be equal to GROWSIZE', GROWSIZE, LDeque.GrowSize);
    finally
      LDeque.Free;
    end;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_Create_Allocator_Capacity;
const
  CAPACITY = 10;
var
  LDeque:     specialize TVecDeque<Integer>;
  LAllocator: TMemAllocator;
begin
  LAllocator := createAllocator;
  try
    LDeque := specialize TVecDeque<Integer>.Create(LAllocator, CAPACITY);
    try
      AssertEquals('Head should be equal to 0', 0, LDeque.Head);
      AssertEquals('Count should be equal to 0', 0, LDeque.Count);
      AssertEquals('Capacity should be equal to CAPACITY', CAPACITY, LDeque.Capacity);
      AssertEquals('GrowSize should be equal to 0', VEC_DEQUE_DEFAULT_GROW_SIZE, LDeque.GrowSize);
    finally
      LDeque.Free;
    end;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_Create_Allocator;
var
  LDeque: specialize TVecDeque<Integer>;
  LAllocator: TMemAllocator;
begin
  LAllocator := createAllocator;
  try
    LDeque := specialize TVecDeque<Integer>.Create(LAllocator);
    try
      AssertEquals('Head should be equal to 0', 0, LDeque.Head);
      AssertEquals('Count should be equal to 0', 0, LDeque.Count);
      AssertEquals('Capacity should be equal to 0', VEC_DEQUE_DEFAULT_CAPACITY, LDeque.Capacity);
      AssertEquals('GrowSize should be equal to 0', VEC_DEQUE_DEFAULT_GROW_SIZE, LDeque.GrowSize);
    finally
      LDeque.Free;
    end;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_Create_Capacity_GrowSize;
const
  CAPACITY = 10;
  GROWSIZE = 5;
var
  LDeque: specialize TVecDeque<Integer>;
begin
  LDeque := specialize TVecDeque<Integer>.Create(CAPACITY, GROWSIZE);
  try
    AssertEquals('Head should be equal to 0', 0, LDeque.Head);
    AssertEquals('Count should be equal to 0', 0, LDeque.Count);
    AssertEquals('Capacity should be equal to CAPACITY', CAPACITY, LDeque.Capacity);
    AssertEquals('GrowSize should be equal to GROWSIZE', GROWSIZE, LDeque.GrowSize);
    AssertTrue('Allocator should be equal to GetDefaultMemAllocator', LDeque.GetAllocator = RtlMemAllocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_Create_Capacity;
const
  CAPACITY = 10;
var
  LDeque: specialize TVecDeque<Integer>;
begin
  LDeque := specialize TVecDeque<Integer>.Create(CAPACITY);
  try
    AssertEquals('Head should be equal to 0', 0, LDeque.Head);
    AssertEquals('Count should be equal to 0', 0, LDeque.Count);
    AssertEquals('Capacity should be equal to CAPACITY', CAPACITY, LDeque.Capacity);
    AssertEquals('GrowSize should be equal to 0', VEC_DEQUE_DEFAULT_GROW_SIZE, LDeque.GrowSize);
    AssertTrue('Allocator should be equal to GetDefaultMemAllocator', LDeque.GetAllocator = RtlMemAllocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_Create;
var
  LDeque: specialize TVecDeque<Integer>;
begin
  LDeque := specialize TVecDeque<Integer>.Create;
  try
    AssertEquals('Head should be equal to 0', 0, LDeque.Head);
    AssertEquals('Count should be equal to 0', 0, LDeque.Count);
    AssertEquals('Capacity should be equal to 0', VEC_DEQUE_DEFAULT_CAPACITY, LDeque.Capacity);
    AssertEquals('GrowSize should be equal to 0', VEC_DEQUE_DEFAULT_GROW_SIZE, LDeque.GrowSize);
    AssertTrue('Allocator should be equal to GetDefaultMemAllocator', LDeque.GetAllocator = RtlMemAllocator);
  finally
    LDeque.Free;
  end;
end;

procedure TTestCase_vecDeque.Test_GetHead;
var
  LDeque: specialize TVecDeque<Integer>;
  LHead: Integer;
begin
  exit;
  LDeque := specialize TVecDeque<Integer>.Create(8);
  try
    LHead := LDeque.Head;
    AssertEquals('Head should be equal to 0', 0, LHead);
    AssertEquals('Capacity should be equal to 8', 8, LDeque.Capacity);

    { 尾部压入元素 }
    LDeque.PushBack(1);
    AssertEquals('Head should be equal to 0', 0, LHead);
    AssertEquals('Count should be equal to 1', 1, LDeque.Count);
    AssertEquals('Capacity should be equal to 8', 8, LDeque.Capacity);

    { 头部压入元素改变头部 }
    LDeque.PushFront(2);
    AssertEquals('Head should be equal to 1', 1, LHead);
    AssertEquals('Count should be equal to 2', 2, LDeque.Count);

    { 多次头部压入直至触发扩容 }


  finally
    LDeque.Free;
  end;
end;

initialization

  RegisterTest(TTestCase_vecDeque);
end.

