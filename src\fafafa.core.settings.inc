{ fafafa.core.settings.inc 框架配置文件 }

{ Enable inline 开启内联 }
{.$DEFINE FAFAFA_CORE_INLINE} 

{ 
  Enable crt memcpy, memmove, etc.
  Priority to using libc under linux, Performance of windows crt is not good, so it is not used.
  in linux, you need to link the libc library.
  启用 crt memcpy, memmove.
  在linux下,优先使用 libc, windows crt 性能表现不佳,无所谓.
  在linux下,开启此选项意味着要链接libc库.
}
{.$DEFINE FAFAFA_CORE_CRT_MEMCPY}
{.$DEFINE FAFAFA_CORE_CRT_MEMMOVE}

{ 
  Enable predefined common generic types, 
  which may lead to code bloat but allows for worry-free use of these types directly.
  启用预定义的常用泛型类型, 这会带来代码膨胀, 但直接用预定义的泛型类型, 是件省心的事情.
}
{.$DEFINE FAFAFA_CORE_TYPE_ALIASES} // Predefine common generic types 预定义常用泛型类型

{ 
  Enable crt allocator, use malloc, calloc, realloc, free, etc.
  in linux, if you want to use the crt allocator, you need to link the libc library.
  启用 crt 分配器, 使用 malloc, calloc, realloc, free 等函数.
  在linux下,开启此选项意味着要链接libc库.
}
{$DEFINE FAFAFA_CORE_CRT_ALLOCATOR}

{ 
  Anonymous functions/references are only supported in fpc 3.3.1 and above.
  开启匿名函数/匿名引用 仅限 fpc 3.3.1 及以上
}
{$IF FPC_FULLVERSION >= 030301}
  {$DEFINE FAFAFA_CORE_ANONYMOUS_REFERENCES}
{$ENDIF}

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
  {$ModeSwitch functionreferences}
  {$ModeSwitch anonymousfunctions}
{$ENDIF}
