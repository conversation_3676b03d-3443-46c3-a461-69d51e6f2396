2025.04.25, Version 1.51.0 (Stable), 5152db2cbfeb5582e9c27c5ea1dba2cd9e10759b

Changes since version 1.50.0:

* win: fix leak in uv_os_tmpdir (<PERSON>úl Ibarra Corretgé)

* docs: fix RTD build (<PERSON><PERSON> Corretgé)

* win: lazy-load [GS]etThreadDescription symbols (<PERSON>)

* linux: try preadv64/pwritev64 before preadv/pwritev (<PERSON>)

* win: check cwd length before spawning a child process (<PERSON><PERSON>)

* macos,bsd: handle missing /dev/null in chroot env (<PERSON><PERSON>)

* doc: fix README link text (<PERSON>)

* win: fix order of FILE_STAT_BASIC_INFORMATION struct fields (<PERSON><PERSON><PERSON><PERSON>)

* macos: increase child process stdio buffer size (Jin<PERSON>)

* doc: add C3 bindings to LINKS.md (Veli<PERSON><PERSON>)

* unix: remove unnecessary errno.h include in poll.c (<PERSON>)

* win: fix the inconsistency in volume serial number (<PERSON><PERSON><PERSON><PERSON>)

* unix: add thread affinity support on openharmony (rainlow)

* unix: enable getrusage for SunOS (Paolo In<PERSON>gna)

* unix,win: accept NAN/INFINITY as file timestamps (Ben Noordhuis)

* win: add ENABLE_VIRTUAL_TERMINAL_INPUT raw tty mode (Anna Henningsen)

* test: handle UV_ENOTSUP in platform_output (cjihrig)

* doc: fix rendering of threading.html (Tobias Nießen)

* unix,sunos: enable use of sendmmsg on Solaris and Illumos (Stacey Marshall)

* unix: handle out of memory in iface name copy (Ben Noordhuis)

* openbsd: do not error out if cpuspeed is not available (Robert Nagy)

* test: skip thread_name_threadpool on AIX/IBMi (Abdirahim Musse)

* aix,ibmi: fix undeclared identifiers (Richard Lau)

* unix,sunos: prefer SO_REUSEPORT for load balancing (Stacey Marshall)

* doc: free lib pointer before function return (mugitya03)

* test: link with libm (Juan José Arboleda)

* style: rename parameter to match definition (Mohammed Keyvanzadeh)

* test: support partial output lines in test runner (cjihrig)

* build: switch from c90 to c11 (Ben Noordhuis)

* linux: allow nul bytes in abstract socket address (Itay Bookstein)

* sunos: use pipe2 on solaris and illumos (Andy Pan)

* unix: remove TOCTOU issues from uv_pipe_chmod (Ben Noordhuis)

* unix: use pipe_fname if getsockname returns nothing (crupest)

* haiku: use uint32 instead of uint32_t (AE1020)

* doc: update thread pool stack size comment (Ben Noordhuis)

* unix: improve uv_loop_init OOM handling (Ben Noordhuis)

* test: merge uv_tcp_connect callbacks (Juan José Arboleda)

* test: skip multievent tests on macOS with TSAN enabled (Juan José Arboleda)

* linux: align CPU quota calculation with Rust (Juan José Arboleda)

* kqueue: improve fs event watcher OOM handling (Juan José Arboleda)

* sunos: improve fs event watcher OOM handling (Juan José Arboleda)

* build: shorten instructions for cmake build (Juan José Arboleda)


2025.01.15, Version 1.50.0 (Stable), 8fb9cb919489a48880680a56efecff6a7dfb4504

Changes since version 1.49.2:

* ci: run macOS and iOS tests also on macOS 14 (Saúl Ibarra Corretgé)

* unix,win: map ENOEXEC errno (Saúl Ibarra Corretgé)

* test: skip multicast join test on ENOEXEC (Saúl Ibarra Corretgé)

* ci: make sure the macOS firewall is disabled (Saúl Ibarra Corretgé)

* darwin,test: squelch EBUSY error on multicast join (Saúl Ibarra Corretgé)

* build: update minimum cmake to 3.10 (Ben Noordhuis)

* kqueue: use EVFILT_USER for async if available (Jameson Nash)

* unix,win: fix off-by-one in uv_wtf8_to_utf16() (Ben Noordhuis)

* doc: add scala-native-loop to LINKS.md (Julian A Avar C)

* unix: fix build breakage on haiku, openbsd, etc (Jeffrey H. Johnson)

* kqueue: lower overhead in uv__io_check_fd (Andy Pan)

* doc: move cjihrig back to active maintainers (cjihrig)

* build(deps): bump actions/checkout from 3 to 4 (dependabot[bot])

* unix,pipe: fix handling null buffer in uv_pipe_get{sock,peer}name (Saúl
  Ibarra Corretgé)

* unix,win: harmonize buffer checking (Saúl Ibarra Corretgé)

* unix,win: add support for detached threads (Juan José Arboleda)

* src: add uv_thread_set/getname() methods (Santiago Gimeno)

* build: fix qemu builds (Ben Noordhuis)

* win: drop support for windows 8 (Ben Noordhuis)

* linux: fix uv_cpu_info() arm cpu model detection (Ben Noordhuis)

* linux: always use io_uring for epoll batching (Ben Noordhuis)

* doc: clarify repeating timer behavior more (Ben Noordhuis)

* unix,win: handle nbufs=0 in uv_udp_try_send (Ben Noordhuis)

* win: use GetQueuedCompletionStatusEx directly (Saúl Ibarra Corretgé)

* win: enable uv_thread_{get,set}name on MinGW (Saúl Ibarra Corretgé)

* win: drop support for the legacy MinGW (Saúl Ibarra Corretgé)

* win,fs: get (most) fstat when no permission (Jameson Nash)

* win: plug uv_fs_event_start memory leak (amcgoogan)

* test: address FreeBSD kernel bug causing NULL path in fsevents (Juan José
  Arboleda)

* unix: refactor udp sendmsg code (Ben Noordhuis)

* unix,win: add uv_udp_try_send2 (Ben Noordhuis)

* test: fix flaky flaky udp_mmsg test (Juan José Arboleda)

* build: enable fdsan in Android (Juan José Arboleda)

* test: fix udp-multicast-join for FreeBSD (Juan José Arboleda)

* win: fix leak processing fs event (Saúl Ibarra Corretgé)

* src: set a default thread name for workers (Rafael Gonzaga)

* misc: implement uv_getrusage_thread (Juan José Arboleda)


2024.10.18, Version 1.49.2 (Stable), e1095c7a4373ce00cd8874d8e820de5afb25776e

Changes since version 1.49.1:

* win,fs: remove trailing slash in junctions (Hüseyin Açacak)

* Revert "linux: eliminate a read on eventfd per wakeup" (Ben Noordhuis)

* win: Fix linked list logic in getaddrinfo (Thad House)

* win: fix compilation against Windows 24H2 SDK (Thad House)

* win: remap ERROR_NOACCESS and ERROR_BUFFER_OVERFLOW (Jameson Nash)

* win,fs: match trailing slash presence in junctions to user input (Jameson
  Nash)


2024.10.11, Version 1.49.1 (Stable), 8be336f4ee296d20e1c071a44d6adf279e202236

Changes since version 1.49.0:

* build: add darwin-syscalls.h to release tarball (Ben Noordhuis)

* linux: use IORING_SETUP_NO_SQARRAY when available (Ben Noordhuis)

* linux: use IORING_OP_FTRUNCATE when available (Ben Noordhuis)

* win: fix pNtQueryDirectoryFile check (Rialbat)

* win: fix WriteFile() error translation (Santiago Gimeno)

* win,fs: uv_fs_rmdir() to return ENOENT on file (Santiago Gimeno)

* win,pipe: ipc code does not support async read (Jameson Nash)

* netbsd: fix build (Adam)

* win,fs: fix bug in fs__readdir (Hüseyin Açacak)

* unix: workaround gcc bug on armv7 (Santiago Gimeno)

* unix: work around arm-linux-gnueabihf-gcc bug (Ben Noordhuis)

* unix: fix uv_tcp_keepalive in smartOS (Santiago Gimeno)

* unix: fix uv_getrusage ru_maxrss on solaris (Poul T Lomholt)


2024.09.25, Version 1.49.0 (Stable), d2e56a5e8d3e39947b78405ca6e4727c70f5568a

Changes since version 1.48.0:

* test: fix -Wpointer-to-int-cast on 32 bits systems (Ben Noordhuis)

* build: add alias for libuv to CMakeLists.txt (Anthony Alayo)

* linux: create io_uring sqpoll ring lazily (Ben Noordhuis)

* misc: run sample CI when code changes (Jameson Nash)

* linux: fix uv_available_parallelism using cgroup (Thomas Walter)

* doc: fix tty example segfault (hiiizxf)

* udp,unix: fix sendmsg use-after-free (Geddy)

* cygwin: implement uv_resident_set_memory (Farzin Monsef)

* win: almost fix race detecting ESRCH in uv_kill (Santiago Gimeno)

* test: disable env var test under win32+asan (Ben Noordhuis)

* unix,fs: fix realpath calls that use the system allocator (Saúl Ibarra
  Corretgé)

* sunos: sync tcp keep-alive with other unices (Andy Pan)

* linux: fix /proc/self/stat executable name parsing (Farzin Monsef)

* test,ci: fix [AM]San, disable ASLR (Ben Noordhuis)

* win: remove _alloca usage (Ben Noordhuis)

* unix: reinstate preadv/pwritev fallback code (Ben Noordhuis)

* linux: don't delay EPOLL_CTL_DEL operations (Ben Noordhuis)

* doc: fix typos in ChangeLog (tgolang)

* unix,win: error on zero delay tcp keepalive (Saúl Ibarra Corretgé)

* win: simplify uv_once implementation (Saúl Ibarra Corretgé)

* doc: correct udp socket options documentation (Ben Noordhuis)

* linux: don't use sendmmsg() for single datagrams (Ben Noordhuis)

* unix: fix fd leaks in SCM_RIGHTS error path (Ben Noordhuis)

* win: robustify uv_os_getenv() error checking (Ben Noordhuis)

* test: use newer ASSERT_MEM_EQ macro (Ben Noordhuis)

* unix: de-duplicate conditions for using kqueue (Brad King)

* darwin: simplify uv_hrtime (Saúl Ibarra Corretgé)

* mailmap: update saghul's main email address (Saúl Ibarra Corretgé)

* win: remove no longer needed define (Saúl Ibarra Corretgé)

* doc: fix some typos (josedelinux)

* linux,darwin: make `uv_fs_copyfile` behaves like `cp -r` (Juan José Arboleda)

* dragonfly: disable SO_REUSEPORT for UDP socket bindings (Andy Pan)

* test: remove the obsolete HAVE_KQUEUE macro (Andy Pan)

* unix: use the presence of SOCK_* instead of OS macros for socketpair (Andy
  Pan)

* bsd: support pipe2() on *BSD (Andy Pan)

* unix: support SO_REUSEPORT with load balancing for TCP (Andy Pan)

* doc: add entries for extended getpw (Juan José Arboleda)

* test: fix the flaky test-tcp-reuseport (Andy Pan)

* aix,ibmi: fix compilation errors in fs_copyfile (Jeffrey H. Johnson)

* unix: support SO_REUSEPORT with load balancing for UDP (Andy Pan)

* tcpkeepalive: distinguish OS versions and use proper time units (Andy Pan)

* win: map ERROR_BAD_EXE_FORMAT to UV_EFTYPE (Hüseyin Açacak)

* doc: add instruction how to install with Conan (Uilian Ries)

* unix,win: remove unused req parameter from macros (Viacheslav Muravyev)

* build: fix android ci build (Ben Noordhuis)

* unix,win: export wtf8 functions properly (Ben Noordhuis)

* hurd: add includes and macro prerequisites (Olivier Valentin)

* hurd: stub uv_thread_setpriority() (Olivier Valentin)

* ci: use macOS 12 for macOS and iOS builds (Saúl Ibarra Corretgé)

* darwin: fix crash on iOS(arm64) (郑苏波 (Super Zheng))

* Create dependabot.yml for updating github-actions (Jameson Nash)

* doc: correct names of Win32 APIs in fs.rst (zeertzjq)

* ci: bump upload and download-artifact versions (dependabot[bot])

* ci: bump actions/setup-python from 4 to 5 (dependabot[bot])

* ci: bump KyleMayes/install-llvm-action from 1 to 2 (dependabot[bot])

* win,error: remap ERROR_NO_DATA to EAGAIN (Jameson Nash)

* test: handle zero-length udp datagram (Ben Noordhuis)

* misc: remove splay trees macros (Viacheslav Muravyev)

* test,openbsd: remove superfluous ifdef guard (Ben Noordhuis)

* win,fs: use posix delete semantics, if supported (Ian Butterworth)

* win: fix env var in uv_os_homedir and uv_os_tmpdir (Hüseyin Açacak)

* fsevents: detect watched directory removal (Santiago Gimeno)

* ci: bump actions/checkout to 4 (dependabot[bot])

* linux: eliminate a read on eventfd per wakeup (Andy Pan)

* test: pipe_overlong_path handle ENAMETOOLONG (Abdirahim Musse)

* win,fs: use the new Windows fast stat API (Hüseyin Açacak)

* win,pipe: fix race with concurrent readers (Jameson Nash)

* win,signal: fix data race dispatching SIGWINCH (Jameson Nash)

* build: ubsan fixes (Matheus Izvekov)

* linux: disable SQPOLL io_uring by default (Santiago Gimeno)

* win: fix fs.c ubsan failure (Matheus Izvekov)

* test: rmdir can return `EEXIST` or `ENOTEMPTY` (Richard Lau)

* test: check for `UV_CHANGE` or `UV_RENAME` event (Richard Lau)

* unix,fs: silence -Wunused-result warning (Santiago Gimeno)

* linux: support abstract unix socket autobinding (Ben Noordhuis)

* kqueue: use EVFILT_USER for async if available (Andy Pan)

* win: remove deprecated GetVersionExW call (Shelley Vohr)

* doc: document uv_loop_option (握猫猫)

* doc: fix the `uv_*_set_data` series of functions (握猫猫)

* doc: properly label enumerations and types (握猫猫)

* doc: document specific macOS fs_event behavior (Santiago Gimeno)

* win,pipe: restore fallback handling for blocking pipes (Jameson Nash)

* unix,win: remove unused rb-tree macro parameters (Viacheslav Muravyev)

* win: compute parallelism from process cpu affinity (Ben Noordhuis)

* win: use NtQueryInformationProcess in uv_os_getppid (Zuohui Yang)

* win,pipe: fix missing assignment to success (Jameson Nash)

* win: fix uv_available_parallelism on win32 (Ben Noordhuis)

* win,pipe: fix another missing assignment to success (Jameson Nash)

* kqueue: disallow ill-suited file descriptor kinds (Andy Pan)

* unix: restore tty attributes on handle close (Ben Noordhuis)

* test: delete test with invalid assumption (Ben Noordhuis)

* dragonflybsd: fix compilation failure (Jeffrey H. Johnson)

* test: run android tests on ci (Edigleysson Silva (Edy))

* darwin: add udp mmsg support (Raihaan Shouhell)

* unix: work around arm-linux-gnueabihf-gcc bug (Ben Noordhuis)

* unix: expand uv_available_parallelism() to support more platforms (Ondřej
  Surý)

* doc: add known issue in armv7 (Santiago Gimeno)


2024.02.07, Version 1.48.0 (Stable), e9f29cb984231524e3931aa0ae2c5dae1a32884e

Changes since version 1.47.0:

* misc: remove deprecated stalebot file (Jameson Nash)

* build: disable windows asan buildbot (Ben Noordhuis)

* test: don't run tcp_writealot under msan (Ben Noordhuis)

* build,win: remove extraneous -lshell32 (Ben Noordhuis)

* unix: ignore ifaddrs with NULL ifa_addr (Stephen Gallagher)

* unix,win: utility for setting priority for thread (Hao Hu)

* pipe: add back error handling to connect / bind (Jameson Nash)

* test: check if ipv6 link-local traffic is routable (Ben Noordhuis)

* win: remove check for UV_PIPE_NO_TRUNCATE (Jameson Nash)

* linux: disable io_uring on hppa below kernel 6.1.51 (matoro)

* unix,win: fix read past end of pipe name buffer (Ben Noordhuis)

* unix: unbreak macOS < 10.14 (Sergey Fedorov)

* aix: disable ipv6 link local (Abdirahim Musse)

* doc: move cjihrig to emeriti (cjihrig)

* unix: correct pwritev conditional (Bo Anderson)

* test_fs.c: Fix issue on 32-bit systems using btrfs (Stephen Gallagher)

* misc: ignore libuv-release-tool files (Jameson Nash)

* win: honor NoDefaultCurrentDirectoryInExePath env var (Ardi Nugraha)

* idna: fix compilation warning (Saúl Ibarra Corretgé)

* linux: remove HAVE_IFADDRS_H macro (Ben Noordhuis)

* test: skip tcp-write-in-a-row on IBM i (Abdirahim Musse)

* build,win: work around missing uuid.dll on MinGW (Anton Bachin)

* win: stop using deprecated names (Matheus Izvekov)

* unix,win: fix busy loop with zero timeout timers (Matheus Izvekov)

* aix,ibmi: use uv_interface_addresses instead of getifaddrs (Abdirahim Musse)

* linux: fix bind/connect for abstract sockets (Santiago Gimeno)

* win: replace c99 comments with c89 comments (Trevor Flynn)

* build: add .cache clangd folder to .gitignore (Juan José Arboleda)

* unix: support full TCP keep-alive on Solaris (Andy Pan)

* freebsd: fix F_KINFO file path handling (David Carlier)

* linux: retry fs op if unsupported by io_uring (Santiago Gimeno)

* freebsd: fix build on non-intel archs (David Carlier)

* unix: optimize uv__tcp_keepalive cpp directives (Andy Pan)

* linux: disable io_uring on ppc64 and ppc64le (Brad King)

* doc: add very basic Security Policy document (Santiago Gimeno)

* build: re-enable msvc-asan job on CI (Jameson Nash)

* win/spawn: optionally run executable paths with no file extension (Brad King)

* win: fix ESRCH implementation (Jameson Nash)

* unix,win: reset the timer queue on stop (Santiago Gimeno)

* fix: always zero-terminate idna output (Ben Noordhuis)

* fix: reject zero-length idna inputs (Ben Noordhuis)

* test: empty strings are not valid IDNA (Santiago Gimeno)

* Merge pull request from GHSA-f74f-cvh7-c6q6 (Ben Noordhuis)


2023.11.06, Version 1.47.0 (Stable), be6b81a352d17513c95be153afcb3148f1a451cd

Changes since version 1.46.0:

* test: fix license blurb (Ben Noordhuis)

* linux: fix harmless warn_unused_result warning (Shuduo Sang)

* darwin: fix build warnings (小明)

* linux: don't use io_uring on pre-5.10.186 kernels (Ben Noordhuis)

* fs: fix WTF-8 decoding issue (Jameson Nash)

* test: enable disabled tcp_connect6_error_fault (Ben Noordhuis)

* test: enable disabled fs_link (Ben Noordhuis)

* test: enable disabled spawn_same_stdout_stderr (Ben Noordhuis)

* linux: handle UNAME26 personality (Ben Noordhuis)

* build: move cmake_minimum_required version to 3.9 (Keith Winstein)

* unix: set ipv6 scope id for link-local addresses (Ben Noordhuis)

* unix: match kqueue and epoll code (Trevor Norris)

* win,spawn: allow `%PATH%` to be unset (Kyle Edwards)

* doc: switch to Furo, a more modern Sphinx theme (Saúl Ibarra Corretgé)

* darwin: make TCP_KEEPINTVL and TCP_KEEPCNT available (小明)

* win,fs: avoid winapi macro redefinition (Brad King)

* linux: add missing riscv syscall numbers (michalbiesek)

* doc: fix broken "Shared library" Wikipedia link (Alois Klink)

* unix: get mainline kernel version in Ubuntu (Santiago Gimeno)

* unix: get mainline kernel version in Debian (Ben Noordhuis)

* build: fix qemu install in CI-unix workflow (Santiago Gimeno)

* unix: disable io_uring close on selected kernels (Santiago Gimeno)

* test: skip tests when ipv6 is not available (Santiago Gimeno)

* ibmi: implement ifaddrs, getifaddrs, freeifaddrs (Abdirahim Musse)

* unix: reset signal counters after fork (SmorkalovG)

* win,process: avoid assert after spawning Store app (Jameson Nash)

* unix: remove pread/preadv conditionals (Ben Noordhuis)

* unix: remove pwrite/pwritev conditionals (Ben Noordhuis)

* darwin: remove workaround for data corruption bug (Ben Noordhuis)

* src: default to stream=stderr in handle printer (Ben Noordhuis)

* test: switch to new-style ASSERT_EQ macros (Pleuvens)

* zos: correctly get cpu model in uv_cpu_info() (jolai)

* test: fix get_passwd2 on IBM i (Abdirahim Musse)

* unix: don't malloc on sync uv_fs_read (Ben Noordhuis)

* freebsd: get fs event path with fcntl(F_KINFO) (David Carlier)

* test: switch from ASSERT_* to ASSERT_PTR_* (Pleuvens)

* darwin: workaround apple pthread_cond_wait bug (Julien Roncaglia)

* doc: uv_close should be called after exit callback (Pleuvens)

* test: *********/24 is the actual -TEST-NET-1 (prubel)

* unix: add back preadv/pwritev fallback (Ben Noordhuis)

* unix: rename variable for consistency (Ben Noordhuis)

* unix: merge read/write code into single functions (Ben Noordhuis)

* doc: filename arg to uv_fs_event_cb can be NULL (Ben Noordhuis)

* build,win: we need to link against shell32.lib (Per Allansson)

* unix: no preadv/pwritev workaround if not needed (Jeffrey H. Johnson)

* build: add CI for Windows ARM64 (build only) (Per Allansson)

* linux: disable io_uring on 32 bits arm systems (Ben Noordhuis)

* build: run sanitizers on macos ci (Ben Noordhuis)

* misc: export WTF8 conversion utilities (Jameson Nash)

* build: fix libuv.a file name for cmake (Jameson Nash)

* build: add windows ubsan and clang ci (Matheus Izvekov)

* win: improve accuracy of ProductName between arch (Christian Heimlich)


2023.06.30, Version 1.46.0 (Stable), f0bb7e40f0508bedf6fad33769b3f87bb8aedfa6

Changes since version 1.45.0:

* Add SHA to ChangeLog (Santiago Gimeno)

* misc: update readthedocs config (Jameson Nash)

* test: remove erroneous RETURN_SKIP (Ben Noordhuis)

* android: disable io_uring support (Ben Noordhuis)

* linux: add some more iouring backed fs ops (Santiago Gimeno)

* build: add autoconf option for disable-maintainer-mode (Jameson Nash)

* fs: use WTF-8 on Windows (Stefan Karpinski)

* unix,win: replace QUEUE with struct uv__queue (Ben Noordhuis)

* linux: fs_read to use io_uring if iovcnt > IOV_MAX (Santiago Gimeno)

* ios: fix uv_getrusage() ru_maxrss calculation (Ben Noordhuis)

* include: update outdated code comment (Ben Noordhuis)

* linux: support abstract unix sockets (Ben Noordhuis)

* unix,win: add UV_PIPE_NO_TRUNCATE flag (Ben Noordhuis)

* unix: add loongarch support (liuxiang88)

* doc: add DPS8M to LINKS.md (Jeffrey H. Johnson)

* include: add EUNATCH errno mapping (Abdirahim Musse)

* src: don't run timers if loop is stopped/unref'd (Trevor Norris)

* win: fix -Wpointer-to-int-cast warning (Ben Noordhuis)

* test,win: fix -Wunused-variable warning (Ben Noordhuis)

* test,win: fix -Wformat warning (Ben Noordhuis)

* linux: work around io_uring IORING_OP_CLOSE bug (Ben Noordhuis)

* win: remove unused functions (Ben Noordhuis)

* bench: add bench to check uv_loop_alive (Trevor Norris)

* test: add uv_cancel test for threadpool (Trevor Norris)

* unix: skip prohibited syscalls on tvOS and watchOS (小明)

* unix,fs: make no_pwritev access thread-safe (Santiago Gimeno)

* unix: fix build for lower versions of Android (小明)


2023.05.19, Version 1.45.0 (Stable), 96e05543f53b19d9642b4b0dd73b86ad3cea313e

Changes since version 1.44.2:

* win: remove stdint-msvc2008.h (Ben Noordhuis)

* android: remove pthread-fixes.c (Ben Noordhuis)

* build: enable MSVC_RUNTIME_LIBRARY setting (自发对称破缺)

* unix: switch to c11 atomics (Ben Noordhuis)

* unix: don't accept() connections in a loop (Ben Noordhuis)

* win: fix off-by-1 buffer overrun in uv_exepath() (Ben Noordhuis)

* build: switch ci from macos-10.15 to macos-11 (Ben Noordhuis)

* win: fix thread race in uv_cwd() and uv_chdir() (Ben Noordhuis)

* unix,win: remove UV_HANDLE_SHUTTING flag (Santiago Gimeno)

* win: support Windows 11 in uv_os_uname() (Luan Devecchi)

* unix: fix uv_getrusage() ru_maxrss reporting (Ben Noordhuis)

* doc: add note about offset -1 in uv_fs_read/write (Steven Schveighoffer)

* test: fix musl libc.a dlerror() test expectation (Ben Noordhuis)

* kqueue: DRY file descriptor deletion logic (Ben Noordhuis)

* linux: teach uv_get_constrained_memory() cgroupsv2 (Ben Noordhuis)

* build: upgrade qemu-user-static package (Ben Noordhuis)

* linux: move epoll.c back into linux-core.c (Ben Noordhuis)

* unix: remove pre-macos 10.8 compatibility hack (Ben Noordhuis)

* unix,win: fix memory leak in uv_fs_scandir() (Ben Noordhuis)

* build: restore qemu download logic (Ben Noordhuis)

* win: fix uv__pipe_accept memory leak (number201724)

* doc: update LINKS.md (Daniel)

* unix: simplify atomic op in uv_tty_reset_mode() (Ben Noordhuis)

* build: add LIBUV_BUILD_SHARED cmake option (Christian Clason)

* linux: remove unused or obsolete syscall wrappers (Ben Noordhuis)

* linux: merge files back into single file (Ben Noordhuis)

* stream: process more than one write req per loop tick (ywave620)

* unix,win: give thread pool threads an 8 MB stack (Ben Noordhuis)

* build: add MemorySanitizer (MSAN) support (Ben Noordhuis)

* doc: add uv_poll_cb status==UV_EBADF note (jensbjorgensen)

* build: support AddressSanitizer on MSVC (Jameson Nash)

* win,pipe: improve method of obtaining pid for ipc (number201724)

* thread: add support for affinity (daomingq)

* include: map ENODATA error code (Ben Noordhuis)

* build: remove bashism from autogen.sh (Santiago Gimeno)

* win,tcp,udp: remove "active streams" optimization (Saúl Ibarra Corretgé)

* win: drop code checking for Windows XP / Server 2k3 (Saúl Ibarra Corretgé)

* unix,win: fix 'sprintf' is deprecated warning (twosee)

* doc: mention close_cb can be NULL (Qix)

* win: optimize udp receive performance (ywave620)

* win: fix an incompatible types warning (twosee)

* doc: document 0 return value for free/total memory (Ben Noordhuis)

* darwin: use hw.cpufrequency again for frequency info (Jameson Nash)

* win,test: change format of TEST_PIPENAME's (Santiago Gimeno)

* win,pipe: fixes in uv_pipe_connect() (Santiago Gimeno)

* misc: fix return value of memory functions (theanarkh)

* src: add new metrics APIs (Trevor Norris)

* thread: add uv_thread_getcpu() (daomingq)

* build: don't use ifaddrs.h on solaris 10 (Edward Humes)

* unix,win: add uv_get_available_memory() (Tim Besard)

* test: fix -Wunused-but-set-variable warnings (Ben Noordhuis)

* doc: bump min supported linux and freebsd versions (Ben Noordhuis)

* Add Socket Runtime to the LINKS.md (Sergey Rubanov)

* unix: drop kfreebsd support (Ben Noordhuis)

* win: fix fstat for pipes and character files (Stefan Stojanovic)

* win: fix -Wunused-variable warning (Ben Noordhuis)

* win: fix -Wunused-function warning (Ben Noordhuis)

* build: drop qemu-alpha from ci matrix (Ben Noordhuis)

* win: move child_stdio_buffer out of uv_process_t (Santiago Gimeno)

* test: fix some unreachable code warnings (Santiago Gimeno)

* linux: simplify uv_uptime() (Ben Noordhuis)

* test: unflake fs_event_watch_dir test (Ben Noordhuis)

* darwin: remove unused fsevents symbol lookups (Ben Noordhuis)

* build: add define guard around UV_EXTERN (Zvicii)

* build: add UndefinedBehaviorSanitizer support (Ben Noordhuis)

* build: enable platform_output test on qemu (Ben Noordhuis)

* linux: handle cpu hotplugging in uv_cpu_info() (Ben Noordhuis)

* build: remove unnecessary policy setting (dundargoc)

* docs: add vcpkg instruction step (Jack·Boos·Yu)

* win,fs: fix readlink errno for a non-symlink file (Darshan Sen)

* misc: extend getpw to take uid as an argument (Jameson Nash)

* unix,win: use static_assert when available (Ben Noordhuis)

* docs: delete code Makefile (Jameson Nash)

* docs: add CI for docs PRs (Jameson Nash)

* docs: update Sphinx version on RTD (Jameson Nash)

* doc: clean up license file (Ben Noordhuis)

* test: fix some warnings when compiling tests (panran)

* build,win: add mingw-w64 CI configuration (Jameson Nash)

* build: add CI for distcheck (Jameson Nash)

* unix: remove busy loop from uv_async_send (Jameson Nash)

* doc: document uv_fs_cb type (Tamás Bálint Misius)

* build: Improve build by cmake for Cygwin (erw7)

* build: add libuv:: namespace to libuvConfig.cmake (AJ Heller)

* test: fix ThreadSanitizer thread leak warning (Ben Noordhuis)

* test: fix ThreadSanitizer data race warning (Ben Noordhuis)

* test: fix ThreadSanitizer data race warning (Ben Noordhuis)

* test: fix ThreadSanitizer data race warning (Ben Noordhuis)

* test: cond-skip fork_threadpool_queue_work_simple (Ben Noordhuis)

* test: cond-skip signal_multiple_loops (Ben Noordhuis)

* test: cond-skip tcp_writealot (Ben Noordhuis)

* build: promote tsan ci to must-pass (Ben Noordhuis)

* build: add CI for OpenBSD and FreeBSD (James McCoy)

* build,test: fix distcheck errors (Jameson Nash)

* test: remove bad tty window size assumption (Ben Noordhuis)

* darwin,process: feed kevent the signal to reap children (Jameson Nash)

* unix: abort on clock_gettime() error (Ben Noordhuis)

* test: remove timing-sensitive check (Ben Noordhuis)

* unix: DRY and fix tcp bind error path (Jameson Nash)

* macos: fix fsevents thread race conditions (Ben Noordhuis)

* win: fix leak in uv_chdir (Trevor Norris)

* test: make valgrind happy (Trevor Norris)

* barrier: wait for prior out before next in (Jameson Nash)

* test: fix visual studio 2015 build error (Ben Noordhuis)

* linux: fix ceph copy error truncating readonly files (Bruno Passeri)

* test: silence more valgrind warnings (Trevor Norris)

* doc: add entries to LINKS.md (Trevor Norris)

* win,unix: change execution order of timers (Trevor Norris)

* doc: add trevnorris to maintainers (Trevor Norris)

* linux: remove epoll_pwait() emulation code path (Ben Noordhuis)

* linux: replace unsafe macro with inline function (Ben Noordhuis)

* linux: remove arm oabi support (Ben Noordhuis)

* unix,sunos: SO_REUSEPORT not valid on all sockets (Stacey Marshall)

* doc: consistent single backquote in misc.rst (Jason Zhang)

* src: switch to use C11 atomics where available (Trevor Norris)

* test: don't use static buffer for formatting (Ben Noordhuis)

* linux: introduce io_uring support (Ben Noordhuis)

* linux: fix academic valgrind warning (Ben Noordhuis)

* test: disable signal test under ASan and MSan (Ben Noordhuis)

* linux: add IORING_OP_OPENAT support (Ben Noordhuis)

* linux: add IORING_OP_CLOSE support (Ben Noordhuis)

* linux: remove bug workaround for obsolete kernels (Ben Noordhuis)

* doc: update active maintainers list (Ben Noordhuis)

* test: add ASSERT_OK (Trevor Norris)

* src: fix events/events_waiting metrics counter (Trevor Norris)

* unix,win: add uv_clock_gettime() (Ben Noordhuis)

* build: remove freebsd and openbsd buildbots (Ben Noordhuis)

* win: fix race condition in uv__init_console() (sivadeilra)

* linux: fix logic bug in sqe ring space check (Ben Noordhuis)

* linux: use io_uring to batch epoll_ctl calls (Ben Noordhuis)

* macos: update minimum supported version (Santiago Gimeno)

* docs: fix some typos (cui fliter)

* unix: use memcpy() instead of type punning (Ben Noordhuis)

* test: add additional assert (Mohammed Keyvanzadeh)

* build: export compile_commands.json (Lewis Russell)

* win,process: write minidumps when sending SIGQUIT (Elliot Saba)

* unix: constrained_memory should return UINT64_MAX (Tim Besard)

* unix: handle CQ overflow in iou ring (Santiago Gimeno)

* unix: remove clang compiler warning pragmas (Ben Noordhuis)

* win: fix mingw build (gengjiawen)

* test: fix -Wbool-compare compiler warning (Ben Noordhuis)

* win: define MiniDumpWithAvxXStateContext always (Santiago Gimeno)

* freebsd: hard-code UV_ENODATA definition (Santiago Gimeno)

* linux: work around EOWNERDEAD io_uring kernel bug (Ben Noordhuis)

* linux: fix WRITEV with lots of bufs using io_uring (Santiago Gimeno)


2022.07.12, Version 1.44.2 (Stable), 0c1fa696aa502eb749c2c4735005f41ba00a27b8

Changes since version 1.44.1:

* Add SHA to ChangeLog (Jameson Nash)

* aix, ibmi: handle server hang when remote sends TCP RST (V-for-Vasili)

* build: make CI a bit noisier (Jameson Nash)

* process: reset the signal mask if the fork fails (Jameson Nash)

* zos: implement cmpxchgi() using assembly (Shuowang (Wayne) Zhang)

* build: AC_SUBST for AM_CFLAGS (Claes Nästén)

* ibmi: Implement UDP disconnect (V-for-Vasili)

* doc: update active maintainers list (Ben Noordhuis)

* build: fix kFreeBSD build (James McCoy)

* build: remove Windows 2016 workflows (Darshan Sen)

* Revert "win,errors: remap ERROR_ACCESS_DENIED to UV_EACCES" (Darshan Sen)

* unix: simplify getpwuid call (Jameson Nash)

* build: filter CI by paths and branches (Jameson Nash)

* build: add iOS to macos CI (Jameson Nash)

* build: re-enable CI for windows changes (Jameson Nash)

* process,iOS: fix build breakage in process.c (Denny C. Dai)

* test: remove unused declarations in tcp_rst test (V-for-Vasili)

* core: add thread-safe strtok implementation (Guilherme Íscaro)

* win: fix incompatible-types warning (twosee)

* test: fix flaky file watcher test (Ben Noordhuis)

* build: fix AIX xlc autotools build (V-for-Vasili)

* unix,win: fix UV_RUN_ONCE + uv_idle_stop loop hang (Ben Noordhuis)

* win: fix unexpected ECONNRESET error on TCP socket (twosee)

* doc: make sample cross-platform build (gengjiawen)

* test: separate some static variables by test cases (Hannah Shi)

* sunos: fs-event callback can be called after uv_close() (Andy Fiddaman)

* uv: re-register interest in a file after change (Shuowang (Wayne) Zhang)

* uv: register UV_RENAME event for _RFIM_UNLINK (Shuowang (Wayne) Zhang)

* uv: register __rfim_event 156 as UV_RENAME (Shuowang (Wayne) Zhang)

* doc: remove smartos from supported platforms (Ben Noordhuis)

* macos: avoid posix_spawnp() cwd bug (Jameson Nash)

* release: check versions of autogen scripts are newer (Jameson Nash)

* test: rewrite embed test (Ben Noordhuis)

* openbsd: use utimensat instead of lutimes (tuftedocelot)

* doc: fix link to uvwget example main() function (blogdaren)

* unix: use MSG_CMSG_CLOEXEC where supported (Ben Noordhuis)

* test: remove disabled callback_order test (Ben Noordhuis)

* win,pipe: fix bugs with pipe resource lifetime management (Jameson Nash)

* loop: better align order-of-events behavior between platforms (Jameson Nash)

* aix,test: uv_backend_fd is not supported by poll (V-for-Vasili)

* kqueue: skip EVFILT_PROC when invalidating fds (chucksilvers)

* darwin: fix atomic-ops.h ppc64 build (Sergey Fedorov)

* zos: don't err when killing a zombie process (Shuowang (Wayne) Zhang)

* zos: avoid fs event callbacks after uv_close() (Shuowang (Wayne) Zhang)

* zos: correctly format interface addresses names (Shuowang (Wayne) Zhang)

* zos: add uv_interface_addresses() netmask support (Shuowang (Wayne) Zhang)

* zos: improve memory management of ip addresses (Shuowang (Wayne) Zhang)

* tcp,pipe: fail `bind` or `listen` after `close` (theanarkh)

* zos: implement uv_available_parallelism() (Shuowang (Wayne) Zhang)

* udp,win: fix UDP compiler warning (Jameson Nash)

* zos: fix early exit of epoll_wait() (Shuowang (Wayne) Zhang)

* unix,tcp: fix errno handling in uv__tcp_bind() (Samuel Cabrero)

* shutdown,unix: reduce code duplication (Jameson Nash)

* unix: fix c99 comments (Ben Noordhuis)

* unix: retry tcgetattr/tcsetattr() on EINTR (Ben Noordhuis)

* docs: update introduction.rst (Ikko Ashimine)

* unix,stream: optimize uv_shutdown() codepath (Jameson Nash)

* zos: delay signal handling until after normal i/o (Shuowang (Wayne) Zhang)

* stream: uv__drain() always needs to stop POLLOUT (Jameson Nash)

* unix,tcp: allow EINVAL errno from setsockopt in uv_tcp_close_reset() (Stacey
  Marshall)

* win,shutdown: improve how shutdown is dispatched (Jameson Nash)


2022.03.09, Version 1.44.1 (Stable), e8b7eb6908a847ffbe6ab2eec7428e43a0aa53a2

Changes since version 1.44.0:

* process: simplify uv__write_int calls (Jameson Nash)

* macos: don't use thread-unsafe strtok() (Ben Noordhuis)

* process: fix hang after NOTE_EXIT (Jameson Nash)


2022.03.07, Version 1.44.0 (Stable), d2bff508457336d808ba7148b33088f6acbfe0a6

Changes since version 1.43.0:

* darwin: remove EPROTOTYPE error workaround (Ben Noordhuis)

* doc: fix v1.43.0 changelog entries (cjihrig)

* win: replace CRITICAL_SECTION+Semaphore with SRWLock (David Machaj)

* darwin: translate EPROTOTYPE to ECONNRESET (Ben Noordhuis)

* android: use libc getifaddrs() (Ben Noordhuis)

* unix: fix STATIC_ASSERT to check what it means to check (Jessica Clarke)

* unix: ensure struct msghdr is zeroed in recvmmsg (Ondřej Surý)

* test: test with maximum recvmmsg buffer (Ondřej Surý)

* unix: don't allow too small thread stack size (Ben Noordhuis)

* bsd: ensure mutex is initialized (Ben Noordhuis)

* doc: add gengjiawen as maintainer (gengjiawen)

* process: monitor for exit with kqueue on BSDs (Jeremy Rose)

* test: fix flaky uv_fs_lutime test (Momtchil Momtchev)

* build: fix cmake install locations (Jameson Nash)

* thread,win: fix C90 style nit (ssrlive)

* build: rename CFLAGS to AM_CFLAGS (Jameson Nash)

* doc/guide: update content and sample code (woclass)

* process,bsd: handle kevent NOTE_EXIT failure (Jameson Nash)

* test: remove flaky test ipc_closed_handle (Ben Noordhuis)

* darwin: bump minimum supported version to 10.15 (Ben Noordhuis)

* win: return fractional seconds in uv_uptime() (Luca Adrian L)

* build: export uv_a for cmake (WenTao Ou)

* loop: add pending work to loop-alive check (Jameson Nash)

* win: use GetTickCount64 for uptime again (Jameson Nash)

* win: restrict system DLL load paths (jonilaitinen)

* win,errors: remap ERROR_ACCESS_DENIED to UV_EACCES (Darshan Sen)

* bench: add `uv_queue_work` ping-pong measurement (Momtchil Momtchev)

* build: fix error C4146 on MSVC (UMU)

* test: fix benchmark-ping-udp (Ryan Liptak)

* win,fs: consider broken pipe error a normal EOF (Momtchil Momtchev)

* document the values of enum uv_stdio_flags (Paul Evans)

* win,loop: add missing uv_update_time (twosee)

* win,fs: avoid closing an invalid handle (Jameson Nash)

* fix oopsie from

* doc: clarify android api level (Ben Noordhuis)

* win: fix style nits [NFC] (Jameson Nash)

* test: fix flaky udp_mmsg test (Santiago Gimeno)

* test: fix ipc_send_recv_pipe flakiness (Ben Noordhuis)

* doc: checkout -> check out (wyckster)

* core: change uv_get_password uid/gid to unsigned (Jameson Nash)

* hurd: unbreak build on GNU/Hurd (Vittore F. Scolari)

* freebsd: use copy_file_range() in uv_fs_sendfile() (David Carlier)

* test: use closefd in runner-unix.c (Guilherme Íscaro)

* Reland "macos: use posix_spawn instead of fork" (Jameson Nash)

* android: fix build error when no ifaddrs.h (ssrlive)

* unix,win: add uv_available_parallelism() (Ben Noordhuis)

* process: remove OpenBSD from kevent list (Jameson Nash)

* zos: fix build breakage (Ben Noordhuis)

* process: only use F_DUPFD_CLOEXEC if it is defined (Jameson Nash)

* win,poll: add the MSAFD GUID for AF_UNIX (roflcopter4)

* unix: simplify uv__cloexec_fcntl() (Ben Noordhuis)

* doc: add secondary GPG ID for vtjnash (Jameson Nash)

* unix: remove uv__cloexec_ioctl() (Jameson Nash)


2022.01.05, Version 1.43.0 (Stable), 988f2bfc4defb9a85a536a3e645834c161143ee0

Changes since version 1.42.0:

* run test named ip6_sin6_len (Jameson Nash)

* docs: fix wrong information about scheduling (Mohamed Edrah)

* unix: protect fork in uv_spawn from signals (Jameson Nash)

* drop only successfully sent packets post sendmmsg (Supragya Raj)

* test: fix typo in test-tty-escape-sequence-processing.c (Ikko Ashimine)

* cmake: use standard installation layout always (Sylvain Corlay)

* win,spawn: allow UNC path with forward slash (earnal)

* win,fsevent: fix uv_fs_event_stop() assert (Ben Noordhuis)

* unix: remove redundant include in unix.h (Juan José Arboleda)

* doc: mark SmartOS as Tier 3 support (Ben Noordhuis)

* doc: fix broken links for netbsd's sysctl manpage (YAKSH BARIYA)

* misc: adjust stalebot deadline (Ben Noordhuis)

* test: remove `dns-server.c` as it is not used anywhere (Darshan Sen)

* build: fix non-cmake android builds (YAKSH BARIYA)

* doc: replace pyuv with uvloop (Ofek Lev)

* asan: fix some tests (Jameson Nash)

* build: add experimental TSAN configuration (Jameson Nash)

* pipe: remove useless assertion (~locpyl-tidnyd)

* bsd: destroy mutex in uv__process_title_cleanup() (Darshan Sen)

* build: add windows build to CI (Darshan Sen)

* win,fs: fix error code in uv_fs_read() and uv_fs_write() (Darshan Sen)

* build: add macos-latest to ci matrix (Ben Noordhuis)

* udp: fix &/&& typo in macro condition (Evan Miller)

* build: install cmake package module (Petr Menšík)

* win: fix build for mingw32 (Nicolas Noble)

* build: fix build failures with MinGW new headers (erw7)

* build: fix win build with cmake versions before v3.14 (AJ Heller)

* unix: support aarch64 in uv_cpu_info() (Juan José Arboleda)

* linux: work around CIFS EPERM bug (Ben Noordhuis)

* sunos: Oracle Developer Studio support (Stacey Marshall)

* Revert "sunos: Oracle Developer Studio support (cjihrig)

* sunos: Oracle Developer Studio support (Stacey Marshall)

* stream: permit read after seeing EOF (Jameson Nash)

* thread: initialize uv_thread_self for all threads (Jameson Nash)

* kqueue: ignore write-end closed notifications (Jameson Nash)

* macos: fix the cfdata length in uv__get_cpu_speed (Jesper Storm Bache)

* unix,win: add uv_ip_name to get name from sockaddr (Campbell He)

* win,test: fix a few typos (AJ Heller)

* zos: use destructor for uv__threadpool_cleanup() (Wayne Zhang)

* linux: use MemAvailable instead of MemFree (Andrey Hohutkin)

* freebsd: call dlerror() only if necessary (Jameson Nash)

* bsd,windows,zos: fix udp disconnect EINVAL (deal)


2021.07.21, Version 1.42.0 (Stable), 6ce14710da7079eb248868171f6343bc409ea3a4

Changes since version 1.41.0:

* doc: fix code highlighting (Darshan Sen)

* test: move to ASSERT_NULL and ASSERT_NOT_NULL test macros (tjarlama)

* zos: build in ascii code page (Shuowang (Wayne) Zhang)

* zos: don't use nanosecond timestamp fields (Shuowang (Wayne) Zhang)

* zos: introduce zoslib (Shuowang (Wayne) Zhang)

* zos: use strnlen() from zoslib (Shuowang (Wayne) Zhang)

* zos: use nanosleep() from zoslib (Shuowang (Wayne) Zhang)

* zos: use __getargv() from zoslib to get exe path (Shuowang (Wayne) Zhang)

* zos: treat __rfim_utok as binary (Shuowang (Wayne) Zhang)

* zos: use execvpe() to set environ explicitly (Shuowang (Wayne) Zhang)

* zos: use custom proctitle implementation (Shuowang (Wayne) Zhang)

* doc: add instructions for building on z/OS (Shuowang (Wayne) Zhang)

* linux,udp: enable full ICMP error reporting (Ondřej Surý)

* test: fix test-udp-send-unreachable (Ondřej Surý)

* include: fix typo in documentation (Tobias Nießen)

* chore: use for(;;) instead of while (Yash Ladha)

* test: remove string + int warning on udp-pummel (Juan José Arboleda)

* cmake: fix linker flags (Zhao Zhili)

* test: fix stack-use-after-scope (Zhao Zhili)

* unix: expose thread_stack_size() internally (Brandon Cheng)

* darwin: use RLIMIT_STACK for fsevents pthread (Brandon Cheng)

* darwin: abort on pthread_attr_init fail (Brandon Cheng)

* benchmark: remove unreachable code (Matvii Hodovaniuk)

* macos: fix memleaks in uv__get_cpu_speed (George Zhao)

* Make Thread Sanitizer aware of file descriptor close in uv__close() (Ondřej
  Surý)

* darwin: fix iOS compilation and functionality (Hayden)

* linux: work around copy_file_range() cephfs bug (Ben Noordhuis)

* zos: implement uv_get_constrained_memory() (Shuowang (Wayne) Zhang)

* zos: fix uv_get_free_memory() (Shuowang (Wayne) Zhang)

* zos: use CVTRLSTG to get total memory accurately (Shuowang (Wayne) Zhang)

* ibmi: Handle interface names longer than 10 chars (Kevin Adler)

* docs: update read-the-docs version of sphinx (Jameson Nash)

* unix: refactor uv_try_write (twosee)

* linux-core: add proper divide by zero assert (yiyuaner)

* misc: remove unnecessary _GNU_SOURCE macros (Darshan Sen)

* test: log to stdout to conform TAP spec (bbara)

* win,fs: fix C4090 warning with MSVC (SeverinLeonhardt)

* build: some systems provide dlopen() in libc (Andy Fiddaman)

* include: add EOVERFLOW status code mapping (Darshan Sen)

* unix,fs: use uv__load_relaxed and uv__store_relaxed (Darshan Sen)

* win: fix string encoding issue of uv_os_gethostname (Eagle Liang)

* unix,process: add uv__write_errno helper function (Ricky Zhou)

* Re-merge "unix,stream: clear read/write states on close/eof" (Jameson Nash)

* unix,core: fix errno handling in uv__getpwuid_r (Darshan Sen)

* errors: map ESOCKTNOSUPPORT errno (Ryan Liptak)

* doc: uv_read_stop always succeeds (Simon Kissane)

* inet: fix inconsistent return value of inet_ntop6 (twosee)

* darwin: fix -Wsometimes-uninitialized warning (twosee)

* stream: introduce uv_try_write2 function (twosee)

* poll,win: UV_PRIORITIZED option should not assert (twosee)

* src: DragonFlyBSD has mmsghdr struct too (David Carlier)

* cleanup,win: Remove _WIN32 guards on threadpool (James M Snell)

* freebsd: fix an incompatible pointer type warning (Darshan Sen)

* core: Correct the conditionals for {cloexec,nonblock}_ioctl (Ali Mohammad
  Pur)

* win,tcp: make uv_close work more like unix (Jameson Nash)

* doc: more accurate list of valid send_handle's (twosee)

* win,tcp: translate system errors correctly (twosee)

* unix: implement cpu_relax() on ppc64 (Ben Noordhuis)

* docs: move list of project links under PR control (Jameson Nash)

* test: wrong pointer arithmetic multiplier (Erkhes N)

* doc: switch discussion forum to github (Jameson Nash)

* idna: fix OOB read in punycode decoder (Ben Noordhuis)

* build: make sure -fvisibility=hidden is set (Santiago Gimeno)

* illumos: event ports to epoll (tjarlama)

* illumos,tty: UV_TTY_MODE_IO waits for 4 bytes (Joshua M. Clulow)

* doc: add vtjnash GPG ID (Jameson Nash)

* linux: read CPU model information on ppc (Richard Lau)

* darwin: fix uv_barrier race condition (Guilherme Íscaro)

* unix,stream: fix loop hang after uv_shutdown (Jameson Nash)

* doc,udp: note that suggested_size is 1 max-sized dgram (Ryan Liptak)

* mingw: fix building for ARM/AArch64 (Martin Storsjö)

* unix: strnlen is not available on Solaris 10 (Claes Nästén)

* sunos: restore use of event ports (Andy Fiddaman)

* sunos,cmake: use thread-safe errno (Andy Fiddaman)


2021.02.14, Version 1.41.0 (Stable), 1dff88e5161cba5c59276d2070d2e304e4dcb242

Changes since version 1.40.0:

* mailmap: update contact information for richardlau (Richard Lau)

* build: add asan checks (gengjiawen)

* unix: report bind error in uv_tcp_connect() (Ben Noordhuis)

* doc: uv_tcp_bind() never returns UV_EADDRINUSE (Ben Noordhuis)

* test: fix pump and tcp_write_batch benchmarks (Santiago Gimeno)

* doc: mark IBM i as Tier 2 support (Jesse Gorzinski)

* doc,poll: add notes (repeated cb & cancel pending cb) (Elad Nachmias)

* linux: fix -Wincompatible-pointer-types warning (Ben Noordhuis)

* linux: fix -Wsign-compare warning (Ben Noordhuis)

* android: add system call api guards (Ben Noordhuis)

* unix,win: harmonize uv_read_start() error handling (Ben Noordhuis)

* unix,win: more uv_read_start() argument validation (Ben Noordhuis)

* build: turn on -fno-strict-aliasing (Ben Noordhuis)

* stream: add uv_pipe and uv_socketpair to the API (Jameson Nash)

* unix,win: initialize timer `timeout` field (Ben Noordhuis)

* bsd-ifaddrs: improve comments (Darshan Sen)

* test: remove unnecessary uv_fs_stat() calls (Ben Noordhuis)

* fs: fix utime/futime timestamp rounding errors (Ben Noordhuis)

* test: ensure reliable floating point comparison (Jameson Nash)

* unix,fs: fix uv_fs_sendfile() (Santiago Gimeno)

* unix: fix uv_fs_stat when using statx (Simon Kadisch)

* linux,macos: fix uv_set_process_title regression (Momtchil Momtchev)

* doc: clarify UDP errors and recvmmsg (Ethel Weston)

* test-getaddrinfo: use example.invalid (Drew DeVault)

* Revert "build: fix android autotools build" (Bernardo Ramos)

* unix,fs: on DVS fs, statx returns EOPNOTSUPP (Mark Klein)

* win, fs: mkdir really return UV_EINVAL for invalid names (Nicholas Vavilov)

* tools: migrate tools/make_dist_html.py to python3 (Dominique Dumont)

* unix: fix uv_uptime() on linux (schamberg97)

* unix: check for partial copy_file_range support (Momtchil Momtchev)

* win: bump minimum supported version to windows 8 (Ben Noordhuis)

* poll,unix: ensure safety of rapid fd reuse (Bob Weinand)

* test: fix some warnings (Issam E. Maghni)

* unix: fix uv_uptime() regression (Santiago Gimeno)

* doc: fix versionadded metadata (cjihrig)

* test: fix 'incompatible pointer types' warnings (cjihrig)

* unix: check for EXDEV in uv__fs_sendfile() (Darshan Sen)


2020.09.26, Version 1.40.0 (Stable), 4e69e333252693bd82d6338d6124f0416538dbfc

Changes since version 1.39.0:

* udp: add UV_UDP_MMSG_FREE recv_cb flag (Ryan Liptak)

* include: re-map UV__EPROTO from 4046 to -4046 (YuMeiJie)

* doc: correct UV_UDP_MMSG_FREE version added (cjihrig)

* doc: add uv_metrics_idle_time() version metadata (Ryan Liptak)

* win,tty: pass through utf-16 surrogate pairs (Mustafa M)

* unix: fix DragonFly BSD build (Aleksej Lebedev)

* win,udp: fix error code returned by connect() (Santiago Gimeno)

* src: suppress user_timeout maybe-uninitialized (Daniel Bevenius)

* test: fix compiler warning (Vladimír Čunát)

* build: fix the Haiku cmake build (David Carlier)

* linux: fix i386 sendmmsg/recvmmsg support (Ben Noordhuis)

* build: add libuv-static pkg-config file (Nikolay Mitev)

* unix,win: add uv_timer_get_due_in() (Ulrik Strid)

* build,unix: add QNX support (Elad Lahav)

* include: remove incorrect UV__ERR() for EPROTO (cjihrig)


2020.08.26, Version 1.39.0 (Stable), 25f4b8b8a3c0f934158cd37a37b0525d75ca488e

Changes since version 1.38.1:

* unix: use relaxed loads/stores for clock id (Ben Noordhuis)

* build,win: link to user32.lib and advapi32.lib (George Zhao)

* unix: squelch harmless valgrind warning (ssrlive)

* include: fx c++ style comments warnings (Turbinya)

* build,cmake: Change installation location on MinGW (erw7)

* linux: use copy_file_range for uv_fs_copyfile when possible (Carter Li)

* win,tcp: avoid reinserting a pending request (

* docs: improve the descriptions for get memory info (Juan Sebastian velez
  Posada)

* test: add udp-mmsg test (Ryan Liptak)

* udp: add uv_udp_using_recvmmsg query (Ryan Liptak)

* doc: add more error constants (TK-one)

* zos: fix potential event loop stall (Trevor Norris)

* include: add internal fields struct to uv_loop_t (Trevor Norris)

* core: add API to measure event loop idle time (Trevor Norris)

* win,fs: use CreateDirectoryW instead of _wmkdir (Mustafa M)

* win,nfc: fix integer comparison signedness (escherstair)

* win,nfc: use

* win,nfc: removed some unused variables (escherstair)

* win,nfc: add missing return statement (escherstair)

* win,nfc: disable clang-format for

* darwin: use IOKit for uv_cpu_info (Evan Lucas)

* test: fix thread race in process_title_threadsafe (Ben Noordhuis)

* win,fs: avoid implicit access to _doserrno (Jameson Nash)

* test: give hrtime test a custom 20s timeout (Jameson Nash)

* build: add more failed test, for qemu version bump (gengjiawen)

* unix: handle src, dest same in uv_fs_copyfile() (cjihrig)

* unix: error when uv_setup_args() is not called (Ryan Liptak)

* aix: protect uv_exepath() from uv_set_process_title() (Richard Lau)

* fs: clobber req->path on uv_fs_mkstemp() error (tjarlama)

* cmake: fix compile error C2001 on Chinese Windows (司徒玟琅)

* test: avoid double evaluation in ASSERT_BASE macro (tjarlama)

* tcp: fail instantly if local port is unbound (Bartosz Sosnowski)

* doc: fix most sphinx warnings (Jameson Nash)

* nfci: address some style nits (Jameson Nash)

* unix: don't use _POSIX_PATH_MAX (Ben Noordhuis)


2020.07.04, Version 1.38.1 (Stable), e8b989ea1f7f9d4083511a2caec7791e9abd1871

Changes since version 1.38.0:

* test: use last matching qemu version (cjihrig)

* win, util: rearrange uv_hrtime (Bartosz Sosnowski)

* test: skip signal_multiple_loops test on QEMU (gengjiawen)

* build: add android build to CI (gengjiawen)

* test: extend fs_event_error_reporting timeout (cjihrig)

* build: link libkvm on netbsd only (Alexander Tokmakov)

* linux: refactor /proc file reader logic (Ben Noordhuis)

* linux: read load average from /proc/loadavg (Ben Noordhuis)

* android: remove patch code for below 21 (gengjiawen)

* win: fix visual studio 2008 build (Arenoros)

* win,tty: fix deadlock caused by inconsistent state (lander0s)

* unix: use relaxed loads/stores for feature checks (Ben Noordhuis)

* build: don't .gitignore m4/ax_pthread.m4 (Ben Noordhuis)

* unix: fix gcc atomics feature check (Ben Noordhuis)

* darwin: work around clock jumping back in time (Ben Noordhuis)

* udp: fix write_queue cleanup on sendmmsg error (Santiago Gimeno)

* src: build fix for Android (David Carlier)


2020.05.18, Version 1.38.0 (Stable), 1ab9ea3790378f9f25c4e78e9e2b511c75f9c9ed

Changes since version 1.37.0:

* test: skip poll_duplex and poll_unidirectional on PASE (Xu Meng)

* linux: make cpu_times consistently be milliseconds (James Ross)

* win: DRY uv_poll_start() and uv_poll_stop() (Ben Noordhuis)

* win: DRY uv_poll_close() (Ben Noordhuis)

* unix,win: add uv_library_shutdown() (Ben Noordhuis)

* unix: yield cpu when spinlocking on async handle (Ben Noordhuis)

* win: remove dep on GetQueuedCompletionStatusEx (Colin Finck)

* doc: correct source lines (Shohei YOSHIDA)

* build,android: fix typo (twosee)

* doc: uv_cancel() handles uv_random_t requests (Philip Chimento)

* doc: fix unescaped character (Philip Chimento)

* build,cmake: fix compilation on old MinGW (erw7)

* build: remove unnessesary MSVC warnings (Bartosz Sosnowski)

* win: make uv_udp_init_ex() accept UV_UDP_RECVMMSG (Ben Noordhuis)

* unix: simplify uv__udp_init_ex() (Ben Noordhuis)

* win: remove MAX_PATH limitations (Bartosz Sosnowski)

* build, win: add long path aware manifest (Bartosz Sosnowski)

* doc: check/idle/prepare functions always succeed (Ben Noordhuis)

* darwin: fix build with non-apple compilers (Ben Noordhuis)

* win: support environment variables > 32767 chars (Ben Noordhuis)

* unix: fully initialize struct msghdr (Ben Noordhuis)

* doc: add uv_replace_allocator thread safety warning (twosee)

* unix: fix int overflow when copying large files (Michal Artazov)

* fs: report original error (Bartosz Sosnowski)

* win, fs: add IO_REPARSE_TAG_APPEXECLINK support (Bartosz Sosnowski)

* doc: fix formatting (Ben Noordhuis)

* unix: fix memory leak when uv_loop_init() fails (Anna Henningsen)

* unix: shrink uv_udp_set_source_membership() stack (Ben Noordhuis)

* unix,win: fix wrong sizeof argument to memcpy() (Ben Noordhuis)

* build: check for libraries not provided by libc (Jeroen Roovers)

* doc: fix the order of arguments to calloc() (MasterDuke17)

* unix: don't abort when getrlimit() fails (Ben Noordhuis)

* test: support common user profile on IBMi (Xu Meng)

* build: test on more platforms via QEMU in CI (gengjiawen)


2020.04.20, Version 1.37.0 (Stable), 02a9e1be252b623ee032a3137c0b0c94afbe6809

Changes since version 1.36.0:

* timer: remove redundant check in heap compare (Yash Ladha)

* udp: add flag to enable recvmmsg(2) explicitly (Saúl Ibarra Corretgé)


2020.04.16, Version 1.36.0 (Stable), 533b738838ad8407032e14b6772b29ef9af63cfa

Changes since version 1.35.0:

* build: add aix-common.c for AIX cmake build (Jesse Gorzinski)

* zos: explicitly mark message queue events (Irek Fakhrutdinov)

* zos: move mq check out of loop to save cpu cycles (Irek Fakhrutdinov)

* zos: add checks to ensure behavior of epoll_wait (Irek Fakhrutdinov)

* src: add uv__reallocf() (Ben Noordhuis)

* build: ibmi support for cmake (Jesse Gorzinski)

* build: fix gyp build for Android API >= 28 (Lin Zhang)

* udp: return recvmmsg-ed datagrams in order (Saúl Ibarra Corretgé)

* zos,test: fix spawn_empty_env for shared library build (Richard Lau)

* zos: fix non-Release builds (Richard Lau)

* zos: fix return value on expired nanosleep() call (Richard Lau)

* build: fix z/OS cmake build (Richard Lau)

* test: add a bunch of ASSERT macros (Santiago Gimeno)

* test: remove unused extern declaration (Ben Noordhuis)

* test: canonicalize argv[0] in exepath test (Ben Noordhuis)

* test: simplify platform_init() (Ben Noordhuis)

* ibmi: Fix isatty EBADF handling and refactor (Kevin Adler)

* test: Test EBADF tty handling (Kevin Adler)

* build: make cmake build benchmarks (Ben Noordhuis)

* win: use RtlGenRandom from advapi32.dll directly (Ben Noordhuis)

* android: fix OOB write in uv_interface_addresses() (Lin Zhang)

* test: pass test when hostname is single character (毛毛)

* ibmi: set the highest process priority to -10 (Xu Meng)

* build: remove support for gyp (Ben Noordhuis)

* doc: add note to README on cross-compiling (Ben Noordhuis)

* fs: add uv_fs_lutime() (Sk Sajidul Kadir)

* unix: implement cpu_relax() for arm (David Carlier)

* linux: fix uv__accept4() (twosee)

* win: handle file paths in uv_fs_statfs() (erw7)

* unix: fix uv_os_environ() null pointer check (Rikard Falkeborn)

* win: fix uv_os_environ() null pointer check (Rikard Falkeborn)

* unix: fix compilation on macOS 32-bit architectures (Brad King)

* win: replace alloca() with stack-based array (Ben Noordhuis)


2020.03.12, Version 1.35.0 (Stable), e45f1ec38db882f8dc17b51f51a6684027034609

Changes since version 1.34.2:

* src: android build fix (David Carlier)

* build: make code compilable for iOS on Xcode (ssrlive)

* ibmi: skip unsupported fs test cases (Xu Meng)

* ibmi: ensure that pipe backlog is not zero (Xu Meng)

* test,udp6: fix udp_ipv6 test flakiness (Jameson Nash)

* test: fix fs_event_watch_dir_recursive flakiness (Santiago Gimeno)

* pipe: disallow listening on an IPC pipe (Witold Kręcicki)

* build,cmake: improve buil experience (Isabella Muerte)

* unix: remove support for FreeBSD < 10 (Saúl Ibarra Corretgé)

* linux: simplify uv__accept() (Ben Noordhuis)

* linux: assume presence of SOCK_CLOEXEC flag (Ben Noordhuis)

* linux: simplify uv__dup2_cloexec() (Ben Noordhuis)

* freebsd,linux: simplify uv__make_socketpair() (Ben Noordhuis)

* unix: fix error handling in uv__make_socketpair() (Ben Noordhuis)

* freebsd,linux: simplify uv__make_pipe() (Ben Noordhuis)

* unix: fix error handling in uv__make_pipe() (Ben Noordhuis)

* linux: simplify uv__async_eventfd() (Ben Noordhuis)

* linux: assume the presence of inotify system calls (Ben Noordhuis)

* doc: strip ICC profile from 2 jpg files (Dominique Dumont)

* unix: make uv_tcp_keepalive predictable (Manuel BACHMANN)

* docs: uv_setup_args() may take ownership of argv (Ben Noordhuis)

* unix: fix error path in uv_setup_args() (Ben Noordhuis)

* unix: fix size check in uv_get_process_title() (Ben Noordhuis)

* doc: add erw7 to maintainers (erw7)

* test: fixed udp4_echo_server implementation (Marek Vavrusa)

* test: added udp ping benchmark (1,10,100 pingers) (Marek Vavrusa)

* freebsd,linux: add recvmmsg() + sendmmsg() udp implementation (Marek Vavrusa)

* win,pipe: DRY/simplify some code paths (Jameson Nash)

* win: address some style nits (Jameson Nash)

* win,pipe: ensure `req->event_handle` is defined (Elliot Saba)

* win,pipe: consolidate overlapped initialization (Elliot Saba)

* win,pipe: erase event_handle after deleting pointer (Jameson Nash)

* build: fix android cmake build, build missing file (Ben Noordhuis)

* test: skip some UDP tests on IBMi (Xu Meng)

* test: skip some spawn test cases on IBMi (Xu Meng)

* src: fix wrong method name in comment (TK-one)

* test: add UV_TIMEOUT_MULTIPLIER environment var (Ben Noordhuis)

* unix: fix uv_cpu_info always returning UV_ENOTDIR on OpenBSD (Ben Davies)

* test: skip the pwd_shell test on IBMi (Xu Meng)

* win,tty: Change to restore cursor shape with uv_tty_reset() (erw7)

* win,tty: Added set cursor style to CSI sequences (erw7)

* test: handle EINTR, fix EOF check in poll test (Ben Noordhuis)

* unix: use socklen_t instead of size_t (Ben Noordhuis)

* doc: fix header file location (TK-one)

* unix: fix signal handle closing deferral (Ben Noordhuis)

* ibmi: set the amount of memory in use to zero (Xu Meng)

* zos: return on realloc failure in scandir() (Milad Farazmand)

* zos: fix scandir() error path NULL pointer deref (Ben Noordhuis)


2020.01.24, Version 1.34.2 (Stable), f868c9ab0c307525a16fff99fd21e32a6ebc3837

Changes since version 1.34.1:

* misc: adjust stalebot deadlines (Jameson Nash)

* test: fix env-vars flakiness (cjihrig)

* test: avoid truncating output lines (Jameson Nash)

* darwin: stop calling SetApplicationIsDaemon() (Ben Noordhuis)

* ibmi: implement uv_interface_addresses() (Xu Meng)

* osx,fsevent: fix race during uv_loop_close (Jameson Nash)

* osx,fsevent: clear pointer when deleting it [NFCI] (Jameson Nash)

* Revert "aix: replace ECONNRESET with EOF if already closed" (Jameson Nash)

* unix: handle uv__open_cloexec return value correctly (Anna Henningsen)


2020.01.13, Version 1.34.1 (Stable), 8aa5636ec72990bb2856f81e14c95813024a5c2b

Changes since version 1.34.0:

* unix: fix -Wstrict-aliasing compiler warning (Ben Noordhuis)

* unix: cache address of dlsym("mkostemp") (Ben Noordhuis)

* build: remove -pedantic from compiler flags (Ben Noordhuis)

* Revert "darwin: assume pthread_setname_np() is available" (Ben Noordhuis)

* Revert "darwin: speed up uv_set_process_title()" (Ben Noordhuis)

* darwin: assume pthread_setname_np() is available (Ben Noordhuis)

* ibmi: fix the false isatty() issue on IBMi (Xu Meng)

* test: fix test failure under NetBSD and OpenBSD (David Carlier)

* test: skip some test cases on IBMi (Xu Meng)

* test: skip uv_(get|set)_process_title on IBMi (Xu Meng)

* doc: remove binaries for Windows from README (Richard Lau)

* unix: fix -Wunused-but-set-variable warning (George Zhao)

* unix: pass sysctl size arg using ARRAY_SIZE macro (David Carlier)

* test: disallow running the test suite as root (cjihrig)

* unix: suppress -Waddress-of-packed-member warning (Ben Noordhuis)

* misc: make more tags "not-stale" (Jameson Nash)

* test: fix pthread memory leak (Trevor Norris)

* docs: delete socks5-proxy sample (Jameson Nash)

* ibmi: fix the CMSG length issue (Xu Meng)

* docs: fix formatting (Jameson Nash)

* unix: squelch fchmod() EPERM on CIFS share (Ben Noordhuis)

* docs: fix linkcheck (Jameson Nash)

* docs: switch from linux.die.net to man7.org (Jameson Nash)

* win: remove abort when non-IFS LSP detection fails (virtualyw)

* docs: clarify that uv_pipe_t is a pipe (Jameson Nash)

* win,tty: avoid regressions in utf-8 handling (Jameson Nash)

* win: remove bad assert in uv_loop_close (Jameson Nash)

* test: fix -fno-common build errors (Ben Noordhuis)

* build: turn on -fno-common to catch regressions (Ben Noordhuis)

* test: fix fs birth time test failure (Ben Noordhuis)

* tty,unix: avoid affecting controlling TTY (Jameson Nash)


2019.12.05, Version 1.34.0 (Stable), 15ae750151ac9341e5945eb38f8982d59fb99201

Changes since version 1.33.1:

* unix: move random-sysctl to random-sysctl-linux (nia)

* netbsd: use KERN_ARND sysctl to get entropy (nia)

* unix: refactor uv__fs_copyfile() logic (cjihrig)

* build: fix android build, add missing sources (Ben Noordhuis)

* build: fix android build, fix symbol redefinition (Ben Noordhuis)

* build: fix android autotools build (Ben Noordhuis)

* fs: handle non-functional statx system call (Milad Farazmand)

* unix,win: add uv_sleep() (cjihrig)

* doc: add richardlau to maintainers (Richard Lau)

* aix: fix netmask for IPv6 (Richard Lau)

* aix: clean up after errors in uv_interface_addresses() (Richard Lau)

* aix: fix setting of physical addresses (Richard Lau)

* fs: add uv_fs_mkstemp (Saúl Ibarra Corretgé)

* unix: switch uv_sleep() to nanosleep() (Ben Noordhuis)

* unix: retry on EINTR in uv_sleep() (Ben Noordhuis)

* zos: fix nanosleep() emulation (Ben Noordhuis)


2019.10.20, Version 1.33.1 (Stable), 07ad32138f4d2285ba2226b5e20462b27b091a59

Changes since version 1.33.0:

* linux: fix arm64 SYS__sysctl build breakage (Ben Noordhuis)


2019.10.17, Version 1.33.0 (Stable), e56e42e9310e4437e1886dbd6771792c14c0a5f3

Changes since version 1.32.0:

* Revert "linux: drop code path for epoll_pwait-less kernels" (Yang Yu)

* build: fix build error with __ANDROID_API__ < 21 (Yang Yu)

* win: fix reading hidden env vars (Anna Henningsen)

* unix,win: add uv_random() (Ben Noordhuis)

* win: simplify mkdtemp (Saúl Ibarra Corretgé)

* docs: fix literal-includes in User Guide (Nhan Khong)

* win, tty: fix problem of receiving unexpected SIGWINCH (erw7)

* unix: fix {Net,Open}BSD build (David Carlier)

* win,mingw: Fix undefined MCAST_* constants (Crunkle)

* build: Add link for test/fixtures/lorem_ipsum.txt (Andrew Paprocki)

* fs: use statvfs in uv__fs_statfs() for Haiku (Calvin Hill)

* fsevents: stop using fsevents to watch files (Jameson Nash)

* fsevents: regression in watching / (Jameson Nash)

* build,cmake: don't try to detect a C++ compiler (Isabella Muerte)

* build: fix build warning on cygwin (MaYuming)

* unix: set sin_len and sin6_len (Ouyang Yadong)

* test: fix order of operations in test (cjihrig)

* doc: improve uv_fs_readdir() cleanup docs (cjihrig)

* build: remove duplicated test in build files (ZYSzys)

* android: enable getentropy on Android >= 28 (David Carlier)

* android: fix build (David Carlier)

* darwin: speed up uv_set_process_title() (Ben Noordhuis)

* darwin: assume pthread_setname_np() is available (Ben Noordhuis)

* unix,udp: ensure addr is non-null (Jameson Nash)

* win,tty: add uv_tty_{get,set}_vterm_state (erw7)

* win: fix uv_statfs_t leak in uv_fs_statfs() (Ryan Liptak)

* build: install files on windows via cmake (Carl Lei)

* darwin,test: include AvailabilityMacros.h (Saúl Ibarra Corretgé)

* darwin,test: update loop time after sleeping (Saúl Ibarra Corretgé)

* doc: remove old FreeBSD 9 related note (Saúl Ibarra Corretgé)

* doc: improve uv_{send,recv}_buffer_size() docs (Ryan Liptak)

* build: move -Wno-long-long check to configure time (Ben Noordhuis)

* unix: update uv_fs_copyfile() fallback logic (Stefan Bender)

* win: cast setsockopt struct to const char* (Shelley Vohr)


2019.09.10, Version 1.32.0 (Stable), 697bea87b3a0b0e9b4e5ff86b39d1dedb70ee46d

Changes since version 1.31.0:

* misc: enable stalebot (Saúl Ibarra Corretgé)

* win: map ERROR_ENVVAR_NOT_FOUND to UV_ENOENT (cjihrig)

* win: use L'\0' as UTF-16 null terminator (cjihrig)

* win: support retrieving empty env variables (cjihrig)

* unix,stream: fix returned error codes (Santiago Gimeno)

* test: fix typo in DYLD_LIBRARY_PATH (Ben Noordhuis)

* unix,signal: keep handle active if pending signal (Santiago Gimeno)

* openbsd: fix uv_cpu_info (Santiago Gimeno)

* src: move uv_free_cpu_info to uv-common.c (Santiago Gimeno)

* tcp: add uv_tcp_close_reset method (Santiago Gimeno)

* test: fix udp-multicast-join tests (Santiago Gimeno)

* test: remove assertion in fs_statfs test (cjihrig)

* doc: clarify uv_buf_t usage in uv_alloc_cb (Tomas Krizek)

* win: fix typo in preprocessor expression (Konstantin Podsvirov)

* timer: fix uv_timer_start on closing timer (seny)

* udp: add source-specific multicast support (Vladimir Karnushin)

* udp: fix error return values (Santiago Gimeno)

* udp: drop IPV6_SSM_SUPPORT macro (Santiago Gimeno)

* udp: fix uv__udp_set_source_membership6 (Santiago Gimeno)

* udp: use sockaddr_storage instead of union (Santiago Gimeno)

* build,zos: add _OPEN_SYS_SOCK_EXT3 flag (Santiago Gimeno)

* test: add specific source multicast tests (Santiago Gimeno)

* include: map EILSEQ error code (cjihrig)

* win, tty: improve SIGWINCH performance (Bartosz Sosnowski)

* build: fix ios build error (MaYuming)

* aix: replace ECONNRESET with EOF if already closed (Milad Farazmand)

* build: add cmake library VERSION, SOVERSION (Eneas U de Queiroz)

* build: make include/ public in CMakeLists.txt (Ben Noordhuis)

* build: export USING_UV_SHARED=1 to cmake deps (Ben Noordhuis)

* build: cmake_minimum_required(VERSION 2.8.12) (Daniel Hahler)

* aix: Fix broken cmpxchgi() XL C++ specialization. (Andrew Paprocki)

* test: fix -Wsign-compare warning (Ben Noordhuis)

* unix: simplify open(O_CLOEXEC) feature detection (Ben Noordhuis)

* unix: fix UV_FS_O_DIRECT definition on Linux (Joran Dirk Greef)

* doc: uv_handle_t documentation suggestion (Daniel Bevenius)


2019.08.10, Version 1.31.0 (Stable), 0a6771cee4c15184c924bfe9d397bdd0c3b206ba

Changes since version 1.30.1:

* win,fs: don't modify global file translation mode (Javier Blazquez)

* win: fix uv_os_tmpdir when env var is 260 chars (Mustafa M)

* win: prevent tty event explosion machine hang (Javier Blazquez)

* win: add UV_FS_O_FILEMAP (João Reis)

* win, fs: mkdir return UV_EINVAL for invalid names (Bartosz Sosnowski)

* github: add root warning to template (cjihrig)

* win: misc fs cleanup (cjihrig)

* unix,win: add uv_fs_statfs() (cjihrig)

* test: avoid AF_LOCAL (Carlo Marcelo Arenas Belón)

* unix,win: add ability to retrieve all env variables (Saúl Ibarra Corretgé)

* Revert "darwin: speed up uv_set_process_title()" (Ben Noordhuis)

* doc: add %p to valgrind log-file arg (Zach Bjornson)

* doc: fix typo in basics.rst (Nan Xiao)

* ibmi: support Makefile build for IBM i (Xu Meng)

* OpenBSD: only get active CPU core count (Ben Davies)

* test: fix gcc 8 warnings for tests (Nhan Khong)

* ibmi: use correct header files (Xu Meng)

* unix: clear UV_HANDLE_READING flag before callback (zyxwvu Shi)

* unix: fix unused-function warning on BSD (Nhan Khong)

* test: fix test runner on MinGW (Crunkle)

* win: remove try-except outside MSVC (Crunkle)

* win: fix uv_spawn() ENOMEM on empty env (Ben Noordhuis)


2019.07.03, Version 1.30.1 (Stable), 1551969c84c2f546a429dac169c7fdac3e38115e

Changes since version 1.30.0:

* doc: fix incorrect versionchanged (cjihrig)

* test: allow UV_ECONNRESET in tcp_try_write_error (cjihrig)

* unix: add uv_get_constrained_memory() cygwin stub (cjihrig)

* build: fix android cmake build (Ben Noordhuis)

* unix: squelch -Wcast-function-type warning (Ben Noordhuis)

* build: fix compile error with uClibc (zlargon)


2019.06.28, Version 1.30.0 (Stable), 365b6f2a0eacda1ff52be8e57ab9381cfddc5dbb

Changes since version 1.29.1:

* darwin: fall back to F_BARRIERFSYNC (Ben Noordhuis)

* darwin: add 32 bit close$NOCANCEL implementation (ken-cunningham-webuse)

* build, core, unix: add support for Haiku (Leorize)

* darwin,linux: more conservative minimum stack size (Ben Noordhuis)

* threadpool: increase UV_THREADPOOL_SIZE limit (Vlad A)

* unix: return actual error from `uv_try_write()` (Anna Henningsen)

* darwin: fix build error with macos 10.10 (Ben Noordhuis)

* unix: make uv_cwd() report UV_ENOBUFS (Ben Noordhuis)

* unix: make uv_fs_read() fill all buffers (Ben Noordhuis)

* test: give hrtime test a custom 10s timeout (Ben Noordhuis)

* fs: fix uv_fs_copyfile if same src and dst (Santiago Gimeno)

* build: add cmake option to skip building tests (Niels Lohmann)

* doc: add link to nodejs.org (Jenil Christo)

* unix: fix a comment typo in signal.c (Evgeny Ermakov)

* unix: remove redundant cast in process.c (gengjiawen)

* doc: fix wrong mutex function prototypes (Leo Chung)


2019.05.22, Version 1.29.1 (Stable), d16e6094e1eb3b0b5981ef1dd7e03ec4d466944d

Changes since version 1.29.0:

* unix: simplify uv/posix.h include logic (cjihrig)

* test: increase test timeout (cjihrig)

* linux: fix sscanf() overflows reading from /proc (Ben Noordhuis)


2019.05.16, Version 1.29.0 (Stable), 43957efd92c167b352b4c948b617ca7afbee0ed1

Changes since version 1.28.0:

* ibmi: read memory and CPU usage info (Xu Meng)

* doc: update the cmake testing instruction (zlargon)

* unix: fix race condition in uv_async_send() (Ben Noordhuis)

* linux: use O_CLOEXEC instead of EPOLL_CLOEXEC (Ben Noordhuis)

* doc: mark uv_async_send() as async-signal-safe (Ben Noordhuis)

* linux: init st_flags and st_gen when using statx (Oscar Waddell)

* linux: read free/total memory from /proc/meminfo (Ben Noordhuis)

* test: test zero-sized uv_fs_sendfile() writes (Ben Noordhuis)

* unix: don't assert on UV_PROCESS_WINDOWS_* flags (Ben Noordhuis)

* linux: set correct mac address for IP-aliases (Santiago Gimeno)

* win,util: fix null pointer dereferencing (Tobias Nießen)

* unix,win: fix `uv_fs_poll_stop()` when active (Anna Henningsen)

* doc: add missing uv_fs_type entries (Michele Caini)

* doc: fix build with sphinx 2.x (FX Coudert)

* unix: don't make statx system call on Android (George Zhao)

* unix: fix clang scan-build warning (Kyle Edwards)

* unix: fall back to kqueue on older macOS systems (ken-cunningham-webuse)

* unix,win: add uv_get_constrained_memory() (Kelvin Jin)

* darwin: fix thread cancellation fd leak (Ben Noordhuis)

* linux: fix thread cancellation fd leak (Ben Noordhuis)


2019.04.16, Version 1.28.0 (Stable), 7bf8fabfa934660ee0fe889f78e151198a1165fc

Changes since version 1.27.0:

* unix,win: add uv_gettimeofday() (cjihrig)

* unix,win: add uv_fs_{open,read,close}dir() (cjihrig)

* unix: fix uv_interface_addresses() (Andreas Rohner)

* fs: remove macOS-specific copyfile(3) (Rich Trott)

* fs: add test for copyfile() respecting permissions (Rich Trott)

* build: partially revert 5234b1c43a (Ben Noordhuis)

* zos: fix setsockopt error when using AF_UNIX (Milad Farazmand)

* unix: suppress EINTR/EINPROGRESS in uv_fs_close() (Ben Noordhuis)

* build: use cmake APPLE variable to detect platform (zlargon)

* distcheck: remove duplicate test/ entry (Jameson Nash)

* unix: remove unused cmpxchgl() function (Ben Noordhuis)

* unix: support sockaddr_un in uv_udp_send() (Yury Selivanov)

* unix: guard use of PTHREAD_STACK_MIN (Kamil Rytarowski)

* unix,win: introduce uv_timeval64_t (cjihrig)

* doc: document uv_timeval_t and uv_timeval64_t (cjihrig)


2019.03.17, Version 1.27.0 (Stable), a4fc9a66cc35256dbc4dcd67c910174f05b6daa6

Changes since version 1.26.0:

* doc: describe unix signal handling better (Vladimír Čunát)

* linux: use statx() to obtain file birth time (Ben Noordhuis)

* src: fill sockaddr_in6.sin6_len when it's defined (Santiago Gimeno)

* test: relax uv_hrtime() test assumptions (Ben Noordhuis)

* build: make cmake install LICENSE only once (Thomas Karl Pietrowski)

* bsd: plug uv_fs_event_start() error path fd leak (Ben Noordhuis)

* unix: fix __FreeBSD_kernel__ typo (cjihrig)

* doc: add note about uv_run() not being reentrant (Ben Noordhuis)

* unix, win: make fs-poll close wait for resource cleanup (Anna Henningsen)

* doc: fix typo in uv_thread_options_t definition (Ryan Liptak)

* win: skip winsock initialization in safe mode (evgley)

* unix: refactor getsockname/getpeername methods (Santiago Gimeno)

* win,udp: allow to use uv_udp_open on bound sockets (Santiago Gimeno)

* udp: add support for UDP connected sockets (Santiago Gimeno)

* build: fix uv_test shared uv Windows cmake build (ptlomholt)

* build: add android-configure scripts to EXTRA_DIST (Ben Noordhuis)

* build: add missing header (cjihrig)

* sunos: add perror() output prior to abort() (Andrew Paprocki)

* test,sunos: disable UV_DISCONNECT handling (Andrew Paprocki)

* sunos: disable __attribute__((unused)) (Andrew Paprocki)

* test,sunos: use unistd.h code branch (Andrew Paprocki)

* build,sunos: better handling of non-GCC compiler (Andrew Paprocki)

* test,sunos: fix statement not reached warnings (Andrew Paprocki)

* sunos: fix argument/prototype mismatch in atomics (Andrew Paprocki)

* test,sunos: test-ipc.c lacks newline at EOF (Andrew Paprocki)

* test: change spawn_stdin_stdout return to void (Andrew Paprocki)

* test: remove call to floor() in test driver (Andrew Paprocki)


2019.02.11, Version 1.26.0 (Stable), 8669d8d3e93cddb62611b267ef62a3ddb5ba3ca0

Changes since version 1.25.0:

* doc: fix uv_get_free_memory doc (Stephen Belanger)

* unix: fix epoll cpu 100% issue (yeyuanfeng)

* openbsd,tcp: special handling of EINVAL on connect (ptlomholt)

* win: simplify registry closing in uv_cpu_info() (cjihrig)

* src,include: define UV_MAXHOSTNAMESIZE (cjihrig)

* win: return product name in uv_os_uname() version (cjihrig)

* thread: allow specifying stack size for new thread (Anna Henningsen)

* win: fix duplicate tty vt100 fn key (erw7)

* unix: don't attempt to invalidate invalid fd (Ben Noordhuis)


2019.01.19, Version 1.25.0 (Stable), 4a10a9d425863330af199e4b74bd688e62d945f1

Changes since version 1.24.1:

* Revert "win,fs: retry if uv_fs_rename fails" (Ben Noordhuis)

* aix: manually trigger fs event monitoring (Gireesh Punathil)

* unix: rename WRITE_RETRY_ON_ERROR macro (Ben Noordhuis)

* darwin: DRY platform-specific error check (Ben Noordhuis)

* unix: refactor uv__write() (Ben Noordhuis)

* unix: don't send handle twice on partial write (Ben Noordhuis)

* tty,win: fix Alt+key under WSL (Bartosz Sosnowski)

* build: support running tests in out-of-tree builds (Jameson Nash)

* fsevents: really watch files with fsevents on macos 10.7+ (Jameson Nash)

* thread,mingw64: need intrin.h header for SSE2 MemoryBarrier (Jameson Nash)

* win: fix sizeof-pointer-div warning (cjihrig)

* unix,win: add uv_os_uname() (cjihrig)

* win, tty: fix CreateFileW() return value check (Bartosz Sosnowski)

* unix: enable IPv6 tests on OpenBSD (ptlomholt)

* test: fix test-ipc spawn_helper exit_cb (Santiago Gimeno)

* test: fix test-ipc tests (Santiago Gimeno)

* unix: better handling of unsupported F_FULLFSYNC (Victor Costan)

* win,test: de-flake fs_event_watch_dir_short_path (Refael Ackermann)

* win: fix msvc warning (sid)

* openbsd: switch to libuv's barrier implementation (ptlomholt)

* unix,stream: fix zero byte writes (Santiago Gimeno)

* ibmi: return EISDIR on read from directory fd (Kevin Adler)

* build: wrap long lines in Makefile.am (cjihrig)


2018.12.17, Version 1.24.1 (Stable), 274f2bd3b70847cadd9a3965577a87e666ab9ac3

Changes since version 1.24.0:

* test: fix platform_output test on cygwin (damon-kwok)

* gitignore: ignore build/ directory (Damon Kwok)

* unix: zero epoll_event before use (Ashe Connor)

* darwin: use runtime check for file cloning (Ben Noordhuis)

* doc: replace deprecated build command on macOS (Rick)

* warnings: fix code that emits compiler warnings (Jameson Nash)

* doc: clarify expected memory management strategy (Ivan Krylov)

* test: add uv_inet_ntop(AF_INET) coverage (Ben Noordhuis)

* unix: harden string copying, introduce strscpy() (Ben Noordhuis)

* linux: get rid of strncpy() call (Ben Noordhuis)

* aix: get rid of strcat() calls (Ben Noordhuis)

* aix: fix data race in uv_fs_event_start() (Ben Noordhuis)

* win: fs: fix `FILE_FLAG_NO_BUFFERING` for writes (Joran Dirk Greef)

* build: don't link against -lpthread on Android (Michael Meier)


2018.11.14, Version 1.24.0 (Stable), 2d427ee0083d1baf995df4ebf79a3f8890e9a3e1

Changes since version 1.23.2:

* unix: do not require PATH_MAX to be defined (Brad King)

* win,doc: path encoding in uv_fs_XX is UTF-8 (hitesh)

* unix: add missing link dependency on kFreeBSD (Svante Signell)

* unix: add support for GNU/Hurd (Samuel Thibault)

* test: avoid memory leak for test_output (Carlo Marcelo Arenas Belón)

* zos: avoid UB with NULL pointer arithmetic (Carlo Marcelo Arenas Belón)

* doc: add vtjnash to maintainers (Jameson Nash)

* unix: restore skipping of phys_addr copy (cjihrig)

* unix,win: make uv_interface_addresses() consistent (cjihrig)

* unix: remove unnecessary linebreaks (cjihrig)

* unix,win: handle zero-sized allocations uniformly (Ben Noordhuis)

* unix: remove unused uv__dup() function (Ben Noordhuis)

* core,bsd: refactor process_title functions (Santiago Gimeno)

* win: Redefine NSIG to consider SIGWINCH (Jeremy Studer)

* test: make sure that reading a directory fails (Sakthipriyan Vairamani)

* win, tty: remove zero-size read callbacks (Bartosz Sosnowski)

* test: fix test runner getenv async-signal-safety (Ben Noordhuis)

* test: fix test runner execvp async-signal-safety (Ben Noordhuis)

* test,unix: fix race in test runner (Ben Noordhuis)

* unix,win: support IDNA 2008 in uv_getaddrinfo() (Ben Noordhuis)

* win, tcp: avoid starving the loop (Bartosz Sosnowski)

* win, dl: proper error messages on some systems (Bartosz Sosnowski)

* win,fs: retry if uv_fs_rename fails (Bartosz Sosnowski)

* darwin: speed up uv_set_process_title() (Ben Noordhuis)

* aix: fix race in uv_get_process_title() (Gireesh Punathil)

* win: support more fine-grained windows hiding (Bartosz Sosnowski)


2018.10.09, Version 1.23.2 (Stable), 34c12788d2e7308f3ac506c0abcbf74c0d6abd20

Changes since version 1.23.1:

* unix: return 0 retrieving rss on cygwin (cjihrig)

* unix: initialize uv_interface_address_t.phys_addr (cjihrig)

* test: handle uv_os_setpriority() windows edge case (cjihrig)

* tty, win: fix read stop for raw mode (Bartosz Sosnowski)

* Revert "Revert "unix,fs: fix for potential partial reads/writes"" (Jameson
  Nash)

* unix,readv: always permit partial reads to return (Jameson Nash)

* win,tty: fix uv_tty_close() (Bartosz Sosnowski)

* doc: remove extraneous "on" (Ben Noordhuis)

* unix,win: fix threadpool race condition (Anna Henningsen)

* unix: rework thread barrier implementation (Ben Noordhuis)

* aix: switch to libuv's own thread barrier impl (Ben Noordhuis)

* unix: signal done to last thread barrier waiter (Ben Noordhuis)

* test: add uv_barrier_wait serial thread test (Ali Ijaz Sheikh)

* unix: optimize uv_fs_readlink() memory allocation (Ben Noordhuis)

* win: remove req.c and other cleanup (Carlo Marcelo Arenas Belón)

* aix: don't EISDIR on read from directory fd (Ben Noordhuis)


2018.09.22, Version 1.23.1 (Stable), d2282b3d67821dc53c907c2155fa8c5c6ce25180

Changes since version 1.23.0:

* unix,win: limit concurrent DNS calls to nthreads/2 (Anna Henningsen)

* doc: add addaleax to maintainers (Anna Henningsen)

* doc: add missing slash in stream.rst (Emil Bay)

* unix,fs: use utimes & friends for uv_fs_utime (Jeremiah Senkpiel)

* unix,fs: remove linux fallback from utimesat() (Jeremiah Senkpiel)

* unix,fs: remove uv__utimesat() syscall fallback (Jeremiah Senkpiel)

* doc: fix argument name in tcp.rts (Emil Bay)

* doc: notes on running tests, benchmarks, tools (Jamie Davis)

* linux: remove epoll syscall wrappers (Ben Noordhuis)

* linux: drop code path for epoll_pwait-less kernels (Ben Noordhuis)

* Partially revert "win,code: remove GetQueuedCompletionStatus-based poller"
  (Jameson Nash)

* build: add compile for android arm64/x86/x86-64 (Andy Zhang)

* doc: clarify that some remarks apply to windows (Bert Belder)

* test: fix compiler warnings (Jamie Davis)

* ibmi: return 0 from uv_resident_set_memory() (dmabupt)

* win: fix uv_udp_recv_start() error translation (Ryan Liptak)

* win,doc: improve uv_os_setpriority() documentation (Bartosz Sosnowski)

* test: increase upper bound in condvar_5 (Jamie Davis)

* win,tty: remove deadcode (Jameson Nash)

* stream: autodetect direction (Jameson Nash)


2018.08.18, Version 1.23.0 (Stable), 7ebb26225f2eaae6db22f4ef34ce76fa16ff89ec

Changes since version 1.22.0:

* win,pipe: restore compatibility with the old IPC framing protocol (Bert
  Belder)

* fs: add uv_open_osfhandle (Bartosz Sosnowski)

* doc: update Visual C++ Build Tools URL (Michał Kozakiewicz)

* unix: loop starvation on successful write complete (jBarz)

* win: add uv__getnameinfo_work() error handling (A. Hauptmann)

* win: return UV_ENOMEM from uv_loop_init() (cjihrig)

* unix,win: add uv_os_{get,set}priority() (cjihrig)

* test: fix warning in test-tcp-open (Santiago Gimeno)


2018.07.11, Version 1.22.0 (Stable), 8568f78a777d79d35eb7d6994617267b9fb33967

Changes since version 1.21.0:

* unix: remove checksparse.sh (Ben Noordhuis)

* win: fix mingw build error (Ben Noordhuis)

* win: fix -Wunused-function warnings in thread.c (Ben Noordhuis)

* unix,win: merge timers implementation (Ben Noordhuis)

* win: fix pointer type in pipe.c (Ben Noordhuis)

* win: fixing build for older MSVC compilers (Michael Fero)

* zos: clear poll events on every iteration (jBarz)

* zos: write-protect message queue (jBarz)

* zos: use correct pointer type in strnlen (jBarz)

* unix,win: merge handle flags (Ben Noordhuis)

* doc: update Imran Iqbal's GitHub handle (cjihrig)

* src: add new error apis to prevent memory leaks (Shelley Vohr)

* test: make test-condvar call uv_cond_wait (Jamie Davis)

* fs: change position of uv_fs_lchown (Ujjwal Sharma)


2018.06.23, Version 1.21.0 (Stable), e4983a9b0c152932f7553ff4a9ff189d2314cdcb

Changes since version 1.20.3:

* unix,windows: map EFTYPE errno (cjihrig)

* win: perform case insensitive PATH= comparison (cjihrig)

* win, fs: uv_fs_fchmod support for -A files (Bartosz Sosnowski)

* src,lib: fix comments (Tobias Nießen)

* win,process: allow child pipe handles to be opened in overlapped mode (Björn
  Linse)

* src,test: fix idiosyncratic comment style (Bert Belder)

* test: fs_fchmod_archive_readonly must return a value (Bert Belder)

* win,pipe: fix incorrect error code returned from uv_pipe_write_impl() (Bert
  Belder)

* win,pipe: properly set uv_write_t.send_handle in uv_write2() (Bert Belder)

* test: add vectored uv_write() ping-pong tests (Bert Belder)

* win,pipe: support vectored uv_write() calls (Bert Belder)

* win,pipe: refactor pipe read cancellation logic (Bert Belder)

* test: improve output from IPC test helpers (Bert Belder)

* test: add test for IPC deadlock on Windows (

* win,pipe: fix IPC pipe deadlock (Bert Belder)

* unix: catch some cases of watching fd twice (Ben Noordhuis)

* test: use custom timeout for getaddrinfo_fail_sync (Ben Noordhuis)

* Revert "win: add Windows XP support to uv_if_indextoname()" (Bert Belder)

* win,thread: remove fallback uv_cond implementation (Bert Belder)

* src,test: s/olny/only (cjihrig)

* unix: close signal pipe fds on unload (Ben Noordhuis)

* win: allow setting udp socket options before bind (cjihrig)

* unix: return UV_ENOTSUP on FICLONE_FORCE failure (cjihrig)

* win,pipe: remove unreferenced local variable (Bert Belder)

* win,code: remove GetQueuedCompletionStatus-based poller (Bert Belder)

* win: remove the remaining dynamic kernel32 imports (Bert Belder)

* test: speedup process-title-threadsafe on macOS (cjihrig)

* core: move all include files except uv.h to uv/ (Saúl Ibarra Corretgé)

* win: move stdint-msvc2008.h to include/uv/ (Ben Noordhuis)

* build: fix cygwin install (Ben Noordhuis)

* build,win: remove MinGW Makefile (Saúl Ibarra Corretgé)

* build: add a cmake build file (Ben Noordhuis)

* build: add test suite option to cmake build (Ben Noordhuis)

* unix: set errno in uv_fs_copyfile() (cjihrig)

* samples: fix inconsistency in parse_opts vs usage (zyxwvu Shi)

* linux: handle exclusive POLLHUP with UV_DISCONNECT (Brad King)

* include: declare uv_cpu_times_s in higher scope (Peter Johnson)

* doc: add uv_fs_fsync() AIX limitations (jBarz)

* unix,win: add uv_fs_lchown() (Paolo Greppi)

* unix: disable clang variable length array warning (Peter Johnson)

* doc: document uv_pipe_t::ipc (Ed Schouten)

* doc: undocument uv_req_type's UV_REQ_TYPE_PRIVATE (Ed Schouten)

* doc: document UV_*_MAP() macros (Ed Schouten)

* win: remove use of min() macro in pipe.c (Peter Johnson)

* doc: add jbarz as maintainer (


2018.05.08, Version 1.20.3 (Stable), 8cfd67e59195251dff793ee47c185c9d6a8f3818

Changes since version 1.20.2:

* win: add Windows XP support to uv_if_indextoname() (ssrlive)

* win: fix `'floor' undefined` compiler warning (ssrlive)

* win, pipe: stop read for overlapped pipe (Bartosz Sosnowski)

* build: fix utf-8 name of copyright holder (Jérémy Lal)

* zos: initialize pollfd revents (jBarz)

* zos,doc: add system V message queue note (jBarz)

* linux: don't use uv__nonblock_ioctl() on sparc (Ben Noordhuis)


2018.04.23, Version 1.20.2 (Stable), c51fd3f66bbb386a1efdeba6812789f35a372d1e

Changes since version 1.20.1:

* zos: use custom semaphore (jBarz)

* win: fix registry API error handling (Kyle Farnung)

* build: add support for 64-bit AIX (Richard Lau)

* aix: guard STATIC_ASSERT for glibc work around (Richard Lau)


2018.04.19, Version 1.20.1 (Stable), 36ac2fc8edfd5ff3e9be529be1d4a3f0d5364e94

Changes since version 1.20.0:

* doc,fs: improve documentation (Bob Burger)

* win: return a floored double from uv_uptime() (Refael Ackermann)

* doc: clarify platform specific pipe naming (Thomas Versteeg)

* unix: fix uv_pipe_chmod() on macOS (zzzjim)

* unix: work around glibc semaphore race condition (Anna Henningsen)

* tcp,openbsd: disable Unix TCP check for IPV6_ONLY (Alex Arslan)

* test,openbsd: use RETURN_SKIP in UDP IPv6 tests (Alex Arslan)

* test,openbsd: fix multicast test (Alex Arslan)

* Revert "win, fs: use FILE_WRITE_ATTRIBUTES when opening files" (cjihrig)


2018.04.03, Version 1.20.0 (Stable), 0012178ee2b04d9e4a2c66c27cf8891ad8325ceb

Changes since version 1.19.2:

* unix,spawn: respect user stdio flags for new pipe (Jameson Nash)

* Revert "Revert "unix,tcp: avoid marking server sockets connected"" (Jameson
  Nash)

* req: revisions to uv_req_t handling (Jameson Nash)

* win: remove unnecessary initialization (cjihrig)

* win: update uv_os_homedir() to use uv_os_getenv() (cjihrig)

* test: fix tcp_oob test flakiness (Santiago Gimeno)

* posix: fix uv__pollfds_del() for invalidated fd's (Jesse Gorzinski)

* doc: README: add note on installing gyp (Jamie Davis)

* unix: refactor uv_os_homedir to use uv_os_getenv (Santiago Gimeno)

* unix: fix several instances of lost errno (Michael Kilburn)

* win,tty: update several TODO comments (Ruslan Bekenev)

* unix: add UV_FS_COPYFILE_FICLONE support (cjihrig)

* test: fix connect_unspecified (Santiago Gimeno)

* unix,win: add UV_FS_COPYFILE_FICLONE_FORCE support (cjihrig)

* win: use long directory name for handle->dirw (Nicholas Vavilov)

* build: build with -D_FILE_OFFSET_BITS=64 again (Ben Noordhuis)

* win, fs: fix uv_fs_unlink for +R -A files (Bartosz Sosnowski)

* win, fs: use FILE_WRITE_ATTRIBUTES when opening files (Bartosz Sosnowski)

* unix: use __PASE__ on IBM i platforms (Jesse Gorzinski)

* test,freebsd: fix flaky poll tests (Santiago Gimeno)

* test: increase connection timeout to 1 second (jBarz)

* win,tcp: handle canceled connect with ECANCELED (Jameson Nash)


2018.02.22, Version 1.19.2 (Stable), c5afc37e2a8a70d8ab0da8dac10b77ba78c0488c

Changes since version 1.19.1:

* test: fix incorrect asserts (cjihrig)

* test: fix a typo in test-fork.c (Felix Yan)

* build: remove long-obsolete gyp workarounds (Ben Noordhuis)

* build: split off tests into separate gyp file (Ben Noordhuis)

* test: check uv_cond_timedwait more carefully (Jamie Davis)

* include,src: introduce UV__ERR() macro (Mason X)

* build: add url field to libuv.pc (Ben Noordhuis)

* doc: mark IBM i as Tier 3 support (Jesse Gorzinski)

* win,build: correct C2059 errors (Michael Fero)

* zos: fix timeout for condition variable (jBarz)

* win: CREATE_NO_WINDOW when stdio is not inherited (Nick Logan)

* build: fix commmon.gypi comment (Ryuichi KAWAMATA)

* doc: document uv_timer_start() on an active timer (Vladimír Čunát)

* doc: add note about handle movability (Bartosz Sosnowski)

* doc: fix syntax error in loop documentation (Bartosz Sosnowski)

* osx,stream: retry sending handle on EMSGSIZE error (Santiago Gimeno)

* unix: delay fs req register until after validation (cjihrig)

* test: add tests for bad inputs (Joyee Cheung)

* unix,win: ensure req->bufs is freed (cjihrig)

* test: add additional fs memory management checks (cjihrig)


2018.01.20, Version 1.19.1 (Stable), 8202d1751196c2374ad370f7f3779daef89befae

Changes since version 1.19.0:

* Revert "unix,tcp: avoid marking server sockets connected" (Ben Noordhuis)

* Revert "unix,fs: fix for potential partial reads/writes" (Ben Noordhuis)

* Revert "win: use RemoveDirectoryW() instead of _wmrmdir()" (Ben Noordhuis)

* cygwin: fix compilation of ifaddrs impl (Brad King)


2018.01.18, Version 1.19.0 (Stable), effbb7c9d29090b2e085a40867f8cdfa916a66df

Changes since version 1.18.0:

* core: add getter/setter functions for easier ABI compat (Anna Henningsen)

* unix: make get(set)_process_title MT-safe (Matt Harrison)

* unix,win: wait for threads to start (Ben Noordhuis)

* test: add threadpool init/teardown test (Bartosz Sosnowski)

* win, process: uv_kill improvements (Bartosz Sosnowski)

* win: set _WIN32_WINNT to 0x0600 (cjihrig)

* zos: implement uv_fs_event* functions (jBarz)

* unix,tcp: avoid marking server sockets connected (Jameson Nash)

* doc: mark Windows 7 as Tier 1 support (Bartosz Sosnowski)

* win: map 0.0.0.0 and :: addresses to localhost (Bartosz Sosnowski)

* build: install libuv.pc unconditionally (Ben Noordhuis)

* test: remove custom timeout for thread test on ppc (Ben Noordhuis)

* test: allow multicast not permitted status (Jérémy Lal)

* test: allow net unreachable status in udp test (Ben Noordhuis)

* unix: use SA_RESTART when setting our sighandler (Brad King)

* unix,fs: fix for potential partial reads/writes (Ben Wijen)

* win,build: do not build executable installer for dll (Bert Belder)

* win: allow directory symlinks to be created in a non-elevated context (Bert
  Belder)

* zos,test: accept SIGKILL for flaky test (jBarz)

* win: use RemoveDirectoryW() instead of _wmrmdir() (Ben Noordhuis)

* unix: fix uv_cpu_info() error on FreeBSD (elephantp)

* zos,test: decrease pings to avoid timeout (jBarz)


2017.12.02, Version 1.18.0 (Stable), 1489c98b7fc17f1702821a269eb0c5e730c5c813

Changes since version 1.17.0:

* aix: fix -Wmaybe-uninitialized warning (cjihrig)

* doc: remove note about SIGWINCH on Windows (Bartosz Sosnowski)

* Revert "unix,win: wait for threads to start" (Ben Noordhuis)

* unix,win: add uv_os_getpid() (Bartosz Sosnowski)

* unix: remove incorrect assertion in uv_shutdown() (Jameson Nash)

* doc: fix IRC URL in CONTRIBUTING.md (Matt Harrison)


2017.11.25, Version 1.17.0 (Stable), 1344d2bb82e195d0eafc0b40ba103f18dfd04cc5

Changes since version 1.16.1:

* unix: avoid malloc() call in uv_spawn() (Ben Noordhuis)

* doc: clarify the description of uv_loop_alive() (Ed Schouten)

* win: map UV_FS_O_EXLOCK to a share mode of 0 (Joran Dirk Greef)

* win: fix build on case-sensitive file systems (Ben Noordhuis)

* win: fix test runner build with mingw64 (Ben Noordhuis)

* win: remove unused variable in test/test-fs.c (Ben Noordhuis)

* zos: add strnlen() implementation (jBarz)

* unix: keep track of bound sockets sent via spawn (jBarz)

* unix,win: wait for threads to start (Ben Noordhuis)

* test: add threadpool init/teardown test (Bartosz Sosnowski)

* test: avoid malloc() in threadpool test (Ben Noordhuis)

* test: lower number of tasks in threadpool test (Ben Noordhuis)

* win: issue memory barrier in uv_thread_join() (Ben Noordhuis)

* ibmi: add support for new platform (Xu Meng)

* test: fix test-spawn compilation (Bartosz Sosnowski)


2017.11.11, Version 1.16.1 (Stable), 4056fbe46493ef87237e307e0025e551db875e13

Changes since version 1.16.0:

* unix: move net/if.h include (cjihrig)

* win: fix undeclared NDIS_IF_MAX_STRING_SIZE (Nick Logan)


2017.11.07, Version 1.16.0 (Stable), d68779f0ea742918f653b9c20237460271c39aeb

Changes since version 1.15.0:

* win: change st_blksize from `2048` to `4096` (Joran Dirk Greef)

* unix,win: add fs open flags, map O_DIRECT|O_DSYNC (Joran Dirk Greef)

* win, fs: fix non-symlink reparse points (Wade Brainerd)

* test: fix -Wstrict-prototypes warnings (Ben Noordhuis)

* unix, windows: map ENOTTY errno (Ben Noordhuis)

* unix: fall back to fsync() if F_FULLFSYNC fails (Joran Dirk Greef)

* unix: do not close invalid kqueue fd after fork (jBarz)

* zos: reset epoll data after fork (jBarz)

* zos: skip fork_threadpool_queue_work_simple (jBarz)

* test: keep platform_output as first test (Bartosz Sosnowski)

* win: fix non-English dlopen error message (Bartosz Sosnowski)

* unix,win: add uv_os_getppid() (cjihrig)

* test: fix const qualification compiler warning (Ben Noordhuis)

* doc: mark uv_default_loop() as not thread safe (rayrase)

* win, pipe: null-initialize stream->shutdown_req (Jameson Nash)

* tty, win: get SetWinEventHook pointer at startup (Bartosz Sosnowski)

* test: no extra new line in skipped test output (Bartosz Sosnowski)

* pipe: allow access from other users (Bartosz Sosnowski)

* unix,win: add uv_if_{indextoname,indextoiid} (Pekka Nikander)


2017.10.03, Version 1.15.0 (Stable), 8b69ce1419d2958011d415a636810705c36c2cc2

Changes since version 1.14.1:

* unix: limit uv__has_forked_with_cfrunloop to macOS (Kamil Rytarowski)

* win: fix buffer size in uv__getpwuid_r() (tux.uudiin)

* win,tty: improve SIGWINCH support (Bartosz Sosnowski)

* unix: use fchmod() in uv_fs_copyfile() (cjihrig)

* unix: support copying empty files (cjihrig)

* unix: truncate destination in uv_fs_copyfile() (Nick Logan)

* win,build: keep cwd when setting build environment (darobs)

* test: add NetBSD support to test-udp-ipv6.c (Kamil Rytarowski)

* unix: add NetBSD support in core.c (Kamil Rytarowski)

* linux: increase thread stack size with musl libc (Ben Noordhuis)

* netbsd: correct uv_exepath() on NetBSD (Kamil Rytarowski)

* test: clean up semaphore after use (jBarz)

* win,build: bump vswhere_usability_wrapper to 2.0.0 (Refael Ackermann)

* win: let UV_PROCESS_WINDOWS_HIDE hide consoles (cjihrig)

* zos: lock protect global epoll list in epoll_ctl (jBarz)

* zos: change platform name to match python (jBarz)

* android: fix getifaddrs() (Zheng, Lei)

* netbsd: implement uv__tty_is_slave() (Kamil Rytarowski)

* zos: fix readlink for mounts with system variables (jBarz)

* test: sort the tests alphabetically (Sakthipriyan Vairamani)

* windows: fix compilation warnings (Carlo Marcelo Arenas Belón)

* build: avoid -fstrict-aliasing compile option (jBarz)

* win: remove unused variables (Carlo Marcelo Arenas Belón)

* unix: remove unused variables (Sakthipriyan Vairamani)

* netbsd: disable poll_bad_fdtype on NetBSD (Kamil Rytarowski)

* netbsd: use uv__cloexec and uv__nonblock (Kamil Rytarowski)

* test: fix udp_multicast_join6 on NetBSD (Kamil Rytarowski)

* unix,win: add uv_mutex_init_recursive() (Scott Parker)

* netbsd: do not exclude IPv6 functionality (Kamil Rytarowski)

* fsevents: watch files with fsevents on macos 10.7+ (Ben Noordhuis)

* unix: retry on ENOBUFS in sendmsg(2) (Kamil Rytarowski)


2017.09.07, Version 1.14.1 (Stable), b0f9fb2a07a5e638b1580fe9a42a356c3ab35f37

Changes since version 1.14.0:

* fs, win: add support for user symlinks (Bartosz Sosnowski)

* cygwin: include uv-posix.h header (Joel Winarske)

* zos: fix semaphore initialization (jBarz)

* zos: improve loop_count benchmark performance (jBarz)

* zos, test: flush out the oob data in callback (jBarz)

* unix,win: check for bad flags in uv_fs_copyfile() (cjihrig)

* unix: modify argv[0] when process title is set (Matthew Taylor)

* unix: don't use req->loop in uv__fs_copyfile() (cjihrig)

* doc: fix a trivial typo (Vladimír Čunát)

* android: fix uv_cond_timedwait on API level < 21 (Gergely Nagy)

* win: add uv__once_init() calls (Bartosz Sosnowski)

* unix,windows: init all requests in fs calls (cjihrig)

* unix,windows: return UV_EINVAL on NULL fs reqs (cjihrig)

* windows: add POST macro to fs functions (cjihrig)

* unix: handle partial sends in uv_fs_copyfile() (A. Hauptmann)

* Revert "win, test: fix double close in test runner" (Bartosz Sosnowski)

* win, test: remove surplus CloseHandle (Bartosz Sosnowski)


2017.08.17, Version 1.14.0 (Stable), e0d31e9e21870f88277746b6d59cf07b977cdfea

Changes since version 1.13.1:

* unix: check for NULL in uv_os_unsetenv for parameter name (André Klitzing)

* doc: add thread safety warning for process title (Matthew Taylor)

* unix: always copy process title into local buffer (Matthew Taylor)

* poll: add support for OOB TCP and GPIO interrupts (CurlyMoo)

* win,build: fix appveyor properly (Refael Ackermann)

* win: include filename in dlopen error message (Ben Noordhuis)

* aix: add netmask, mac address into net interfaces (Gireesh Punathil)

* unix, windows: map EREMOTEIO errno (Ben Noordhuis)

* unix: fix wrong MAC of uv_interface_address (XadillaX)

* win,build: fix building from Windows SDK or VS console (Saúl Ibarra Corretgé)

* github: fix link to help repo in issue template (Ben Noordhuis)

* zos: remove nonexistent include from autotools build (Saúl Ibarra Corretgé)

* misc: remove reference to pthread-fixes.h from LICENSE (Saúl Ibarra Corretgé)

* docs: fix guide source code example paths (Anticrisis)

* android: fix compilation with new NDK versions (Saúl Ibarra Corretgé)

* misc: add android-toolchain to .gitignore (Saúl Ibarra Corretgé)

* win, fs: support unusual reparse points (Bartosz Sosnowski)

* android: fix detection of pthread_condattr_setclock (Saúl Ibarra Corretgé)

* android: remove no longer needed check (Saúl Ibarra Corretgé)

* doc: update instructions for building on Android (Saúl Ibarra Corretgé)

* win, process: support semicolons in PATH variable (Bartosz Sosnowski)

* doc: document uv_async_(init|send) return values (Ben Noordhuis)

* doc: add Android as a tier 3 supported platform (Saúl Ibarra Corretgé)

* unix: add missing semicolon (jBarz)

* win, test: fix double close in test runner (Bartosz Sosnowski)

* doc: update supported windows version baseline (Ben Noordhuis)

* test,zos: skip chown root test (jBarz)

* test,zos: use gid=-1 to test spawn_setgid_fails (jBarz)

* zos: fix hr timer resolution (jBarz)

* android: fix blocking recvmsg due to netlink bug (Jacob Segal)

* zos: read more accurate rss info from RSM (jBarz)

* win: allow bound/connected socket in uv_tcp_open() (Maciej Szeptuch
  (Neverous))

* doc: differentiate SmartOS and SunOS support (cjihrig)

* unix: make uv_poll_stop() remove fd from pollset (Ben Noordhuis)

* unix, windows: add basic uv_fs_copyfile() (cjihrig)


2017.07.07, Version 1.13.1 (Stable), 2bb4b68758f07cd8617838e68c44c125bc567ba6

Changes since version 1.13.0:

* Now working on version 1.13.1 (cjihrig)

* build: workaround AppVeyor quirk (Refael Ackermann)


2017.07.06, Version 1.13.0 (Stable), 8342fcaab815f33b988c1910ea988f28dfe27edb

Changes since version 1.12.0:

* Now working on version 1.12.1 (cjihrig)

* unix: avoid segfault in uv_get_process_title (Michele Caini)

* build: add a comma to uv.gyp (Gemini Wen)

* win: restore file pos after positional read/write (Bartosz Sosnowski)

* unix,stream: return error on closed handle passing (Santiago Gimeno)

* unix,benchmark: use fd instead of FILE* after fork (jBarz)

* zos: avoid compiler warnings (jBarz)

* win,pipe: race condition canceling readfile thread (Jameson Nash)

* sunos: filter out non-IPv4/IPv6 interfaces (Sebastian Wiedenroth)

* sunos: fix cmpxchgi and cmpxchgl type error (Sai Ke WANG)

* unix: reset signal disposition before execve() (Ben Noordhuis)

* unix: reset signal mask before execve() (Ben Noordhuis)

* unix: fix POLLIN assertion on server read (jBarz)

* zos: use stckf builtin for high-res timer (jBarz)

* win,udp: implements uv_udp_try_send (Barnabas Gema)

* win,udp: return UV_EINVAL instead of aborting (Romain Caire)

* freebsd: replace kvm with sysctl (Robert Ayrapetyan)

* aix: fix un-initialized pointer field in fs handle (Gireesh Punathil)

* win,build: support building with VS2017 (Refael Ackermann)

* doc: add instructions for building on Windows (Refael Ackermann)

* doc: format README (Refael Ackermann)


2017.05.31, Version 1.12.0 (Stable), d6ac141ac674657049598c36604f26e031fae917

Changes since version 1.11.0:

* Now working on version 1.11.1 (cjihrig)

* test: fix tests on OpenBSD (Santiago Gimeno)

* test: fix -Wformat warning (Santiago Gimeno)

* win,fs: avoid double freeing uv_fs_event_t.dirw (Vladimir Matveev)

* unix: remove unused code in `uv__io_start` (Fedor Indutny)

* signal: add uv_signal_start_oneshot method (Santiago Gimeno)

* unix: factor out reusable POSIX hrtime impl (Brad King)

* unix,win: add uv_os_{get,set,unset}env() (cjihrig)

* win: add uv__convert_utf8_to_utf16() (cjihrig)

* docs: improve UV_ENOBUFS scenario documentation (cjihrig)

* unix: return UV_EINVAL for NULL env name (jBarz)

* unix: filter getifaddrs results consistently (Brad King)

* unix: factor out getifaddrs result filter (Brad King)

* unix: factor out reusable BSD ifaddrs impl (Brad King)

* unix: use union to follow strict aliasing rules (jBarz)

* unix: simplify async watcher dispatch logic (Ben Noordhuis)

* samples: update timer callback prototype (Ben Noordhuis)

* unix: make loops and watchers usable after fork() (Jason Madden)

* win: free uv__loops once empty (cjihrig)

* tools: add make_dist_html.py script (Ben Noordhuis)

* win,sunos: stop handle on uv_fs_event_start() err (cjihrig)

* unix,windows: refactor request init logic (Ben Noordhuis)

* win: fix memory leak inside uv__pipe_getname (A. Hauptmann)

* fsevent: support for files without short name (Bartosz Sosnowski)

* doc: fix multiple doc typos (Jamie Davis)

* test,osx: fix flaky kill test (Santiago Gimeno)

* unix: inline uv_pipe_bind() err_bind goto target (cjihrig)

* unix,test: deadstore fixes (Rasmus Christian Pedersen)

* win: fix memory leak inside uv_fs_access() (A. Hauptmann)

* doc: fix docs/src/fs.rst build warning (Daniel Bevenius)

* doc: minor grammar fix in Installation section (Daniel Bevenius)

* doc: suggestions for design page (Daniel Bevenius)

* doc: libuv does not touch uv_loop_t.data (Ben Noordhuis)

* github: add ISSUE_TEMPLATE.md (Ben Noordhuis)

* doc: add link to libuv/help to README (Ben Noordhuis)

* udp: fix fast path in uv_udp_send() on unix (Fedor Indutny)

* test: add test for uv_udp_send() fix (Trevor Norris)

* doc: fix documentation for uv_handle_t.type (Daniel Kahn Gillmor)

* zos: use proper prototype for epoll_init() (Ben Noordhuis)

* doc: rename docs to "libuv documentation" (Saúl Ibarra Corretgé)

* doc: update copyright years (Saúl Ibarra Corretgé)

* doc: move TOC to a dedicated document (Saúl Ibarra Corretgé)

* doc: move documentation section up (Saúl Ibarra Corretgé)

* doc: move "upgrading" to a standalone document (Saúl Ibarra Corretgé)

* doc: add initial version of the User Guide (Saúl Ibarra Corretgé)

* doc: removed unused file (Saúl Ibarra Corretgé)

* doc: update guide/about and mention new maintainership (Saúl Ibarra Corretgé)

* doc: remove licensing note from guide/about (Saúl Ibarra Corretgé)

* doc: add warning note to user guide (Saúl Ibarra Corretgé)

* doc: change license to CC BY 4.0 (Saúl Ibarra Corretgé)

* doc: remove ubvook reference from README (Saúl Ibarra Corretgé)

* doc: add code samples from uvbook (unadapted) (Saúl Ibarra Corretgé)

* doc: update supported linux/glibc baseline (Ben Noordhuis)

* win: avoid leaking pipe handles to child processes (Jameson Nash)

* win,test: support stdout output larger than 1kb (Bartosz Sosnowski)

* win: remove __declspec(inline) from atomic op (Keane)

* test: fix VC++ compilation warning (Rasmus Christian Pedersen)

* build: add -Wstrict-prototypes (Jameson Nash)

* zos: implement uv__io_fork, skip fs event tests (jBarz)

* unix: do not close udp sockets on bind error (Marc Schlaich)

* unix: remove FSEventStreamFlushSync() call (cjihrig)

* build,openbsd: remove kvm-related code (James McCoy)

* test: fix flaky tcp-write-queue-order (Santiago Gimeno)

* unix,win: add uv_os_gethostname() (cjihrig)

* zos: increase timeout for tcp_writealot (jBarz)

* zos: do not inline OOB data by default (jBarz)

* test: fix -Wstrict-prototypes compiler warnings (Ben Noordhuis)

* unix: factor out reusable no-proctitle impl (Brad King)

* test: factor out fsevents skip explanation (Brad King)

* test: skip fork fsevent cases when lacking support (Brad King)

* unix: factor out reusable no-fsevents impl (Brad King)

* unix: factor out reusable sysinfo memory lookup (Brad King)

* unix: factor out reusable sysinfo loadavg impl (Brad King)

* unix: factor out reusable procfs exepath impl (Brad King)

* unix: add a uv__io_poll impl using POSIX poll(2) (Brad King)

* cygwin: implement support for cygwin and msys2 (Brad King)

* cygwin: recognize EOF on named pipe closure (Brad King)

* cygwin: fix uv_pipe_connect report of ENOTSOCK (Brad King)

* cygwin: disable non-functional ipc handle send (Brad King)

* test: skip self-connecting tests on cygwin (Brad King)

* doc: mark uv_loop_fork() as experimental (cjihrig)

* doc: add bzoz to maintainers (Bartosz Sosnowski)

* doc: fix memory leak in tcp-echo-server example (Bernardo Ramos)

* win: make uv__get_osfhandle() public (Juan Cruz Viotti)

* doc: use valid pipe name in pipe-echo-server (Bernardo Ramos)


2017.02.02, Version 1.11.0 (Stable), 7452ef4e06a4f99ee26b694c65476401534f2725

Changes since version 1.10.2:

* Now working on version 1.10.3 (cjihrig)

* win: added fcntl.h to uv-win.h (Michele Caini)

* unix: move function call out of assert (jBarz)

* fs: cleanup uv__fs_scandir (Santiago Gimeno)

* fs: fix crash in uv_fs_scandir_next (muflub)

* win,signal: fix potential deadlock (Bartosz Sosnowski)

* unix: use async-signal safe functions between fork and exec (jBarz)

* sunos: fix SUNOS_NO_IFADDRS build (Ben Noordhuis)

* zos: make platform functional (John Barboza)

* doc: add repitition qualifier to version regexs (Daniel Bevenius)

* zos: use gyp OS label "os390" on z/OS (John Barboza)

* aix: enable uv_get/set_process_title (Howard Hellyer)

* zos: use built-in proctitle implementation (John Barboza)

* Revert "darwin: use clock_gettime in macOS 10.12" (Chris Araman)

* win,test: don't write uninitialized buffer to tty (Bert Belder)

* win: define ERROR_ELEVATION_REQUIRED for MinGW (Richard Lau)

* aix: re-enable fs watch facility (Gireesh Punathil)


2017.01.10, Version 1.10.2 (Stable), cb9f579a454b8db592030ffa274ae58df78dbe20

Changes since version 1.10.1:

* Now working on version 1.10.2 (cjihrig)

* darwin: fix fsync and fdatasync (Joran Dirk Greef)

* Revert "Revert "win,tty: add support for ANSI codes in win10 v1511""
  (Santiago Gimeno)

* win,tty: fix MultiByteToWideChar output buffer (Santiago Gimeno)

* win: remove dead code related to BACKUP_SEMANTICS (Sam Roberts)

* win: fix comment in quote_cmd_arg (Eric Sciple)

* darwin: use clock_gettime in macOS 10.12 (Saúl Ibarra Corretgé)

* win, tty: fix crash on restarting with pending data (Nicholas Vavilov)

* fs: fix uv__to_stat on BSD platforms (Santiago Gimeno)

* win: map ERROR_ELEVATION_REQUIRED to UV_EACCES (Richard Lau)

* win: fix free() on bad input in uv_getaddrinfo() (Ben Noordhuis)


2016.11.17, Version 1.10.1 (Stable), 2e49e332bdede6db7cf17fa784a902e8386d5d86

Changes since version 1.10.0:

* Now working on version 1.10.1 (cjihrig)

* win: fix anonymous union syntax (Brad King)

* unix: use uv__is_closing everywhere (Santiago Gimeno)

* win: add missing break statement (cjihrig)

* doc: fix wrong man page link for uv_fs_lstat() (Michele Caini)

* win, tty: handle empty buffer in uv_tty_write_bufs (Hitesh Kanwathirtha)

* doc: add cjihrig alternative GPG ID (cjihrig)

* Revert "win,tty: add support for ANSI codes in win10 v1511" (Ben Noordhuis)


2016.10.25, Version 1.10.0 (Stable), c8a373c729b4c9392e0e14fc53cd6b67b3051ab9

Changes since version 1.9.1:

* Now working on version 1.9.2 (Saúl Ibarra Corretgé)

* doc: add cjihrig GPG ID (cjihrig)

* win,build: fix compilation on old Windows / MSVC (Saúl Ibarra Corretgé)

* darwin: fix setting fd to non-blocking in select(() trick (Saúl Ibarra
  Corretgé)

* unix: allow nesting of kqueue fds in uv_poll_start (Ben Noordhuis)

* doc: fix generation the first time livehtml runs (Saúl Ibarra Corretgé)

* test: fix test_close_accept flakiness on Centos5 (Santiago Gimeno)

* license: libuv is no longer a Node project (Saúl Ibarra Corretgé)

* license: add license text we've been using for a while (Saúl Ibarra Corretgé)

* doc: add licensing information to README (Saúl Ibarra Corretgé)

* win,pipe: fixed formatting, DWORD is long unsigned (Miodrag Milanovic)

* win: support sub-second precision in uv_fs_futimes() (Jason Ginchereau)

* unix: ignore EINPROGRESS in uv__close (Saúl Ibarra Corretgé)

* doc: add Imran Iqbal (iWuzHere) to maintainers (Imran Iqbal)

* doc: update docs with AIX related information (Imran Iqbal)

* test: silence build warnings (Kári Tristan Helgason)

* doc: add iWuzHere GPG ID (Imran Iqbal)

* linux-core: fix uv_get_total/free_memory on uclibc (Nicolas Cavallari)

* build: fix build on DragonFly (Michael Neumann)

* unix: correctly detect named pipes on DragonFly (Michael Neumann)

* test: make tap output the default (Ben Noordhuis)

* test: don't dump output for skipped tests (Ben Noordhuis)

* test: improve formatting of diagnostic messages (Ben Noordhuis)

* test: remove unused RETURN_TODO macro (Ben Noordhuis)

* doc: fix stream typos (Pierre-Marie de Rodat)

* doc: update coding style link (Imran Iqbal)

* unix,fs: use uint64_t instead of unsigned long (Imran Iqbal)

* build: check for warnings for -fvisibility=hidden (Imran Iqbal)

* unix: remove unneeded TODO note (Saúl Ibarra Corretgé)

* test: skip tty_pty test if pty is not available (Luca Bruno)

* sunos: set phys_addr of interface_address using ARP (Brian Maher)

* doc: clarify callbacks won't be called in error case (Saúl Ibarra Corretgé)

* unix: don't convert stat buffer when syscall fails (Ben Noordhuis)

* win: compare entire filename in watch events (cjihrig)

* doc: add a note on safe reuse of uv_write_t (neevek)

* linux: fix potential event loop stall (Ben Noordhuis)

* unix,win: make uv_get_process_title() stricter (cjihrig)

* test: close server before initiating new connection (John Barboza)

* test: account for multiple handles in one ipc read (John Barboza)

* unix: fix errno and retval conflict (liuxiaobo)

* doc: add missing entry in uv_fs_type enum (Michele Caini)

* unix: preserve loop->data across loop init/done (Ben Noordhuis)

* win: return UV_EINVAL on bad uv_tty_mode mode arg (Ben Noordhuis)

* win: simplify memory copy logic in fs.c (Ben Noordhuis)

* win: fix compilation on mingw (Bartosz Sosnowski)

* win: ensure 32-bit printf precision (Matej Knopp)

* darwin: handle EINTR in /dev/tty workaround (Ben Noordhuis)

* test: fix OOB buffer access (Saúl Ibarra Corretgé)

* test: don't close CRT fd handed off to uv_pipe_t (Saúl Ibarra Corretgé)

* test: fix android build error. (sunjin.lee)

* win: evaluate timers when system wakes up (Bartosz Sosnowski)

* doc: add supported platforms description (Saúl Ibarra Corretgé)

* win: fix lstat reparse point without link data (Jason Ginchereau)

* unix,win: make on_alloc_cb failures more resilient (Saúl Ibarra Corretgé)

* zos: add support for new platform (John Barboza)

* test: make tcp_close_while_connecting more resilient (Saúl Ibarra Corretgé)

* build: use '${prefix}' for pkg-config 'exec_prefix' (Matt Clarkson)

* build: GNU/kFreeBSD support (Jeffrey Clark)

* zos: use PLO instruction for atomic operations (John Barboza)

* zos: use pthread helper functions (John Barboza)

* zos: implement uv__fs_futime (John Barboza)

* unix: expand range of values for usleep (John Barboza)

* zos: track unbound handles and bind before listen (John Barboza)

* test: improve tap output on test failures (Santiago Gimeno)

* test: refactor fs_event_close_in_callback (Julien Gilli)

* zos: implement uv__io_check_fd (John Barboza)

* unix: unnecessary use const qualifier in container_of (John Barboza)

* win,tty: add support for ANSI codes in win10 v1511 (Imran Iqbal)

* doc: add santigimeno to maintainers (Santiago Gimeno)

* win: fix typo in type name (Saúl Ibarra Corretgé)

* unix: always define pthread barrier fallback pad (Saúl Ibarra Corretgé)

* test: use RETURN_SKIP in spawn_setuid_setgid test (Santiago Gimeno)

* win: add disk read/write count to uv_getrusage (Imran Iqbal)

* doc: document uv_fs_realpath caveats (Saúl Ibarra Corretgé)

* test: improve spawn_setuid_setgid test (Santiago Gimeno)

* test: fix building pty test on Android (Saúl Ibarra Corretgé)

* doc: uv_buf_t members are not readonly (Saúl Ibarra Corretgé)

* doc: improve documentation on uv_alloc_cb (Saúl Ibarra Corretgé)

* fs: fix uv_fs_fstat on platforms using musl libc (Santiago Gimeno)

* doc: update supported fields for uv_rusage_t (Imran Iqbal)

* test: fix test-tcp-writealot flakiness on arm (Santiago Gimeno)

* test: fix fs_event_watch_dir flakiness on arm (Santiago Gimeno)

* unix: don't use alphasort in uv_fs_scandir() (Ben Noordhuis)

* doc: fix confusing doc of uv_tcp_nodelay (Bart Robinson)

* build,osx: fix warnings on tests compilation with gyp (Santiago Gimeno)

* doc: add ABI tracker link to README (Saúl Ibarra Corretgé)

* win,tty: fix uv_tty_set_mode race conditions (Bartosz Sosnowski)

* test: fix fs_fstat on Android (Vit Gottwald)

* win, test: fix fs_event_watch_dir_recursive (Bartosz Sosnowski)

* doc: add description of uv_handle_type (Vit Gottwald)

* build: use -pthreads for tests with autotools (Julien Gilli)

* win: fix leaky fs request buffer (Jason Ginchereau)

* doc: note buffer lifetime requirements in uv_write (Vladimír Čunát)

* doc: add reference to uv_update_time on uv_timer_start (Alex Hultman)

* win: fix winapi function pointer typedef syntax (Brad King)

* test: fix tcp_close_while_connecting CI failures (Ben Noordhuis)

* test: make threadpool_cancel_single deterministic (Ben Noordhuis)

* test: make threadpool saturation reliable (Ben Noordhuis)

* unix: don't malloc in uv_thread_create() (Ben Noordhuis)

* unix: don't include CoreServices globally on macOS (Brad King)

* unix,win: add uv_translate_sys_error() public API (Philippe Laferriere)

* win: remove unused static variables (Ben Noordhuis)

* win: silence -Wmaybe-uninitialized warning (Ben Noordhuis)

* signal: replace pthread_once with uv_once (Santiago Gimeno)

* test: fix sign-compare warning (Will Speak)

* common: fix unused variable warning (Brad King)


2016.05.17, Version 1.9.1 (Stable), d989902ac658b4323a4f4020446e6f4dc449e25c

Changes since version 1.9.0:

* test: handle root home directories (cjihrig)

* unix: implement uv__fs_futime for AIX 7.1 (Imran Iqbal)

* test: skip early bind tests if no IPv6 is supported (Saúl Ibarra Corretgé)

* win: fix var declaration to be C89 compliant (Michael Fero)

* unix: use POLL{IN,OUT,etc} constants directly (Ben Noordhuis)

* doc: add ability to live reload and regenerate HTML (Saúl Ibarra Corretgé)

* Revert "win,build: remove unused build defines" (cjihrig)

* linux: fix fd leaks in uv_cpu_info() error paths (Ben Noordhuis)

* linux: don't abort on malformed /proc/stat (Ben Noordhuis)

* linux: fix long lines in linux-core.c (Ben Noordhuis)

* test: fix fs_event_watch_file_current_dir for AIX (Imran Iqbal)

* unix,fs: code cleanup of uv_fs_event_start for AIX (Imran Iqbal)

* unix: delay signal handling until after normal i/o (Ben Noordhuis)

* android: pthread_sigmask() does not set errno (Oguz Bastemur)

* win: work around sharepoint scandir bug (Ben Noordhuis)

* unix: guard against clobbering errno in uv__free() (Ben Noordhuis)

* unix: remove unneeded SAVE_ERRNO wrappers (Ben Noordhuis)

* test: skip fs_event_close_in_callback on AIX (Imran Iqbal)

* win: add maxrss, pagefaults to uv_getrusage() (Robert Jefe Lindstaedt)

* test: set a big send buffer size for tcp_write_queue_order (Andrius Bentkus)

* unix: error on realpath if PATH_MAX is undefined (Myles Borins)

* unix: fix bug in barrier fallback implementation (Kári Tristan Helgason)

* build: bump android ndk version (Kári Tristan Helgason)

* build: always compile with -fvisibility=hidden (Ben Noordhuis)

* test: fix -Wformat warnings in platform test (Ben Noordhuis)

* win: clarify fsevents handling code (Saúl Ibarra Corretgé)

* test: fix POLLHDRUP related failures for AIX (Imran Iqbal)

* build, mingw: set LIBS in configure.ac (Tony Theodore)

* win: improve uv__convert_utf16_to_utf8 (Saúl Ibarra Corretgé)

* win: simplified UTF16 -> UTF8 conversions (Saúl Ibarra Corretgé)

* win: remove unneeded condition (Saúl Ibarra Corretgé)

* darwin: work around condition variable kernel bug (Ben Noordhuis)

* darwin: make thread stack multiple of page size (Ben Noordhuis)

* build,win: rename platform to msbuild_platform (João Reis)

* gitignore: ignore VS temporary database files (João Reis)

* test: skip emfile on AIX (Imran Iqbal)

* unix: use system allocator for scandir() (cjihrig)

* common: release uv_fs_scandir() array (cjihrig)

* win: call uv__fs_scandir_cleanup() (cjihrig)

* win,tty: fix read stop in line mode (João Reis)

* win,tty: don't duplicate handle for line reads (João Reis)

* win,tty: restore cursor after canceling line read (Alexis Campailla)


2016.04.08, Version 1.9.0 (Stable), 229b3a4cc150aebd6561e6bd43076eafa7a03756

Changes since version 1.8.0:

* win: wait for full timeout duration (João Reis)

* unix: fix support for uClibc-ng (Martin Bark)

* doc: indicate where new test files need to be added (Dave)

* test,unix: fix logic error in test runner (Ben Noordhuis)

* fs: don't nullify req->bufs on EINTR (Dave)

* osx: set the default thread stack size to RLIMIT_STACK (Saúl Ibarra Corretgé)

* build: invoke libtoolize with --copy (Ben Noordhuis)

* test: fixup eintr_handling (Saúl Ibarra Corretgé)

* osx: avoid compilation warning with Clang (Saúl Ibarra Corretgé)

* test,win: fix compilation with shared lib (Alexis Murzeau)

* test: fix race condition in pipe-close-stdout (Imran Iqbal)

* unix,win: add uv_os_tmpdir() (cjihrig)

* ios: fix undefined PTHREAD_STACK_MIN (Didiet)

* test: fix threadpool_multiple_event_loops for AIX (Imran Iqbal)

* unix: report errors for unpollable fds (Ben Noordhuis)

* win: fix watching root files (Nicholas Vavilov)

* build,win: print the Visual Studio version in use (Saúl Ibarra Corretgé)

* build,win: remove unneeded condition from GYP file (Saúl Ibarra Corretgé)

* test,win: fix compilation warning (Saúl Ibarra Corretgé)

* test: use uv_loop_close and assert its result (Nan Xiang)

* build: map 'AMD64' host arch to 'x64' (Ben Noordhuis)

* osx: protected use of potentially undefined macro (Samuel Lorétan)

* linux: fix compilation with musl (Saúl Ibarra Corretgé)

* doc: describe how to make release builds on Unix (Saúl Ibarra Corretgé)

* doc: add missing link in README (Saúl Ibarra Corretgé)

* build: python 2.x/3.x consistent print usage (Rasmus Christian Pedersen)

* test: assume no IPv6 if interfaces cannot be listed (Nan Xiang)

* darwin: replace F_FULLFSYNC with fdatasync syscall (Saúl Ibarra Corretgé)

* doc: add missing write callback to example (Nándor István Krácser)

* build: compile with -D_THREAD_SAFE on AIX (Imran Iqbal)

* test: fix threadpool_multiple_event_loops on PPC (Imran Iqbal)

* test: reduce timeout in tcp_close_while_connecting (Imran Iqbal)

* unix, win: consistently null-terminate buffers (Saúl Ibarra Corretgé)

* unix, win: count null byte on UV_ENOBUFS (Saúl Ibarra Corretgé)

* test: fix deadlocks in uv_cond_wait (Katsutoshi Horie)

* linux: fix cpu count (Lukasz Jagiello)

* unix: fix uv__handle_type for AIX (Imran Iqbal)

* linux: call fclose(), fix fdopen() memory leak (Ben Noordhuis)

* win: remove unneeded condition (Saúl Ibarra Corretgé)

* unix: fix compile error in Android using bionic (Robert Chiras)

* linux: add braces to multi-statement if (Kári Tristan Helgason)

* doc: add @cjihrig as a maintainer (Saúl Ibarra Corretgé)

* unix: add fork-safe open file function (Kári Tristan Helgason)

* linux: replace calls to fopen with uv__open_file (Kári Tristan Helgason)

* linux: remove redundant call to rewind() (Krishnaraj Bhat)

* win: remove duplicated code when processing fsevents (Saúl Ibarra Corretgé)

* test: fix poll_bad_fdtype for AIX (Imran Iqbal)

* linux: fix error checking in uv__open_file (Saúl Ibarra Corretgé)

* poll: add UV_DISCONNECT event (Santiago Gimeno)

* fs: realpath: fix string size before converting (Yuval Brik)

* win: use native APIs for UTF conversions (cjihrig)

* doc: clarify uv_loop_close() (Ben Noordhuis)

* unix: retry ioctl(TIOCGWINSZ) on EINTR (Ben Noordhuis)

* win,build: remove unused build defines (Saúl Ibarra Corretgé)

* win: fix buffer overflow in fs events (Joran Dirk Greef)

* win: fix uv_relative_path and remove dead branch (Joran Dirk Greef)

* unix: use open(2) with O_CLOEXEC on OS X (Kári Tristan Helgason)

* test: add missing copyright header (cjihrig)

* aix: fix 'POLLRDHUP undeclared' build error (Ben Noordhuis)

* unix,win: add uv_get_passwd() (cjihrig)

* process: fix uv_spawn edge-case (Santiago Gimeno)

* test: use %ld for printing uid/gid (Ben Noordhuis)

* aix: fix ahafs implementation (Imran Iqbal)

* aix: do not store absolute path to ahafs (Imran Iqbal)

* process: close process pipes safely (Santiago Gimeno)

* unix: open ttyname instead of /dev/tty (Enno Boland)

* unix: remove outdated comment (Kári Tristan Helgason)


2015.12.15, Version 1.8.0 (Stable), 5467299450ecf61635657557b6e01aaaf6c3fdf4

Changes since version 1.7.5:

* unix: fix memory leak in uv_interface_addresses (Jianghua Yang)

* unix: make uv_guess_handle work properly for AIX (Gireesh Punathil)

* fs: undo uv__req_init when uv__malloc failed (Jianghua Yang)

* build: remove unused 'component' GYP option (Saúl Ibarra Corretgé)

* include: remove duplicate extern declaration (Jianghua Yang)

* win: use the MSVC provided snprintf where possible (Jason Williams)

* win, test: fix compilation warning (Saúl Ibarra Corretgé)

* win: fix compilation with VS < 2012 (Ryan Johnston)

* stream: support empty uv_try_write on unix (Fedor Indutny)

* unix: fix request handle leak in uv__udp_send (Jianghua Yang)

* src: replace QUEUE_SPLIT with QUEUE_MOVE (Ben Noordhuis)

* unix: use QUEUE_MOVE when iterating over lists (Ben Noordhuis)

* unix: squelch harmless valgrind warning (Ben Noordhuis)

* test: don't abort on setrlimit() failure (Ben Noordhuis)

* unix: only undo fs req registration in async mode (Ben Noordhuis)

* unix: fix uv__getiovmax return value (HungMingWu)

* unix: make work with Solaris Studio. (Adam Stylinski)

* test: fix fs_event_watch_file_currentdir flakiness (Santiago Gimeno)

* unix: skip prohibited syscalls on tvOS and watchOS (Nathan Corvino)

* test: use FQDN in getaddrinfo_fail test (Wink Saville)

* docs: clarify documentation of uv_tcp_init_ex (Andrius Bentkus)

* win: fix comment (Miodrag Milanovic)

* doc: fix typo in README (Angel Leon)

* darwin: abort() if (un)locking fs mutex fails (Ben Noordhuis)

* pipe: enable inprocess uv_write2 on Windows (Louis DeJardin)

* win: properly return UV_EBADF when _close() fails (Nicholas Vavilov)

* test: skip process_title for AIX (Imran Iqbal)

* misc: expose handle print APIs (Petka Antonov)

* include: add stdio.h to uv.h (Saúl Ibarra Corretgé)

* misc: remove unnecessary null pointer checks (Ian Kronquist)

* test,freebsd: skip udp_dual_stack if not supported (Santiago Gimeno)

* linux: don't retry dup2/dup3 on EINTR (Ben Noordhuis)

* unix: don't retry dup2/dup3 on EINTR (Ben Noordhuis)

* test: fix -Wtautological-pointer-compare warnings (Saúl Ibarra Corretgé)

* win: map ERROR_BAD_PATHNAME to UV_ENOENT (Tony Kelman)

* test: fix test/test-tty.c for AIX (Imran Iqbal)

* android: support api level less than 21 (kkdaemon)

* fsevents: fix race on simultaneous init+close (Fedor Indutny)

* linux,fs: fix p{read,write}v with a 64bit offset (Saúl Ibarra Corretgé)

* fs: add uv_fs_realpath() (Yuval Brik)

* win: fix path for removed and renamed fs events (Joran Dirk Greef)

* win: do not read more from stream than available (Jeremy Whitlock)

* test: test that uv_close() doesn't corrupt QUEUE (Andrey Mazo)

* unix: fix uv_fs_event_stop() from fs_event_cb (Andrey Mazo)

* test: fix self-deadlocks in thread_rwlock_trylock (Ben Noordhuis)

* src: remove non ascii character (sztomi)

* test: fix test udp_multicast_join6 for AIX (Imran Iqbal)


2015.09.23, Version 1.7.5 (Stable), a8c1136de2cabf25b143021488cbaab05834daa8

Changes since version 1.7.4:

* unix: Support atomic compare & swap xlC on AIX (nmushell)

* unix: Fix including uv-aix.h on AIX (nmushell)

* unix: consolidate rwlock tryrdlock trywrlock errors (Saúl Ibarra Corretgé)

* unix, win: consolidate mutex trylock errors (Saúl Ibarra Corretgé)

* darwin: fix memory leak in uv_cpu_info (Jianghua Yang)

* test: add tests for the uv_rwlock implementation (Bert Belder)

* win: redo/fix the uv_rwlock APIs (Bert Belder)

* win: don't fetch function pointers to SRWLock APIs (Bert Belder)


2015.09.12, Version 1.7.4 (Stable), a7ad4f52189d89cfcba35f78bfc5ff3b1f4105c4

Changes since version 1.7.3:

* doc: uv_read_start and uv_read_cb clarifications (Ben Trask)

* freebsd: obtain true uptime through clock_gettime() (Jianghua Yang)

* win, tty: do not convert \r to \r\n (Colin Snover)

* build,gyp: add DragonFly to the list of OSes (Michael Neumann)

* fs: fix bug in sendfile for DragonFly (Michael Neumann)

* doc: add uv_dlsym() return type (Brian White)

* tests: fix fs tests run w/o full getdents support (Jeremy Whitlock)

* doc: fix typo (Devchandra Meetei Leishangthem)

* doc: fix uv-unix.h location (Sakthipriyan Vairamani)

* unix: fix error check when closing process pipe fd (Ben Noordhuis)

* test,freebsd: fix ipc_listen_xx_write tests (Santiago Gimeno)

* win: fix unsavory rwlock fallback implementation (Bert Belder)

* doc: clarify repeat timer behavior (Eli Skeggs)


2015.08.28, Version 1.7.3 (Stable), 93877b11c8b86e0a6befcda83a54555c1e36e4f0

Changes since version 1.7.2:

* threadpool: fix thread starvation bug (Ben Noordhuis)


2015.08.25, Version 1.7.2 (Stable), 4d13a013fcfa72311f0102751fdc7951873f466c

Changes since version 1.7.1:

* unix, win: make uv_loop_init return on error (Willem Thiart)

* win: reset pipe handle for pipe servers (Saúl Ibarra Corretgé)

* win: fix replacing pipe handle for pipe servers (Saúl Ibarra Corretgé)

* win: fix setting pipe pending instances after bind (Saúl Ibarra Corretgé)


2015.08.20, Version 1.7.1 (Stable), 44f4b6bd82d8ae4583ccc4768a83af778ef69f85

Changes since version 1.7.0:

* doc: document the procedure for verifying releases (Saúl Ibarra Corretgé)

* doc: add note about Windows binaries to the README (Saúl Ibarra Corretgé)

* doc: use long GPG IDs in MAINTAINERS.md (Saúl Ibarra Corretgé)

* Revert "stream: squelch ECONNRESET error if already closed" (Saúl Ibarra
  Corretgé)

* doc: clarify uv_read_stop() is idempotent (Corbin Simpson)

* unix: OpenBSD's setsockopt needs an unsigned char for multicast (Zachary
  Hamm)

* test: Fix two memory leaks (Karl Skomski)

* unix,win: return EINVAL on nullptr args in uv_fs_{read,write} (Karl Skomski)

* win: set accepted TCP sockets as non-inheritable (Saúl Ibarra Corretgé)

* unix: remove superfluous parentheses in fs macros (Ben Noordhuis)

* unix: don't copy arguments for sync fs requests (Ben Noordhuis)

* test: plug small memory leak in unix test runner (Ben Noordhuis)

* unix,windows: allow NULL loop for sync fs requests (Ben Noordhuis)

* unix,windows: don't assert on unknown error code (Ben Noordhuis)

* stream: retry write on EPROTOTYPE on OSX (Brian White)

* common: fix use of snprintf on Windows (Saúl Ibarra Corretgé)

* tests: refactored fs watch_dir tests for stability (Jeremy Whitlock)


2015.08.06, Version 1.7.0 (Stable), 415a865d6365ba58d02b92b89d46ba5d7744ec8b

Changes since version 1.6.1:

* win,stream: add slot to remember CRT fd (Bert Belder)

* win,pipe: properly close when created from CRT fd (Bert Belder)

* win,pipe: don't close fd 0-2 (Bert Belder)

* win,tty: convert fd -> handle safely (Bert Belder)

* win,tty: properly close when created from CRT fd (Bert Belder)

* win,tty: don't close fd 0-2 (Bert Belder)

* win,fs: don't close fd 0-2 (Bert Belder)

* win: include "malloc.h" (Cheng Zhao)

* windows: MSVC 2015 has C99 inline (Jason Williams)

* dragonflybsd: fixes for nonblocking and cloexec (Michael Neumann)

* dragonflybsd: use sendfile(2) for uv_fs_sendfile (Michael Neumann)

* dragonflybsd: fix uv_exepath (Michael Neumann)

* win,fs: Fixes align(8) directive on mingw (Stefano Cristiano)

* unix, win: prevent replacing fd in uv_{udp,tcp,pipe}_t (Saúl Ibarra Corretgé)

* win: move logic to set socket non-inheritable to uv_tcp_set_socket (Saúl
  Ibarra Corretgé)

* unix, win: add ability to create tcp/udp sockets early (Saúl Ibarra Corretgé)

* test: retry select() on EINTR, honor milliseconds (Ben Noordhuis)

* unix: consolidate tcp and udp bind error (Saúl Ibarra Corretgé)

* test: conditionally skip udp_ipv6_multicast_join6 (heshamsafi)

* core: add UV_VERSION_HEX macro (Saúl Ibarra Corretgé)

* doc: add section with version-checking macros and functions (Saúl Ibarra
  Corretgé)

* tty: cleanup handle if uv_tty_init fails (Saúl Ibarra Corretgé)

* darwin: save a fd when FSEvents is used (Saúl Ibarra Corretgé)

* win: fix returning thread id in uv_thread_self (Saúl Ibarra Corretgé)

* common: use offsetof for QUEUE_DATA (Saúl Ibarra Corretgé)

* win: remove UV_HANDLE_CONNECTED (A. Hauptmann)

* docs: add Windows specific note for uv_fs_open (Saúl Ibarra Corretgé)

* doc: add note about uv_fs_scandir (Saúl Ibarra Corretgé)

* test,unix: reduce stack size of watchdog threads (Ben Noordhuis)

* win: add support for recursive file watching (Saúl Ibarra Corretgé)

* win,tty: support consoles with non-default colors (John McNamee)

* doc: add missing variable name (Yosuke Furukawa)

* stream: squelch ECONNRESET error if already closed (Santiago Gimeno)

* build: remove ancient condition from common.gypi (Saúl Ibarra Corretgé)

* tests: skip some tests when network is unreachable (Luca Bruno)

* build: proper support for android cross compilation (guworks)

* android: add missing include to pthread-fixes.c (RossBencina)

* test: fix compilation warning (Saúl Ibarra Corretgé)

* doc: add a note about uv_dirent_t.type (Saúl Ibarra Corretgé)

* win,test: fix shared library build (Saúl Ibarra Corretgé)

* test: fix compilation warning (Santiago Gimeno)

* build: add experimental Windows installer (Roger A. Light)

* threadpool: send signal only when queue is empty (chenttuuvv)

* aix: fix uv_exepath with relative paths (Richard Lau)

* build: fix version syntax in AppVeyor file (Saúl Ibarra Corretgé)

* unix: allow nbufs > IOV_MAX in uv_fs_{read,write} (ronkorving)


2015.06.06, Version 1.6.1 (Stable), 30c8be07bb78a66fdee5141626bf53a49a17094a

Changes since version 1.6.0:

* unix: handle invalid _SC_GETPW_R_SIZE_MAX values (cjihrig)


2015.06.04, Version 1.6.0 (Stable), adfccad76456061dfcf79b8df8e7dbfee51791d7

Changes since version 1.5.0:

* aix: fix setsockopt for multicast options (Michael)

* unix: don't block for io if any io handle is primed (Saúl Ibarra Corretgé)

* windows: MSVC 2015 has snprintf() (Rui Abreu Ferreira)

* windows: Add VS2015 support to vcbuild.bat (Jason Williams)

* doc: fix typo in tcp.rst (Igor Soarez)

* linux: work around epoll bug in kernels < 2.6.37 (Ben Noordhuis)

* unix,win: add uv_os_homedir() (cjihrig)

* stream: fix `select()` race condition (Fedor Indutny)

* unix: prevent infinite loop in uv__run_pending (Saúl Ibarra Corretgé)

* unix: make sure UDP send callbacks are asynchronous (Saúl Ibarra Corretgé)

* test: fix `platform_output` netmask printing. (Andrew Paprocki)

* aix: add ahafs autoconf detection and README notes (Andrew Paprocki)

* core: add ability to customize memory allocator (Saúl Ibarra Corretgé)


2015.05.07, Version 1.5.0 (Stable), 4e77f74c7b95b639b3397095db1bc5bcc016c203

Changes since version 1.4.2:

* doc: clarify that the thread pool primites are not thread safe (Andrius
  Bentkus)

* aix: always deregister closing fds from epoll (Michael)

* unix: fix glibc-2.20+ macro incompatibility (Massimiliano Torromeo)

* doc: add Sphinx plugin for generating links to man pages (Saúl Ibarra
  Corretgé)

* doc: link system and library calls to man pages (Saúl Ibarra Corretgé)

* doc: document uv_getnameinfo_t.{host|service} (Saúl Ibarra Corretgé)

* build: update the location of gyp (Stephen von Takach)

* win: name all anonymous structs and unions (TomCrypto)

* linux: work around epoll bug in kernels 3.10-3.19 (Ben Noordhuis)

* darwin: fix size calculation in select() fallback (Ole André Vadla Ravnås)

* solaris: fix setsockopt for multicast options (Julien Gilli)

* test: fix race condition in multithreaded test (Ben Noordhuis)

* doc: fix long lines in tty.rst (Ben Noordhuis)

* test: use UV_TTY_MODE_* values in tty test (Ben Noordhuis)

* unix: don't clobber errno in uv_tty_reset_mode() (Ben Noordhuis)

* unix: reject non-tty fds in uv_tty_init() (Ben Noordhuis)

* win: fix pipe blocking writes (Alexis Campailla)

* build: fix cross-compiling for iOS (Steven Kabbes)

* win: remove unnecessary malloc.h

* include: use `extern "c++"` for defining C++ code (Kazuho Oku)

* unix: reap child on execvp() failure (Ryan Phillips)

* windows: fix handle leak on EMFILE (Brian Green)

* test: fix tty_file, close handle if initialized (Saúl Ibarra Corretgé)

* doc: clarify what uv_*_open accepts (Saúl Ibarra Corretgé)

* doc: clarify that we don't maintain external doc resources (Saúl Ibarra
  Corretgé)

* build: add documentation for ninja support (Devchandra Meetei Leishangthem)

* doc: document uv_buf_t members (Corey Farrell)

* linux: fix epoll_pwait() fallback on arm64 (Ben Noordhuis)

* android: fix compilation warning (Saúl Ibarra Corretgé)

* unix: don't close the fds we just setup (Sam Roberts)

* test: spawn child replacing std{out,err} to stderr (Saúl Ibarra Corretgé)

* unix: fix swapping fds order in uv_spawn (Saúl Ibarra Corretgé)

* unix: fix potential bug if dup2 fails in uv_spawn (Saúl Ibarra Corretgé)

* test: remove LOG and LOGF variadic macros (Saúl Ibarra Corretgé)

* win: fix uv_fs_access on directories (Saúl Ibarra Corretgé)

* win: fix of double free in uv_uptime (Per Nilsson)

* unix: open "/dev/null" instead of "/" for emfile_fd (Alan Rogers)

* docs: add some missing words (Daryl Haresign)

* unix: clean up uv_fs_open() O_CLOEXEC logic (Ben Noordhuis)

* build: set SONAME for shared library in uv.gyp (Rui Abreu Ferreira)

* windows: define snprintf replacement as inline instead of static (Rui Abreu
  Ferreira)

* win: fix unlink of readonly files (João Reis)

* doc: fix uv_run(UV_RUN_DEFAULT) description (Ben Noordhuis)

* linux: intercept syscall when running under memory sanitizer (Keno Fischer)

* aix: fix uv_interface_addresses return value (farblue68)

* windows: defer reporting TCP write failure until next tick (Saúl Ibarra
  Corretgé)

* test: add test for deferred TCP write failure (Saúl Ibarra Corretgé)


2015.02.27, Version 1.4.2 (Stable), 1a7391348a11d5450c0f69c828d5302e2cb842eb

Changes since version 1.4.1:

* stream: ignore EINVAL for SO_OOBINLINE on OS X (Fedor Indutny)


2015.02.25, Version 1.4.1 (Stable), e8e3fc5789cc0f02937879d141cca0411274093c

Changes since version 1.4.0:

* win: don't use inline keyword in thread.c (Ben Noordhuis)

* windows: fix setting dirent types on uv_fs_scandir_next (Saúl Ibarra
  Corretgé)

* unix,windows: make uv_thread_create() return errno (Ben Noordhuis)

* tty: fix build for SmartOS (Julien Gilli)

* unix: fix for uv_async data race (Michael Penick)

* unix, windows: map EHOSTDOWN errno (Ben Noordhuis)

* stream: use SO_OOBINLINE on OS X (Fedor Indutny)


2015.02.10, Version 1.4.0 (Stable), 19fb8a90648f3763240db004b77ab984264409be

Changes since version 1.3.0:

* unix: check Android support for pthread_cond_timedwait_monotonic_np (Leith
  Bade)

* test: use modified path in test (cjihrig)

* unix: implement uv_stream_set_blocking() (Ben Noordhuis)


2015.01.29, Version 1.3.0 (Stable), 165685b2a9a42cf96501d79cd6d48a18aaa16e3b

Changes since version 1.2.1:

* unix, windows: set non-block mode in uv_poll_init (Saúl Ibarra Corretgé)

* doc: clarify which flags are supported in uv_fs_event_start (Saúl Ibarra
  Corretgé)

* win,unix: move loop functions which have identical implementations (Andrius
  Bentkus)

* doc: explain how the threadpool is allocated (Alex Mo)

* doc: clarify uv_default_loop (Saúl Ibarra Corretgé)

* unix: fix implicit declaration compiler warning (Ben Noordhuis)

* unix: fix long line introduced in commit 94e628fa (Ben Noordhuis)

* unix, win: add synchronous uv_get{addr,name}info (Saúl Ibarra Corretgé)

* linux: fix epoll_pwait() regression with < 2.6.19 (Ben Noordhuis)

* build: compile -D_GNU_SOURCE on linux (Ben Noordhuis)

* build: use -fvisibility=hidden in autotools build (Ben Noordhuis)

* fs, pipe: no trailing terminator in exact sized buffers (Andrius Bentkus)

* style: rename buf to buffer and len to size for consistency (Andrius Bentkus)

* test: fix test-spawn on MinGW32 (Luis Martinez de Bartolome)

* win, pipe: fix assertion when destroying timer (Andrius Bentkus)

* win, unix: add pipe_peername implementation (Andrius Bentkus)


2015.01.29, Version 0.10.33 (Stable), 7a2253d33ad8215a26c1b34f1952aee7242dd687

Changes since version 0.10.32:

* linux: fix epoll_pwait() regression with < 2.6.19 (Ben Noordhuis)

* test: back-port uv_loop_configure() test (Ben Noordhuis)


2015.01.15, Version 1.2.1 (Stable), 4ca78e989062a1099dc4b9ad182a98e8374134b1

Changes since version 1.2.0:

* unix: remove unused dtrace file (Saúl Ibarra Corretgé)

* test: skip TTY select test if /dev/tty can't be opened (Saúl Ibarra Corretgé)

* doc: clarify the behavior of uv_tty_init (Saúl Ibarra Corretgé)

* doc: clarify how uv_async_send behaves (Saúl Ibarra Corretgé)

* build: make dist now generates a full tarball (Johan Bergström)

* freebsd: make uv_exepath more resilient (Saúl Ibarra Corretgé)

* unix: make setting the tty mode to the same value a no-op (Saúl Ibarra
  Corretgé)

* win,tcp: support uv_try_write (Bert Belder)

* test: enable test-tcp-try-write on windows (Bert Belder)

* win,tty: support uv_try_write (Bert Belder)

* unix: set non-block mode in uv_{pipe,tcp,udp}_open (Ben Noordhuis)


2015.01.06, Version 1.2.0 (Stable), 09f25b13cd149c7981108fc1a75611daf1277f83

Changes since version 1.1.0:

* linux: fix epoll_pwait() sigmask size calculation (Ben Noordhuis)

* tty: implement binary I/O terminal mode (Yuri D'Elia)

* test: fix spawn test with autotools build (Ben Noordhuis)

* test: skip ipv6 tests when ipv6 is not supported (Ben Noordhuis)

* common: move STATIC_ASSERT to uv-common.h (Alexey Melnichuk)

* win/thread: store thread handle in a TLS slot (Alexey Melnichuk)

* unix: fix ttl, multicast ttl and loop options on IPv6 (Saúl Ibarra Corretgé)

* linux: fix support for preadv/pwritev-less kernels (Ben Noordhuis)

* unix: make uv_exepath(size=0) return UV_EINVAL (Ben Noordhuis)

* darwin: fix uv_exepath(smallbuf) UV_EPERM error (Ben Noordhuis)

* openbsd: fix uv_exepath(smallbuf) UV_EINVAL error (Ben Noordhuis)

* linux: fix uv_exepath(size=1) UV_EINVAL error (Ben Noordhuis)

* sunos: preemptively fix uv_exepath(size=1) (Ben Noordhuis)

* win: fix and clarify comments in winapi.h (Bert Belder)

* win: make available NtQueryDirectoryFile (Bert Belder)

* win: add definitions for directory information types (Bert Belder)

* win: use NtQueryDirectoryFile to implement uv_fs_scandir (Bert Belder)

* unix: don't unlink unix socket on bind error (Ben Noordhuis)

* build: fix bad comment in autogen.sh (Ben Noordhuis)

* build: add AC_PROG_LIBTOOL to configure.ac (Ben Noordhuis)

* test: skip udp_options6 if there no IPv6 support (Saúl Ibarra Corretgé)

* win: add definitions for MUI errors mingw lacks (Bert Belder)

* build: enable warnings in autotools build (Ben Noordhuis)

* build: remove -Wno-dollar-in-identifier-extension (Ben Noordhuis)

* build: move flags from Makefile.am to configure.ac (Ben Noordhuis)


2015.01.06, Version 0.10.32 (Stable), 378de30c59aef5fdb6d130fa5cfcb0a68fce571c

Changes since version 0.10.31:

* linux: fix epoll_pwait() sigmask size calculation (Ben Noordhuis)


2014.12.25, Version 1.1.0 (Stable), 9572f3e74a167f59a8017e57ca3ebe91ffd88e18

Changes since version 1.0.2:

* test: test that closing a poll handle doesn't corrupt the stack (Bert Belder)

* win: fix compilation of tests (Marc Schlaich)

* Revert "win: keep a reference to AFD_POLL_INFO in cancel poll" (Bert Belder)

* win: avoid stack corruption when closing a poll handle (Bert Belder)

* test: fix test-fs-file-loop on Windows (Bert Belder)

* test: fix test-cwd-and-chdir (Bert Belder)

* doc: indicate what version uv_loop_configure was added on (Saúl Ibarra
  Corretgé)

* doc: fix sphinx warning (Saúl Ibarra Corretgé)

* test: skip spawn_setuid_setgid if we get EACCES (Saúl Ibarra Corretgé)

* test: silence some Clang warnings (Saúl Ibarra Corretgé)

* test: relax osx_select_many_fds (Saúl Ibarra Corretgé)

* test: fix compilation warnings when building with Clang (Saúl Ibarra
  Corretgé)

* win: fix autotools build of tests (Luis Lavena)

* gitignore: ignore Visual Studio files (Marc Schlaich)

* win: set fallback message if FormatMessage fails (Marc Schlaich)

* win: fall back to default language in uv_dlerror (Marc Schlaich)

* test: improve compatibility for dlerror test (Marc Schlaich)

* test: check dlerror is "no error" in no error case (Marc Schlaich)

* unix: change uv_cwd not to return a trailing slash (Saúl Ibarra Corretgé)

* test: fix cwd_and_chdir test on Unix (Saúl Ibarra Corretgé)

* test: add uv_cwd output to platform_output test (Saúl Ibarra Corretgé)

* build: fix dragonflybsd autotools build (John Marino)

* win: scandir use 'ls' for formatting long strings (Kenneth Perry)

* build: remove clang and gcc_version gyp defines (Ben Noordhuis)

* unix, windows: don't treat uv_run_mode as a bitmask (Saúl Ibarra Corretgé)

* unix, windows: fix UV_RUN_ONCE mode if progress was made (Saúl Ibarra
  Corretgé)


2014.12.25, Version 0.10.31 (Stable), 4dbd27e2219069a6daa769fb37f98673b77b4261

Changes since version 0.10.30:

* test: test that closing a poll handle doesn't corrupt the stack (Bert Belder)

* win: fix compilation of tests (Marc Schlaich)

* Revert "win: keep a reference to AFD_POLL_INFO in cancel poll" (Bert Belder)

* win: avoid stack corruption when closing a poll handle (Bert Belder)

* gitignore: ignore Visual Studio files (Marc Schlaich)

* win: set fallback message if FormatMessage fails (Marc Schlaich)

* win: fall back to default language in uv_dlerror (Marc Schlaich)

* test: improve compatibility for dlerror test (Marc Schlaich)

* test: check dlerror is "no error" in no error case (Marc Schlaich)

* build: link against -pthread (Logan Rosen)

* win: scandir use 'ls' for formatting long strings (Kenneth Perry)


2014.12.10, Version 1.0.2 (Stable), eec671f0059953505f9a3c9aeb7f9f31466dd7cd

Changes since version 1.0.1:

* linux: fix sigmask size arg in epoll_pwait() call (Ben Noordhuis)

* linux: handle O_NONBLOCK != SOCK_NONBLOCK case (Helge Deller)

* doc: fix spelling (Joey Geralnik)

* unix, windows: fix typos in comments (Joey Geralnik)

* test: canonicalize test runner path (Ben Noordhuis)

* test: fix compilation warnings (Saúl Ibarra Corretgé)

* test: skip tty test if detected width and height are 0 (Saúl Ibarra Corretgé)

* doc: update README with IRC channel (Saúl Ibarra Corretgé)

* Revert "unix: use cfmakeraw() for setting raw TTY mode" (Ben Noordhuis)

* doc: document how to get result of uv_fs_mkdtemp (Tim Caswell)

* unix: add flag for blocking SIGPROF during poll (Ben Noordhuis)

* unix, windows: add uv_loop_configure() function (Ben Noordhuis)

* win: keep a reference to AFD_POLL_INFO in cancel poll (Marc Schlaich)

* test: raise fd limit for OSX select test (Saúl Ibarra Corretgé)

* unix: remove overzealous assert in uv_read_stop (Saúl Ibarra Corretgé)

* unix: reset the reading flag when a stream gets EOF (Saúl Ibarra Corretgé)

* unix: stop reading if an error is produced (Saúl Ibarra Corretgé)

* cleanup: remove all dead assignments (Maciej Małecki)

* linux: return early if we have no interfaces (Maciej Małecki)

* cleanup: remove a dead increment (Maciej Małecki)


2014.12.10, Version 0.10.30 (Stable), 5a63f5e9546dca482eeebc3054139b21f509f21f

Changes since version 0.10.29:

* linux: fix sigmask size arg in epoll_pwait() call (Ben Noordhuis)

* linux: handle O_NONBLOCK != SOCK_NONBLOCK case (Helge Deller)

* doc: update project links (Ben Noordhuis)

* windows: fix compilation of tests (Marc Schlaich)

* unix: add flag for blocking SIGPROF during poll (Ben Noordhuis)

* unix, windows: add uv_loop_configure() function (Ben Noordhuis)

* win: keep a reference to AFD_POLL_INFO in cancel poll (Marc Schlaich)


2014.11.27, Version 1.0.1 (Stable), 0a8e81374e861d425b56c45c8599595d848911d2

Changes since version 1.0.0:

* readme: remove Rust from users (Elijah Andrews)

* doc,build,include: update project links (Ben Noordhuis)

* doc: fix typo: Strcutures -> Structures (Michael Ira Krufky)

* unix: fix processing process handles queue (Saúl Ibarra Corretgé)

* win: replace non-ansi characters in source file (Bert Belder)


2014.11.21, Version 1.0.0 (Stable), feb2a9e6947d892f449b2770c4090f7d8c88381b

Changes since version 1.0.0-rc2:

* doc: fix git/svn url for gyp repo in README (Emmanuel Odeke)

* windows: fix fs_read with nbufs > 1 and offset (Unknown W. Brackets)

* win: add missing IP_ADAPTER_UNICAST_ADDRESS_LH definition for MinGW
  (huxingyi)

* doc: mention homebrew in README (Mikhail Mukovnikov)

* doc: add learnuv workshop to README (Thorsten Lorenz)

* doc: fix parameter name in uv_fs_access (Saúl Ibarra Corretgé)

* unix: use cfmakeraw() for setting raw TTY mode (Yuri D'Elia)

* win: fix uv_thread_self() (Alexis Campailla)

* build: add x32 support to gyp build (Ben Noordhuis)

* build: remove dtrace probes (Ben Noordhuis)

* doc: fix link in misc.rst (Manos Nikolaidis)

* mailmap: remove duplicated entries (Saúl Ibarra Corretgé)

* gyp: fix comment regarding version info location (Saúl Ibarra Corretgé)


2014.10.21, Version 1.0.0-rc2 (Pre-release)

Changes since version 1.0.0-rc1:

* build: add missing fixtures to distribution tarball (Rob Adams)

* doc: update references to current stable branch (Zachary Newman)

* fs: fix readdir on empty directory (Fedor Indutny)

* fs: rename uv_fs_readdir to uv_fs_scandir (Saúl Ibarra Corretgé)

* doc: document uv_alloc_cb (Saúl Ibarra Corretgé)

* doc: add migration guide from version 0.10 (Saúl Ibarra Corretgé)

* build: add DragonFly BSD support in autotools (Robin Hahling)

* doc: document missing stream related structures (Saúl Ibarra Corretgé)

* doc: clarify uv_loop_t.data field lifetime (Saúl Ibarra Corretgé)

* doc: add documentation for missing functions and structures (Saúl Ibarra
  Corretgé)

* doc: fix punctuation and grammar in README (Jeff Widman)

* windows: return libuv error codes in uv_poll_init() (cjihrig)

* unix, windows: add uv_fs_access() (cjihrig)

* windows: fix netmask detection (Alexis Campailla)

* unix, windows: don't include null byte in uv_cwd size (Saúl Ibarra Corretgé)

* unix, windows: add uv_thread_equal (Tomasz Kołodziejski)

* windows: fix fs_write with nbufs > 1 and offset (Unknown W. Brackets)


2014.10.21, Version 0.10.29 (Stable), 2d728542d3790183417f8f122a110693cd85db14

Changes since version 0.10.28:

* darwin: allocate enough space for select() hack (Fedor Indutny)

* linux: try epoll_pwait if epoll_wait is missing (Michael Hudson-Doyle)

* windows: map ERROR_INVALID_DRIVE to UV_ENOENT (Saúl Ibarra Corretgé)


2014.09.18, Version 1.0.0-rc1 (Unstable), 0c28bbf7b42882853d1799ab96ff68b07f7f8d49

Changes since version 0.11.29:

* windows: improve timer precision (Alexis Campailla)

* build, gyp: set xcode flags (Recep ASLANTAS)

* ignore: include m4 files which are created manually (Recep ASLANTAS)

* build: add m4 for feature/flag-testing (Recep ASLANTAS)

* ignore: ignore Xcode project and workspace files (Recep ASLANTAS)

* unix: fix warnings about dollar symbol usage in identifiers (Recep ASLANTAS)

* unix: fix warnings when loading functions with dlsym (Recep ASLANTAS)

* linux: try epoll_pwait if epoll_wait is missing (Michael Hudson-Doyle)

* test: add test for closing and recreating default loop (Saúl Ibarra Corretgé)

* windows: properly close the default loop (Saúl Ibarra Corretgé)

* version: add ability to specify a version suffix (Saúl Ibarra Corretgé)

* doc: add API documentation (Saúl Ibarra Corretgé)

* test: don't close connection on write error (Trevor Norris)

* windows: further simplify the code for timers (Saúl Ibarra Corretgé)

* gyp: remove UNLIMITED_SELECT from dependent define (Fedor Indutny)

* darwin: allocate enough space for select() hack (Fedor Indutny)

* unix, windows: don't allow a NULL callback on timers (Saúl Ibarra Corretgé)

* windows: simplify code in uv_timer_again (Saúl Ibarra Corretgé)

* test: use less requests on tcp-write-queue-order (Saúl Ibarra Corretgé)

* unix: stop child process watcher after last one exits (Saúl Ibarra Corretgé)

* unix: simplify how process handle queue is managed (Saúl Ibarra Corretgé)

* windows: remove duplicated field (mattn)

* core: add a reserved field to uv_handle_t and uv_req_t (Saúl Ibarra Corretgé)

* windows: fix buffer leak after failed udp send (Bert Belder)

* windows: make sure sockets and handles are reset on close (Saúl Ibarra Corretgé)

* unix, windows: add uv_fileno (Saúl Ibarra Corretgé)

* build: use same CFLAGS in autotools build as in gyp (Saúl Ibarra Corretgé)

* build: remove unneeded define in uv.gyp (Saúl Ibarra Corretgé)

* test: fix watcher_cross_stop on Windows (Saúl Ibarra Corretgé)

* unix, windows: move includes for EAI constants (Saúl Ibarra Corretgé)

* unix: fix exposing EAI_* glibc-isms (Saúl Ibarra Corretgé)

* unix: fix tcp write after bad connect freezing (Andrius Bentkus)


2014.08.20, Version 0.11.29 (Unstable), 35451fed830807095bbae8ef981af004a4b9259e

Changes since version 0.11.28:

* windows: make uv_read_stop immediately stop reading (Jameson Nash)

* windows: fix uv__getaddrinfo_translate_error (Alexis Campailla)

* netbsd: fix build (Saúl Ibarra Corretgé)

* unix, windows: add uv_recv_buffer_size and uv_send_buffer_size (Andrius
  Bentkus)

* windows: add support for UNC paths on uv_spawn (Paul Goldsmith)

* windows: replace use of inet_addr with uv_inet_pton (Saúl Ibarra Corretgé)

* unix: replace some asserts with returning errors (Andrius Bentkus)

* windows: use OpenBSD implementation for uv_fs_mkdtemp (Pavel Platto)

* windows: fix GetNameInfoW error handling (Alexis Campailla)

* fs: introduce uv_readdir_next() and report types (Fedor Indutny)

* fs: extend reported types in uv_fs_readdir_next (Saúl Ibarra Corretgé)

* unix: read on stream even when UV__POLLHUP set. (Julien Gilli)


2014.08.08, Version 0.11.28 (Unstable), fc9e2a0bc487b299c0cd3b2c9a23aeb554b5d8d1

Changes since version 0.11.27:

* unix, windows: const-ify handle in uv_udp_getsockname (Rasmus Pedersen)

* windows: use UV_ECANCELED for aborted TCP writes (Saúl Ibarra Corretgé)

* windows: add more required environment variables (Jameson Nash)

* windows: sort environment variables before calling CreateProcess (Jameson
  Nash)

* unix, windows: move uv_loop_close out of assert (John Firebaugh)

* windows: fix buffer overflow on uv__getnameinfo_work() (lilohuang)

* windows: add uv_backend_timeout (Jameson Nash)

* test: disable tcp_close_accept on Windows (Saúl Ibarra Corretgé)

* windows: read the PATH env var of the child (Alex Crichton)

* include: avoid using C++ 'template' reserved word (Iñaki Baz Castillo)

* include: fix version number (Saúl Ibarra Corretgé)


2014.07.32, Version 0.11.27 (Unstable), ffe24f955032d060968ea0289af365006afed55e

Changes since version 0.11.26:

* unix, windows: use the same threadpool implementation (Saúl Ibarra Corretgé)

* unix: use struct sockaddr_storage for target UDP addr (Saúl Ibarra Corretgé)

* doc: add documentation to uv_udp_start_recv (Andrius Bentkus)

* common: use common uv__count_bufs code (Andrius Bentkus)

* unix, win: add send_queue_size and send_queue_count to uv_udp_t (Andrius
  Bentkus)

* unix, win: add uv_udp_try_send (Andrius Bentkus)

* unix: return UV_EAGAIN if uv_try_write cannot write any data (Saúl Ibarra
  Corretgé)

* windows: fix compatibility with cygwin pipes (Jameson Nash)

* windows: count queued bytes even if request completed immediately (Saúl
  Ibarra Corretgé)

* windows: disable CRT debug handler on MinGW32 (Saúl Ibarra Corretgé)

* windows: map ERROR_INVALID_DRIVE to UV_ENOENT (Saúl Ibarra Corretgé)

* unix: try to write immediately in uv_udp_send (Saúl Ibarra Corretgé)

* unix: remove incorrect assert (Saúl Ibarra Corretgé)

* openbsd: avoid requiring privileges for uv_resident_set_memory (Aaron Bieber)

* unix: guarantee write queue cb execution order in streams (Andrius Bentkus)

* img: add logo files (Saúl Ibarra Corretgé)

* aix: improve AIX compatibility (Andrew Low)

* windows: return bind error immediately when implicitly binding (Saúl Ibarra
  Corretgé)

* windows: don't use atexit for cleaning up the threadpool (Saúl Ibarra
  Corretgé)

* windows: destroy work queue elements when colsing a loop (Saúl Ibarra
  Corretgé)

* unix, windows: add uv_fs_mkdtemp (Pavel Platto)

* build: handle platforms without multiprocessing.synchronize (Saúl Ibarra
  Corretgé)

* windows: change GENERIC_ALL to GENERIC_WRITE in fs__create_junction (Tony
  Kelman)

* windows: relay TCP bind errors via ipc (Alexis Campailla)


2014.07.32, Version 0.10.28 (Stable), 9c14b616f5fb84bfd7d45707bab4bbb85894443e

Changes since version 0.10.27:

* windows: fix handling closed socket while poll handle is closing (Saúl Ibarra
  Corretgé)

* unix: return system error on EAI_SYSTEM (Saúl Ibarra Corretgé)

* unix: fix bogus structure field name (Saúl Ibarra Corretgé)

* darwin: invoke `mach_timebase_info` only once (Fedor Indutny)


2014.06.28, Version 0.11.26 (Unstable), 115281a1058c4034d5c5ccedacb667fe3f6327ea

Changes since version 0.11.25:

* windows: add VT100 codes ?25l and ?25h (JD Ballard)

* windows: add invert ANSI (7 / 27) emulation (JD Ballard)

* unix: fix handling error on UDP socket creation (Saúl Ibarra Corretgé)

* unix, windows: getnameinfo implementation (Rasmus Pedersen)

* heap: fix `heap_remove()` (Fedor Indutny)

* unix, windows: fix parsing scoped IPv6 addresses (Saúl Ibarra Corretgé)

* windows: fix handling closed socket while poll handle is closing (Saúl Ibarra
  Corretgé)

* thread: barrier functions (Ben Noordhuis)

* windows: fix PYTHON environment variable usage (Jay Satiro)

* unix, windows: return system error on EAI_SYSTEM (Saúl Ibarra Corretgé)

* windows: fix handling closed socket while poll handle is closing (Saúl Ibarra
  Corretgé)

* unix: don't run i/o callbacks after prepare callbacks (Saúl Ibarra Corretgé)

* windows: add tty unicode support for input (Peter Atashian)

* header: introduce `uv_loop_size()` (Andrius Bentkus)

* darwin: invoke `mach_timebase_info` only once (Fedor Indutny)


2014.05.02, Version 0.11.25 (Unstable), 2acd544cff7142e06aa3b09ec64b4a33dd9ab996

Changes since version 0.11.24:

* osx: pass const handle pointer to uv___stream_fd (Chernyshev Viacheslav)

* unix, windows: pass const handle ptr to uv_tcp_get*name (Chernyshev
  Viacheslav)

* common: pass const sockaddr ptr to uv_ip*_name (Chernyshev Viacheslav)

* unix, windows: validate flags on uv_udp|tcp_bind (Saúl Ibarra Corretgé)

* unix: handle case when addr is not initialized after recvmsg (Saúl Ibarra
  Corretgé)

* unix, windows: uv_now constness (Rasmus Pedersen)


2014.04.15, Version 0.11.24 (Unstable), ed948c29f6e8c290f79325a6f0bc9ef35bcde644

Changes since version 0.11.23:

* linux: reduce file descriptor count of async pipe (Ben Noordhuis)

* sunos: support IPv6 qualified link-local addresses (Saúl Ibarra Corretgé)

* windows: fix opening of read-only stdin pipes (Alexis Campailla)

* windows: Fix an infinite loop in uv_spawn (Alex Crichton)

* windows: fix console signal handler refcount (李港平)

* inet: allow scopeid in uv_inet_pton (Fedor Indutny)


2014.04.07, Version 0.11.23 (Unstable), e54de537efcacd593f36fcaaf8b4cb9e64313275

Changes since version 0.11.22:

* fs: avoid using readv/writev where possible (Fedor Indutny)

* mingw: fix build with autotools (Saúl Ibarra Corretgé)

* bsd: support IPv6 qualified link-local addresses (Saúl Ibarra Corretgé)

* unix: add UV_HANDLE_IPV6 flag to tcp and udp handles (Saúl Ibarra Corretgé)

* unix, windows: do not set SO_REUSEADDR by default on udp (Saúl Ibarra
  Corretgé)

* windows: fix check in uv_tty_endgame() (Maks Naumov)

* unix, windows: add IPv6 support for uv_udp_multicast_interface (Saúl Ibarra
  Corretgé)

* unix: fallback to blocking writes if reopening a tty fails (Saúl Ibarra
  Corretgé)

* unix: fix handling uv__open_cloexec failure (Saúl Ibarra Corretgé)

* unix, windows: add IPv6 support to uv_udp_set_membership (Saúl Ibarra
  Corretgé)

* unix, windows: removed unused status parameter (Saúl Ibarra Corretgé)

* android: add support of ifaddrs in android (Javier Hernández)

* build: fix SunOS and AIX build with autotools (Saúl Ibarra Corretgé)

* build: freebsd link with libelf if dtrace enabled (Saúl Ibarra Corretgé)

* stream: do not leak `alloc_cb` buffers on error (Fedor Indutny)

* unix: fix setting written size on uv_wd (Saúl Ibarra Corretgé)


2014.03.11, Version 0.11.22 (Unstable), cd0c19b1d3c56acf0ade7687006e12e75fbda36d

Changes since version 0.11.21:

* unix, windows: map ERANGE errno (Saúl Ibarra Corretgé)

* unix, windows: make uv_cwd be consistent with uv_exepath (Saúl Ibarra
  Corretgé)

* process: remove debug perror() prints (Fedor Indutny)

* windows: fall back for volume info query (Isaiah Norton)

* pipe: allow queueing pending handles (Fedor Indutny)

* windows: fix winsock status codes for address errors (Raul Martins)

* windows: Remove unused variable from uv__pipe_insert_pending_socket (David
  Capello)

* unix: workaround broken pthread_sigmask on Android (Paul Tan)

* error: add ENXIO for O_NONBLOCK FIFO open() (Fedor Indutny)

* freebsd: use accept4, introduced in version 10 (Saúl Ibarra Corretgé)

* windows: fix warnings of MinGW -Wall -O3 (StarWing)

* openbsd, osx: fix compilation warning on scandir (Saúl Ibarra Corretgé)

* linux: always deregister closing fds from epoll (Geoffry Song)

* unix: reopen tty as /dev/tty (Saúl Ibarra Corretgé)

* kqueue: invalidate fd in uv_fs_event_t (Fedor Indutny)


2014.02.28, Version 0.11.21 (Unstable), 3ef958158ae1019e027ebaa93114160099db5206

Changes since version 0.11.20:

* unix: fix uv_fs_write when using an empty buffer (Saúl Ibarra Corretgé)

* unix, windows: add assertion in uv_loop_delete (Saúl Ibarra Corretgé)


2014.02.27, Version 0.11.20 (Unstable), 88355e081b51c69ee1e2b6b0015a4e3d38bd0579

Changes since version 0.11.19:

* stream: start thread after assignments (Oguz Bastemur)

* fs: `uv__cloexec()` opened fd (Fedor Indutny)

* gyp: qualify `library` variable (Fedor Indutny)

* unix, win: add uv_udp_set_multicast_interface() (Austin Foxley)

* unix: fix uv_tcp_nodelay return value in case of error (Saúl Ibarra Corretgé)

* unix: call setgoups before calling setuid/setgid (Saúl Ibarra Corretgé)

* include: mark close_cb field as private (Saúl Ibarra Corretgé)

* unix, windows: map EFBIG errno (Saúl Ibarra Corretgé)

* unix: correct error when calling uv_shutdown twice (Keno Fischer)

* windows: fix building on MinGW (Alex Crichton)

* windows: always initialize uv_process_t (Alex Crichton)

* include: expose libuv version in header files (Saúl Ibarra Corretgé)

* fs: vectored IO API for filesystem read/write (Benjamin Saunders)

* windows: freeze in uv_tcp_endgame (Alexis Campailla)

* sunos: handle rearm errors (Fedor Indutny)

* unix: use a heap for timers (Ben Noordhuis)

* linux: always deregister closing fds from epoll (Geoffry Song)

* linux: include grp.h for setgroups() (William Light)

* unix, windows: add uv_loop_init and uv_loop_close (Saúl Ibarra Corretgé)

* unix, windows: add uv_getrusage() function (Oleg Efimov)

* win: minor error handle fix to uv_pipe_write_impl (Rasmus Pedersen)

* heap: fix node removal (Keno Fischer)

* win: fix C99/C++ comment (Rasmus Pedersen)

* fs: vectored IO API for filesystem read/write (Benjamin Saunders)

* unix, windows: add uv_pipe_getsockname (Saúl Ibarra Corretgé)

* unix, windows: map ENOPROTOOPT errno (Saúl Ibarra Corretgé)

* errno: add ETXTBSY (Fedor Indutny)

* fsevent: rename filename field to path (Saúl Ibarra Corretgé)

* unix, windows: add uv_fs_event_getpath (Saúl Ibarra Corretgé)

* unix, windows: add uv_fs_poll_getpath (Saúl Ibarra Corretgé)

* unix, windows: map ERANGE errno (Saúl Ibarra Corretgé)

* unix, windows: set required size on UV_ENOBUFS (Saúl Ibarra Corretgé)

* unix, windows: clarify what uv_stream_set_blocking does (Saúl Ibarra
  Corretgé)

* fs: use preadv on Linux if available (Brian White)


2014.01.30, Version 0.11.19 (Unstable), 336a1825309744f920230ec3e427e78571772347

Changes since version 0.11.18:

* linux: move sscanf() out of the assert() (Trevor Norris)

* linux: fix C99/C++ comment (Fedor Indutny)


2014.05.02, Version 0.10.27 (Stable), 6e24ce23b1e7576059f85a608eca13b766458a01

Changes since version 0.10.26:

* windows: fix console signal handler refcount (Saúl Ibarra Corretgé)

* win: always leave crit section in get_proc_title (Fedor Indutny)


2014.04.07, Version 0.10.26 (Stable), d864907611c25ec986c5e77d4d6d6dee88f26926

Changes since version 0.10.25:

* process: don't close stdio fds during spawn (Tonis Tiigi)

* build, windows: do not fail on Windows SDK Prompt (Marc Schlaich)

* build, windows: fix x64 configuration issue (Marc Schlaich)

* win: fix buffer leak on error in pipe.c (Fedor Indutny)

* kqueue: invalidate fd in uv_fs_event_t (Fedor Indutny)

* linux: always deregister closing fds from epoll (Geoffry Song)

* error: add ENXIO for O_NONBLOCK FIFO open() (Fedor Indutny)


2014.02.19, Version 0.10.25 (Stable), d778dc588507588b12b9f9d2905078db542ed751

Changes since version 0.10.24:

* stream: start thread after assignments (Oguz Bastemur)

* unix: correct error when calling uv_shutdown twice (Saúl Ibarra Corretgé)

2014.01.30, Version 0.10.24 (Stable), aecd296b6bce9b40f06a61c5c94e43d45ac7308a

Changes since version 0.10.23:

* linux: move sscanf() out of the assert() (Trevor Norris)

* linux: fix C99/C++ comment (Fedor Indutny)


2014.01.23, Version 0.11.18 (Unstable), d47962e9d93d4a55a9984623feaf546406c9cdbb

Changes since version 0.11.17:

* osx: Fix a possible segfault in uv__io_poll (Alex Crichton)

* windows: improved handling of invalid FDs (Alexis Campailla)

* doc: adding ARCHS flag to OS X build command (Nathan Sweet)

* tcp: reveal bind-time errors before listen (Alexis Campailla)

* tcp: uv_tcp_dualstack() (Fedor Indutny)

* linux: relax assumption on /proc/stat parsing (Luca Bruno)

* openbsd: fix obvious bug in uv_cpu_info (Fedor Indutny)

* process: close stdio after dup2'ing it (Fedor Indutny)

* linux: move sscanf() out of the assert() (Trevor Norris)


2014.01.23, Version 0.10.23 (Stable), dbd218e699fec8be311d85e4788be9e28ae884f8

Changes since version 0.10.22:

* linux: relax assumption on /proc/stat parsing (Luca Bruno)

* openbsd: fix obvious bug in uv_cpu_info (Fedor Indutny)

* process: close stdio after dup2'ing it (Fedor Indutny)


2014.01.08, Version 0.10.22 (Stable), f526c90eeff271d9323a9107b9a64a4671fd3103

Changes since version 0.10.21:

* windows: avoid assertion failure when pipe server is closed (Bert Belder)


2013.12.32, Version 0.11.17 (Unstable), 589c224d4c2e79fec65db01d361948f1e4976858

Changes since version 0.11.16:

* stream: allow multiple buffers for uv_try_write (Fedor Indutny)

* unix: fix a possible memory leak in uv_fs_readdir (Alex Crichton)

* unix, windows: add uv_loop_alive() function (Sam Roberts)

* windows: avoid assertion failure when pipe server is closed (Bert Belder)

* osx: Fix a possible segfault in uv__io_poll (Alex Crichton)

* stream: fix uv__stream_osx_select (Fedor Indutny)


2013.12.14, Version 0.11.16 (Unstable), ae0ed8c49d0d313c935c22077511148b6e8408a4

Changes since version 0.11.15:

* fsevents: remove kFSEventStreamCreateFlagNoDefer polyfill (ci-innoq)

* libuv: add more getaddrinfo errors (Steven Kabbes)

* unix: fix accept() EMFILE error handling (Ben Noordhuis)

* linux: fix up SO_REUSEPORT back-port (Ben Noordhuis)

* fsevents: fix subfolder check (Fedor Indutny)

* fsevents: fix invalid memory access (huxingyi)

* windows/timer: fix uv_hrtime discontinuity (Bert Belder)

* unix: fix various memory leaks and undef behavior (Fedor Indutny)

* unix, windows: always update loop time (Saúl Ibarra Corretgé)

* windows: translate system errors in uv_spawn (Alexis Campailla)

* windows: uv_spawn code refactor (Alexis Campailla)

* unix, windows: detect errors in uv_ip4/6_addr (Yorkie)

* stream: introduce uv_try_write(...) (Fedor Indutny)


2013.12.13, Version 0.10.20 (Stable), 04141464dd0fba90ace9aa6f7003ce139b888a40

Changes since version 0.10.19:

* linux: fix up SO_REUSEPORT back-port (Ben Noordhuis)

* fs-event: fix invalid memory access (huxingyi)


2013.11.21, Version 0.11.15 (Unstable), bfe645ed7e99ca5670d9279ad472b604c129d2e5

Changes since version 0.11.14:

* fsevents: report errors to user (Fedor Indutny)

* include: UV_FS_EVENT_RECURSIVE is a flag (Fedor Indutny)

* linux: use CLOCK_MONOTONIC_COARSE if available (Ben Noordhuis)

* build: make systemtap probes work with gyp build (Ben Noordhuis)

* unix: update events from pevents between polls (Fedor Indutny)

* fsevents: support japaneese characters in path (Chris Bank)

* linux: don't turn on SO_REUSEPORT socket option (Ben Noordhuis)

* queue: strengthen type checks (Ben Noordhuis)

* include: remove uv_strlcat() and uv_strlcpy() (Ben Noordhuis)

* build: fix windows smp build with gyp (Geert Jansen)

* unix: return exec errors from uv_spawn, not async (Alex Crichton)

* fsevents: use native character encoding file paths (Ben Noordhuis)

* linux: handle EPOLLHUP without EPOLLIN/EPOLLOUT (Ben Noordhuis)

* windows: use _snwprintf(), not swprintf() (Ben Noordhuis)

* fsevents: use FlagNoDefer for FSEventStreamCreate (Fedor Indutny)

* unix: fix reopened fd bug (Fedor Indutny)

* core: fix fake watcher list and count preservation (Fedor Indutny)

* unix: set close-on-exec flag on received fds (Ben Noordhuis)

* netbsd, openbsd: enable futimes() wrapper (Ben Noordhuis)

* unix: nicer error message when kqueue() fails (Ben Noordhuis)

* samples: add socks5 proxy sample application (Ben Noordhuis)


2013.11.13, Version 0.10.19 (Stable), 33959f7524090b8d2c6c41e2400ca77e31755059

Changes since version 0.10.18:

* darwin: avoid calling GetCurrentProcess (Fedor Indutny)

* unix: update events from pevents between polls (Fedor Indutny)

* fsevents: support japaneese characters in path (Chris Bank)

* linux: don't turn on SO_REUSEPORT socket option (Ben Noordhuis)

* build: fix windows smp build with gyp (Geert Jansen)

* linux: handle EPOLLHUP without EPOLLIN/EPOLLOUT (Ben Noordhuis)

* unix: fix reopened fd bug (Fedor Indutny)

* core: fix fake watcher list and count preservation (Fedor Indutny)


2013.10.30, Version 0.11.14 (Unstable), d7a6482f45c1b4eb4a853dbe1a9ce8090a35633a

Changes since version 0.11.13:

* darwin: create fsevents thread on demand (Ben Noordhuis)

* fsevents: FSEvents is most likely not thread-safe (Fedor Indutny)

* fsevents: use shared FSEventStream (Fedor Indutny)

* windows: make uv_fs_chmod() report errors correctly (Bert Belder)

* windows: make uv_shutdown() for write-only pipes work (Bert Belder)

* windows/fs: wrap multi-statement macros in do..while block (Bert Belder)

* windows/fs: make uv_fs_open() report EINVAL correctly (Bert Belder)

* windows/fs: handle _open_osfhandle() failure correctly (Bert Belder)

* windows/fs: wrap multi-statement macros in do..while block (Bert Belder)

* windows/fs: make uv_fs_open() report EINVAL correctly (Bert Belder)

* windows/fs: handle _open_osfhandle() failure correctly (Bert Belder)

* build: clarify instructions for Windows (Brian Kaisner)

* build: remove GCC_WARN_ABOUT_MISSING_NEWLINE (Ben Noordhuis)

* darwin: fix 10.6 build error in fsevents.c (Ben Noordhuis)

* windows: run close callbacks after polling for i/o (Saúl Ibarra Corretgé)

* include: clarify uv_tcp_bind() behavior (Ben Noordhuis)

* include: clean up includes in uv.h (Ben Noordhuis)

* include: remove UV_IO_PRIVATE_FIELDS macro (Ben Noordhuis)

* include: fix typo in comment in uv.h (Ben Noordhuis)

* include: update uv_is_active() documentation (Ben Noordhuis)

* include: make uv_process_options_t.cwd const (Ben Noordhuis)

* unix: wrap long lines at 80 columns (Ben Noordhuis)

* unix, windows: make uv_is_*() always return 0 or 1 (Ben Noordhuis)

* bench: measure total/init/dispatch/cleanup times (Ben Noordhuis)

* build: use -pthread on sunos (Timothy J. Fontaine)

* windows: remove duplicate check in stream.c (Ben Noordhuis)

* unix: sanity-check fds before closing (Ben Noordhuis)

* unix: remove uv__pipe_accept() (Ben Noordhuis)

* unix: fix uv_spawn() NULL pointer deref on ENOMEM (Ben Noordhuis)

* unix: don't close inherited fds on uv_spawn() fail (Ben Noordhuis)

* unix: revert recent FSEvent changes (Ben Noordhuis)

* fsevents: fix clever rescheduling (Fedor Indutny)

* linux: ignore fractional time in uv_uptime() (Ben Noordhuis)

* unix: fix SIGCHLD waitpid() race in process.c (Ben Noordhuis)

* unix, windows: add uv_fs_event_start/stop functions (Saúl Ibarra Corretgé)

* unix: fix non-synchronized access in signal.c (Ben Noordhuis)

* unix: add atomic-ops.h (Ben Noordhuis)

* unix: add spinlock.h (Ben Noordhuis)

* unix: clean up uv_tty_set_mode() a little (Ben Noordhuis)

* unix: make uv_tty_reset_mode() async signal-safe (Ben Noordhuis)

* include: add E2BIG status code mapping (Ben Noordhuis)

* windows: fix duplicate case build error (Ben Noordhuis)

* windows: remove unneeded check (Saúl Ibarra Corretgé)

* include: document pipe path truncation behavior (Ben Noordhuis)

* fsevents: increase stack size for OSX 10.9 (Fedor Indutny)

* windows: _snprintf expected wrong parameter type in string (Maks Naumov)

* windows: "else" keyword is missing (Maks Naumov)

* windows: incorrect check for SOCKET_ERROR (Maks Naumov)

* windows: add stdlib.h to satisfy reference to abort (Sean Farrell)

* build: fix check target for mingw (Sean Farrell)

* unix: move uv_shutdown() assertion (Keno Fischer)

* darwin: avoid calling GetCurrentProcess (Fedor Indutny)


2013.10.19, Version 0.10.18 (Stable), 9ec52963b585e822e87bdc5de28d6143aff0d2e5

Changes since version 0.10.17:

* unix: fix uv_spawn() NULL pointer deref on ENOMEM (Ben Noordhuis)

* unix: don't close inherited fds on uv_spawn() fail (Ben Noordhuis)

* unix: revert recent FSEvent changes (Ben Noordhuis)

* unix: fix non-synchronized access in signal.c (Ben Noordhuis)


2013.09.25, Version 0.10.17 (Stable), 9670e0a93540c2f0d86c84a375f2303383c11e7e

Changes since version 0.10.16:

* build: remove GCC_WARN_ABOUT_MISSING_NEWLINE (Ben Noordhuis)

* darwin: fix 10.6 build error in fsevents.c (Ben Noordhuis)


2013.09.06, Version 0.10.16 (Stable), 2bce230d81f4853a23662cbeb26fe98010b1084b

Changes since version 0.10.15:

* windows: make uv_shutdown() for write-only pipes work (Bert Belder)

* windows: make uv_fs_open() report EINVAL when invalid arguments are passed
  (Bert Belder)

* windows: make uv_fs_open() report _open_osfhandle() failure correctly (Bert
  Belder)

* windows: make uv_fs_chmod() report errors correctly (Bert Belder)

* windows: wrap multi-statement macros in do..while block (Bert Belder)


2013.09.05, Version 0.11.13 (Unstable), f5b6db6c1d7f93d28281207fd47c3841c9a9792e

Changes since version 0.11.12:

* unix: define _GNU_SOURCE, exposes glibc-isms (Ben Noordhuis)

* windows: check for nonconforming swprintf arguments (Brent Cook)

* build: include internal headers in source list (Brent Cook)

* include: merge uv_tcp_bind and uv_tcp_bind6 (Ben Noordhuis)

* include: merge uv_tcp_connect and uv_tcp_connect6 (Ben Noordhuis)

* include: merge uv_udp_bind and uv_udp_bind6 (Ben Noordhuis)

* include: merge uv_udp_send and uv_udp_send6 (Ben Noordhuis)


2013.09.03, Version 0.11.12 (Unstable), 82d01d5f6780d178f5176a01425ec297583c0811

Changes since version 0.11.11:

* test: fix epoll_wait() usage in test-embed.c (Ben Noordhuis)

* include: uv_alloc_cb now takes uv_buf_t* (Ben Noordhuis)

* include: uv_read{2}_cb now takes const uv_buf_t* (Ben Noordhuis)

* include: uv_ip[46]_addr now takes sockaddr_in* (Ben Noordhuis)

* include: uv_tcp_bind{6} now takes sockaddr_in* (Ben Noordhuis)

* include: uv_tcp_connect{6} now takes sockaddr_in* (Ben Noordhuis)

* include: uv_udp_recv_cb now takes const uv_buf_t* (Ben Noordhuis)

* include: uv_udp_bind{6} now takes sockaddr_in* (Ben Noordhuis)

* include: uv_udp_send{6} now takes sockaddr_in* (Ben Noordhuis)

* include: uv_spawn takes const uv_process_options_t* (Ben Noordhuis)

* include: make uv_write{2} const correct (Ben Noordhuis)

* windows: fix flags assignment in uv_fs_readdir() (Ben Noordhuis)

* windows: fix stray comments (Ben Noordhuis)

* windows: remove unused is_path_dir() function (Ben Noordhuis)


2013.08.30, Version 0.11.11 (Unstable), ba876d53539ed0427c52039012419cd9374c6f0d

Changes since version 0.11.10:

* unix, windows: add thread-local storage API (Ben Noordhuis)

* linux: don't turn on SO_REUSEPORT socket option (Ben Noordhuis)

* darwin: fix 10.6 build error in fsevents.c (Ben Noordhuis)

* windows: make uv_shutdown() for write-only pipes work (Bert Belder)

* include: update uv_udp_open() / uv_udp_bind() docs (Ben Noordhuis)

* unix: req queue must be empty when destroying loop (Ben Noordhuis)

* unix: move loop functions from core.c to loop.c (Ben Noordhuis)

* darwin: remove CoreFoundation dependency (Ben Noordhuis)

* windows: make autotools build system work with mingw (Keno Fischer)

* windows: fix mingw build (Alex Crichton)

* windows: tweak Makefile.mingw for easier usage (Alex Crichton)

* build: remove _GNU_SOURCE macro definition (Ben Noordhuis)


2013.08.25, Version 0.11.10 (Unstable), 742dadcb7154cc7bb89c0c228a223b767a36cf0d

* windows: Re-implement uv_fs_stat. The st_ctime field now contains the change
  time, not the creation time, like on unix systems. st_dev, st_ino, st_blocks
  and st_blksize are now also filled out. (Bert Belder)

* linux: fix setsockopt(SO_REUSEPORT) error handling (Ben Noordhuis)

* windows: report uv_process_t exit code correctly (Bert Belder)

* windows: make uv_fs_chmod() report errors correctly (Bert Belder)

* windows: make some more NT apis available for libuv's internal use (Bert
  Belder)

* windows: squelch some compiler warnings (Bert Belder)


2013.08.24, Version 0.11.9 (Unstable), a2d29b5b068cbac93dc16138fb30a74e2669daad

Changes since version 0.11.8:

* fsevents: share FSEventStream between multiple FS watchers, which removes a
  limit on the maximum number of file watchers that can be created on OS X.
  (Fedor Indutny)

* process: the `exit_status` parameter for a uv_process_t's exit callback now
  is an int64_t, and no longer an int. (Bert Belder)

* process: make uv_spawn() return some types of errors immediately on windows,
  instead of passing the error code the exit callback. This brings it on
  par with libuv's behavior on unix. (Bert Belder)


2013.08.24, Version 0.10.15 (Stable), 221078a8fdd9b853c6b557b3d9a5dd744b4fdd6b

Changes since version 0.10.14:

* fsevents: create FSEvents thread on demand (Ben Noordhuis)

* fsevents: use a single thread for interacting with FSEvents, because it's not
  thread-safe. (Fedor Indutny)

* fsevents: share FSEventStream between multiple FS watchers, which removes a
  limit on the maximum number of file watchers that can be created on OS X.
  (Fedor Indutny)


2013.08.22, Version 0.11.8 (Unstable), a5260462db80ab0deab6b9e6a8991dd8f5a9a2f8

Changes since version 0.11.7:

* unix: fix missing return value warning in stream.c (Ben Noordhuis)

* build: serial-tests was added in automake v1.12 (Ben Noordhuis)

* windows: fix uninitialized local variable warning (Ben Noordhuis)

* windows: fix missing return value warning (Ben Noordhuis)

* build: fix string comparisons in autogen.sh (Ben Noordhuis)

* windows: move INLINE macro, remove UNUSED (Ben Noordhuis)

* unix: clean up __attribute__((quux)) usage (Ben Noordhuis)

* sunos: remove futimes() macro (Ben Noordhuis)

* unix: fix uv__signal_unlock() prototype (Ben Noordhuis)

* unix, windows: allow NULL async callback (Ben Noordhuis)

* build: apply dtrace -G to all object files (Timothy J. Fontaine)

* darwin: fix indentation in uv__hrtime() (Ben Noordhuis)

* darwin: create fsevents thread on demand (Ben Noordhuis)

* darwin: reduce fsevents thread stack size (Ben Noordhuis)

* darwin: call pthread_setname_np() if available (Ben Noordhuis)

* build: fix automake serial-tests check again (Ben Noordhuis)

* unix: retry waitpid() on EINTR (Ben Noordhuis)

* darwin: fix ios build error (Ben Noordhuis)

* darwin: fix ios compiler warning (Ben Noordhuis)

* test: simplify test-ip6-addr.c (Ben Noordhuis)

* unix, windows: fix ipv6 link-local address parsing (Ben Noordhuis)

* fsevents: FSEvents is most likely not thread-safe (Fedor Indutny)

* windows: omit stdint.h, fix msvc 2008 build error (Ben Noordhuis)


2013.08.22, Version 0.10.14 (Stable), 15d64132151c18b26346afa892444b95e2addad0

Changes since version 0.10.13:

* unix: retry waitpid() on EINTR (Ben Noordhuis)


2013.08.07, Version 0.11.7 (Unstable), 3cad361f8776f70941b39d65bd9426bcb1aa817b

Changes since version 0.11.6:

* unix, windows: fix uv_fs_chown() function prototype (Ben Noordhuis)

* unix, windows: remove unused variables (Brian White)

* test: fix signed/unsigned comparison warnings (Ben Noordhuis)

* build: dtrace shouldn't break out of tree builds (Timothy J. Fontaine)

* unix, windows: don't read/recv if buf.len==0 (Ben Noordhuis)

* build: add mingw makefile (Ben Noordhuis)

* unix, windows: add MAC to uv_interface_addresses() (Brian White)

* build: enable AM_INIT_AUTOMAKE([subdir-objects]) (Ben Noordhuis)

* unix, windows: make buf arg to uv_fs_write const (Ben Noordhuis)

* sunos: fix build breakage introduced in e3a657c (Ben Noordhuis)

* aix: fix build breakage introduced in 3ee4d3f (Ben Noordhuis)

* windows: fix mingw32 build, define JOB_OBJECT_XXX (Yasuhiro Matsumoto)

* windows: fix mingw32 build, include limits.h (Yasuhiro Matsumoto)

* test: replace sprintf() with snprintf() (Ben Noordhuis)

* test: replace strcpy() with strncpy() (Ben Noordhuis)

* openbsd: fix uv_ip6_addr() unused variable warnings (Ben Noordhuis)

* openbsd: fix dlerror() const correctness warning (Ben Noordhuis)

* openbsd: fix uv_fs_sendfile() unused variable warnings (Ben Noordhuis)

* build: disable parallel automake tests (Ben Noordhuis)

* test: add windows-only snprintf() function (Ben Noordhuis)

* build: add automake serial-tests version check (Ben Noordhuis)


2013.07.26, Version 0.10.13 (Stable), 381312e1fe6fecbabc943ccd56f0e7d114b3d064

Changes since version 0.10.12:

* unix, windows: fix uv_fs_chown() function prototype (Ben Noordhuis)


2013.07.21, Version 0.11.6 (Unstable), 6645b93273e0553d23823c576573b82b129bf28c

Changes since version 0.11.5:

* test: open stdout fd in write-only mode (Ben Noordhuis)

* windows: uv_spawn shouldn't reject reparse points (Bert Belder)

* windows: use WSAGetLastError(), not errno (Ben Noordhuis)

* build: darwin: disable -fstrict-aliasing warnings (Ben Noordhuis)

* test: fix signed/unsigned compiler warning (Ben Noordhuis)

* test: add 'start timer from check handle' test (Ben Noordhuis)

* build: `all` now builds static and dynamic lib (Ben Noordhuis)

* unix, windows: add extra fields to uv_stat_t (Saúl Ibarra Corretgé)

* build: add install target to the makefile (Navaneeth Kedaram Nambiathan)

* build: switch to autotools (Ben Noordhuis)

* build: use AM_PROG_AR conditionally (Ben Noordhuis)

* test: fix fs_fstat test on sunos (Ben Noordhuis)

* test: fix fs_chown when running as root (Ben Noordhuis)

* test: fix spawn_setgid_fails and spawn_setuid_fails (Ben Noordhuis)

* build: use AM_SILENT_RULES conditionally (Ben Noordhuis)

* build: add DTrace detection for autotools (Timothy J. Fontaine)

* linux,darwin,win: link-local IPv6 addresses (Miroslav Bajtoš)

* unix: fix build when !defined(PTHREAD_MUTEX_ERRORCHECK) (Ben Noordhuis)

* unix, windows: return error codes directly (Ben Noordhuis)


2013.07.10, Version 0.10.12 (Stable), 58a46221bba726746887a661a9f36fe9ff204209

Changes since version 0.10.11:

* linux: add support for MIPS (Andrei Sedoi)

* windows: uv_spawn shouldn't reject reparse points (Bert Belder)

* windows: use WSAGetLastError(), not errno (Ben Noordhuis)

* build: darwin: disable -fstrict-aliasing warnings (Ben Noordhuis)

* build: `all` now builds static and dynamic lib (Ben Noordhuis)

* unix: fix build when !defined(PTHREAD_MUTEX_ERRORCHECK) (Ben Noordhuis)


2013.06.27, Version 0.11.5 (Unstable), e3c63ff1627a14e96f54c1c62b0d68b446d8425b

Changes since version 0.11.4:

* build: remove CSTDFLAG, use only CFLAGS (Ben Noordhuis)

* unix: support for android builds (Linus Mårtensson)

* unix: avoid extra read, short-circuit on POLLHUP (Ben Noordhuis)

* uv: support android libuv standalone build (Linus Mårtensson)

* src: make queue.h c++ compatible (Ben Noordhuis)

* unix: s/ngx-queue.h/queue.h/ in checksparse.sh (Ben Noordhuis)

* unix: unconditionally stop handle on close (Ben Noordhuis)

* freebsd: don't enable dtrace if it's not available (Brian White)

* build: make HAVE_DTRACE=0 should disable dtrace (Timothy J. Fontaine)

* unix: remove overzealous assert (Ben Noordhuis)

* unix: remove unused function uv_fatal_error() (Ben Noordhuis)

* unix, windows: clean up uv_thread_create() (Ben Noordhuis)

* queue: fix pointer truncation on LLP64 platforms (Bert Belder)

* build: set OS=="android" for android builds (Linus Mårtensson)

* windows: don't use uppercase in include filename (Ben Noordhuis)

* stream: add an API to make streams do blocking writes (Henry Rawas)

* windows: use WSAGetLastError(), not errno (Ben Noordhuis)


2013.06.13, Version 0.10.11 (Stable), c3b75406a66a10222a589cb173e8f469e9665c7e

Changes since version 0.10.10:

* unix: unconditionally stop handle on close (Ben Noordhuis)

* freebsd: don't enable dtrace if it's not available (Brian White)

* build: make HAVE_DTRACE=0 should disable dtrace (Timothy J. Fontaine)

* unix: remove overzealous assert (Ben Noordhuis)

* unix: clear UV_STREAM_SHUTTING after shutdown() (Ben Noordhuis)

* unix: fix busy loop, write if POLLERR or POLLHUP (Ben Noordhuis)


2013.06.05, Version 0.10.10 (Stable), 0d95a88bd35fce93863c57a460be613aea34d2c5

Changes since version 0.10.9:

* include: document uv_update_time() and uv_now() (Ben Noordhuis)

* linux: fix cpu model parsing on newer arm kernels (Ben Noordhuis)

* linux: fix a memory leak in uv_cpu_info() error path (Ben Noordhuis)

* linux: don't ignore out-of-memory errors in uv_cpu_info() (Ben Noordhuis)

* unix, windows: move uv_now() to uv-common.c (Ben Noordhuis)

* test: fix a compilation problem in test-osx-select.c that was caused by the
  use of c-style comments (Bert Belder)

* darwin: use uv_fs_sendfile() use the sendfile api correctly (Wynn Wilkes)


2013.05.30, Version 0.11.4 (Unstable), e43e5b3d954a0989db5588aa110e1fe4fe6e0219

Changes since version 0.11.3:

* windows: make uv_spawn not fail when the libuv embedding application is run
  under external job control (Bert Belder)

* darwin: assume CFRunLoopStop() isn't thread-safe, fixing a race condition
  when stopping the 'stdin select hack' thread (Fedor Indutny)

* win: fix UV_EALREADY not being reported correctly to the libuv user in some
  cases (Bert Belder)

* darwin: make the uv__cf_loop_runner and uv__cf_loop_cb functions static (Ben
  Noordhuis)

* darwin: task_info() cannot fail (Ben Noordhuis)

* unix: add error mapping for ENETDOWN (Ben Noordhuis)

* unix: implicitly signal write errors to the libuv user (Ben Noordhuis)

* unix: fix assertion error on signal pipe overflow (Bert Belder)

* unix: turn off POLLOUT after stream connect (Ben Noordhuis)

* unix: fix stream refcounting buglet (Ben Noordhuis)

* unix: remove assert statements that are no longer correct (Ben Noordhuis)

* unix: appease warning about non-standard `inline` (Sean Silva)

* unix: add uv__is_closing() macro (Ben Noordhuis)

* unix: stop stream POLLOUT watcher on write error (Ben Noordhuis)

* include: document uv_update_time() and uv_now() (Ben Noordhuis)

* linux: fix cpu model parsing on newer arm kernels (Ben Noordhuis)

* linux: fix a memory leak in uv_cpu_info() error path (Ben Noordhuis)

* linux: don't ignore out-of-memory errors in uv_cpu_info() (Ben Noordhuis)

* unix, windows: move uv_now() to uv-common.c (Ben Noordhuis)

* test: fix a compilation problem in test-osx-select.c that was caused by the
  use of c-style comments (Bert Belder)

* darwin: use uv_fs_sendfile() use the sendfile api correctly (Wynn Wilkes)

* windows: call idle handles on every loop iteration, something the unix
  implementation already did (Bert Belder)

* test: update the idle-starvation test to verify that idle handles are called
  in every loop iteration (Bert Belder)

* unix, windows: ensure that uv_run() in RUN_ONCE mode calls timers that expire
  after blocking (Ben Noordhuis)


2013.05.29, Version 0.10.9 (Stable), a195f9ace23d92345baf57582678bfc3017e6632

Changes since version 0.10.8:

* unix: fix stream refcounting buglet (Ben Noordhuis)

* unix: remove erroneous asserts (Ben Noordhuis)

* unix: add uv__is_closing() macro (Ben Noordhuis)

* unix: stop stream POLLOUT watcher on write error (Ben Noordhuis)


2013.05.25, Version 0.10.8 (Stable), 0f39be12926fe2d8766a9f025797a473003e6504

Changes since version 0.10.7:

* windows: make uv_spawn not fail under job control (Bert Belder)

* darwin: assume CFRunLoopStop() isn't thread-safe (Fedor Indutny)

* win: fix UV_EALREADY incorrectly set (Bert Belder)

* darwin: make two uv__cf_*() functions static (Ben Noordhuis)

* darwin: task_info() cannot fail (Ben Noordhuis)

* unix: add mapping for ENETDOWN (Ben Noordhuis)

* unix: implicitly signal write errors to libuv user (Ben Noordhuis)

* unix: fix assert on signal pipe overflow (Bert Belder)

* unix: turn off POLLOUT after stream connect (Ben Noordhuis)


2013.05.16, Version 0.11.3 (Unstable), 0a48c05b5988aea84c605751900926fa25443b34

Changes since version 0.11.2:

* unix: clean up uv_accept() (Ben Noordhuis)

* unix: remove errno preserving code (Ben Noordhuis)

* darwin: fix ios build, don't require ApplicationServices (Ben Noordhuis)

* windows: kill child processes when the parent dies (Bert Belder)

* build: set soname in shared library (Ben Noordhuis)

* build: make `make test` link against .a again (Ben Noordhuis)

* build: only set soname on shared object builds (Timothy J. Fontaine)

* build: convert predefined $PLATFORM to lower case (Elliot Saba)

* test: fix process_title failing on linux (Miroslav Bajtoš)

* test, sunos: disable process_title test (Miroslav Bajtoš)

* test: add error logging to tty unit test (Miroslav Bajtoš)


2013.05.15, Version 0.10.7 (Stable), 028baaf0846b686a81e992cb2f2f5a9b8e841fcf

Changes since version 0.10.6:

* windows: kill child processes when the parent dies (Bert Belder)


2013.05.15, Version 0.10.6 (Stable), 11e6613e6260d95c8cf11bf89a2759c24649319a

Changes since version 0.10.5:

* stream: fix osx select hack (Fedor Indutny)

* stream: fix small nit in select hack, add test (Fedor Indutny)

* build: link with libkvm on openbsd (Ben Noordhuis)

* stream: use harder sync restrictions for osx-hack (Fedor Indutny)

* unix: fix EMFILE error handling (Ben Noordhuis)

* darwin: fix unnecessary include headers (Daisuke Murase)

* darwin: rename darwin-getproctitle.m (Ben Noordhuis)

* build: convert predefined $PLATFORM to lower case (Elliot Saba)

* build: set soname in shared library (Ben Noordhuis)

* build: make `make test` link against .a again (Ben Noordhuis)

* darwin: fix ios build, don't require ApplicationServices (Ben Noordhuis)

* build: only set soname on shared object builds (Timothy J. Fontaine)


2013.05.11, Version 0.11.2 (Unstable), 3fba0bf65f091b91a9760530c05c6339c658d88b

Changes since version 0.11.1:

* darwin: look up file path with F_GETPATH (Ben Noordhuis)

* unix, windows: add uv_has_ref() function (Saúl Ibarra Corretgé)

* build: avoid double / in paths for dtrace (Timothy J. Fontaine)

* unix: remove src/unix/cygwin.c (Ben Noordhuis)

* windows: deal with the fact that GetTickCount might lag (Bert Belder)

* unix: silence STATIC_ASSERT compiler warnings (Ben Noordhuis)

* linux: don't use fopen() in uv_resident_set_memory() (Ben Noordhuis)


2013.04.24, Version 0.10.5 (Stable), 6595a7732c52eb4f8e57c88655f72997a8567a67

Changes since version 0.10.4:

* unix: silence STATIC_ASSERT compiler warnings (Ben Noordhuis)

* windows: make timers handle large timeouts (Miroslav Bajtoš)

* windows: remove superfluous assert statement (Bert Belder)

* unix: silence STATIC_ASSERT compiler warnings (Ben Noordhuis)

* linux: don't use fopen() in uv_resident_set_memory() (Ben Noordhuis)


2013.04.12, Version 0.10.4 (Stable), 85827e26403ac6dfa331af8ec9916ea7e27bd833

Changes since version 0.10.3:

* include: update uv_backend_fd() documentation (Ben Noordhuis)

* unix: include uv.h in src/version.c (Ben Noordhuis)

* unix: don't write more than IOV_MAX iovecs (Fedor Indutny)

* mingw-w64: don't call _set_invalid_parameter_handler (Nils Maier)

* build: gyp disable thin archives (Timothy J. Fontaine)

* sunos: re-export entire library when static (Timothy J. Fontaine)

* unix: dtrace probes for tick-start and tick-stop (Timothy J. Fontaine)

* windows: fix memory leak in fs__sendfile (Shannen Saez)

* windows: remove double initialization in uv_tty_init (Shannen Saez)

* build: fix dtrace-enabled out of tree build (Ben Noordhuis)

* build: squelch -Wdollar-in-identifier-extension warnings (Ben Noordhuis)

* inet: snprintf returns int, not size_t (Brian White)

* win: refactor uv_cpu_info (Bert Belder)

* build: add support for Visual Studio 2012 (Nicholas Vavilov)

* build: -Wno-dollar-in-identifier-extension is clang only (Ben Noordhuis)


2013.04.11, Version 0.11.1 (Unstable), 5c10e82ae0bc99eff86d4b9baff1f1aa0bf84c0a

This is the first versioned release from the current unstable libuv branch.

Changes since Node.js v0.11.0:

* all platforms: nanosecond resolution support for uv_fs_[fl]stat (Timothy J.
  Fontaine)

* all platforms: add netmask to uv_interface_address (Ben Kelly)

* unix: make sure the `status` parameter passed to the `uv_getaddrinfo` is 0 or
  -1 (Ben Noordhuis)

* unix: limit the number of iovecs written in a single `writev` syscall to
  IOV_MAX (Fedor Indutny)

* unix: add dtrace probes for tick-start and tick-stop (Timothy J. Fontaine)

* mingw-w64: don't call _set_invalid_parameter_handler (Nils Maier)

* windows: fix memory leak in fs__sendfile (Shannen Saez)

* windows: fix edge case bugs in uv_cpu_info (Bert Belder)

* include: no longer ship with / include ngx-queue.h (Ben Noordhuis)

* include: remove UV_VERSION_* macros from uv.h (Ben Noordhuis)

* documentation updates (Kristian Evensen, Ben Kelly, Ben Noordhuis)

* build: fix dtrace-enabled builds (Ben Noordhuis, Timothy J. Fontaine)

* build: gyp disable thin archives (Timothy J. Fontaine)

* build: add support for Visual Studio 2012 (Nicholas Vavilov)


2013.03.28, Version 0.10.3 (Stable), 31ebe23973dd98fd8a24c042b606f37a794e99d0

Changes since version 0.10.2:

* include: remove extraneous const from uv_version() (Ben Noordhuis)

* doc: update README, replace `OS` by `PLATFORM` (Ben Noordhuis)

* build: simplify .buildstamp rule (Ben Noordhuis)

* build: disable -Wstrict-aliasing on darwin (Ben Noordhuis)

* darwin: don't select(&exceptfds) in fallback path (Ben Noordhuis)

* unix: don't clear flags after closing UDP handle (Saúl Ibarra Corretgé)


2013.03.25, Version 0.10.2 (Stable), 0f36a00568f3e7608f97f6c6cdb081f4800a50c9

This is the first officially versioned release of libuv. Starting now
libuv will make releases independently of Node.js.

Changes since Node.js v0.10.0:

* test: add tap output for windows (Timothy J. Fontaine)

* unix: fix uv_tcp_simultaneous_accepts() logic (Ben Noordhuis)

* include: bump UV_VERSION_MINOR (Ben Noordhuis)

* unix: improve uv_guess_handle() implementation (Ben Noordhuis)

* stream: run try_select only for pipes and ttys (Fedor Indutny)

Changes since Node.js v0.10.1:

* build: rename OS to PLATFORM (Ben Noordhuis)

* unix: make uv_timer_init() initialize repeat (Brian Mazza)

* unix: make timers handle large timeouts (Ben Noordhuis)

* build: add OBJC makefile var (Ben Noordhuis)

* Add `uv_version()` and `uv_version_string()` APIs (Bert Belder)
