unit fafafa.core.sync.primitives;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes, SyncObjs,
  fafafa.core.base,
  fafafa.core.sync.types,
  fafafa.core.sync.atomic;

type
  // 互斥锁实现
  TMutex = class(TInterfacedObject, ILock)
  private
    FCriticalSection: TCriticalSection;
  public
    constructor Create;
    destructor Destroy; override;
    
    // ILock 接口实现
    procedure Acquire;
    procedure Release;
    function TryAcquire(aTimeoutMs: Cardinal = 0): Boolean;
  end;

  // 自旋锁实现（基于原子操作）
  TSpinLock = class(TInterfacedObject, ILock)
  private
    FLocked: TAtomicBoolean;
    FSpinCount: Cardinal;
  public
    constructor Create(aSpinCount: Cardinal = 4000);
    destructor Destroy; override;
    
    // ILock 接口实现
    procedure Acquire;
    procedure Release;
    function TryAcquire(aTimeoutMs: Cardinal = 0): Boolean;
    
    property SpinCount: <PERSON> read FSpinCount write FSpinCount;
  end;

  // 事件实现
  TEvent = class(TInterfacedObject, IEvent)
  private
    FEvent: TSimpleEvent;
    FManualReset: Boolean;
    FSignaled: TAtomicBoolean;
  public
    constructor Create(aManualReset: Boolean = False; aInitialState: Boolean = False);
    destructor Destroy; override;
    
    // IEvent 接口实现
    procedure SetEvent;
    procedure ResetEvent;
    function WaitFor(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
    
    property ManualReset: Boolean read FManualReset;
    property Signaled: Boolean read FSignaled.Load;
  end;

  // 信号量实现
  TSemaphore = class(TInterfacedObject, ISemaphore)
  private
    FSemaphore: System.TSemaphore;
    FCurrentCount: TAtomicInt32;
    FMaxCount: Integer;
  public
    constructor Create(aInitialCount: Integer = 0; aMaxCount: Integer = MaxInt);
    destructor Destroy; override;
    
    // ISemaphore 接口实现
    function Acquire(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
    function Release(aReleaseCount: Integer = 1): Integer;
    function GetCount: Integer;
    
    property MaxCount: Integer read FMaxCount;
  end;

  // 轻量级事件（基于原子操作和自旋）
  TLightEvent = class(TInterfacedObject, IEvent)
  private
    FSignaled: TAtomicBoolean;
    FManualReset: Boolean;
    FSpinCount: Cardinal;
  public
    constructor Create(aManualReset: Boolean = False; aInitialState: Boolean = False; 
      aSpinCount: Cardinal = 1000);
    destructor Destroy; override;
    
    // IEvent 接口实现
    procedure SetEvent;
    procedure ResetEvent;
    function WaitFor(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
    
    property SpinCount: Cardinal read FSpinCount write FSpinCount;
  end;

  // 计数事件（类似信号量但更轻量）
  TCountEvent = class(TInterfacedObject)
  private
    FCount: TAtomicInt32;
    FMaxCount: Integer;
  public
    constructor Create(aInitialCount: Integer = 0; aMaxCount: Integer = MaxInt);
    destructor Destroy; override;
    
    function Signal(aCount: Integer = 1): Integer;
    function Wait(aTimeoutMs: Cardinal = INFINITE): TWaitResult;
    function TryWait: Boolean;
    function GetCount: Integer;
    
    property MaxCount: Integer read FMaxCount;
  end;

// 工厂函数
function CreateMutex: ILock;
function CreateSpinLock(aSpinCount: Cardinal = 4000): ILock;
function CreateEvent(aManualReset: Boolean = False; aInitialState: Boolean = False): IEvent;
function CreateLightEvent(aManualReset: Boolean = False; aInitialState: Boolean = False; 
  aSpinCount: Cardinal = 1000): IEvent;
function CreateSemaphore(aInitialCount: Integer = 0; aMaxCount: Integer = MaxInt): ISemaphore;

// C风格API
function sync_mutex_create: Pointer;
procedure sync_mutex_destroy(aMutex: Pointer);
function sync_mutex_lock(aMutex: Pointer): Boolean;
function sync_mutex_unlock(aMutex: Pointer): Boolean;
function sync_mutex_trylock(aMutex: Pointer; aTimeoutMs: Cardinal = 0): Boolean;

function sync_event_create(aManualReset: Boolean = False; aInitialState: Boolean = False): Pointer;
procedure sync_event_destroy(aEvent: Pointer);
procedure sync_event_set(aEvent: Pointer);
procedure sync_event_reset(aEvent: Pointer);
function sync_event_wait(aEvent: Pointer; aTimeoutMs: Cardinal = INFINITE): Integer;

implementation

// TMutex 实现

constructor TMutex.Create;
begin
  inherited Create;
  FCriticalSection := TCriticalSection.Create;
end;

destructor TMutex.Destroy;
begin
  FreeAndNil(FCriticalSection);
  inherited Destroy;
end;

procedure TMutex.Acquire;
begin
  FCriticalSection.Enter;
end;

procedure TMutex.Release;
begin
  FCriticalSection.Leave;
end;

function TMutex.TryAcquire(aTimeoutMs: Cardinal): Boolean;
begin
  if aTimeoutMs = 0 then
    Result := FCriticalSection.TryEnter
  else
  begin
    // FreePascal的TCriticalSection不支持超时，使用简单的自旋等待
    var StartTime := GetTickCount64;
    repeat
      Result := FCriticalSection.TryEnter;
      if Result then
        Exit;
      Sleep(1);
    until (GetTickCount64 - StartTime) >= aTimeoutMs;
    Result := False;
  end;
end;

// TSpinLock 实现

constructor TSpinLock.Create(aSpinCount: Cardinal);
begin
  inherited Create;
  FLocked.Store(False);
  FSpinCount := aSpinCount;
end;

destructor TSpinLock.Destroy;
begin
  inherited Destroy;
end;

procedure TSpinLock.Acquire;
var
  SpinCounter: Cardinal;
begin
  SpinCounter := 0;
  while not FLocked.CompareExchange(False, True) do
  begin
    Inc(SpinCounter);
    if SpinCounter >= FSpinCount then
    begin
      Sleep(0); // 让出CPU时间片
      SpinCounter := 0;
    end;
  end;
end;

procedure TSpinLock.Release;
begin
  FLocked.Store(False);
end;

function TSpinLock.TryAcquire(aTimeoutMs: Cardinal): Boolean;
var
  StartTime: QWord;
  SpinCounter: Cardinal;
begin
  StartTime := GetTickCount64;
  SpinCounter := 0;
  
  repeat
    Result := FLocked.CompareExchange(False, True);
    if Result then
      Exit;
    
    Inc(SpinCounter);
    if SpinCounter >= FSpinCount then
    begin
      Sleep(1);
      SpinCounter := 0;
    end;
  until (aTimeoutMs <> INFINITE) and ((GetTickCount64 - StartTime) >= aTimeoutMs);
  
  Result := False;
end;

// TEvent 实现

constructor TEvent.Create(aManualReset: Boolean; aInitialState: Boolean);
begin
  inherited Create;
  FManualReset := aManualReset;
  FSignaled.Store(aInitialState);
  FEvent := TSimpleEvent.Create;
  if aInitialState then
    FEvent.SetEvent;
end;

destructor TEvent.Destroy;
begin
  FreeAndNil(FEvent);
  inherited Destroy;
end;

procedure TEvent.SetEvent;
begin
  FSignaled.Store(True);
  FEvent.SetEvent;
end;

procedure TEvent.ResetEvent;
begin
  FSignaled.Store(False);
  FEvent.ResetEvent;
end;

function TEvent.WaitFor(aTimeoutMs: Cardinal): TWaitResult;
var
  WaitResult: TWaitResult;
begin
  case FEvent.WaitFor(aTimeoutMs) of
    wrSignaled: 
    begin
      if not FManualReset then
      begin
        FSignaled.Store(False);
        FEvent.ResetEvent;
      end;
      Result := wrSignaled;
    end;
    wrTimeout: Result := wrTimeout;
    wrAbandoned: Result := wrAbandoned;
    wrError: Result := wrError;
    else Result := wrError;
  end;
end;

// TSemaphore 实现

constructor TSemaphore.Create(aInitialCount: Integer; aMaxCount: Integer);
begin
  inherited Create;
  FMaxCount := aMaxCount;
  FCurrentCount.Store(aInitialCount);
  FSemaphore := System.TSemaphore.Create(nil, aInitialCount, aMaxCount, '');
end;

destructor TSemaphore.Destroy;
begin
  FreeAndNil(FSemaphore);
  inherited Destroy;
end;

function TSemaphore.Acquire(aTimeoutMs: Cardinal): TWaitResult;
begin
  case FSemaphore.WaitFor(aTimeoutMs) of
    wrSignaled: 
    begin
      FCurrentCount.Decrement;
      Result := wrSignaled;
    end;
    wrTimeout: Result := wrTimeout;
    wrAbandoned: Result := wrAbandoned;
    wrError: Result := wrError;
    else Result := wrError;
  end;
end;

function TSemaphore.Release(aReleaseCount: Integer): Integer;
var
  i: Integer;
begin
  Result := FCurrentCount.Load;
  for i := 1 to aReleaseCount do
  begin
    if FCurrentCount.Load < FMaxCount then
    begin
      FSemaphore.Release;
      FCurrentCount.Increment;
    end;
  end;
end;

function TSemaphore.GetCount: Integer;
begin
  Result := FCurrentCount.Load;
end;

// TLightEvent 实现

constructor TLightEvent.Create(aManualReset: Boolean; aInitialState: Boolean; aSpinCount: Cardinal);
begin
  inherited Create;
  FManualReset := aManualReset;
  FSignaled.Store(aInitialState);
  FSpinCount := aSpinCount;
end;

destructor TLightEvent.Destroy;
begin
  inherited Destroy;
end;

procedure TLightEvent.SetEvent;
begin
  FSignaled.Store(True);
end;

procedure TLightEvent.ResetEvent;
begin
  FSignaled.Store(False);
end;

function TLightEvent.WaitFor(aTimeoutMs: Cardinal): TWaitResult;
var
  StartTime: QWord;
  SpinCounter: Cardinal;
begin
  StartTime := GetTickCount64;
  SpinCounter := 0;
  
  repeat
    if FSignaled.Load then
    begin
      if not FManualReset then
        FSignaled.Store(False);
      Result := wrSignaled;
      Exit;
    end;
    
    Inc(SpinCounter);
    if SpinCounter >= FSpinCount then
    begin
      Sleep(1);
      SpinCounter := 0;
    end;
  until (aTimeoutMs <> INFINITE) and ((GetTickCount64 - StartTime) >= aTimeoutMs);
  
  Result := wrTimeout;
end;

// TCountEvent 实现

constructor TCountEvent.Create(aInitialCount: Integer; aMaxCount: Integer);
begin
  inherited Create;
  FCount.Store(aInitialCount);
  FMaxCount := aMaxCount;
end;

destructor TCountEvent.Destroy;
begin
  inherited Destroy;
end;

function TCountEvent.Signal(aCount: Integer): Integer;
var
  Current, NewValue: Integer;
begin
  repeat
    Current := FCount.Load;
    NewValue := Current + aCount;
    if NewValue > FMaxCount then
      NewValue := FMaxCount;
  until FCount.CompareExchange(Current, NewValue);
  Result := NewValue;
end;

function TCountEvent.Wait(aTimeoutMs: Cardinal): TWaitResult;
var
  StartTime: QWord;
  Current: Integer;
begin
  StartTime := GetTickCount64;

  repeat
    Current := FCount.Load;
    if Current > 0 then
    begin
      if FCount.CompareExchange(Current, Current - 1) then
      begin
        Result := wrSignaled;
        Exit;
      end;
    end
    else
    begin
      Sleep(1);
    end;
  until (aTimeoutMs <> INFINITE) and ((GetTickCount64 - StartTime) >= aTimeoutMs);

  Result := wrTimeout;
end;

function TCountEvent.TryWait: Boolean;
var
  Current: Integer;
begin
  Current := FCount.Load;
  Result := (Current > 0) and FCount.CompareExchange(Current, Current - 1);
end;

function TCountEvent.GetCount: Integer;
begin
  Result := FCount.Load;
end;

// 工厂函数实现

function CreateMutex: ILock;
begin
  Result := TMutex.Create;
end;

function CreateSpinLock(aSpinCount: Cardinal): ILock;
begin
  Result := TSpinLock.Create(aSpinCount);
end;

function CreateEvent(aManualReset: Boolean; aInitialState: Boolean): IEvent;
begin
  Result := TEvent.Create(aManualReset, aInitialState);
end;

function CreateLightEvent(aManualReset: Boolean; aInitialState: Boolean; aSpinCount: Cardinal): IEvent;
begin
  Result := TLightEvent.Create(aManualReset, aInitialState, aSpinCount);
end;

function CreateSemaphore(aInitialCount: Integer; aMaxCount: Integer): ISemaphore;
begin
  Result := TSemaphore.Create(aInitialCount, aMaxCount);
end;

// C风格API实现

function sync_mutex_create: Pointer;
begin
  Result := Pointer(CreateMutex);
end;

procedure sync_mutex_destroy(aMutex: Pointer);
var
  Mutex: ILock;
begin
  if aMutex <> nil then
  begin
    Mutex := ILock(aMutex);
    Mutex := nil; // 释放引用
  end;
end;

function sync_mutex_lock(aMutex: Pointer): Boolean;
var
  Mutex: ILock;
begin
  Result := False;
  if aMutex = nil then
    Exit;

  try
    Mutex := ILock(aMutex);
    Mutex.Acquire;
    Result := True;
  except
    Result := False;
  end;
end;

function sync_mutex_unlock(aMutex: Pointer): Boolean;
var
  Mutex: ILock;
begin
  Result := False;
  if aMutex = nil then
    Exit;

  try
    Mutex := ILock(aMutex);
    Mutex.Release;
    Result := True;
  except
    Result := False;
  end;
end;

function sync_mutex_trylock(aMutex: Pointer; aTimeoutMs: Cardinal): Boolean;
var
  Mutex: ILock;
begin
  Result := False;
  if aMutex = nil then
    Exit;

  try
    Mutex := ILock(aMutex);
    Result := Mutex.TryAcquire(aTimeoutMs);
  except
    Result := False;
  end;
end;

function sync_event_create(aManualReset: Boolean; aInitialState: Boolean): Pointer;
begin
  Result := Pointer(CreateEvent(aManualReset, aInitialState));
end;

procedure sync_event_destroy(aEvent: Pointer);
var
  Event: IEvent;
begin
  if aEvent <> nil then
  begin
    Event := IEvent(aEvent);
    Event := nil; // 释放引用
  end;
end;

procedure sync_event_set(aEvent: Pointer);
var
  Event: IEvent;
begin
  if aEvent <> nil then
  begin
    Event := IEvent(aEvent);
    Event.SetEvent;
  end;
end;

procedure sync_event_reset(aEvent: Pointer);
var
  Event: IEvent;
begin
  if aEvent <> nil then
  begin
    Event := IEvent(aEvent);
    Event.ResetEvent;
  end;
end;

function sync_event_wait(aEvent: Pointer; aTimeoutMs: Cardinal): Integer;
var
  Event: IEvent;
begin
  Result := Integer(wrError);
  if aEvent = nil then
    Exit;

  try
    Event := IEvent(aEvent);
    Result := Integer(Event.WaitFor(aTimeoutMs));
  except
    Result := Integer(wrError);
  end;
end;

end.
