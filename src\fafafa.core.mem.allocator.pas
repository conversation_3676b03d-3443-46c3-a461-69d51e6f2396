{

```text
   ______   ______     ______   ______     ______   ______
  /\  ___\ /\  __ \   /\  ___\ /\  __ \   /\  ___\ /\  __ \
  \ \  __\ \ \  __ \  \ \  __\ \ \  __ \  \ \  __\ \ \  __ \
   \ \_\    \ \_\ \_\  \ \_\    \ \_\ \_\  \ \_\    \ \_\ \_\
    \/_/     \/_/\/_/   \/_/     \/_/\/_/   \/_/     \/_/\/_/  Studio

```
# fafafa.core.mem.allocator

## Abstract 摘要

Provides interfaces and implementations for memory allocators.
提供内存分配器的接口和实现.

All interfaces in this unit fully comply with the `null operation principle`, and when `count = 0`, no operations are performed.
本单元所有接口完全遵守 `空操作原则`, 输入参数 `count = 0` 时, 不进行任何操作.

## Declaration 

For forwarding or using it for your own project, please retain the copyright notice of this project. Thank you.


Author:    fafafaStudio
Contact:   <EMAIL> | QQ Group: 685403987 | QQ:179033731
Copyright: (c) 2025 fafafaStudio. All rights reserved.
}

unit fafafa.core.mem.allocator;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils,
  fafafa.core.base;

type

  {**
   * IAllocator
   *
   * @desc Interface for a generic memory allocator.
   *       通用内存分配器的接口.
   *}
  IAllocator = interface
    ['{1CEB691D-D538-48D2-A5C4-A4F0A1B98928}']

    {**
     * GetMem
     *
     * @desc Allocates a block of uninitialized memory of a specified size.
     *       分配指定大小的未初始化内存块.
     *
     * @params
     *   aSize The size of the memory block to allocate, in bytes.
     *         要分配的内存块大小, 以字节为单位.
     *
     * @return A pointer to the allocated memory block, or `nil` if `aSize` is 0.
     *         指向已分配内存块的指针, 如果 `aSize` 为 0 则返回 `nil`.
     *}
    function GetMem(aSize: SizeUInt): Pointer;

    {**
     * AllocMem
     *
     * @desc Allocates a block of zero-initialized memory of a specified size.
     *       分配指定大小的零初始化内存块.
     *
     * @params
     *   aSize The size of the memory block to allocate, in bytes.
     *         要分配的内存块大小, 以字节为单位.
     *
     * @return A pointer to the allocated memory block, or `nil` if `aSize` is 0.
     *         指向已分配内存块的指针, 如果 `aSize` 为 0 则返回 `nil`.
     *}
    function AllocMem(aSize: SizeUInt): Pointer;

    {**
     * ReallocMem
     *
     * @desc Reallocates a memory block to a new size.
     *       重新分配内存块到新的大小.
     *
     * @params
     *   aDst  A pointer to the memory block to reallocate. Can be `nil`.
     *         指向要重新分配的内存块的指针. 可以为 `nil`.
     *   aSize The new size of the memory block, in bytes.
     *         新内存块的大小, 以字节为单位.
     *
     * @return A pointer to the reallocated memory block, or `nil` if `aSize` is 0.
     *         指向重新分配内存块的指针, 如果 `aSize` 为 0 则返回 `nil`.
     *}
    function ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;

    {**
     * FreeMem
     *
     * @desc Frees a previously allocated memory block.
     *       释放先前分配的内存块.
     *
     * @params
     *   aDst A pointer to the memory block to free. Cannot be `nil`.
     *        指向要释放的内存块的指针. 不可以为 `nil`.
     *
     * @exceptions
     *   EArgumentNil If `aDst` is `nil`.
     *         如果 `aDst` 为 `nil`, 将抛出异常.
     *}
    procedure FreeMem(aDst: Pointer);
  end;


  {**
   * TAllocator
   *
   * @desc Abstract base class for memory allocators, implementing `IAllocator`.
   *       内存分配器的抽象基类, 实现了 `IAllocator` 接口.
   *
   * @remark
   *   This class provides common functionality and serves as a base for concrete allocator implementations.
   *   此类提供通用功能, 并作为具体分配器实现的基类.
   *}
  TAllocator = class(TInterfacedObject, IAllocator)
  protected
    function DoGetMem(aSize: SizeUInt): Pointer; virtual; abstract;
    function DoAllocMem(aSize: SizeUInt): Pointer; virtual; abstract;
    function DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; virtual; abstract;
    procedure DoFreeMem(aDst: Pointer); virtual; abstract;

  public
    function  GetMem(aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function  AllocMem(aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function  ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    procedure FreeMem(aDst: Pointer); {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
  end;


  // Callback types for custom allocator
  TGetMemCallback     = function(aSize: SizeUInt): Pointer;
  TAllocMemCallback   = function(aSize: SizeUInt): Pointer;
  TReallocMemCallback = function(aDst: Pointer; aSize: SizeUInt): Pointer;
  TFreeMemCallback    = procedure(aDst: Pointer);

  {**
   * TCallbackAllocator
   *
   * @desc Concrete implementation of `IAllocator` that uses user-provided callback functions for memory management.
   *       使用用户提供的回调函数进行内存管理的 `IAllocator` 具体类.
   *
   * @remark
   *   This allocator allows for flexible integration with external memory management systems.
   *   此分配器允许与外部内存管理系统灵活集成.
   *}
  TCallbackAllocator = class(TAllocator)
  private
    FGetMemCallback:     TGetMemCallback;
    FAllocMemCallback:   TAllocMemCallback;
    FReallocMemCallback: TReallocMemCallback;
    FFreeMemCallback:    TFreeMemCallback;
  protected
    function  DoGetMem(aSize: SizeUInt): Pointer; override;
    function  DoAllocMem(aSize: SizeUInt): Pointer; override;
    function  DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; override;
    procedure DoFreeMem(aDst: Pointer); override;
  public
    constructor Create(aGetMem: TGetMemCallback; aAllocMem: TAllocMemCallback; aReallocMem: TReallocMemCallback; aFreeMem: TFreeMemCallback);
  end;


  {$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
  {**
   * TCrtAllocator
   *
   * @desc Concrete implementation of `IAllocator` using the C Runtime Library (CRT) memory manager.
   *       使用 C 运行时库 (CRT) 内存管理器实现的 `IAllocator` 具体类.
   *
   * @remark
   *   This allocator uses `malloc`, `calloc`, `realloc`, and `free`.
   *   此分配器使用 `malloc`, `calloc`, `realloc` 和 `free`.
   *}
  TCrtAllocator = class(TAllocator)
  protected
    function  DoGetMem(aSize: SizeUInt): Pointer; override;
    function  DoAllocMem(aSize: SizeUInt): Pointer; override;
    function  DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; override;
    procedure DoFreeMem(aDst: Pointer); override;
  end;
  {$ENDIF}


  {**
   * TRtlAllocator
   *
   * @desc Concrete implementation of `IAllocator` using the standard Pascal RTL memory manager.
   *       使用标准 Pascal RTL 内存管理器实现的 `IAllocator` 具体类.
   *
   * @remark
   *   This allocator uses `System.GetMem`, `System.AllocMem`, `System.ReallocMem`, and `System.FreeMem`.
   *   此分配器使用 `System.GetMem`, `System.AllocMem`, `System.ReallocMem` 和 `System.FreeMem`.
   *}
  TRtlAllocator = class(TAllocator)
  protected
    function  DoGetMem(aSize: SizeUInt): Pointer; override;
    function  DoAllocMem(aSize: SizeUInt): Pointer; override;
    function  DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer; override;
    procedure DoFreeMem(aDst: Pointer); override;
  end;

{
  @note 线程安全性说明：
  当前实现不是线程安全的。在多线程环境中使用时，请确保：
  1. 在程序启动时（单线程阶段）调用GetRtlAllocator()和GetCrtAllocator()
  2. 或者在应用层提供适当的同步机制
  
  @todo 等框架内线程同步模块完成后，将添加内置的线程安全保护
}
  // TODO: 缺少线程安全保护,等框架内线程同步部分完成后再加个mutex保护, 或者使用TLS来实现线程本地分配器
  function GetRtlAllocator: TAllocator;
  {$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
  function GetCrtAllocator: TAllocator;
  {$ENDIF}

implementation

{$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
function  crt_malloc(aSize: SizeUInt): Pointer; cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'malloc';
function  crt_calloc(aNum, aSize: SizeUInt): Pointer; cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'calloc';
function  crt_realloc(aPtr: Pointer; aSize: SizeUInt): Pointer; cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'realloc';
procedure crt_free(aPtr: Pointer); cdecl external {$IFDEF MSWINDOWS}'msvcrt.dll'{$ELSE}'libc'{$ENDIF} name 'free';
{$ENDIF}

{ TRtlAllocator }

function TRtlAllocator.DoGetMem(aSize: SizeUInt): Pointer;
begin
  Result := System.GetMem(aSize);
end;

function TRtlAllocator.DoAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := System.AllocMem(aSize);
end;

function TRtlAllocator.DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := System.ReallocMem(aDst, aSize);
end;

procedure TRtlAllocator.DoFreeMem(aDst: Pointer);
begin
  System.FreeMem(aDst);
end;

{$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
{ TCrtAllocator }

function TCrtAllocator.DoGetMem(aSize: SizeUInt): Pointer;
begin
  Result := crt_malloc(aSize);
end;

function TCrtAllocator.DoAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := crt_calloc(1, aSize);
end;

function TCrtAllocator.DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := crt_realloc(aDst, aSize);
end;

procedure TCrtAllocator.DoFreeMem(aDst: Pointer);
begin
  crt_free(aDst);
end;
{$ENDIF}

{ TCallbackAllocator }

constructor TCallbackAllocator.Create(aGetMem: TGetMemCallback; aAllocMem: TAllocMemCallback; aReallocMem: TReallocMemCallback; aFreeMem: TFreeMemCallback);
begin
  inherited Create;

  if (aGetMem = nil) or (aAllocMem = nil) or (aReallocMem = nil) or (aFreeMem = nil) then
    raise EArgumentNil.Create('TCallbackAllocator.Create: aGetMem, aAllocMem, aReallocMem, aFreeMem cannot be nil.');

  FGetMemCallback     := aGetMem;
  FAllocMemCallback   := aAllocMem;
  FReallocMemCallback := aReallocMem;
  FFreeMemCallback    := aFreeMem;
end;

function TCallbackAllocator.DoGetMem(aSize: SizeUInt): Pointer;
begin
  Result := FGetMemCallback(aSize)
end;

function TCallbackAllocator.DoAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := FAllocMemCallback(aSize)
end;

function TCallbackAllocator.DoReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := FReallocMemCallback(aDst, aSize)
end;

procedure TCallbackAllocator.DoFreeMem(aDst: Pointer);
begin
  FFreeMemCallback(aDst)
end;

{ TAllocator }

function TAllocator.GetMem(aSize: SizeUInt): Pointer;
begin
  if aSize = 0 then
    Exit(nil);

  Result := DoGetMem(aSize);
end;

function TAllocator.AllocMem(aSize: SizeUInt): Pointer;
begin
  if aSize = 0 then
    Exit(nil);

  Result := DoAllocMem(aSize);
end;

function TAllocator.ReallocMem(aDst: Pointer; aSize: SizeUInt): Pointer;
begin
  if aSize = 0 then
  begin
    if aDst <> nil then
      DoFreeMem(aDst);

    Exit(nil);
  end;

  if aDst = nil then
    Exit(GetMem(aSize));

  Result := DoReallocMem(aDst, aSize);
end;

procedure TAllocator.FreeMem(aDst: Pointer);
begin
  if aDst = nil then
    raise EArgumentNil.Create('TAllocator.FreeMem: aDst cannot be nil.');

  DoFreeMem(aDst);
end;

var
  _RTLAllocator: TAllocator = nil;
  _CrtAllocator: TAllocator = nil;

function GetRtlAllocator: TAllocator;
begin
  if _RTLAllocator = nil then
    _RTLAllocator := TRtlAllocator.Create;

  Result := _RTLAllocator;
end;

{$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
function GetCrtAllocator: TAllocator;
begin
  if _CrtAllocator = nil then
    _CrtAllocator := TCrtAllocator.Create;

  Result := _CrtAllocator;
end;
{$ENDIF}

finalization

  if _RTLAllocator <> nil then
    _RTLAllocator.Free;

{$IFDEF FAFAFA_CORE_CRT_ALLOCATOR}
  if _CrtAllocator <> nil then
    _CrtAllocator.Free;
{$ENDIF}

end.
