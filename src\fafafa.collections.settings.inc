{ 开启内联 }
{.$DEFINE FAFAFA_COLLECTIONS_INLINE} 

{ 
  linux下优先使用 libc
  windows crt 性能表现不佳,无所谓.
}

{.$DEFINE FAFAFA_COLLECTIONS_CRT}     // 使用c运行库
{.$DEFINE FAFAFA_COLLECTIONS_TYPE_ALIASES} // 预定义常用泛型类型

{ 开启匿名函数/匿名引用 仅限 fpc 3.3.1 及以上 }
{$IFDEF FPC_VERSION >= 030301}
  {$DEFINE FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
{$ENDIF}

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  {$ModeSwitch functionreferences}
  {$ModeSwitch anonymousfunctions}
{$ENDIF}
