unit fafafa.core.collections.deque;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base,
  fafafa.core.math,
  fafafa.core.mem.utils,
  fafafa.core.mem.allocator,
  fafafa.core.collections.base,
  fafafa.core.collections.queue,
  fafafa.core.collections.arr;

type

  { IDeque 泛型双端队列接口 }
  generic IDeque<T> = interface(specialize IQueue<T>)
  ['{F1A2B3C4-D5E6-4F78-9A0B-1C2D3E4F5A6B}']

    {**
     * PushFront
     *
     * @desc 将元素添加到队列头部
     *
     * @params
     *   aElement 要添加的元素
     *
     * @remark 该方法将元素添加到队列的头部 (前端)。
     *}
    procedure PushFront(const aElement: T); overload;

    {**
     * PushFront
     *
     * @desc 将元素数组添加到队列头部
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将元素数组中的每个元素依次添加到队列的头部。
     *}
    procedure PushFront(const aElements: array of T); overload;

    {**
     * PushFront
     *
     * @desc 从内存指针添加多个元素到队列头部
     *
     * @params
     *   aSrc          指向源数据的指针
     *   aElementCount 要添加的元素数量
     *
     * @remark 该方法从指定内存位置复制指定数量的元素到队列头部。
     *
     * @exceptions
     *   EArgumentNil  If `aSrc` is `nil` and `aElementCount` > 0.
     *                 当 `aSrc` 为 `nil` 且 `aElementCount` > 0 时抛出.
     *
     *   EOutOfMemory  If memory allocation fails.
     *                 如果内存分配失败.
     *}
    procedure PushFront(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * PushBack
     *
     * @desc 将元素添加到队列尾部 (等同于 Enqueue)
     *
     * @params
     *   aElement 要添加的元素
     *
     * @remark 该方法将元素添加到队列的尾部 (后端)。
     *}
    procedure PushBack(const aElement: T); overload;

    {**
     * PushBack
     *
     * @desc 将元素数组添加到队列尾部
     *
     * @params
     *   aElements 要添加的元素数组
     *
     * @remark 该方法将元素数组中的每个元素依次添加到队列的尾部。
     *}
    procedure PushBack(const aElements: array of T); overload;

    {**
     * PushBack
     *
     * @desc 从内存指针添加多个元素到队列尾部
     *
     * @params
     *   aSrc          指向源数据的指针
     *   aElementCount 要添加的元素数量
     *
     * @remark 该方法从指定内存位置复制指定数量的元素到队列尾部。
     *
     * @exceptions
     *   EArgumentNil  If `aSrc` is `nil` and `aElementCount` > 0.
     *                 当 `aSrc` 为 `nil` 且 `aElementCount` > 0 时抛出.
     *
     *   EOutOfMemory  If memory allocation fails.
     *                 如果内存分配失败.
     *}
    procedure PushBack(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * PopFront
     *
     * @desc 移除并返回队列头部的元素 (等同于 Dequeue)
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法移除队列头部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function PopFront: T; overload;

    {**
     * PopFront
     *
     * @desc 尝试移除队列头部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试移除队列头部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PopFront(var aElement: T): Boolean; overload;

    {**
     * PopBack
     *
     * @desc 移除并返回队列尾部的元素
     *
     * @return 返回被移除的元素
     *
     * @remark
     *   该方法移除队列尾部的元素并返回。
     *   如果队列为空,将抛出异常。
     *}
    function PopBack: T; overload;

    {**
     * PopBack
     *
     * @desc 尝试移除队列尾部元素
     *
     * @params
     *   aElement 用于存储出队元素的变量
     *
     * @return 如果成功出队返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试移除队列尾部的元素并通过 aElement 参数返回。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PopBack(var aElement: T): Boolean; overload;

    {**
     * PeekFront
     *
     * @desc 获取队列头部元素但不移除 (等同于 Peek)
     *
     * @return 返回队列头部元素
     *
     * @remark
     *   该方法返回队列头部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function PeekFront: T; overload;

    {**
     * PeekFront
     *
     * @desc 尝试获取队列头部元素但不移除
     *
     * @params
     *   aElement 用于存储队列头部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列头部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PeekFront(var aElement: T): Boolean; overload;

    {**
     * PeekBack
     *
     * @desc 获取队列尾部元素但不移除
     *
     * @return 返回队列尾部元素
     *
     * @remark
     *   该方法返回队列尾部元素但不会移除它。
     *   如果队列为空,将抛出异常。
     *}
    function PeekBack: T; overload;

    {**
     * PeekBack
     *
     * @desc 尝试获取队列尾部元素但不移除
     *
     * @params
     *   aElement 用于存储队列尾部元素的变量
     *
     * @return 如果成功获取返回 True,队列为空时返回 False
     *
     * @remark
     *   该方法尝试获取队列尾部元素并通过 aElement 参数返回,但不会移除它。
     *   如果队列为空,将返回 False 且不修改 aElement。
     *}
    function PeekBack(var aElement: T): Boolean; overload;

  end;

  { TDeque 双端队列实现类 - 基于TArray的实现 }
  generic TDeque<T> = class(specialize TGenericCollection<T>, specialize IDeque<T>)
  type
    TArray = specialize TArray<T>;
  private
    FArray: TArray;         // 内部数组容器
    FHead:  SizeUInt;       // 头部索引 (逻辑起始位置)
    FCount: SizeUInt;       // 元素数量

  { 内部辅助方法 }
  private
    function  GetTailIndex: SizeUInt; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure EnsureCapacity(aRequiredCapacity: SizeUInt);
    procedure ShiftElementsRight(aStartIndex: SizeUInt; aShiftCount: SizeUInt);
    procedure ShiftElementsLeft(aStartIndex: SizeUInt; aShiftCount: SizeUInt);
    procedure OptimizeLayout; // 优化内部布局，减少浪费的空间

  { ITerator 迭代器回调 }
  protected
    function  DoIterGetCurrent(aIter: PPtrIter): Pointer;
    function  DoIterMoveNext(aIter: PPtrIter): Boolean;
    function  DoIterMovePrev(aIter: PPtrIter): Boolean;

  { 内存重叠检查 }
  protected
    function IsOverlap(const aSrc: Pointer; aElementCount: SizeUInt): Boolean; override;

  { 算法实现 }
  protected
    procedure DoZero(); override;
    procedure DoReverse; override;

  public
    constructor Create; overload;
    constructor Create(aAllocator: TAllocator); overload;
    constructor Create(aAllocator: TAllocator; aData: Pointer); override; overload;
    constructor Create(aCapacity: SizeUInt); overload;
    constructor Create(aCapacity: SizeUInt; aAllocator: TAllocator); overload;
    constructor Create(aCapacity: SizeUInt; aAllocator: TAllocator; aData: Pointer); overload;
    constructor Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy; aAllocator: TAllocator); overload;
    constructor Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy; aAllocator: TAllocator; aData: Pointer); overload;
    constructor Create(const aSrc: array of T); overload;
    constructor Create(const aSrc: array of T; aAllocator: TAllocator); overload;
    constructor Create(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer); overload;

    destructor  Destroy; override;

    { ICollection 接口实现 }
    function  PtrIter: TPtrIter; override;
    function  GetCount: SizeUInt; override;
    procedure Clear; override;
    procedure SerializeToArrayBuffer(aDst: Pointer; aCount: SizeUInt); override;
    procedure AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt); override;
    procedure AppendToUnChecked(const aDst: TCollection); override;

    { IQueue<T> 接口实现 }
    procedure Enqueue(const aElement: T);
    procedure Push(const aElement: T);
    function  Dequeue: T; overload;
    function  Pop: T; overload;
    function  Dequeue(var aElement: T): Boolean; overload;
    function  Pop(var aElement: T): Boolean; overload;
    function  Peek: T; overload;
    function  Peek(var aElement: T): Boolean; overload;

    { IDeque<T> 接口实现 }
    procedure PushFront(const aElement: T); overload;
    procedure PushFront(const aElements: array of T); overload;
    procedure PushFront(const aSrc: Pointer; aElementCount: SizeUInt); overload;
    procedure PushBack(const aElement: T); overload;
    procedure PushBack(const aElements: array of T); overload;
    procedure PushBack(const aSrc: Pointer; aElementCount: SizeUInt); overload;
    function  PopFront: T; overload;
    function  PopFront(var aElement: T): Boolean; overload;
    function  PopBack: T; overload;
    function  PopBack(var aElement: T): Boolean; overload;
    function  PeekFront: T; overload;
    function  PeekFront(var aElement: T): Boolean; overload;
    function  PeekBack: T; overload;
    function  PeekBack(var aElement: T): Boolean; overload;

  private
    function GetCapacity: SizeUInt; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function GetGrowStrategy: TGrowthStrategy; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure SetGrowStrategy(const aValue: TGrowthStrategy); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  public
    { 属性 }
    property Capacity: SizeUInt read GetCapacity;
    property GrowStrategy: TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;

  end;

implementation

{ TDeque<T> }

{ 内部辅助方法 }

function TDeque.GetTailIndex: SizeUInt;
begin
  if FCount = 0 then
    Result := FHead
  else
    Result := FHead + FCount - 1;
end;

procedure TDeque.EnsureCapacity(aRequiredCapacity: SizeUInt);
begin
  // 确保数组有足够的容量来容纳从FHead开始的aRequiredCapacity个元素
  if FHead + aRequiredCapacity > FArray.Count then
  begin
    // 如果需要，先优化布局
    if FHead > 0 then
      OptimizeLayout;

    // 然后扩展数组容量
    FArray.Resize(FHead + aRequiredCapacity);
  end;
end;

procedure TDeque.ShiftElementsRight(aStartIndex: SizeUInt; aShiftCount: SizeUInt);
var
  i: SizeUInt;
begin
  if aShiftCount = 0 then
    Exit;

  // 确保有足够的空间
  EnsureCapacity(FCount + aShiftCount);

  // 从后往前移动元素
  for i := FCount downto aStartIndex + 1 do
  begin
    FArray.PutUnChecked(FHead + i + aShiftCount - 1, FArray.GetUnChecked(FHead + i - 1));
  end;
end;

procedure TDeque.ShiftElementsLeft(aStartIndex: SizeUInt; aShiftCount: SizeUInt);
var
  i: SizeUInt;
begin
  if aShiftCount = 0 then
    Exit;

  // 从前往后移动元素
  for i := aStartIndex to FCount - aShiftCount - 1 do
  begin
    FArray.PutUnChecked(FHead + i, FArray.GetUnChecked(FHead + i + aShiftCount));
  end;
end;

procedure TDeque.OptimizeLayout;
var
  i: SizeUInt;
  LTempArray: TArray;
begin
  if (FHead = 0) or (FCount = 0) then
    Exit;

  // 创建临时数组来存储当前元素
  LTempArray := TArray.Create(FCount, FArray.Allocator);
  try
    // 复制当前元素到临时数组
    for i := 0 to FCount - 1 do
    begin
      LTempArray.PutUnChecked(i, FArray.GetUnChecked(FHead + i));
    end;

    // 将元素移回到数组开头
    for i := 0 to FCount - 1 do
    begin
      FArray.PutUnChecked(i, LTempArray.GetUnChecked(i));
    end;

    FHead := 0;
  finally
    LTempArray.Free;
  end;
end;

function TDeque.GetCapacity: SizeUInt;
begin
  Result := FArray.Count;
end;

function TDeque.GetGrowStrategy: TGrowthStrategy;
begin
  // TArray doesn't expose GrowStrategy, return nil for now
  Result := nil;
end;

procedure TDeque.SetGrowStrategy(const aValue: TGrowthStrategy);
begin
  // TArray doesn't expose GrowStrategy, ignore for now
end;

{ 迭代器回调 }

function TDeque.DoIterGetCurrent(aIter: PPtrIter): Pointer;
begin
  Result := FArray.Ptr[FHead + SizeUInt(aIter^.Data)];
end;

function TDeque.DoIterMoveNext(aIter: PPtrIter): Boolean;
var
  LIndex: SizeUInt;
begin
  if not aIter^.Started then
  begin
    aIter^.Started := True;
    aIter^.Data := Pointer(0);
    Result := FCount > 0;
  end
  else
  begin
    LIndex := SizeUInt(aIter^.Data);
    Inc(LIndex);
    aIter^.Data := Pointer(LIndex);
    Result := LIndex < FCount;
  end;
end;

function TDeque.DoIterMovePrev(aIter: PPtrIter): Boolean;
var
  LIndex: SizeUInt;
begin
  if not aIter^.Started then
  begin
    aIter^.Started := True;
    if FCount > 0 then
    begin
      aIter^.Data := Pointer(FCount - 1);
      Result := True;
    end
    else
    begin
      aIter^.Data := Pointer(0);
      Result := False;
    end;
  end
  else
  begin
    LIndex := SizeUInt(aIter^.Data);
    if LIndex > 0 then
    begin
      Dec(LIndex);
      aIter^.Data := Pointer(LIndex);
      Result := True;
    end
    else
      Result := False;
  end;
end;

{ 内存重叠检查 }

function TDeque.IsOverlap(const aSrc: Pointer; aElementCount: SizeUInt): Boolean;
begin
  // 检查源指针是否与deque的有效数据区域重叠
  if (aSrc = nil) or (FCount = 0) then
  begin
    Result := False;
    Exit;
  end;

  // 使用FArray的内存检查
  Result := (PByte(aSrc) >= PByte(FArray.Ptr[FHead])) and
            (PByte(aSrc) < PByte(FArray.Ptr[FHead + FCount]));
end;

{ 算法实现 }

procedure TDeque.DoZero;
var
  i: SizeUInt;
begin
  for i := 0 to FCount - 1 do
  begin
    FArray.Zero(FHead + i, 1);
  end;
end;

procedure TDeque.DoReverse;
var
  i, j: SizeUInt;
  LTemp: T;
begin
  if FCount <= 1 then
    Exit;

  i := 0;
  j := FCount - 1;

  while i < j do
  begin
    LTemp := FArray.GetUnChecked(FHead + i);
    FArray.PutUnChecked(FHead + i, FArray.GetUnChecked(FHead + j));
    FArray.PutUnChecked(FHead + j, LTemp);

    Inc(i);
    Dec(j);
  end;
end;

{ 构造函数和析构函数 }

constructor TDeque.Create;
begin
  Create(16); // 默认容量
end;

constructor TDeque.Create(aAllocator: TAllocator);
begin
  Create(16, aAllocator);
end;

constructor TDeque.Create(aAllocator: TAllocator; aData: Pointer);
begin
  inherited Create(aAllocator, aData);
  FArray := TArray.Create(16, aAllocator);
  FHead := 0;
  FCount := 0;
end;

constructor TDeque.Create(aCapacity: SizeUInt);
begin
  Create(aCapacity, GetRtlAllocator());
end;

constructor TDeque.Create(aCapacity: SizeUInt; aAllocator: TAllocator);
begin
  inherited Create(aAllocator, nil);
  FArray := TArray.Create(aCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
end;

constructor TDeque.Create(aCapacity: SizeUInt; aAllocator: TAllocator; aData: Pointer);
begin
  inherited Create(aAllocator, aData);
  FArray := TArray.Create(aCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
end;

constructor TDeque.Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy);
begin
  inherited Create;
  FArray := TArray.Create(aCapacity);
  FHead := 0;
  FCount := 0;
  // Note: TArray doesn't support GrowthStrategy in constructor, ignoring for now
end;

constructor TDeque.Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy; aAllocator: TAllocator);
begin
  inherited Create(aAllocator);
  FArray := TArray.Create(aCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
  // Note: TArray doesn't support GrowthStrategy in constructor, ignoring for now
end;

constructor TDeque.Create(aCapacity: SizeUInt; aGrowStrategy: TGrowthStrategy; aAllocator: TAllocator; aData: Pointer);
begin
  inherited Create(aAllocator, aData);
  FArray := TArray.Create(aCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
  // Note: TArray doesn't support GrowthStrategy in constructor, ignoring for now
end;

constructor TDeque.Create(const aSrc: array of T);
begin
  Create(aSrc, GetRtlAllocator());
end;

constructor TDeque.Create(const aSrc: array of T; aAllocator: TAllocator);
var
  LCapacity: SizeUInt;
begin
  LCapacity := Max(SizeUInt(Length(aSrc)), SizeUInt(16));
  inherited Create(aAllocator, nil);
  FArray := TArray.Create(LCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
  if Length(aSrc) > 0 then
    PushBack(aSrc);
end;

constructor TDeque.Create(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer);
var
  LCapacity: SizeUInt;
begin
  LCapacity := Max(SizeUInt(Length(aSrc)), SizeUInt(16));
  inherited Create(aAllocator, aData);
  FArray := TArray.Create(LCapacity, aAllocator);
  FHead := 0;
  FCount := 0;
  if Length(aSrc) > 0 then
    PushBack(aSrc);
end;

destructor TDeque.Destroy;
begin
  FArray.Free;
  inherited Destroy;
end;

{ ICollection 接口实现 }

function TDeque.PtrIter: TPtrIter;
begin
  Result.Init(Self, @DoIterGetCurrent, @DoIterMoveNext, @DoIterMovePrev, nil);
end;

function TDeque.GetCount: SizeUInt;
begin
  Result := FCount;
end;

procedure TDeque.Clear;
begin
  FCount := 0;
  FHead := 0;
  // 不需要清理FArray，因为我们只是重置逻辑指针
end;

procedure TDeque.SerializeToArrayBuffer(aDst: Pointer; aCount: SizeUInt);
var
  i: SizeUInt;
  LDstPtr: PByte;
begin
  if aDst = nil then
    raise EArgumentNil.Create('TDeque.SerializeToArrayBuffer: aDst is nil');

  if aCount > FCount then
    raise EOutOfRange.Create('TDeque.SerializeToArrayBuffer: aCount exceeds collection size');

  if IsOverlap(aDst, aCount) then
    raise EInvalidArgument.Create('TDeque.SerializeToArrayBuffer: destination overlaps with collection memory');

  LDstPtr := PByte(aDst);
  for i := 0 to aCount - 1 do
  begin
    Move(FArray.Ptr[FHead + i]^, LDstPtr^, FElementSizeCache);
    Inc(LDstPtr, FElementSizeCache);
  end;
end;

procedure TDeque.AppendUnChecked(const aSrc: Pointer; aElementCount: SizeUInt);
var
  i: SizeUInt;
  LSrcPtr: PByte;
begin
  if aElementCount = 0 then
    Exit;

  EnsureCapacity(FCount + aElementCount);

  LSrcPtr := PByte(aSrc);
  for i := 0 to aElementCount - 1 do
  begin
    Move(LSrcPtr^, FArray.Ptr[FHead + FCount + i]^, FElementSizeCache);
    Inc(LSrcPtr, FElementSizeCache);
  end;

  Inc(FCount, aElementCount);
end;

procedure TDeque.AppendToUnChecked(const aDst: TCollection);
begin
  if FCount > 0 then
    aDst.AppendUnChecked(FArray.Ptr[FHead], FCount);
end;

{ IQueue<T> 接口实现 }

procedure TDeque.Enqueue(const aElement: T);
begin
  PushBack(aElement);
end;

procedure TDeque.Push(const aElement: T);
begin
  PushBack(aElement);
end;

function TDeque.Dequeue: T;
begin
  Result := PopFront;
end;

function TDeque.Pop: T;
begin
  Result := PopFront;
end;

function TDeque.Dequeue(var aElement: T): Boolean;
begin
  Result := PopFront(aElement);
end;

function TDeque.Pop(var aElement: T): Boolean;
begin
  Result := PopFront(aElement);
end;

function TDeque.Peek: T;
begin
  Result := PeekFront;
end;

function TDeque.Peek(var aElement: T): Boolean;
begin
  Result := PeekFront(aElement);
end;

{ IDeque<T> 接口实现 }

procedure TDeque.PushFront(const aElement: T);
begin
  if FHead = 0 then
  begin
    // 需要在数组前面腾出空间
    ShiftElementsRight(0, 1);
    Inc(FHead);
  end;

  Dec(FHead);
  FArray.PutUnChecked(FHead, aElement);
  Inc(FCount);
end;

procedure TDeque.PushFront(const aElements: array of T);
var
  i: Integer;
begin
  // 从后往前添加，保持元素顺序
  for i := High(aElements) downto Low(aElements) do
    PushFront(aElements[i]);
end;

procedure TDeque.PushFront(const aSrc: Pointer; aElementCount: SizeUInt);
var
  i: Integer;
  LSrcPtr: PByte;
begin
  if aSrc = nil then
  begin
    if aElementCount > 0 then
      raise EArgumentNil.Create('TDeque.PushFront: aSrc is nil but aElementCount > 0');
    Exit;
  end;

  if aElementCount = 0 then
    Exit;

  if IsOverlap(aSrc, aElementCount) then
    raise EInvalidArgument.Create('TDeque.PushFront: source overlaps with collection memory');

  // 从后往前添加，保持元素顺序
  LSrcPtr := PByte(aSrc) + (aElementCount - 1) * FElementSizeCache;
  for i := Integer(aElementCount) - 1 downto 0 do
  begin
    PushFront(PElement(LSrcPtr)^);
    Dec(LSrcPtr, FElementSizeCache);
  end;
end;

procedure TDeque.PushBack(const aElement: T);
begin
  EnsureCapacity(FCount + 1);
  FArray.PutUnChecked(FHead + FCount, aElement);
  Inc(FCount);
end;

procedure TDeque.PushBack(const aElements: array of T);
var
  i: Integer;
begin
  for i := Low(aElements) to High(aElements) do
    PushBack(aElements[i]);
end;

procedure TDeque.PushBack(const aSrc: Pointer; aElementCount: SizeUInt);
var
  i: SizeUInt;
  LSrcPtr: PByte;
begin
  if aSrc = nil then
  begin
    if aElementCount > 0 then
      raise EArgumentNil.Create('TDeque.PushBack: aSrc is nil but aElementCount > 0');
    Exit;
  end;

  if aElementCount = 0 then
    Exit;

  if IsOverlap(aSrc, aElementCount) then
    raise EInvalidArgument.Create('TDeque.PushBack: source overlaps with collection memory');

  LSrcPtr := PByte(aSrc);
  for i := 0 to aElementCount - 1 do
  begin
    PushBack(PElement(LSrcPtr)^);
    Inc(LSrcPtr, FElementSizeCache);
  end;
end;

function TDeque.PopFront: T;
begin
  if FCount = 0 then
    raise EOutOfRange.Create('TDeque.PopFront: deque is empty');

  if not PopFront(Result) then
    raise EOutOfRange.Create('TDeque.PopFront: deque is empty');
end;

function TDeque.PopFront(var aElement: T): Boolean;
begin
  if FCount = 0 then
  begin
    Result := False;
    Exit;
  end;

  aElement := FArray.GetUnChecked(FHead);
  Inc(FHead);
  Dec(FCount);
  Result := True;
end;

function TDeque.PopBack: T;
begin
  if FCount = 0 then
    raise EOutOfRange.Create('TDeque.PopBack: deque is empty');

  if not PopBack(Result) then
    raise EOutOfRange.Create('TDeque.PopBack: deque is empty');
end;

function TDeque.PopBack(var aElement: T): Boolean;
begin
  if FCount = 0 then
  begin
    Result := False;
    Exit;
  end;

  Dec(FCount);
  aElement := FArray.GetUnChecked(FHead + FCount);
  Result := True;
end;

function TDeque.PeekFront: T;
begin
  if FCount = 0 then
    raise EOutOfRange.Create('TDeque.PeekFront: deque is empty');

  if not PeekFront(Result) then
    raise EOutOfRange.Create('TDeque.PeekFront: deque is empty');
end;

function TDeque.PeekFront(var aElement: T): Boolean;
begin
  if FCount = 0 then
  begin
    Result := False;
    Exit;
  end;

  aElement := FArray.GetUnChecked(FHead);
  Result := True;
end;

function TDeque.PeekBack: T;
begin
  if FCount = 0 then
    raise EOutOfRange.Create('TDeque.PeekBack: deque is empty');

  if not PeekBack(Result) then
    raise EOutOfRange.Create('TDeque.PeekBack: deque is empty');
end;

function TDeque.PeekBack(var aElement: T): Boolean;
begin
  if FCount = 0 then
  begin
    Result := False;
    Exit;
  end;

  aElement := FArray.GetUnChecked(FHead + FCount - 1);
  Result := True;
end;

end.
