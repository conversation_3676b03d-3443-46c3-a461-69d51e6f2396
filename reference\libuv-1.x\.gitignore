*.swp
*.[oa]
*.l[oa]
*.opensdf
*.orig
*.pyc
*.sdf
*.suo
.vs/
.vscode/
*.VC.db
*.VC.opendb
core
.cache
vgcore.*
.buildstamp
.dirstamp
.deps/
/.libs/
/aclocal.m4
/ar-lib
/autom4te.cache/
/compile
/config.guess
/config.log
/config.status
/config.sub
/configure
/depcomp
/install-sh
/libtool
/libuv.a
/libuv.dylib
/libuv.pc
/libuv.so
/ltmain.sh
/missing
/test-driver
Makefile
Makefile.in

/build/

/test/.libs/
/test/run-tests
/test/run-tests.exe
/test/run-tests.dSYM
/test/run-benchmarks
/test/run-benchmarks.exe
/test/run-benchmarks.dSYM
test_file_*

*.sln
*.sln.cache
*.ncb
*.vcproj
*.vcproj*.user
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
_UpgradeReport_Files/
UpgradeLog*.XML
Debug
Release
ipch

# sphinx generated files
/docs/build/

# Clion / IntelliJ project files
/.idea/
cmake-build-debug/

*.xcodeproj
*.xcworkspace

# make dist output
libuv-*.tar.*
/dist.libuv.org/
/libuv-release-tool/
