unit fafafa.core.thread.future;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes, SyncObjs,
  fafafa.core.base,
  fafafa.core.thread.types;

type
  // Future实现类
  generic TFuture<T> = class(TInterfacedObject, specialize IFuture<T>)
  private
    FState: TFutureState;
    FValue: T;
    FException: Exception;
    FEvent: TEvent;
    FLock: TCriticalSection;
    FCancelled: Boolean;
    
    procedure SetState(aNewState: TFutureState);
    procedure CheckNotCancelled;
  public
    constructor Create;
    destructor Destroy; override;
    
    // IFuture<T> 接口实现
    function GetState: TFutureState;
    function IsDone: Boolean;
    function IsCancelled: Boolean;
    function IsCompleted: Boolean;
    function IsFailed: Boolean;
    
    function GetValue(aTimeoutMs: Cardinal = INFINITE): T;
    function TryGetValue(out aValue: T; aTimeoutMs: Cardinal = 0): Boolean;
    function GetException: Exception;
    
    procedure Cancel;
    
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Then<TResult>(const aFunc: specialize TFunc<T, TResult>): specialize IFuture<TResult>;
    function Catch(const aHandler: TExceptionRefHandler): specialize IFuture<T>;
    function Finally(const aAction: TAction): specialize IFuture<T>;
    {$ENDIF}
    
    // 内部方法，供Promise使用
    procedure InternalSetValue(const aValue: T);
    procedure InternalSetException(aException: Exception);
  end;

  // Promise实现类
  generic TPromise<T> = class(TInterfacedObject, specialize IPromise<T>)
  private
    FFuture: specialize TFuture<T>;
    FFulfilled: Boolean;
    FLock: TCriticalSection;
  public
    constructor Create;
    destructor Destroy; override;
    
    // IPromise<T> 接口实现
    procedure SetValue(const aValue: T);
    procedure SetException(aException: Exception);
    function GetFuture: specialize IFuture<T>;
    function IsFulfilled: Boolean;
  end;

  // 线程安全队列实现
  generic TThreadSafeQueue<T> = class(TInterfacedObject, specialize IThreadSafeQueue<T>)
  private
    FQueue: TList;
    FLock: TCriticalSection;
    FEvent: TEvent;
    FShutdown: Boolean;
  public
    constructor Create;
    destructor Destroy; override;

    procedure Enqueue(const aItem: T);
    function TryDequeue(out aItem: T; aTimeoutMs: Cardinal = 0): Boolean;
    function GetCount: Integer;
    function IsEmpty: Boolean;
    procedure Clear;
    procedure Shutdown;
  end;

// 工厂函数实现
generic function CreateFuture<T>: specialize IFuture<T>;
generic function CreatePromise<T>: specialize IPromise<T>;
generic function CreateThreadSafeQueue<T>: specialize IThreadSafeQueue<T>;

implementation

// TFuture<T> 实现

constructor TFuture.Create;
begin
  inherited Create;
  FState := fsWaiting;
  FException := nil;
  FCancelled := False;
  FEvent := TEvent.Create(nil, True, False, '');
  FLock := TCriticalSection.Create;
end;

destructor TFuture.Destroy;
begin
  FreeAndNil(FEvent);
  FreeAndNil(FLock);
  FreeAndNil(FException);
  inherited Destroy;
end;

procedure TFuture.SetState(aNewState: TFutureState);
begin
  FLock.Enter;
  try
    if FState = fsWaiting then
    begin
      FState := aNewState;
      FEvent.SetEvent;
    end;
  finally
    FLock.Leave;
  end;
end;

procedure TFuture.CheckNotCancelled;
begin
  if FCancelled then
    raise EFutureCancelled.Create;
end;

function TFuture.GetState: TFutureState;
begin
  FLock.Enter;
  try
    Result := FState;
  finally
    FLock.Leave;
  end;
end;

function TFuture.IsDone: Boolean;
begin
  Result := GetState <> fsWaiting;
end;

function TFuture.IsCancelled: Boolean;
begin
  Result := GetState = fsCancelled;
end;

function TFuture.IsCompleted: Boolean;
begin
  Result := GetState = fsCompleted;
end;

function TFuture.IsFailed: Boolean;
begin
  Result := GetState = fsFailed;
end;

function TFuture.GetValue(aTimeoutMs: Cardinal): T;
begin
  if not TryGetValue(Result, aTimeoutMs) then
  begin
    if IsCancelled then
      raise EFutureCancelled.Create
    else if IsFailed then
      raise FException
    else
      raise EFutureTimeout.Create;
  end;
end;

function TFuture.TryGetValue(out aValue: T; aTimeoutMs: Cardinal): Boolean;
var
  WaitResult: TWaitResult;
begin
  Result := False;
  
  // 如果已经完成，直接返回结果
  if IsDone then
  begin
    FLock.Enter;
    try
      case FState of
        fsCompleted:
          begin
            aValue := FValue;
            Result := True;
          end;
        fsCancelled:
          raise EFutureCancelled.Create;
        fsFailed:
          raise FException;
      end;
    finally
      FLock.Leave;
    end;
    Exit;
  end;
  
  // 等待完成
  WaitResult := FEvent.WaitFor(aTimeoutMs);
  case WaitResult of
    wrSignaled:
      begin
        FLock.Enter;
        try
          case FState of
            fsCompleted:
              begin
                aValue := FValue;
                Result := True;
              end;
            fsCancelled:
              raise EFutureCancelled.Create;
            fsFailed:
              raise FException;
          end;
        finally
          FLock.Leave;
        end;
      end;
    wrTimeout:
      Result := False;
    wrError:
      raise EFuture.Create('Error waiting for future completion');
  end;
end;

function TFuture.GetException: Exception;
begin
  FLock.Enter;
  try
    Result := FException;
  finally
    FLock.Leave;
  end;
end;

procedure TFuture.Cancel;
begin
  FLock.Enter;
  try
    if FState = fsWaiting then
    begin
      FCancelled := True;
      SetState(fsCancelled);
    end;
  finally
    FLock.Leave;
  end;
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TFuture.Then<TResult>(const aFunc: specialize TFunc<T, TResult>): specialize IFuture<TResult>;
var
  ResultFuture: specialize TFuture<TResult>;
begin
  ResultFuture := specialize TFuture<TResult>.Create;
  Result := ResultFuture;
  
  // 在新线程中执行链式操作
  TThread.CreateAnonymousThread(
    procedure
    begin
      try
        var Value := GetValue;
        var NewValue := aFunc(Value);
        ResultFuture.InternalSetValue(NewValue);
      except
        on E: Exception do
          ResultFuture.InternalSetException(E);
      end;
    end
  ).Start;
end;

function TFuture.Catch(const aHandler: TExceptionRefHandler): specialize IFuture<T>;
var
  ResultFuture: specialize TFuture<T>;
begin
  ResultFuture := specialize TFuture<T>.Create;
  Result := ResultFuture;
  
  TThread.CreateAnonymousThread(
    procedure
    begin
      try
        var Value := GetValue;
        ResultFuture.InternalSetValue(Value);
      except
        on E: Exception do
        begin
          aHandler(E);
          ResultFuture.InternalSetException(E);
        end;
      end;
    end
  ).Start;
end;

function TFuture.Finally(const aAction: TAction): specialize IFuture<T>;
var
  ResultFuture: specialize TFuture<T>;
begin
  ResultFuture := specialize TFuture<T>.Create;
  Result := ResultFuture;
  
  TThread.CreateAnonymousThread(
    procedure
    begin
      try
        var Value := GetValue;
        aAction();
        ResultFuture.InternalSetValue(Value);
      except
        on E: Exception do
        begin
          aAction();
          ResultFuture.InternalSetException(E);
        end;
      end;
    end
  ).Start;
end;
{$ENDIF}

procedure TFuture.InternalSetValue(const aValue: T);
begin
  FLock.Enter;
  try
    if FState = fsWaiting then
    begin
      FValue := aValue;
      SetState(fsCompleted);
    end;
  finally
    FLock.Leave;
  end;
end;

procedure TFuture.InternalSetException(aException: Exception);
begin
  FLock.Enter;
  try
    if FState = fsWaiting then
    begin
      FException := aException;
      SetState(fsFailed);
    end;
  finally
    FLock.Leave;
  end;
end;

// TPromise<T> 实现

constructor TPromise.Create;
begin
  inherited Create;
  FFuture := specialize TFuture<T>.Create;
  FFulfilled := False;
  FLock := TCriticalSection.Create;
end;

destructor TPromise.Destroy;
begin
  FreeAndNil(FLock);
  inherited Destroy;
end;

procedure TPromise.SetValue(const aValue: T);
begin
  FLock.Enter;
  try
    if not FFulfilled then
    begin
      FFulfilled := True;
      FFuture.InternalSetValue(aValue);
    end;
  finally
    FLock.Leave;
  end;
end;

procedure TPromise.SetException(aException: Exception);
begin
  FLock.Enter;
  try
    if not FFulfilled then
    begin
      FFulfilled := True;
      FFuture.InternalSetException(aException);
    end;
  finally
    FLock.Leave;
  end;
end;

function TPromise.GetFuture: specialize IFuture<T>;
begin
  Result := FFuture;
end;

function TPromise.IsFulfilled: Boolean;
begin
  FLock.Enter;
  try
    Result := FFulfilled;
  finally
    FLock.Leave;
  end;
end;

// TThreadSafeQueue<T> 实现

constructor TThreadSafeQueue.Create;
begin
  inherited Create;
  FQueue := TList.Create;
  FLock := TCriticalSection.Create;
  FEvent := TEvent.Create(nil, False, False, '');
  FShutdown := False;
end;

destructor TThreadSafeQueue.Destroy;
begin
  Clear;
  FreeAndNil(FQueue);
  FreeAndNil(FLock);
  FreeAndNil(FEvent);
  inherited Destroy;
end;

procedure TThreadSafeQueue.Enqueue(const aItem: T);
var
  ItemPtr: Pointer;
begin
  if FShutdown then
    Exit;

  GetMem(ItemPtr, SizeOf(T));
  T(ItemPtr^) := aItem;

  FLock.Enter;
  try
    FQueue.Add(ItemPtr);
    FEvent.SetEvent;
  finally
    FLock.Leave;
  end;
end;

function TThreadSafeQueue.TryDequeue(out aItem: T; aTimeoutMs: Cardinal): Boolean;
var
  ItemPtr: Pointer;
  WaitResult: TWaitResult;
begin
  Result := False;

  FLock.Enter;
  try
    if FQueue.Count > 0 then
    begin
      ItemPtr := FQueue[0];
      FQueue.Delete(0);
      aItem := T(ItemPtr^);
      FreeMem(ItemPtr);
      Result := True;
      Exit;
    end;
  finally
    FLock.Leave;
  end;

  if FShutdown then
    Exit;

  // 等待新项目
  WaitResult := FEvent.WaitFor(aTimeoutMs);
  if WaitResult = wrSignaled then
  begin
    FLock.Enter;
    try
      if (FQueue.Count > 0) and not FShutdown then
      begin
        ItemPtr := FQueue[0];
        FQueue.Delete(0);
        aItem := T(ItemPtr^);
        FreeMem(ItemPtr);
        Result := True;
      end;

      // 如果队列还有项目，保持事件信号
      if FQueue.Count > 0 then
        FEvent.SetEvent;
    finally
      FLock.Leave;
    end;
  end;
end;

function TThreadSafeQueue.GetCount: Integer;
begin
  FLock.Enter;
  try
    Result := FQueue.Count;
  finally
    FLock.Leave;
  end;
end;

function TThreadSafeQueue.IsEmpty: Boolean;
begin
  Result := GetCount = 0;
end;

procedure TThreadSafeQueue.Clear;
var
  i: Integer;
  ItemPtr: Pointer;
begin
  FLock.Enter;
  try
    for i := 0 to FQueue.Count - 1 do
    begin
      ItemPtr := FQueue[i];
      FreeMem(ItemPtr);
    end;
    FQueue.Clear;
  finally
    FLock.Leave;
  end;
end;

procedure TThreadSafeQueue.Shutdown;
begin
  FLock.Enter;
  try
    FShutdown := True;
    FEvent.SetEvent;
  finally
    FLock.Leave;
  end;
end;

// 工厂函数实现

generic function CreateFuture<T>: specialize IFuture<T>;
begin
  Result := specialize TFuture<T>.Create;
end;

generic function CreatePromise<T>: specialize IPromise<T>;
begin
  Result := specialize TPromise<T>.Create;
end;

generic function CreateThreadSafeQueue<T>: specialize IThreadSafeQueue<T>;
begin
  Result := specialize TThreadSafeQueue<T>.Create;
end;

end.
