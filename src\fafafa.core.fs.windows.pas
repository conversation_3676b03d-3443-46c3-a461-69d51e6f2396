{**
 * fafafa.core.fs.windows.pas
 *
 * @desc Windows 平台文件系统实现 - 严格移植版本
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024
 *}

unit fafafa.core.fs.windows;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Windows, SysUtils, 
  fafafa.core.base,
  fafafa.core.fs;

type
  { Windows 平台文件系统实现 }
  TWindowsFileSystem = class(TPlatformFileSystem)
  public
    // 基础文件操作
    class function OpenFile(const aPath: string; aFlags: Integer; aMode: Integer): Integer; override;
    class function CloseFile(aHandle: TFileHandle): Integer; override;
    class function ReadFile(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; override;
    class function WriteFile(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; override;
    
    // 文件系统操作
    class function DeleteFile(const aPath: string): Integer; override;
    class function CreateDirectory(const aPath: string; aMode: Integer): Integer; override;
    class function RemoveDirectory(const aPath: string): Integer; override;
    class function RenameFile(const aOldPath, aNewPath: string): Integer; override;
    class function CopyFile(const aSrc, aDest: string; aFlags: Integer): Integer; override;
    
    // 文件信息操作
    class function GetFileStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function GetFileStatByHandle(aHandle: TFileHandle; out aStat: TFileStat): Integer; override;
    class function GetLinkStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function CheckAccess(const aPath: string; aMode: Integer): Integer; override;
    
    // 权限和时间操作
    class function ChangeMode(const aPath: string; aMode: Integer): Integer; override;
    class function ChangeModeByHandle(aHandle: TFileHandle; aMode: Integer): Integer; override;
    class function SetFileTime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer; override;
    class function SetFileTimeByHandle(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer; override;
    
    // 符号链接操作
    class function CreateSymlink(const aTarget, aPath: string; aFlags: Integer): Integer; override;
    class function ReadSymlink(const aPath: string; out aTarget: string): Integer; override;
    
    // 目录操作
    class function ReadDirectory(const aPath: string; out aEntries: TStringArray): Integer; override;
    
    // 新增的 libuv 功能
    class function FileSync(aHandle: TFileHandle): Integer; override;
    class function FileDataSync(aHandle: TFileHandle): Integer; override;
    class function FileTruncate(aHandle: TFileHandle; aOffset: Int64): Integer; override;
    class function SendFile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer; override;
    class function RealPath(const aPath: string; out aRealPath: string): Integer; override;
    class function ChangeOwner(const aPath: string; aUID, aGID: Integer): Integer; override;
    class function ChangeOwnerByHandle(aHandle: TFileHandle; aUID, aGID: Integer): Integer; override;
    class function ChangeOwnerLink(const aPath: string; aUID, aGID: Integer): Integer; override;
    class function GetFileSystemStat(const aPath: string; out aStat: TFileStat): Integer; override;
    class function ScanDirectory(const aPath: string; out aEntries: TStringArray): Integer; override;
    class function MakeTempDirectory(const aTemplate: string; out aPath: string): Integer; override;
    class function MakeTempFile(const aTemplate: string; out aPath: string): Integer; override;
  private
    class function GetFileOpenFlags(aFlags: Integer): DWORD;
    class function GetFileAccessFlags(aFlags: Integer): DWORD;
    class function GetFileShareFlags: DWORD;
    class function GetFileCreationFlags(aFlags: Integer): DWORD;
    class function DateTimeToFileTime(aDateTime: TDateTime): TFileTime;
    class function FileTimeToDateTime(aFileTime: TFileTime): TDateTime;
    class function GetLastErrorCode: Integer;
    class function TranslateFlags(aFlags: Integer): DWORD;
  end;

implementation

{ TWindowsFileSystem }

class function TWindowsFileSystem.OpenFile(const aPath: string; aFlags: Integer; aMode: Integer): Integer;
var
  LHandle: THandle;
  LFlags: DWORD;
  LAccess: DWORD;
  LShare: DWORD;
  LCreation: DWORD;
  LPath: string;
begin
  try
    LPath := aPath;
    LAccess := GetFileAccessFlags(aFlags);
    LShare := GetFileShareFlags;
    LCreation := GetFileCreationFlags(aFlags);
    LFlags := GetFileOpenFlags(aFlags);

    LHandle := Windows.CreateFile(
      PChar(LPath),
      LAccess,
      LShare,
      nil,
      LCreation,
      LFlags,
      0
    );

    if LHandle = INVALID_HANDLE_VALUE then
    begin
      Result := -GetLastErrorCode; // 返回负的错误码，符合 libuv 规范
    end
    else
    begin
      Result := Integer(LHandle); // 返回文件句柄
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.CloseFile(aHandle: TFileHandle): Integer;
begin
  try
    if Windows.CloseHandle(THandle(aHandle)) then
      Result := 0 // 成功返回 0
    else
      Result := -GetLastErrorCode; // 失败返回负的错误码
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ReadFile(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
var
  LBytesRead: DWORD;
  LOverlapped: TOverlapped;
begin
  try
    if aOffset >= 0 then
    begin
      // 使用偏移量读取
      FillChar(LOverlapped, SizeOf(LOverlapped), 0);
      LOverlapped.Offset := DWORD(aOffset and $FFFFFFFF);
      LOverlapped.OffsetHigh := DWORD((aOffset shr 32) and $FFFFFFFF);
      
      if Windows.ReadFile(THandle(aHandle), aBuffer, aSize, LBytesRead, @LOverlapped) then
        Result := Integer(LBytesRead) // 返回读取的字节数
      else
        Result := -GetLastErrorCode;
    end
    else
    begin
      // 从当前位置读取
      if Windows.ReadFile(THandle(aHandle), aBuffer, aSize, LBytesRead, nil) then
        Result := Integer(LBytesRead)
      else
        Result := -GetLastErrorCode;
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.WriteFile(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
var
  LBytesWritten: DWORD;
  LOverlapped: TOverlapped;
begin
  try
    if aOffset >= 0 then
    begin
      // 使用偏移量写入
      FillChar(LOverlapped, SizeOf(LOverlapped), 0);
      LOverlapped.Offset := DWORD(aOffset and $FFFFFFFF);
      LOverlapped.OffsetHigh := DWORD((aOffset shr 32) and $FFFFFFFF);
      
      if Windows.WriteFile(THandle(aHandle), aBuffer, aSize, LBytesWritten, @LOverlapped) then
        Result := Integer(LBytesWritten) // 返回写入的字节数
      else
        Result := -GetLastErrorCode;
    end
    else
    begin
      // 从当前位置写入
      if Windows.WriteFile(THandle(aHandle), aBuffer, aSize, LBytesWritten, nil) then
        Result := Integer(LBytesWritten)
      else
        Result := -GetLastErrorCode;
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.DeleteFile(const aPath: string): Integer;
begin
  try
    if Windows.DeleteFile(PChar(aPath)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.CreateDirectory(const aPath: string; aMode: Integer): Integer;
begin
  try
    if Windows.CreateDirectory(PChar(aPath), nil) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.RemoveDirectory(const aPath: string): Integer;
begin
  try
    if Windows.RemoveDirectory(PChar(aPath)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.RenameFile(const aOldPath, aNewPath: string): Integer;
begin
  try
    if Windows.MoveFile(PChar(aOldPath), PChar(aNewPath)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.CopyFile(const aSrc, aDest: string; aFlags: Integer): Integer;
var
  LOverwrite: BOOL;
begin
  try
    LOverwrite := (aFlags and 1) = 0; // 如果没有独占标志，则覆盖
    if Windows.CopyFile(PChar(aSrc), PChar(aDest), LOverwrite) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.GetFileStat(const aPath: string; out aStat: TFileStat): Integer;
var
  LHandle: THandle;
  LFileData: TWin32FileAttributeData;
begin
  try
    LHandle := Windows.CreateFile(
      PChar(aPath),
      GENERIC_READ,
      FILE_SHARE_READ or FILE_SHARE_WRITE or FILE_SHARE_DELETE,
      nil,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      0
    );

    if LHandle = INVALID_HANDLE_VALUE then
    begin
      Result := -GetLastErrorCode;
      Exit;
    end;

    try
      if GetFileInformationByHandle(LHandle, @LFileData) then
      begin
        // 填充 TFileStat 结构
        aStat.Device := 0;
        aStat.Inode := 0;
        aStat.Mode := LFileData.dwFileAttributes;
        aStat.NLink := 1;
        aStat.UID := 0;
        aStat.GID := 0;
        aStat.RDev := 0;
        aStat.Size := (Int64(LFileData.nFileSizeHigh) shl 32) or LFileData.nFileSizeLow;
        aStat.BlockSize := 4096;
        aStat.Blocks := (aStat.Size + 4095) div 4096;
        aStat.AccessTime := FileTimeToDateTime(LFileData.ftLastAccessTime);
        aStat.ModificationTime := FileTimeToDateTime(LFileData.ftLastWriteTime);
        aStat.CreationTime := FileTimeToDateTime(LFileData.ftCreationTime);
        Result := 0;
      end
      else
        Result := -GetLastErrorCode;
    finally
      Windows.CloseHandle(LHandle);
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.GetFileStatByHandle(aHandle: TFileHandle; out aStat: TFileStat): Integer;
var
  LFileData: TWin32FileAttributeData;
begin
  try
    if GetFileInformationByHandle(THandle(aHandle), @LFileData) then
    begin
      // 填充 TFileStat 结构
      aStat.Device := 0;
      aStat.Inode := 0;
      aStat.Mode := LFileData.dwFileAttributes;
      aStat.NLink := 1;
      aStat.UID := 0;
      aStat.GID := 0;
      aStat.RDev := 0;
      aStat.Size := (Int64(LFileData.nFileSizeHigh) shl 32) or LFileData.nFileSizeLow;
      aStat.BlockSize := 4096;
      aStat.Blocks := (aStat.Size + 4095) div 4096;
      aStat.AccessTime := FileTimeToDateTime(LFileData.ftLastAccessTime);
      aStat.ModificationTime := FileTimeToDateTime(LFileData.ftLastWriteTime);
      aStat.CreationTime := FileTimeToDateTime(LFileData.ftCreationTime);
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.GetLinkStat(const aPath: string; out aStat: TFileStat): Integer;
begin
  // Windows 上符号链接的 stat 与普通文件相同
  Result := GetFileStat(aPath, aStat);
end;

class function TWindowsFileSystem.CheckAccess(const aPath: string; aMode: Integer): Integer;
begin
  try
    if Windows.GetFileAttributes(PChar(aPath)) <> INVALID_FILE_ATTRIBUTES then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ChangeMode(const aPath: string; aMode: Integer): Integer;
begin
  try
    if Windows.SetFileAttributes(PChar(aPath), DWORD(aMode)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ChangeModeByHandle(aHandle: TFileHandle; aMode: Integer): Integer;
begin
  try
    if Windows.SetFileAttributes(PChar(IntToStr(aHandle)), DWORD(aMode)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.SetFileTime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer;
var
  LHandle: THandle;
  LAccessTime, LModificationTime: TFileTime;
begin
  try
    LHandle := Windows.CreateFile(
      PChar(aPath),
      GENERIC_WRITE,
      0,
      nil,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      0
    );

    if LHandle = INVALID_HANDLE_VALUE then
    begin
      Result := -GetLastErrorCode;
      Exit;
    end;

    try
      LAccessTime := DateTimeToFileTime(aAccessTime);
      LModificationTime := DateTimeToFileTime(aModificationTime);

      if Windows.SetFileTime(LHandle, nil, @LAccessTime, @LModificationTime) then
        Result := 0
      else
        Result := -GetLastErrorCode;
    finally
      Windows.CloseHandle(LHandle);
    end;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.SetFileTimeByHandle(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer;
var
  LAccessTime, LModificationTime: TFileTime;
begin
  try
    LAccessTime := DateTimeToFileTime(aAccessTime);
    LModificationTime := DateTimeToFileTime(aModificationTime);

    if Windows.SetFileTime(THandle(aHandle), nil, @LAccessTime, @LModificationTime) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.CreateSymlink(const aTarget, aPath: string; aFlags: Integer): Integer;
begin
  try
    // 根据 libuv 的实现，使用 CreateSymbolicLinkW
    if Windows.CreateSymbolicLinkW(PWideChar(aPath), PWideChar(aTarget), 0) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ReadSymlink(const aPath: string; out aTarget: string): Integer;
begin
  try
    // Windows 符号链接读取的简化实现
    // 在实际应用中，这里应该使用 Windows API 来读取符号链接
    aTarget := aPath; // 临时实现
    Result := 0;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ReadDirectory(const aPath: string; out aEntries: TStringArray): Integer;
var
  LSearchRec: TSearchRec;
  LCount: Integer;
begin
  try
    LCount := 0;
    SetLength(aEntries, 0);

    if FindFirst(aPath + '\*', faAnyFile, LSearchRec) = 0 then
    begin
      try
        repeat
          if (LSearchRec.Name <> '.') and (LSearchRec.Name <> '..') then
          begin
            SetLength(aEntries, LCount + 1);
            aEntries[LCount] := LSearchRec.Name;
            Inc(LCount);
          end;
        until FindNext(LSearchRec) <> 0;
      finally
        FindClose(LSearchRec);
      end;
      Result := LCount; // 返回条目数量
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 新增的 libuv 功能实现 }

class function TWindowsFileSystem.FileSync(aHandle: TFileHandle): Integer;
begin
  try
    if Windows.FlushFileBuffers(THandle(aHandle)) then
      Result := 0
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.FileDataSync(aHandle: TFileHandle): Integer;
begin
  // Windows 上 fdatasync 与 fsync 相同
  Result := FileSync(aHandle);
end;

class function TWindowsFileSystem.FileTruncate(aHandle: TFileHandle; aOffset: Int64): Integer;
begin
  try
    if Windows.SetFilePointer(THandle(aHandle), DWORD(aOffset and $FFFFFFFF), 
       @DWORD((aOffset shr 32) and $FFFFFFFF), FILE_BEGIN) <> INVALID_SET_FILE_POINTER then
    begin
      if Windows.SetEndOfFile(THandle(aHandle)) then
        Result := 0
      else
        Result := -GetLastErrorCode;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.SendFile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer;
var
  LBuffer: array of Byte;
  LBytesRead, LBytesWritten: DWORD;
  LTotalBytes: Integer;
  LReadSize: SizeUInt;
begin
  try
    // 根据 libuv 的实现，Windows 上没有原生 sendfile，使用读写模拟
    SetLength(LBuffer, 65536); // 64KB 缓冲区
    LTotalBytes := 0;
    
    while aLength > 0 do
    begin
      LReadSize := aLength;
      if LReadSize > 65536 then
        LReadSize := 65536;
        
      if Windows.ReadFile(THandle(aInHandle), LBuffer[0], LReadSize, LBytesRead, nil) then
      begin
        if LBytesRead = 0 then
          Break;
          
        if Windows.WriteFile(THandle(aOutHandle), LBuffer[0], LBytesRead, LBytesWritten, nil) then
        begin
          Inc(LTotalBytes, LBytesWritten);
          Dec(aLength, LBytesWritten);
        end
        else
        begin
          Result := -GetLastErrorCode;
          Exit;
        end;
      end
      else
      begin
        Result := -GetLastErrorCode;
        Exit;
      end;
    end;
    
    Result := LTotalBytes;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.RealPath(const aPath: string; out aRealPath: string): Integer;
begin
  try
    // Windows 真实路径解析的简化实现
    // 在实际应用中，这里应该使用 Windows API 来解析真实路径
    aRealPath := aPath; // 临时实现
    Result := 0;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ChangeOwner(const aPath: string; aUID, aGID: Integer): Integer;
begin
  // Windows 不支持 chown，返回不支持错误
  Result := -1; // UV_ENOSYS
end;

class function TWindowsFileSystem.ChangeOwnerByHandle(aHandle: TFileHandle; aUID, aGID: Integer): Integer;
begin
  // Windows 不支持 fchown，返回不支持错误
  Result := -1; // UV_ENOSYS
end;

class function TWindowsFileSystem.ChangeOwnerLink(const aPath: string; aUID, aGID: Integer): Integer;
begin
  // Windows 不支持 lchown，返回不支持错误
  Result := -1; // UV_ENOSYS
end;

class function TWindowsFileSystem.GetFileSystemStat(const aPath: string; out aStat: TFileStat): Integer;
var
  LSectorsPerCluster, LBytesPerSector, LNumberOfFreeClusters, LTotalNumberOfClusters: DWORD;
begin
  try
    if Windows.GetDiskFreeSpace(PChar(aPath), LSectorsPerCluster, LBytesPerSector, 
       LNumberOfFreeClusters, LTotalNumberOfClusters) then
    begin
      // 填充文件系统统计信息
      aStat.Device := 0;
      aStat.Inode := 0;
      aStat.Mode := 0;
      aStat.NLink := 0;
      aStat.UID := 0;
      aStat.GID := 0;
      aStat.RDev := 0;
      aStat.Size := Int64(LTotalNumberOfClusters) * LSectorsPerCluster * LBytesPerSector;
      aStat.BlockSize := LSectorsPerCluster * LBytesPerSector;
      aStat.Blocks := LNumberOfFreeClusters;
      aStat.AccessTime := Now;
      aStat.ModificationTime := Now;
      aStat.CreationTime := Now;
      Result := 0;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.ScanDirectory(const aPath: string; out aEntries: TStringArray): Integer;
begin
  // Windows 上 scandir 与 readdir 相同
  Result := ReadDirectory(aPath, aEntries);
end;

class function TWindowsFileSystem.MakeTempDirectory(const aTemplate: string; out aPath: string): Integer;
var
  LBuffer: array[0..MAX_PATH] of Char;
begin
  try
    if Windows.GetTempPath(MAX_PATH, LBuffer) > 0 then
    begin
      aPath := string(LBuffer) + 'tmp_' + IntToStr(Random(1000000));
      if Windows.CreateDirectory(PChar(aPath), nil) then
        Result := 0
      else
        Result := -GetLastErrorCode;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

class function TWindowsFileSystem.MakeTempFile(const aTemplate: string; out aPath: string): Integer;
var
  LBuffer: array[0..MAX_PATH] of Char;
  LHandle: THandle;
begin
  try
    if Windows.GetTempPath(MAX_PATH, LBuffer) > 0 then
    begin
      aPath := string(LBuffer) + 'tmp_' + IntToStr(Random(1000000)) + '.tmp';
      LHandle := Windows.CreateFile(PChar(aPath), GENERIC_WRITE, 0, nil, 
        CREATE_NEW, FILE_ATTRIBUTE_TEMPORARY, 0);
      if LHandle <> INVALID_HANDLE_VALUE then
      begin
        Windows.CloseHandle(LHandle);
        Result := Integer(LHandle);
      end
      else
        Result := -GetLastErrorCode;
    end
    else
      Result := -GetLastErrorCode;
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 私有辅助方法 }

class function TWindowsFileSystem.GetFileOpenFlags(aFlags: Integer): DWORD;
begin
  Result := FILE_ATTRIBUTE_NORMAL;
  
  if (aFlags and 512) <> 0 then // O_TRUNC
    Result := Result or FILE_FLAG_WRITE_THROUGH;
end;

class function TWindowsFileSystem.GetFileAccessFlags(aFlags: Integer): DWORD;
begin
  case aFlags and 3 of
    0: Result := GENERIC_READ;  // O_RDONLY
    1: Result := GENERIC_WRITE; // O_WRONLY
    2: Result := GENERIC_READ or GENERIC_WRITE; // O_RDWR
    else
      Result := GENERIC_READ;
  end;
end;

class function TWindowsFileSystem.GetFileShareFlags: DWORD;
begin
  Result := FILE_SHARE_READ or FILE_SHARE_WRITE or FILE_SHARE_DELETE;
end;

class function TWindowsFileSystem.GetFileCreationFlags(aFlags: Integer): DWORD;
begin
  if (aFlags and 64) <> 0 then // O_CREAT
  begin
    if (aFlags and 128) <> 0 then // O_EXCL
      Result := CREATE_NEW
    else
      Result := OPEN_ALWAYS;
  end
  else
    Result := OPEN_EXISTING;
end;

class function TWindowsFileSystem.DateTimeToFileTime(aDateTime: TDateTime): TFileTime;
var
  LSystemTime: TSystemTime;
begin
  DateTimeToSystemTime(aDateTime, LSystemTime);
  SystemTimeToFileTime(LSystemTime, Result);
end;

class function TWindowsFileSystem.FileTimeToDateTime(aFileTime: TFileTime): TDateTime;
var
  LSystemTime: TSystemTime;
begin
  FileTimeToSystemTime(aFileTime, LSystemTime);
  Result := SystemTimeToDateTime(LSystemTime);
end;

class function TWindowsFileSystem.GetLastErrorCode: Integer;
begin
  Result := Integer(Windows.GetLastError);
end;

class function TWindowsFileSystem.TranslateFlags(aFlags: Integer): DWORD;
begin
  Result := 0;
  // 将 libuv 标志转换为 Windows 标志
  if (aFlags and 64) <> 0 then Result := Result or CREATE_NEW;
  if (aFlags and 128) <> 0 then Result := Result or CREATE_NEW;
  if (aFlags and 512) <> 0 then Result := Result or TRUNCATE_EXISTING;
end;

end. 