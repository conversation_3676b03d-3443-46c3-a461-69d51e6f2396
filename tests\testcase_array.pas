unit testcase_Array;

{$mode objfpc}{$H+}
{$I ../src/fafafa.collections.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  fafafa.collections;

type

  custom_data_t = record
    a: Integer;
    b: Integer;
    c: Integer;
    d: Integer;
  end;
  pcustom_data_t = ^custom_data_t;

type

  ptest_equals_data_t = ^test_equals_data_t;
  test_equals_data_t = record
    size: SizeUInt;
    data: Pointer;
  end;

  ptest_compare_data_t = ptest_equals_data_t;

const
  SIZE_CUSTOM_COMPARE_DATA = sizeof(custom_data_t);

type

  { TTestCase_Array }

  TTestCase_Array= class(TTestCase)
  private
    function DoForEachInt1(const aValue: Integer; aData: Pointer): Boolean;
    function DoForEachInt2(const aValue: Integer; aData: Pointer): Boolean;
    function DoForEachStr1(const aValue: string; aData: Pointer): Boolean;
    function DoForEachStr2(const aValue: string; aData: Pointer): Boolean;

    function ForEach_IntCounter(const aValue: Integer; aData: Pointer): Boolean;
    function ForEach_IntCounter_Stop(const aValue: Integer; aData: Pointer): Boolean;
    function ForEach_StrCounter(const aValue: string; aData: Pointer): Boolean;

    function DoEqualsBool(const aLeft, aRight: Boolean; aData: Pointer): Boolean;
    function DoEqualsChar(const aLeft, aRight: Char; aData: Pointer): Boolean;
    function DoEqualsWChar(const aLeft, aRight: WideChar; aData: Pointer): Boolean;
    function DoEqualsI8(const aLeft, aRight: Int8; aData: Pointer): Boolean;
    function DoEqualsI16(const aLeft, aRight: Int16; aData: Pointer): Boolean;
    function DoEqualsI32(const aLeft, aRight: Int32; aData: Pointer): Boolean;
    function DoEqualsI64(const aLeft, aRight: Int64; aData: Pointer): Boolean;
    function DoEqualsU8(const aLeft, aRight: UInt8; aData: Pointer): Boolean;
    function DoEqualsU16(const aLeft, aRight: UInt16; aData: Pointer): Boolean;
    function DoEqualsU32(const aLeft, aRight: UInt32; aData: Pointer): Boolean;
    function DoEqualsU64(const aLeft, aRight: UInt64; aData: Pointer): Boolean;
    function DoEqualsSingle(const aLeft, aRight: Single; aData: Pointer): Boolean;
    function DoEqualsDouble(const aLeft, aRight: Double; aData: Pointer): Boolean;
    function DoEqualsExtended(const aLeft, aRight: Extended; aData: Pointer): Boolean;
    function DoEqualsCurrency(const aLeft, aRight: Currency; aData: Pointer): Boolean;
    function DoEqualsComp(const aLeft, aRight: Comp; aData: Pointer): Boolean;
    function DoEqualsShortString(const aLeft, aRight: ShortString; aData: Pointer): Boolean;
    function DoEqualsAnsiString(const aLeft, aRight: AnsiString; aData: Pointer): Boolean;
    function DoEqualsWideString(const aLeft, aRight: WideString; aData: Pointer): Boolean;
    function DoEqualsUnicodeString(const aLeft, aRight: UnicodeString; aData: Pointer): Boolean;
    function DoEqualsPointer(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
    function DoEqualsVariant(const aLeft, aRight: Variant; aData: Pointer): Boolean;
    function DoEqualsString(const aLeft, aRight: string; aData: Pointer): Boolean;
    function DoEqualsMethod(const aLeft, aRight: TMethod; aData: Pointer): Boolean;
    function DoEqualsBin(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
    function DoEqualsDynArray(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
    function DoEqualsCustom(const aLeft, aRight: custom_data_t; aData: Pointer): Boolean;

    function DoCompareBool(const aLeft, aRight: Boolean; aData: Pointer): SizeInt;
    function DoCompareChar(const aLeft, aRight: Char; aData: Pointer): SizeInt;
    function DoCompareWChar(const aLeft, aRight: WideChar; aData: Pointer): SizeInt;
    function DoCompareI8(const aLeft, aRight: Int8; aData: Pointer): SizeInt;
    function DoCompareI16(const aLeft, aRight: Int16; aData: Pointer): SizeInt;
    function DoCompareI32(const aLeft, aRight: Int32; aData: Pointer): SizeInt;
    function DoCompareI64(const aLeft, aRight: Int64; aData: Pointer): SizeInt;
    function DoCompareU8(const aLeft, aRight: UInt8; aData: Pointer): SizeInt;
    function DoCompareU16(const aLeft, aRight: UInt16; aData: Pointer): SizeInt;
    function DoCompareU32(const aLeft, aRight: UInt32; aData: Pointer): SizeInt;
    function DoCompareU64(const aLeft, aRight: UInt64; aData: Pointer): SizeInt;
    function DoCompareSingle(const aLeft, aRight: Single; aData: Pointer): SizeInt;
    function DoCompareDouble(const aLeft, aRight: Double; aData: Pointer): SizeInt;
    function DoCompareExtended(const aLeft, aRight: Extended; aData: Pointer): SizeInt;
    function DoCompareCurrency(const aLeft, aRight: Currency; aData: Pointer): SizeInt;
    function DoCompareComp(const aLeft, aRight: Comp; aData: Pointer): SizeInt;
    function DoCompareShortString(const aLeft, aRight: ShortString; aData: Pointer): SizeInt;
    function DoCompareAnsiString(const aLeft, aRight: AnsiString; aData: Pointer): SizeInt;
    function DoCompareWideString(const aLeft, aRight: WideString; aData: Pointer): SizeInt;
    function DoCompareUnicodeString(const aLeft, aRight: UnicodeString; aData: Pointer): SizeInt;
    function DoComparePointer(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
    function DoCompareVariant(const aLeft, aRight: Variant; aData: Pointer): SizeInt;
    function DoCompareString(const aLeft, aRight: string; aData: Pointer): SizeInt;
    function DoCompareMethod(const aLeft, aRight: TMethod; aData: Pointer): SizeInt;
    function DoCompareBin(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
    function DoCompareDynArray(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
    function DoCompareCustom(const aLeft, aRight: custom_data_t; aData: Pointer): SizeInt;
    
  published
    { 构造函数 }
    procedure Test_Create;
    procedure Test_Create_Count;
    procedure Test_Create_Allocator;
    procedure Test_Create_Allocator_Count;
    procedure Test_Create_Array;
    procedure Test_Create_Array_Allocator;

    { ICollection }
    procedure Test_GetAllocator;
    procedure Test_GetCount;
    procedure Test_IsEmpty;
    procedure Test_SetData;
    procedure Test_Clear;
    procedure Test_Clone;
    procedure Test_LoadFrom_Collection;
    procedure Test_SaveTo_Collection;
    procedure Test_LoadFrom_Memory;
    procedure Test_Append_Memory;
    procedure Test_Append_Collection;
    procedure Test_AppendTo_Collection;
    procedure Test_WriteToArrayMemory;

    { IGenericCollection }
    procedure Test_GetEnumerator;
    procedure Test_GetElementSize;
    procedure Test_GetIsManagedType;
    procedure Test_GetElementAllocator;
    procedure Test_LoadFrom_Array;
    procedure Test_Append_Array;
    procedure Test_ToArray;
    procedure Test_Reverse1;
    procedure Test_Reverse2;
    procedure Test_Reverse3;

    procedure Test_ForEach1;
    procedure Test_ForEach2;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_ForEach3;
    {$ENDIF}

    procedure Test_ForEach4;
    procedure Test_ForEach5;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_ForEach6;
    {$ENDIF}

    procedure Test_ForEach7;
    procedure Test_ForEach8;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_ForEach9;
    {$ENDIF}

    procedure Test_Contains1;
    procedure Test_Contains2;
    procedure Test_Contains3;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Contains4;
    {$ENDIF}
    procedure Test_Contains5;
    procedure Test_Contains6;
    procedure Test_Contains7;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Contains8;
    {$ENDIF}
    procedure Test_Contains9;
    procedure Test_Contains10;
    procedure Test_Contains11;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Contains12;
    {$ENDIF}

    { IArray }
    procedure Test_Get;
    procedure Test_Put;
    procedure Test_GetMemory;
    procedure Test_GetPtr;
    procedure Test_Resize;
    procedure Test_Ensure;
    procedure Test_WriteMemory;
    procedure Test_WriteArray;
    procedure Test_WriteCollection;
    procedure Test_WriteCollection2;
    procedure Test_ReadMemory;
    procedure Test_ReadArray;
    procedure Test_Fill1;
    procedure Test_Fill2;
    procedure Test_Fill3;
    procedure Test_Zero1;
    procedure Test_Zero2;
    procedure Test_Zero3;
    procedure Test_Swap;
    procedure Test_Swap2;
    procedure Test_Copy;
    procedure Test_Find1;
    procedure Test_Find2;
    procedure Test_Find3;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Find4;
    {$ENDIF}
    procedure Test_Find5;
    procedure Test_Find6;
    procedure Test_Find7;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Find8;
    {$ENDIF}
    procedure Test_Find9;
    procedure Test_Find10;
    procedure Test_Find11;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Find12;
    {$ENDIF}
    
    procedure Test_Sort1;
    procedure Test_Sort2;
    procedure Test_Sort3;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Sort4;
    {$ENDIF}
    procedure Test_Sort5;
    procedure Test_Sort6;
    procedure Test_Sort7;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Sort8;
    {$ENDIF}
    procedure Test_Sort9;
    procedure Test_Sort10;
    procedure Test_Sort11;
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    procedure Test_Sort12;
    {$ENDIF}

  end;

  function custom_equals_bool(const aLeft, aRight: Boolean; aData: Pointer): Boolean;
  function custom_equals_char(const aLeft, aRight: Char; aData: Pointer): Boolean;
  function custom_equals_wchar(const aLeft, aRight: WideChar; aData: Pointer): Boolean;
  function custom_equals_i8(const aLeft, aRight: Int8; aData: Pointer): Boolean;
  function custom_equals_i16(const aLeft, aRight: Int16; aData: Pointer): Boolean;
  function custom_equals_i32(const aLeft, aRight: Int32; aData: Pointer): Boolean;
  function custom_equals_i64(const aLeft, aRight: Int64; aData: Pointer): Boolean;
  function custom_equals_u8(const aLeft, aRight: UInt8; aData: Pointer): Boolean;
  function custom_equals_u16(const aLeft, aRight: UInt16; aData: Pointer): Boolean;
  function custom_equals_u32(const aLeft, aRight: UInt32; aData: Pointer): Boolean;
  function custom_equals_u64(const aLeft, aRight: UInt64; aData: Pointer): Boolean;
  function custom_equals_single(const aLeft, aRight: Single; aData: Pointer): Boolean;
  function custom_equals_double(const aLeft, aRight: Double; aData: Pointer): Boolean;
  function custom_equals_extended(const aLeft, aRight: Extended; aData: Pointer): Boolean;
  function custom_equals_currency(const aLeft, aRight: Currency; aData: Pointer): Boolean;
  function custom_equals_comp(const aLeft, aRight: Comp; aData: Pointer): Boolean;
  function custom_equals_shortstring(const aLeft, aRight: ShortString; aData: Pointer): Boolean;
  function custom_equals_ansistring(const aLeft, aRight: AnsiString; aData: Pointer): Boolean;
  function custom_equals_widestring(const aLeft, aRight: WideString; aData: Pointer): Boolean;
  function custom_equals_unicodestring(const aLeft, aRight: UnicodeString; aData: Pointer): Boolean;
  function custom_equals_pointer(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
  function custom_equals_variant(const aLeft, aRight: Variant; aData: Pointer): Boolean;
  function custom_equals_string(const aLeft, aRight: string; aData: Pointer): Boolean;
  function custom_equals_method(const aLeft, aRight: TMethod; aData: Pointer): Boolean;
  function custom_equals_bin(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
  function custom_equals_dynarray(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
  function custom_equals_custom(const aLeft, aRight: custom_data_t; aData: Pointer): Boolean;

  function custom_compare_bool(const aLeft, aRight: Boolean; aData: Pointer): SizeInt;
  function custom_compare_char(const aLeft, aRight: Char; aData: Pointer): SizeInt;
  function custom_compare_wchar(const aLeft, aRight: WideChar; aData: Pointer): SizeInt;
  function custom_compare_i8(const aLeft, aRight: Int8; aData: Pointer): SizeInt;
  function custom_compare_i16(const aLeft, aRight: Int16; aData: Pointer): SizeInt;
  function custom_compare_i32(const aLeft, aRight: Int32; aData: Pointer): SizeInt;
  function custom_compare_i64(const aLeft, aRight: Int64; aData: Pointer): SizeInt;
  function custom_compare_u8(const aLeft, aRight: UInt8; aData: Pointer): SizeInt;
  function custom_compare_u16(const aLeft, aRight: UInt16; aData: Pointer): SizeInt;
  function custom_compare_u32(const aLeft, aRight: UInt32; aData: Pointer): SizeInt;
  function custom_compare_u64(const aLeft, aRight: UInt64; aData: Pointer): SizeInt;
  function custom_compare_single(const aLeft, aRight: Single; aData: Pointer): SizeInt;
  function custom_compare_double(const aLeft, aRight: Double; aData: Pointer): SizeInt;
  function custom_compare_extended(const aLeft, aRight: Extended; aData: Pointer): SizeInt;
  function custom_compare_currency(const aLeft, aRight: Currency; aData: Pointer): SizeInt;
  function custom_compare_comp(const aLeft, aRight: Comp; aData: Pointer): SizeInt;
  function custom_compare_shortstring(const aLeft, aRight: ShortString; aData: Pointer): SizeInt;
  function custom_compare_ansistring(const aLeft, aRight: AnsiString; aData: Pointer): SizeInt;
  function custom_compare_widestring(const aLeft, aRight: WideString; aData: Pointer): SizeInt;
  function custom_compare_unicodestring(const aLeft, aRight: UnicodeString; aData: Pointer): SizeInt;
  function custom_compare_pointer(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
  function custom_compare_variant(const aLeft, aRight: Variant; aData: Pointer): SizeInt;
  function custom_compare_string(const aLeft, aRight: string; aData: Pointer): SizeInt;
  function custom_compare_method(const aLeft, aRight: TMethod; aData: Pointer): SizeInt;
  function custom_compare_bin(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
  function custom_compare_dynarray(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
  function custom_compare_custom(const aLeft, aRight: custom_data_t; aData: Pointer): SizeInt;
 

 generic procedure AssertArrayEquals<T>(const aActual: specialize IArray<T>; const aExpected: array of T);

implementation

function TTestCase_Array.DoForEachInt1(const aValue: Integer; aData: Pointer): Boolean;
begin
  AssertEquals(SizeUInt(aData^), SizeUInt(aValue));
  Inc(SizeUInt(aData^));
  Result := True;
end;

function TTestCase_Array.DoForEachInt2(const aValue: Integer; aData: Pointer): Boolean;
begin
  AssertEquals(SizeUInt(aData^), SizeUInt(aValue));
  Inc(SizeUInt(aData^));
  Result := SizeUInt(aData^) < 2;
end;

function TTestCase_Array.DoForEachStr1(const aValue: string; aData: Pointer): Boolean;
begin
  AssertEquals(SizeUInt(aData^), SizeUInt(StrToInt(aValue)));
  Inc(SizeUInt(aData^));
  Result := True;
end;

function TTestCase_Array.DoForEachStr2(const aValue: string; aData: Pointer): Boolean;
begin
  AssertEquals(SizeUInt(aData^), SizeUInt(StrToInt(aValue)));
  Inc(SizeUInt(aData^));
  Result := SizeUInt(aData^) < 2;
end;

function TTestCase_Array.ForEach_IntCounter(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := True;
end;

function TTestCase_Array.ForEach_IntCounter_Stop(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := PSIZEUINT(aData)^ < 3;
end;

function TTestCase_Array.ForEach_StrCounter(const aValue: string; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := True;
end;

function TTestCase_Array.DoEqualsBool(const aLeft, aRight: Boolean; aData: Pointer): Boolean;
begin
  Result := custom_equals_bool(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsChar(const aLeft, aRight: Char; aData: Pointer): Boolean;
begin
  Result := custom_equals_char(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsWChar(const aLeft, aRight: WideChar; aData: Pointer): Boolean;
begin
  Result := custom_equals_wchar(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsI8(const aLeft, aRight: Int8; aData: Pointer): Boolean;
begin
  Result := custom_equals_i8(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsI16(const aLeft, aRight: Int16; aData: Pointer): Boolean;
begin
  Result := custom_equals_i16(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsI32(const aLeft, aRight: Int32; aData: Pointer): Boolean;
begin
  Result := custom_equals_i32(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsI64(const aLeft, aRight: Int64; aData: Pointer): Boolean;
begin
  Result := custom_equals_i64(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsU8(const aLeft, aRight: UInt8; aData: Pointer): Boolean;
begin
  Result := custom_equals_u8(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsU16(const aLeft, aRight: UInt16; aData: Pointer): Boolean;
begin
  Result := custom_equals_u16(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsU32(const aLeft, aRight: UInt32; aData: Pointer): Boolean;
begin
  Result := custom_equals_u32(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsU64(const aLeft, aRight: UInt64; aData: Pointer): Boolean;
begin
  Result := custom_equals_u64(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsSingle(const aLeft, aRight: Single; aData: Pointer): Boolean;
begin
  Result := custom_equals_single(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsDouble(const aLeft, aRight: Double; aData: Pointer): Boolean;
begin
  Result := custom_equals_double(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsExtended(const aLeft, aRight: Extended; aData: Pointer): Boolean;
begin
  Result := custom_equals_extended(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsCurrency(const aLeft, aRight: Currency; aData: Pointer): Boolean;
begin
  Result := custom_equals_currency(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsComp(const aLeft, aRight: Comp; aData: Pointer): Boolean;
begin
  Result := custom_equals_comp(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsShortString(const aLeft, aRight: ShortString; aData: Pointer): Boolean;
begin
  Result := custom_equals_shortstring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsAnsiString(const aLeft, aRight: AnsiString; aData: Pointer): Boolean;
begin
  Result := custom_equals_ansistring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsWideString(const aLeft, aRight: WideString; aData: Pointer): Boolean;
begin
  Result := custom_equals_widestring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsUnicodeString(const aLeft, aRight: UnicodeString; aData: Pointer): Boolean;
begin
  Result := custom_equals_unicodestring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsPointer(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
begin
  Result := custom_equals_pointer(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsVariant(const aLeft, aRight: Variant; aData: Pointer): Boolean;
begin
  Result := custom_equals_variant(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsString(const aLeft, aRight: string; aData: Pointer): Boolean;
begin
  Result := custom_equals_string(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsMethod(const aLeft, aRight: TMethod; aData: Pointer): Boolean;
begin
  Result := custom_equals_method(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsBin(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
begin
  Result := custom_equals_bin(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsDynArray(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
begin
  Result := custom_equals_dynarray(aLeft, aRight, aData);
end;

function TTestCase_Array.DoEqualsCustom(const aLeft, aRight: custom_data_t; aData: Pointer): Boolean;
begin
  Result := custom_equals_custom(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareBool(const aLeft, aRight: Boolean; aData: Pointer): SizeInt;
begin
  Result := custom_compare_bool(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareChar(const aLeft, aRight: Char; aData: Pointer): SizeInt;
begin
  Result := custom_compare_char(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareWChar(const aLeft, aRight: WideChar; aData: Pointer): SizeInt;
begin
  Result := custom_compare_wchar(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareI8(const aLeft, aRight: Int8; aData: Pointer): SizeInt;
begin
  Result := custom_compare_i8(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareI16(const aLeft, aRight: Int16; aData: Pointer): SizeInt;
begin
  Result := custom_compare_i16(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareI32(const aLeft, aRight: Int32; aData: Pointer): SizeInt;
begin
  Result := custom_compare_i32(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareI64(const aLeft, aRight: Int64; aData: Pointer): SizeInt;
begin
  Result := custom_compare_i64(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareU8(const aLeft, aRight: UInt8; aData: Pointer): SizeInt;
begin
  Result := custom_compare_u8(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareU16(const aLeft, aRight: UInt16; aData: Pointer): SizeInt;
begin
  Result := custom_compare_u16(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareU32(const aLeft, aRight: UInt32; aData: Pointer): SizeInt;
begin
  Result := custom_compare_u32(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareU64(const aLeft, aRight: UInt64; aData: Pointer): SizeInt;
begin
  Result := custom_compare_u64(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareSingle(const aLeft, aRight: Single; aData: Pointer): SizeInt;
begin
  Result := custom_compare_single(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareDouble(const aLeft, aRight: Double; aData: Pointer): SizeInt;
begin
  Result := custom_compare_double(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareExtended(const aLeft, aRight: Extended; aData: Pointer): SizeInt;
begin
  Result := custom_compare_extended(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareCurrency(const aLeft, aRight: Currency; aData: Pointer): SizeInt;
begin
  Result := custom_compare_currency(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareComp(const aLeft, aRight: Comp; aData: Pointer): SizeInt;
begin
  Result := custom_compare_comp(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareShortString(const aLeft, aRight: ShortString; aData: Pointer): SizeInt;
begin
  Result := custom_compare_shortstring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareAnsiString(const aLeft, aRight: AnsiString; aData: Pointer): SizeInt;
begin
  Result := custom_compare_ansistring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareWideString(const aLeft, aRight: WideString; aData: Pointer): SizeInt;
begin
  Result := custom_compare_widestring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareUnicodeString(const aLeft, aRight: UnicodeString; aData: Pointer): SizeInt;
begin
  Result := custom_compare_unicodestring(aLeft, aRight, aData);
end;

function TTestCase_Array.DoComparePointer(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
begin
  Result := custom_compare_pointer(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareVariant(const aLeft, aRight: Variant; aData: Pointer): SizeInt;
begin
  Result := custom_compare_variant(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareString(const aLeft, aRight: string; aData: Pointer): SizeInt;
begin
  Result := custom_compare_string(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareMethod(const aLeft, aRight: TMethod; aData: Pointer): SizeInt;
begin
  Result := custom_compare_method(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareBin(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
begin
  Result := custom_compare_bin(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareDynArray(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
begin
  Result := custom_compare_dynarray(aLeft, aRight, aData);
end;

function TTestCase_Array.DoCompareCustom(const aLeft, aRight: custom_data_t; aData: Pointer): SizeInt;
begin
  Result := custom_compare_custom(aLeft, aRight, aData);
end;

function GlobalForEach_IntCounter(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := True;
end;

function GlobalForEach_IntCounter_Stop(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := PSIZEUINT(aData)^ < 3;
end;

function GlobalForEach_StrCounter(const aValue: string; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := True;
end;


procedure TTestCase_Array.Test_Create;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  AssertNotNull('Array should be created successfully', LIntArr);
  AssertEquals('Initial count should be 0', 0, LIntArr.GetCount);
  AssertTrue('Memory should be initialized (nil or valid)', (LIntArr.Memory = nil));

  { 托管类型 }
  LStrArr := specialize TArray<string>.Create;
  AssertNotNull('Array should be created successfully', LStrArr);
  AssertEquals('Initial count should be 0', 0, LStrArr.GetCount);
  AssertTrue('Memory should be initialized (nil or valid)', (LStrArr.Memory = nil));
end;

procedure TTestCase_Array.Test_Create_Count;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create(8);
  AssertNotNull('Array should be created successfully', LIntArr);
  AssertEquals('Count should match input parameter', 8, LIntArr.GetCount);
  AssertTrue('Memory should be allocated for non-zero count', LIntArr.Memory <> nil);

  { 测试边界情况：aCount = 0 }
  LIntArr := specialize TArray<Integer>.Create(0);
  AssertNotNull('Array should be created with count 0', LIntArr);
  AssertEquals('Count should be 0 when initialized with 0', 0, LIntArr.GetCount);
  AssertTrue('Memory should be initialized (nil or valid) for count 0', (LIntArr.Memory = nil));

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(8);
  AssertNotNull('Array should be created successfully', LStrArr);
  AssertEquals('Count should match input parameter', 8, LStrArr.GetCount);
  AssertTrue('Memory should be allocated for non-zero count', LStrArr.Memory <> nil);

  { 测试边界情况：aCount = 0 }
  LStrArr := specialize TArray<string>.Create(0);
  AssertNotNull('Array should be created with count 0', LStrArr);
  AssertEquals('Count should be 0 when initialized with 0', 0, LStrArr.GetCount);
  AssertTrue('Memory should be initialized (nil or valid) for count 0', (LStrArr.Memory = nil));
end;

function TestGetMem(aSize: SizeUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function TestAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function TestReallocMem(aPtr: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure TestFreeMem(aPtr: Pointer);
begin
  FreeMem(aPtr);
end;

procedure TTestCase_Array.Test_Create_Allocator;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LAllocator: TMemAllocator;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    { 构造空数组 }
    LIntArr := specialize TArray<Integer>.Create(LAllocator);
    AssertNotNull('Array should be created successfully with allocator', LIntArr);
    AssertEquals('Initial count should be 0 with allocator only', 0, LIntArr.GetCount);
    AssertTrue('Memory should be nil when no count specified', LIntArr.Memory = nil);

    { 托管类型 }
    LStrArr := specialize TArray<string>.Create(LAllocator);
    AssertNotNull('Array should be created successfully with allocator', LStrArr);
    AssertEquals('Initial count should be 0 with allocator only', 0, LStrArr.GetCount);
    AssertTrue('Memory should be nil when no count specified', LStrArr.Memory = nil);

    { 构造非空数组 }
    LStrArr := specialize TArray<string>.Create(LAllocator, 8);
    AssertNotNull('Array should be created successfully with allocator', LStrArr);
    AssertEquals('Initial count should be 8 with allocator only', 8, LStrArr.GetCount);
    AssertTrue('Memory should be allocated for non-zero count', LStrArr.Memory <> nil);

    LIntArr := nil;
    LStrArr := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_Array.Test_Create_Allocator_Count;
var
  LAllocator: TMemAllocator;
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    { 构造非空数组 }
    LIntArr := specialize TArray<Integer>.Create(LAllocator, 8);
    AssertNotNull('Array should be created successfully with allocator', LIntArr);
    AssertEquals('Initial count should be 8 with allocator only', 8, LIntArr.GetCount);
    AssertTrue('Memory should be allocated for non-zero count', LIntArr.Memory <> nil);

    { 测试边界情况：aCount = 0 }
    LIntArr := specialize TArray<Integer>.Create(LAllocator, 0);
    AssertNotNull('Array should be created with count 0', LIntArr);
    AssertEquals('Count should be 0 when initialized with 0', 0, LIntArr.GetCount);
    AssertTrue('Memory should be nil for count 0', LIntArr.Memory = nil);

    { 托管类型 }

    LStrArr := specialize TArray<string>.Create(LAllocator, 8);
    AssertNotNull('Array should be created successfully with allocator', LStrArr);
    AssertEquals('Initial count should be 8 with allocator only', 8, LStrArr.GetCount);
    AssertTrue('Memory should be allocated for non-zero count', LStrArr.Memory <> nil);

    { 测试边界情况：aCount = 0 }
    LStrArr := specialize TArray<string>.Create(LAllocator, 0);
    AssertNotNull('Array should be created with count 0', LStrArr);
    AssertEquals('Count should be 0 when initialized with 0', 0, LStrArr.GetCount);
    AssertTrue('Memory should be nil for count 0', LStrArr.Memory = nil);

    LIntArr := nil;
    LStrArr := nil;
  finally
    LAllocator.Free;
  end;
end;


procedure TTestCase_Array.Test_Create_Array;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntData: array[0..2] of Integer = (10, 20, 30);
  LStrData: array[0..2] of string = ('10', '20', '30');
  i:        Integer;
begin
  { 值类型 }
  { 从静态数组构造 }
  LIntArr := specialize TArray<Integer>.Create(LIntData);
  AssertNotNull('Create(Static Int Array): should not be nil', LIntArr);
  AssertEquals('Create(Static Int Array): Count should match', SizeUInt(Length(LIntData)), LIntArr.GetCount);
  AssertTrue('Create(Static Int Array): Data should match', CompareMem(LIntArr.Memory, @LIntData[0], LIntArr.GetCount * SizeOf(Integer)));

  { 从开放数组构造 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertNotNull('Create(Open Int Array): should not be nil', LIntArr);
  AssertEquals('Create(Open Int Array): Count should match', 4, LIntArr.GetCount);

  for i := 0 to LIntArr.GetCount - 1 do
    AssertEquals(Format('Create(Open Int Array): Item %d should match', [i]), i + 1, LIntArr.Items[i]);

  { 从空数组构造 }
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertNotNull('Create(Empty Int Array): should not be nil', LIntArr);
  AssertEquals('Create(Empty Int Array): Count should be 0', 0, LIntArr.GetCount);
  AssertTrue('Create(Empty Int Array): Memory should be nil', LIntArr.Memory = nil);

  { 托管类型 }

  { 从静态数组构造 }
  LStrArr := specialize TArray<string>.Create(LStrData);
  AssertNotNull('Create(Static Str Array): should not be nil', LStrArr);
  AssertEquals('Create(Static Str Array): Count should match', SizeUInt(Length(LStrData)), LStrArr.GetCount);

  for i := 0 to High(LStrData) do
    AssertEquals(Format('Create(Static Str Array): Item %d should match', [i]), LStrData[i], LStrArr.Items[i]);

  { 从开放数组构造 }
  LStrArr := specialize TArray<string>.Create(['1','2','3','4']);
  AssertNotNull('Create(Open Str Array): should not be nil', LStrArr);
  AssertEquals('Create(Open Str Array): Count should match', 4, LStrArr.GetCount);

  for i := 0 to LStrArr.GetCount - 1 do
    AssertEquals(Format('Create(Open Str Array): Item %d should match', [i]), IntToStr(i+1), LStrArr.Items[i]);

  { 从空数组构造 }
  LStrArr := specialize TArray<string>.Create([]);
  AssertNotNull('Create(Empty Str Array): should not be nil', LStrArr);
  AssertEquals('Create(Empty Str Array): Count should be 0', 0, LStrArr.GetCount);
  AssertTrue('Create(Empty Str Array): Memory should be nil', LStrArr.Memory = nil);
end;

procedure TTestCase_Array.Test_Create_Array_Allocator;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LAllocator: TMemAllocator;
  LIntData:   array[0..2] of Integer = (10, 20, 30);
  LStrData:   array[0..2] of string = ('10', '20', '30');
  i:          Integer;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    { 值类型 }
    { 从静态数组构造 }
    LIntArr := specialize TArray<Integer>.Create(LIntData, LAllocator);
    AssertNotNull('Create(Static Int Array, Alloc): should not be nil', LIntArr);
    AssertEquals('Create(Static Int Array, Alloc): Count should match', SizeUInt(Length(LIntData)), LIntArr.GetCount);
    AssertTrue('Create(Static Int Array, Alloc): Allocator should be set', LIntArr.Allocator = LAllocator);
    AssertTrue('Create(Static Int Array, Alloc): Data should match', CompareMem(LIntArr.Memory, @LIntData[0], LIntArr.GetCount * SizeOf(Integer)));

    { 从开放数组构造 }
    LIntArr := specialize TArray<Integer>.Create([1,2,3,4], LAllocator);
    AssertNotNull('Create(Open Int Array, Alloc): should not be nil', LIntArr);
    AssertEquals('Create(Open Int Array, Alloc): Count should match', 4, LIntArr.GetCount);
    AssertTrue('Create(Open Int Array, Alloc): Allocator should be set', LIntArr.Allocator = LAllocator);
  
    for i := 0 to LIntArr.GetCount - 1 do
      AssertEquals(Format('Create(Open Int Array, Alloc): Item %d should match', [i]), i + 1, LIntArr.Items[i]);

    { 从空数组构造 }
    LIntArr := specialize TArray<Integer>.Create([], LAllocator);
    AssertNotNull('Create(Empty Int Array, Alloc): should not be nil', LIntArr);
    AssertEquals('Create(Empty Int Array, Alloc): Count should be 0', 0, LIntArr.GetCount);
    AssertTrue('Create(Empty Int Array, Alloc): Memory should be nil', LIntArr.Memory = nil);
    AssertTrue('Create(Empty Int Array, Alloc): Allocator should be set', LIntArr.Allocator = LAllocator);

    { 托管类型 }

    { 从静态数组构造 }
    LStrArr := specialize TArray<string>.Create(LStrData, LAllocator);
    AssertNotNull('Create(Static Str Array, Alloc): should not be nil', LStrArr);
    AssertEquals('Create(Static Str Array, Alloc): Count should match', SizeUInt(Length(LStrData)), LStrArr.GetCount);
    AssertTrue('Create(Static Str Array, Alloc): Allocator should be set', LStrArr.Allocator = LAllocator);

    for i := 0 to High(LStrData) do
      AssertEquals(Format('Create(Static Str Array, Alloc): Item %d should match', [i]), LStrData[i], LStrArr.Items[i]);

    { 从开放数组构造 }
    LStrArr := specialize TArray<string>.Create(['1','2','3','4'], LAllocator);
    AssertNotNull('Create(Open Str Array, Alloc): should not be nil', LStrArr);
    AssertEquals('Create(Open Str Array, Alloc): Count should match', 4, LStrArr.GetCount);
    AssertTrue('Create(Open Str Array, Alloc): Allocator should be set', LStrArr.Allocator = LAllocator);

    for i := 0 to LStrArr.GetCount - 1 do
      AssertEquals(Format('Create(Open Str Array, Alloc): Item %d should match', [i]), IntToStr(i+1), LStrArr.Items[i]);

    { 从空数组构造 }
    LStrArr := specialize TArray<string>.Create([], LAllocator);
    AssertNotNull('Create(Empty Str Array, Alloc): should not be nil', LStrArr);
    AssertEquals('Create(Empty Str Array, Alloc): Count should be 0', 0, LStrArr.GetCount);
    AssertTrue('Create(Empty Str Array, Alloc): Memory should be nil', LStrArr.Memory = nil);
    AssertTrue('Create(Empty Str Array, Alloc): Allocator should be set', LStrArr.Allocator = LAllocator);

    { 确保数组在分配器释放之前被释放 }
    LIntArr := nil;
    LStrArr := nil;

  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_Array.Test_GetCount;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  AssertEquals('Count should be 0 for default constructor', 0, LIntArr.GetCount);

  LIntArr := specialize TArray<Integer>.Create(5);
  AssertEquals('Count should match initialized size', 5, LIntArr.GetCount);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create;
  AssertEquals('Count should be 0 for default constructor', 0, LStrArr.GetCount);

  LStrArr := specialize TArray<string>.Create(5);
  AssertEquals('Count should match initialized size', 5, LStrArr.GetCount);
end;

procedure TTestCase_Array.Test_GetAllocator;
var
  LArr:             specialize IArray<Integer>;
  LCustomAllocator: TMemAllocator;
begin
  { 测试默认分配器 }
  LArr := specialize TArray<Integer>.Create;
  AssertTrue('Default allocator should be RtlMemAllocator', LArr.GetAllocator = RtlMemAllocator);

  { 测试自定义分配器 }
  LCustomAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LArr := specialize TArray<Integer>.Create(LCustomAllocator);
    AssertTrue('Custom allocator should be set correctly', LArr.GetAllocator = LCustomAllocator);
    LArr := nil; // 在释放分配器之前释放数组
  finally
    LCustomAllocator.Free;
  end;
end;

procedure TTestCase_Array.Test_GetElementAllocator;
var
  LArr:       specialize IArray<Integer>;
  LAllocator: TMemAllocator;
begin
  LArr := specialize TArray<Integer>.Create;
  AssertNotNull('Element allocator should not be nil', LArr.GetElementAllocator);
  AssertTrue('Element allocator should be the same', LArr.GetElementAllocator.GetMemAllocator = RtlMemAllocator());

  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LArr := specialize TArray<Integer>.Create(LAllocator);
  AssertTrue('Allocator should match the one used in creation', LArr.GetElementAllocator.GetMemAllocator = LAllocator);

  LAllocator.Free;
end;

procedure TTestCase_Array.Test_IsEmpty;
var
  LArr: specialize IArray<Integer>;
begin
  LArr := specialize TArray<Integer>.Create;
  AssertTrue('IsEmpty: Newly created array should be empty', LArr.IsEmpty);

  LArr := specialize TArray<Integer>.Create(10);
  AssertFalse('IsEmpty: Array with count > 0 should not be empty', LArr.IsEmpty);

  LArr.Resize(0);
  AssertTrue('IsEmpty: Array after Resize(0) should be empty', LArr.IsEmpty);

  LArr.Ensure(5);
  AssertFalse('IsEmpty: Array after Ensure() should not be empty', LArr.IsEmpty);

  LArr.Clear;
  AssertTrue('IsEmpty: Array after Clear() should be empty', LArr.IsEmpty);
end;



procedure TTestCase_Array.Test_SetData;
var
  LArr:  specialize IArray<Integer>;
  LData: Pointer;
begin
  LArr := specialize TArray<Integer>.Create;
  AssertNull('Data should be null on creation', LArr.GetData);

  LData := Pointer(NativeUInt($DEADBEEF)); // 使用一个更独特的十六进制值
  LArr.SetData(LData);
  AssertTrue('Data should be set and retrieved correctly', LData = LArr.GetData);

  LArr.SetData(nil);
  AssertNull('Data should be settable to nil', LArr.GetData);
end;


procedure TTestCase_Array.Test_Clear;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  { 值类型 }
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3]);
  AssertFalse('Clear(Int): Array should not be empty before clear', LIntArr.IsEmpty);
  LIntArr.Clear;
  AssertTrue('Clear(Int): Array should be empty after clear', LIntArr.IsEmpty);
  AssertTrue('Clear(Int): Memory should be nil after clear', LIntArr.Memory = nil);
  LIntArr.Clear;
  AssertTrue('Clear(Int): Repeated clear should be safe', LIntArr.IsEmpty);

  { 托管类型 }
  LStrArr := specialize TArray<string>.Create(['A', 'B', 'C']);
  AssertFalse('Clear(String): Array should not be empty before clear', LStrArr.IsEmpty);
  LStrArr.Clear;
  AssertTrue('Clear(String): Array should be empty after clear', LStrArr.IsEmpty);
  AssertTrue('Clear(String): Memory should be nil after clear', LStrArr.Memory = nil);
  LStrArr.Clear;
  AssertTrue('Clear(String): Repeated clear should be safe', LStrArr.IsEmpty);
end;

procedure TTestCase_Array.Test_GetEnumerator;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntEnum: specialize IEnumerator<Integer>;
  LStrEnum: specialize IEnumerator<string>;
  LIntItem: Integer;
  LStrItem: string;
  i:        Integer;
begin
  LIntArr  := specialize TArray<Integer>.Create([1, 2, 3]);
  LIntEnum := LIntArr.GetEnumerator;
  AssertNotNull('Enumerator should not be null', LIntEnum);

  AssertTrue(LIntEnum.MoveNext);
  AssertEquals('First item should be 1',  1, LIntEnum.Current);

  AssertTrue(LIntEnum.MoveNext);
  AssertEquals('Second item should be 2', 2, LIntEnum.Current);

  AssertTrue(LIntEnum.MoveNext);
  AssertEquals('Third item should be 3',  3, LIntEnum.Current);

  AssertFalse(LIntEnum.MoveNext);

  { 测试 for 迭代 }
  i := 1;

  for LIntItem in LIntArr do
  begin
    AssertEquals('Item should be 1', i, LIntItem);
    Inc(i);
  end;

  { 托管类型 }

  LStrArr  := specialize TArray<string>.Create(['1', '2', '3']);
  LStrEnum := LStrArr.GetEnumerator;
  AssertNotNull('Enumerator should not be null', LStrEnum);

  AssertTrue(LStrEnum.MoveNext);
  AssertEquals('First item should be 1', '1', LStrEnum.Current);

  AssertTrue(LStrEnum.MoveNext);
  AssertEquals('Second item should be 2', '2', LStrEnum.Current);

  AssertTrue(LStrEnum.MoveNext);
  AssertEquals('Third item should be 3', '3', LStrEnum.Current);

  AssertFalse(LStrEnum.MoveNext);

  { 测试 for 迭代 }
  i := 1;

  for LStrItem in LStrArr do
  begin
    AssertEquals('Item should be 1', IntToStr(i), LStrItem);
    Inc(i);
  end;
end;

procedure TTestCase_Array.Test_GetElementSize;
var
  LIntArr:      specialize IArray<Integer>;
  LStrArr:      specialize IArray<String>;
  LObjArr:      specialize IArray<TObject>;
  LInt64Arr:    specialize IArray<Int64>;
  LSingleArr:   specialize IArray<Single>;
  LDoubleArr:   specialize IArray<Double>;
  LExtendedArr: specialize IArray<Extended>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  AssertEquals('Element size should be 4 for Integer', SizeUInt(sizeof(Integer)), LIntArr.GetElementSize);

  LStrArr := specialize TArray<String>.Create;
  AssertEquals(Format('Element size should be %d for String', [sizeof(String)]), SizeUInt(sizeof(String)), LStrArr.GetElementSize);

  LObjArr := specialize TArray<TObject>.Create;
  AssertEquals(Format('Element size should be %d for TObject', [sizeof(TObject)]),SizeUInt(sizeof(TObject)), LObjArr.GetElementSize);
  
  LInt64Arr := specialize TArray<Int64>.Create;
  AssertEquals(Format('Element size should be %d for Int64', [sizeof(Int64)]), SizeUInt(sizeof(Int64)), LInt64Arr.GetElementSize);

  LSingleArr := specialize TArray<Single>.Create;
  AssertEquals(Format('Element size should be %d for Single', [sizeof(Single)]), SizeUInt(sizeof(Single)), LSingleArr.GetElementSize);
  
  LDoubleArr := specialize TArray<Double>.Create;
  AssertEquals(Format('Element size should be %d for Double', [sizeof(Double)]), SizeUInt(sizeof(Double)), LDoubleArr.GetElementSize);

  LExtendedArr := specialize TArray<Extended>.Create;
  AssertEquals(Format('Element size should be %d for Extended', [sizeof(Extended)]), SizeUInt(sizeof(Extended)), LExtendedArr.GetElementSize);
end;

procedure TTestCase_Array.Test_GetIsManagedType;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  AssertFalse('Integer should not be managed type', LIntArr.GetIsManagedType);

  LStrArr := specialize TArray<string>.Create;
  AssertTrue('String should be managed type', LStrArr.GetIsManagedType);
end;

procedure TTestCase_Array.Test_Get;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create([10, 20, 30]);
  AssertEquals('Get should return correct element', 10, LIntArr.Get(0));
  AssertEquals('Get should return correct element', 20, LIntArr.Get(1));
  AssertEquals('Get should return correct element', 30, LIntArr.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Get(3);
    end);
  {$ENDIF}

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['Hello', 'World', 'Test']);
  AssertEquals('Get should return correct element', 'Hello', LStrArr.Get(0));
  AssertEquals('Get should return correct element', 'World', LStrArr.Get(1));
  AssertEquals('Get should return correct element', 'Test',  LStrArr.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Get(3);
    end);
  {$ENDIF}
end;

procedure TTestCase_Array.Test_Put;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create(3);
  LIntArr.Put(0, 100);
  LIntArr.Put(1, 200);
  LIntArr.Put(2, 300);
  AssertEquals('Put should store value correctly', 100, LIntArr.Get(0));
  AssertEquals('Put should store value correctly', 200, LIntArr.Get(1));
  AssertEquals('Put should store value correctly', 300, LIntArr.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Put(3, 400);
    end);
  {$ENDIF}

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(3);
  LStrArr.Put(0, 'Hello');
  LStrArr.Put(1, 'World');
  LStrArr.Put(2, 'Test');
  AssertEquals('Put should store value correctly', 'Hello', LStrArr.Get(0));
  AssertEquals('Put should store value correctly', 'World', LStrArr.Get(1));
  AssertEquals('Put should store value correctly', 'Test',  LStrArr.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Put(3, 'Test2');
    end);
  {$ENDIF}
end;

procedure TTestCase_Array.Test_GetMemory;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
  LIntPtr: PInteger;
  LStrPtr: PString;
begin
  LIntArr := specialize TArray<Integer>.Create(5);
  LIntPtr := LIntArr.GetMemory;
  AssertNotNull('Collection memory should not be null', LIntPtr);
  LIntPtr[0] := 100;
  LIntPtr[1] := 200;
  LIntPtr[2] := 300;
  AssertEquals('Value should be stored correctly', 100, LIntArr.Get(0));
  AssertEquals('Value should be stored correctly', 200, LIntArr.Get(1));
  AssertEquals('Value should be stored correctly', 300, LIntArr.Get(2));

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(5);
  LStrPtr := LStrArr.GetMemory;
  AssertNotNull('Collection memory should not be null', LStrPtr);
  LStrPtr[0] := 'Hello';
  LStrPtr[1] := 'World';
  LStrPtr[2] := 'Test';
  AssertEquals('Value should be stored correctly', 'Hello', LStrArr.Get(0));
  AssertEquals('Value should be stored correctly', 'World', LStrArr.Get(1));
  AssertEquals('Value should be stored correctly', 'Test',  LStrArr.Get(2));
end;

procedure TTestCase_Array.Test_GetPtr;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
  LIntPtr: PInteger;
  LStrPtr: PString;
begin
  LIntArr := specialize TArray<Integer>.Create([10, 20, 30]);
  LIntPtr := LIntArr.GetPtr(0);
  AssertNotNull('Pointer to first element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 10, LIntPtr^);

  LIntPtr := LIntArr.GetPtr(1);
  AssertNotNull('Pointer to second element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 20, LIntPtr^);

  LIntPtr := LIntArr.GetPtr(2);
  AssertNotNull('Pointer to third element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 30, LIntPtr^);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 访问越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.GetPtr(3);
    end);
  {$ENDIF}

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['Hello', 'World', 'Test']);
  LStrPtr := LStrArr.GetPtr(0);
  AssertNotNull('Pointer to first element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'Hello', LStrPtr^);

  LStrPtr := LStrArr.GetPtr(1);
  AssertNotNull('Pointer to second element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'World', LStrPtr^);

  LStrPtr := LStrArr.GetPtr(2);
  AssertNotNull('Pointer to third element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'Test', LStrPtr^);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 访问越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.GetPtr(3);
    end);
  {$ENDIF}
end;

procedure TTestCase_Array.Test_Resize;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create(5);
  LIntArr.Resize(10);
  AssertEquals('New size should be correct for Integer', 10, LIntArr.GetCount);
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  LIntArr.Resize(3);
  AssertEquals('New size should be correct for Integer', 3, LIntArr.GetCount);
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  LIntArr.Resize(0);
  AssertEquals('New size should be correct for Integer', 0, LIntArr.GetCount);
  AssertTrue('Memory should be nil', LIntArr.Memory = nil);

  LIntArr.Resize(4);
  AssertEquals('New size should be correct for Integer', 4, LIntArr.GetCount);
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);
  LIntArr.Put(0, 100);
  LIntArr.Put(1, 200);
  LIntArr.Put(2, 300);
  LIntArr.Put(3, 400);
  AssertEquals('Value should be stored correctly', 100, LIntArr.Get(0));
  AssertEquals('Value should be stored correctly', 200, LIntArr.Get(1));
  AssertEquals('Value should be stored correctly', 300, LIntArr.Get(2));
  AssertEquals('Value should be stored correctly', 400, LIntArr.Get(3));

  LIntArr.Resize(0);
  AssertEquals('New size should be correct for Integer', 0, LIntArr.GetCount);
  AssertTrue('Memory should be nil', LIntArr.Memory = nil);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(5);
  LStrArr.Resize(10);
  AssertEquals('New size should be correct for String', 10, LStrArr.GetCount);
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);

  LStrArr.Resize(3);
  AssertEquals('New size should be correct for String', 3, LStrArr.GetCount);
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);

  LStrArr.Resize(0);
  AssertEquals('New size should be correct for String', 0, LStrArr.GetCount);
  AssertTrue('Memory should be nil', LStrArr.Memory = nil);

  LStrArr.Resize(4);
  AssertEquals('New size should be correct for String', 4, LStrArr.GetCount);
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);
  LStrArr.Put(0, 'Hello');
  LStrArr.Put(1, 'World');
  LStrArr.Put(2, 'Test');
  LStrArr.Put(3, 'Test2');
  AssertEquals('Value should be stored correctly', 'Hello', LStrArr.Get(0));
  AssertEquals('Value should be stored correctly', 'World', LStrArr.Get(1));
  AssertEquals('Value should be stored correctly', 'Test',  LStrArr.Get(2));
  AssertEquals('Value should be stored correctly', 'Test2', LStrArr.Get(3));

  LStrArr.Resize(0);
  AssertEquals('New size should be correct for String', 0, LStrArr.GetCount);
  AssertTrue('Memory should be nil', LStrArr.Memory = nil);
end;

procedure TTestCase_Array.Test_Ensure;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create(5);
  LIntArr.Ensure(10);
  AssertEquals('Ensured size should be correct', 10, LIntArr.GetCount);
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  { 缩小会被忽略 }
  LIntArr.Ensure(3);
  AssertEquals('Ensured size should be correct', 10, LIntArr.GetCount);
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(5);
  LStrArr.Ensure(10);
  AssertEquals('Ensured size should be correct', 10, LStrArr.GetCount);
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);

  { 缩小会被忽略 }
  LStrArr.Ensure(3);
  AssertEquals('Ensured size should be correct', 10, LStrArr.GetCount);
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);
end;

procedure TTestCase_Array.Test_LoadFrom_Array;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntData: array of Integer;
  LStrData: array of string;
  I:        Integer;
begin
  { 从空数组加载 }
  LIntArr := specialize TArray<Integer>.Create;
  LIntData := [];
  AssertTrue('LoadFromArray should succeed: load empty array', LIntArr.LoadFrom(LIntData));
  AssertEquals('Size should be zero', 0, LIntArr.GetCount);
  AssertTrue('Memory should be nil', LIntArr.Memory = nil);

  { 从非空数组加载 }
  LIntData := [10, 20, 30, 40, 50];
  AssertTrue('LoadFromArray should succeed: load non-empty array', LIntArr.LoadFrom(LIntData));
  AssertEquals('Size should match source array', SizeUInt(Length(LIntData)), LIntArr.GetCount);

  for I := 0 to High(LIntData) do
    AssertEquals('Element should match source', LIntData[I], LIntArr.Get(I));

  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  { 数组已有数据,加载非空数组 }
  LIntData := [100, 200, 300];
  AssertTrue('LoadFromArray should succeed: load non-empty array', LIntArr.LoadFrom(LIntData));
  AssertEquals('Size should match source array', SizeUInt(Length(LIntData)), LIntArr.GetCount);

  for I := 0 to High(LIntData) do
    AssertEquals('Element should match source', LIntData[I], LIntArr.Get(I));

  { 数组已有数据,加载空数组 }
  AssertTrue('LoadFromArray should succeed: load empty array', LIntArr.LoadFrom([]));
  AssertEquals('Size should be zero', 0, LIntArr.GetCount);
  AssertTrue('Memory should be nil', LIntArr.Memory = nil);

  { 托管类型 }

  { 从空数组加载 }
  LStrArr := specialize TArray<string>.Create;
  LStrData := [];
  AssertTrue('LoadFromArray should succeed: load empty array', LStrArr.LoadFrom(LStrData));
  AssertEquals('Size should be zero', 0, LStrArr.GetCount);
  AssertTrue('Memory should be nil', LStrArr.Memory = nil);

  { 从非空数组加载 }
  LStrData := ['A', 'B', 'C', 'D', 'E'];
  AssertTrue('LoadFromArray should succeed: load non-empty array', LStrArr.LoadFrom(LStrData));
  AssertEquals('Size should match source array', SizeUInt(Length(LStrData)), LStrArr.GetCount);

  for I := 0 to High(LStrData) do
    AssertEquals('Element should match source', LStrData[I], LStrArr.Get(I));

  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);

  { 数组已有数据,加载非空数组 }
  LStrData := ['F', 'G', 'H'];
  AssertTrue('LoadFromArray should succeed: load non-empty array', LStrArr.LoadFrom(LStrData));
  AssertEquals('Size should match source array', SizeUInt(Length(LStrData)), LStrArr.GetCount);

  for I := 0 to High(LStrData) do
    AssertEquals('Element should match source', LStrData[I], LStrArr.Get(I));

  { 数组已有数据,加载空数组 }
  AssertTrue('LoadFromArray should succeed: load empty array', LStrArr.LoadFrom([]));
  AssertEquals('Size should be zero', 0, LStrArr.GetCount);
  AssertTrue('Memory should be nil', LStrArr.Memory = nil);
end;

procedure TTestCase_Array.Test_LoadFrom_Memory;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntData: array of Integer;
  LStrData: array of string;
  I:        Integer;
  LIntPtr:  PInteger;
  LStrPtr:  PString;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 加载 nil }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
  'LoadFromMemory should fail: load nil memory',
  ENil,
  procedure
  begin
    LIntArr.LoadFrom(nil, 1);
  end);

  { 异常测试: 加载容器自身范围内存 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'LoadFromMemory should fail: load container memory',
  EOverlap,
  procedure
  begin
    LIntArr.LoadFrom(LIntArr.Memory, 4);
  end);

  { 异常测试: 加载空内存 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
  'LoadFromMemory should fail: load empty memory',
  ENil,
  procedure
  begin
    LIntArr.LoadFrom(nil, 0);
  end);
  {$ENDIF}
  { 加载非空内存 }
  LIntArr := specialize TArray<Integer>.Create;
  LIntData := [10, 20, 30, 40, 50];
  AssertTrue('LoadFromMemory should succeed: load non-empty memory', LIntArr.LoadFrom(@LIntData[0], SizeUInt(Length(LIntData))));
  AssertEquals('Size should match source array', SizeUInt(Length(LIntData)), LIntArr.GetCount);

  for I := 0 to High(LIntData) do
    AssertEquals('Element should match source', LIntData[I], LIntArr.Get(I));

  { 通过内存指针初始化 }
  GetMem(LIntPtr, 3 * SizeOf(Integer));
  try
    LIntPtr[0] := 100;
    LIntPtr[1] := 200;
    LIntPtr[2] := 300;
    AssertTrue('LoadFromMemory should succeed: load non-empty memory', LIntArr.LoadFrom(LIntPtr, 3));
    AssertEquals('Size should match pointer data', 3, LIntArr.GetCount);
    AssertEquals('First element should be 100',  100, LIntArr.Get(0));
    AssertEquals('Second element should be 200', 200, LIntArr.Get(1));
    AssertEquals('Third element should be 300',  300, LIntArr.Get(2));
  finally
    FreeMem(LIntPtr);
  end;

  { 托管类型 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 加载空内存 }
  LStrArr := specialize TArray<string>.Create;
  AssertException(
  'LoadFromMemory should fail: load nil memory',
  ENil,
  procedure
  begin
    LStrArr.LoadFrom(nil, 1);
  end);
  {$ENDIF}

  { 加载非空内存 }
  LStrArr := specialize TArray<string>.Create;
  LStrData := ['A', 'B', 'C', 'D', 'E'];
  AssertTrue('LoadFromMemory should succeed: load non-empty memory', LStrArr.LoadFrom(@LStrData[0], SizeUInt(Length(LStrData))));
  AssertEquals('Size should match source array', SizeUInt(Length(LStrData)), LStrArr.GetCount);

  for I := 0 to High(LStrData) do
    AssertEquals('Element should match source', LStrData[I], LStrArr.Get(I));

  { 通过内存指针初始化 }
  GetMem(LStrPtr, 3 * SizeOf(string));
  try
    InitializeArray(LStrPtr, Typeinfo(string), 3);
    LStrPtr[0] := 'A';
    LStrPtr[1] := 'B';
    LStrPtr[2] := 'C';
    AssertTrue('LoadFromMemory should succeed: load non-empty memory', LStrArr.LoadFrom(LStrPtr, 3));
    AssertEquals('Size should match pointer data', 3, LStrArr.GetCount);
    AssertEquals('First element should be A',  'A', LStrArr.Get(0));
    AssertEquals('Second element should be B', 'B', LStrArr.Get(1));
    AssertEquals('Third element should be C',  'C', LStrArr.Get(2));
    FinalizeArray(LStrPtr, Typeinfo(string), 3);
  finally
    FreeMem(LStrPtr);
  end;
end;

procedure TTestCase_Array.Test_LoadFrom_Collection;
var
  LIntArr1:   specialize IArray<Integer>;
  LIntArr2:   specialize IArray<Integer>;
  LIntVec:    specialize IVec<Integer>;
  LInt64Arr1: specialize IArray<Int64>;
  LStrArr1:   specialize IArray<string>;
  LStrArr2:   specialize IArray<string>;
  LStrVec:    specialize IVec<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 加载自身 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'LoadFromCollection should fail: load self',
  ESelf,
  procedure
  begin
    LIntArr1.LoadFrom(LIntArr1 as TCollection);
  end);

  { 异常测试: 加载不兼容的泛型 }
  LInt64Arr1 := specialize TArray<Int64>.Create([1,2,3,4]);
  LIntArr1   := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertException(
  'LoadFromCollection should fail: load incompatible generic',
  ENotCompatible,
  procedure
  begin
    LIntArr1.LoadFrom(LInt64Arr1 as TCollection);
  end);

  { 异常测试: 加载nil容器 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertException(
  'LoadFromCollection should fail: load nil container',
  ENil,
  procedure
  begin
    LIntArr1.LoadFrom(LIntArr2 as TCollection);
  end);

  {$ENDIF}

  { 空数组加载空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue('LoadFromCollection should fail: load empty array', LIntArr1.LoadFrom(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 空数组加载非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue('LoadFromCollection should fail', LIntArr1.LoadFrom(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr1.Get(3));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  { 非空数组加载空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue('LoadFromCollection should fail', LIntArr1.LoadFrom(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 非空数组加载非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr2 := specialize TArray<Integer>.Create([4,5,6,7]);
  AssertTrue('LoadFromCollection should fail', LIntArr1.LoadFrom(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('First element should be 4',  4, LIntArr1.Get(0));
  AssertEquals('Second element should be 5', 5, LIntArr1.Get(1));
  AssertEquals('Third element should be 6',  6, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 7', 7, LIntArr1.Get(3));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  // From Vec

  { 空数组从空Vec加载 }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.LoadFrom(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);
  
  { 空数组从非空Vec加载 }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.LoadFrom(LIntVec as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr1.Get(3));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  { 非空数组从空Vec加载 }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue(LIntArr1.LoadFrom(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);
  
  { 非空数组从非空Vec加载 }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertTrue(LIntArr1.LoadFrom(LIntVec as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr1.Get(3));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  { 托管类型 }

  { 失败测试: 加载自身 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertFalse('LoadFromCollection should fail: load self', LStrArr1.LoadFrom(LStrArr1 as TCollection));

  { 失败测试: 加载不兼容的泛型 }
  LInt64Arr1 := specialize TArray<Int64>.Create([1,2,3,4]);
  LStrArr1   := specialize TArray<string>.Create(['a','b','c','d']);
  AssertFalse('LoadFromCollection should fail: load incompatible generic', LStrArr1.LoadFrom(LInt64Arr1 as TCollection));

  { 失败测试: 加载nil容器 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := nil;
  AssertFalse('LoadFromCollection should fail: load nil container', LStrArr1.LoadFrom(LStrArr2 as TCollection));

  { 空数组加载空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue('LoadFromCollection should fail: load empty array', LStrArr1.LoadFrom(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 空数组加载非空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue(LStrArr1.LoadFrom(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C', 'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组加载空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.LoadFrom(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 非空数组加载非空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue(LStrArr1.LoadFrom(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be E',  'E', LStrArr1.Get(0));
  AssertEquals('Second element should be F', 'F', LStrArr1.Get(1));
  AssertEquals('Third element should be G',  'G', LStrArr1.Get(2));
  AssertEquals('Fourth element should be H', 'H', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  // From Vec

  { 空数组从空Vec加载 }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.LoadFrom(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);
  
  { 空数组从非空Vec加载 }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.LoadFrom(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C', 'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组从空Vec加载 }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue(LStrArr1.LoadFrom(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);
  
  { 非空数组从非空Vec加载 }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue(LStrArr1.LoadFrom(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C', 'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  // todo: 应该提供更多不同类型容器的追加测试
end;

procedure TTestCase_Array.Test_SaveTo_Collection;
var
  LIntArr1:   specialize IArray<Integer>;
  LIntArr2:   specialize IArray<Integer>;
  LIntVec:    specialize IVec<Integer>;
  LInt64Arr1: specialize IArray<Int64>;
  LStrArr1:   specialize IArray<string>;
  LStrArr2:   specialize IArray<string>;
  LStrVec:    specialize IVec<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 容器保存到自身 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'SaveToCollection should fail: save to self',
  ESelf,
  procedure
  begin
    LIntArr1.SaveTo(LIntArr1 as TCollection);
  end);

  { 异常测试: 保存到不兼容的泛型容器 }
  LInt64Arr1 := specialize TArray<Int64>.Create([1,2,3,4]);
  LIntArr1   := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertException(
  'SaveToCollection should fail: save to incompatible generic',
  ENotCompatible,
  procedure
  begin
    LIntArr1.SaveTo(LInt64Arr1 as TCollection);
  end);

  { 异常测试: 保存到nil容器 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertException(
  'SaveToCollection should fail: save to nil container',
  ENil,
  procedure
  begin
    LIntArr1.SaveTo(LIntArr2 as TCollection);
  end);

  {$ENDIF}

  { 空数组容器保存到空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.SaveTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 空数组容器保存到非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue(LIntArr1.SaveTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr2.GetCount);
  AssertTrue('Memory should be nil', LIntArr2.Memory = nil);

  { 非空数组容器保存到空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.SaveTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr2.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr2.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr2.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr2.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr2.Get(3));
  AssertTrue('Memory should be allocated', LIntArr2.Memory <> nil);

  { 非空数组容器保存到非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertTrue(LIntArr1.SaveTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr2.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr2.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr2.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr2.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr2.Get(3));
  AssertTrue('Memory should be allocated', LIntArr2.Memory <> nil);

  // save to vec

  { 空数组容器保存到空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.SaveTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntVec.GetCount);

  { 空数组容器保存到非空Vec }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue(LIntArr1.SaveTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntVec.GetCount);

  { 非空数组容器保存到空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue(LIntArr1.SaveTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 4, LIntVec.GetCount);
  AssertEquals('First element should be 1',  1, LIntVec.Get(0));
  AssertEquals('Second element should be 2', 2, LIntVec.Get(1));
  AssertEquals('Third element should be 3',  3, LIntVec.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntVec.Get(3));

  { 非空数组容器保存到非空Vec }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertTrue(LIntArr1.SaveTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 4, LIntVec.GetCount);
  AssertEquals('First element should be 5',  5, LIntVec.Get(0));
  AssertEquals('Second element should be 6', 6, LIntVec.Get(1));
  AssertEquals('Third element should be 7',  7, LIntVec.Get(2));
  AssertEquals('Fourth element should be 8', 8, LIntVec.Get(3));

  { 托管类型 }

  { 失败测试: 容器保存到自身 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertFalse('SaveToCollection should fail: save to self', LStrArr1.SaveTo(LStrArr1 as TCollection));

  { 失败测试: 保存到不兼容的泛型容器 }
  LInt64Arr1 := specialize TArray<Int64>.Create([1,2,3,4]);
  LStrArr1   := specialize TArray<string>.Create(['a','b','c','d']);
  AssertFalse('SaveToCollection should fail: save to incompatible generic', LStrArr1.SaveTo(LInt64Arr1 as TCollection));

  { 失败测试: 保存到nil容器 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := nil;
  AssertFalse('SaveToCollection should fail: save to nil container', LStrArr1.SaveTo(LStrArr2 as TCollection));

  { 空数组容器保存到空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.SaveTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 空数组容器保存到非空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue(LStrArr1.SaveTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr2.GetCount);
  AssertTrue('Memory should be nil', LStrArr2.Memory = nil);

  { 非空数组容器保存到空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.SaveTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr2.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr2.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr2.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr2.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr2.Get(3));
  AssertTrue('Memory should be allocated', LStrArr2.Memory <> nil);

  { 非空数组容器保存到非空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue(LStrArr1.SaveTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr2.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr2.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr2.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr2.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr2.Get(3));
  AssertTrue('Memory should be allocated', LStrArr2.Memory <> nil);

  // save to Vec

  { 空数组容器保存到空Vec }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.SaveTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrVec.GetCount);

  { 空数组容器保存到非空Vec }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue(LStrArr1.SaveTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrVec.GetCount);

  { 非空数组容器保存到空Vec }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue(LStrArr1.SaveTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrVec.GetCount);
  AssertEquals('First element should be A',  'A', LStrVec.Get(0));
  AssertEquals('Second element should be B', 'B', LStrVec.Get(1));
  AssertEquals('Third element should be C',  'C', LStrVec.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrVec.Get(3));

  { 非空数组容器保存到非空Vec }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue(LStrArr1.SaveTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrVec.GetCount);
  AssertEquals('First element should be E',  'E', LStrVec.Get(0));
  AssertEquals('Second element should be F', 'F', LStrVec.Get(1));
  AssertEquals('Third element should be G',  'G', LStrVec.Get(2));
  AssertEquals('Fourth element should be H', 'H', LStrVec.Get(3));


  // todo: 应该继续提供保存到其他容器的测试

end;

procedure TTestCase_Array.Test_Append_Array;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  AssertTrue('Append should succeed: append non-empty array', LIntArr.Append([0,1,2,3]));
  AssertEquals('Size should match source array', 4, LIntArr.GetCount);
  AssertEquals('First element should be 0',  0, LIntArr.Get(0));
  AssertEquals('Second element should be 1', 1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',  2, LIntArr.Get(2));
  AssertEquals('Last element should be 3',   3, LIntArr.Get(3));

  AssertTrue('Append should succeed: append non-empty array', LIntArr.Append([4,5,6,7]));
  AssertEquals('Size should match source array', 8, LIntArr.GetCount);
  AssertEquals('First element should be 0',   0, LIntArr.Get(0));
  AssertEquals('Second element should be 1',  1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',   2, LIntArr.Get(2));
  AssertEquals('Fourth element should be 3',  3, LIntArr.Get(3));
  AssertEquals('Fifth element should be 4',   4, LIntArr.Get(4));
  AssertEquals('Sixth element should be 5',   5, LIntArr.Get(5));
  AssertEquals('Seventh element should be 6', 6, LIntArr.Get(6));
  AssertEquals('Eighth element should be 7',  7, LIntArr.Get(7));

  AssertTrue('Append should succeed: append empty array', LIntArr.Append([]));
  AssertEquals('Size should match source array', 8, LIntArr.GetCount);
  AssertEquals('First element should be 0',   0, LIntArr.Get(0));
  AssertEquals('Second element should be 1',  1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',   2, LIntArr.Get(2));
  AssertEquals('Fourth element should be 3',  3, LIntArr.Get(3));
  AssertEquals('Fifth element should be 4',   4, LIntArr.Get(4));
  AssertEquals('Sixth element should be 5',   5, LIntArr.Get(5));
  AssertEquals('Seventh element should be 6', 6, LIntArr.Get(6));
  AssertEquals('Eighth element should be 7',  7, LIntArr.Get(7));

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create;
  AssertTrue('Append should succeed: append non-empty array', LStrArr.Append(['A','B','C']));
  AssertEquals('Size should match source array', 3, LStrArr.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr.Get(2));

  AssertTrue('Append should succeed: append non-empty array', LStrArr.Append(['D','E','F']));
  AssertEquals('Size should match source array', 6, LStrArr.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr.Get(3));

  AssertTrue('Append should succeed: append empty array', LStrArr.Append([]));
  AssertEquals('Size should match source array', 6, LStrArr.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr.Get(3));
  AssertEquals('Fifth element should be E',  'E', LStrArr.Get(4));
  AssertEquals('Sixth element should be F',  'F', LStrArr.Get(5));
end;

procedure TTestCase_Array.Test_ToArray;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntData: array of Integer;
  LStrData: array of string;
begin
  Initialize(LIntData);
  Initialize(LStrData);

  { 空数组 }
  LIntArr  := specialize TArray<Integer>.Create;
  LIntData := LIntArr.ToArray;
  AssertEquals('Size should match source array', 0, Length(LIntData));

  { 非空数组 }
  LIntArr  := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntData := LIntArr.ToArray;
  AssertEquals('Size should match source array', 4, Length(LIntData));
  AssertEquals('First element should be 1',  1, LIntData[0]);
  AssertEquals('Second element should be 2', 2, LIntData[1]);
  AssertEquals('Third element should be 3',  3, LIntData[2]);
  AssertEquals('Fourth element should be 4', 4, LIntData[3]);

  { 托管类型 }

  { 空数组 }
  LStrArr  := specialize TArray<string>.Create;
  LStrData := LStrArr.ToArray;
  AssertEquals('Size should match source array', 0, Length(LStrData));

  { 非空数组 }
  LStrArr  := specialize TArray<string>.Create(['A','B','C','D']);
  LStrData := LStrArr.ToArray;
  AssertEquals('Size should match source array', 4, Length(LStrData));
  AssertEquals('First element should be A',  'A', LStrData[0]);
  AssertEquals('Second element should be B', 'B', LStrData[1]);
  AssertEquals('Third element should be C',  'C', LStrData[2]);
  AssertEquals('Fourth element should be D', 'D', LStrData[3]);
end;

procedure TTestCase_Array.Test_Reverse1;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntArr.Reverse;
  AssertEquals(8, LIntArr.Get(0));
  AssertEquals(7, LIntArr.Get(1));
  AssertEquals(6, LIntArr.Get(2));
  AssertEquals(5, LIntArr.Get(3));
  AssertEquals(4, LIntArr.Get(4));
  AssertEquals(3, LIntArr.Get(5));
  AssertEquals(2, LIntArr.Get(6));
  AssertEquals(1, LIntArr.Get(7));

  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8,9]);
  LIntArr.Reverse;
  AssertEquals(9, LIntArr.Get(0));
  AssertEquals(8, LIntArr.Get(1));
  AssertEquals(7, LIntArr.Get(2));
  AssertEquals(6, LIntArr.Get(3));
  AssertEquals(5, LIntArr.Get(4));
  AssertEquals(4, LIntArr.Get(5));
  AssertEquals(3, LIntArr.Get(6));
  AssertEquals(2, LIntArr.Get(7));
  AssertEquals(1, LIntArr.Get(8));

  LStrArr := specialize TArray<string>.Create(['A','B','C','D','E','F','G','H','I']);
  LStrArr.Reverse;
  AssertEquals('I', LStrArr.Get(0));
  AssertEquals('H', LStrArr.Get(1));
  AssertEquals('G', LStrArr.Get(2));
  AssertEquals('F', LStrArr.Get(3));
  AssertEquals('E', LStrArr.Get(4));
  AssertEquals('D', LStrArr.Get(5));
  AssertEquals('C', LStrArr.Get(6));
  AssertEquals('B', LStrArr.Get(7));
  AssertEquals('A', LStrArr.Get(8));
  
  LStrArr := specialize TArray<string>.Create(['A','B','C','D','E','F','G','H','I','J']);
  LStrArr.Reverse;
  AssertEquals('J', LStrArr.Get(0));
  AssertEquals('I', LStrArr.Get(1));
  AssertEquals('H', LStrArr.Get(2));
  AssertEquals('G', LStrArr.Get(3));
  AssertEquals('F', LStrArr.Get(4));
  AssertEquals('E', LStrArr.Get(5));
  AssertEquals('D', LStrArr.Get(6));
  AssertEquals('C', LStrArr.Get(7));
  AssertEquals('B', LStrArr.Get(8));
  AssertEquals('A', LStrArr.Get(9));
end;

procedure TTestCase_Array.Test_Reverse2;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntArr.Reverse(2);
  AssertEquals(1, LIntArr.Get(0));
  AssertEquals(2, LIntArr.Get(1));
  AssertEquals(8, LIntArr.Get(2));
  AssertEquals(7, LIntArr.Get(3));
  AssertEquals(6, LIntArr.Get(4));
  AssertEquals(5, LIntArr.Get(5));
  AssertEquals(4, LIntArr.Get(6));
  AssertEquals(3, LIntArr.Get(7));

  LStrArr := specialize TArray<string>.Create(['A','B','C','D','E','F','G','H','I','J']);
  LStrArr.Reverse(2);
  AssertEquals('A', LStrArr.Get(0));
  AssertEquals('B', LStrArr.Get(1));
  AssertEquals('J', LStrArr.Get(2));
  AssertEquals('I', LStrArr.Get(3));
  AssertEquals('H', LStrArr.Get(4));
  AssertEquals('G', LStrArr.Get(5));
  AssertEquals('F', LStrArr.Get(6));
  AssertEquals('E', LStrArr.Get(7));
  AssertEquals('D', LStrArr.Get(8));
  AssertEquals('C', LStrArr.Get(9));
end;

procedure TTestCase_Array.Test_Reverse3;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntArr.Reverse(2, 3);
  AssertEquals(1, LIntArr.Get(0));
  AssertEquals(2, LIntArr.Get(1));
  AssertEquals(5, LIntArr.Get(2));
  AssertEquals(4, LIntArr.Get(3));
  AssertEquals(3, LIntArr.Get(4));
  AssertEquals(6, LIntArr.Get(5));
  AssertEquals(7, LIntArr.Get(6));
  AssertEquals(8, LIntArr.Get(7));

  LStrArr := specialize TArray<string>.Create(['A','B','C','D','E','F','G','H','I','J']);
  LStrArr.Reverse(2, 3);
  AssertEquals('A', LStrArr.Get(0));
  AssertEquals('B', LStrArr.Get(1));
  AssertEquals('E', LStrArr.Get(2));
  AssertEquals('D', LStrArr.Get(3));
  AssertEquals('C', LStrArr.Get(4));
  AssertEquals('F', LStrArr.Get(5));
  AssertEquals('G', LStrArr.Get(6));
  AssertEquals('H', LStrArr.Get(7));
  AssertEquals('I', LStrArr.Get(8));
  AssertEquals('J', LStrArr.Get(9));
end;

function forEachCounter(const aValue: Integer; aData: Pointer): Boolean;
begin
  Inc(PSIZEUINT(aData)^);
  Result := True;
end;

function forEachInt1(const aValue: Integer; aData: Pointer): Boolean;
begin
  if SizeUInt(aData^) <> SizeUInt(aValue) then
    raise Exception.Create('Value mismatch');

  Inc(SizeUInt(aData^));
  Result := True;
end;

function forEachInt2(const aValue: Integer; aData: Pointer): Boolean;
begin
  if SizeUInt(aData^) <> SizeUInt(aValue) then
    raise Exception.Create('Value mismatch');

  Inc(SizeUInt(aData^));
  Result := SizeUInt(aData^) < 2;
end;

function forEachStr1(const aValue: string; aData: Pointer): Boolean;
begin
  if SizeUInt(aData^) <> SizeUInt(StrToInt(aValue)) then
    raise Exception.Create('Value mismatch');

  Inc(SizeUInt(aData^));
  Result := True;
end;

function forEachStr2(const aValue: string; aData: Pointer): Boolean;
begin
  if SizeUInt(aData^) <> SizeUInt(StrToInt(aValue)) then
    raise Exception.Create('Value mismatch');

  Inc(SizeUInt(aData^));
  Result := SizeUInt(aData^) < 2;
end;

procedure TTestCase_Array.Test_ForEach1;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基础测试 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LIntArr.ForEach(@forEachInt1, @LCounter));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LIntArr := specialize TArray<Integer>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LIntArr.ForEach(@forEachInt1, @LCounter));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LIntArr.ForEach(@forEachInt2, @LCounter));
  AssertEquals('Count should be 2', 2, LCounter);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LStrArr.ForEach(@forEachStr1, @LCounter));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LStrArr := specialize TArray<string>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LStrArr.ForEach(@forEachStr1, @LCounter));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LStrArr.ForEach(@forEachStr2, @LCounter));
  AssertEquals('Count should be 2', 2, LCounter);
end;

procedure TTestCase_Array.Test_ForEach2;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基础测试 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertTrue('ForEach should succeed',LIntArr.ForEach(@DoForEachInt1, @LCounter));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LIntArr := specialize TArray<Integer>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LIntArr.ForEach(@DoForEachInt1, @LCounter));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LIntArr.ForEach(@DoForEachInt2, @LCounter));
  AssertEquals('Count should be 2', 2, LCounter);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LStrArr.ForEach(@DoForEachStr1, @LCounter));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LStrArr := specialize TArray<string>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LStrArr.ForEach(@DoForEachStr1, @LCounter));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LStrArr.ForEach(@DoForEachStr2, @LCounter));
  AssertEquals('Count should be 2', 2, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_ForEach3;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基础测试 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertTrue('ForEach should succeed',
    LIntArr.ForEach(
      function(const aValue: Integer): Boolean
      begin
        AssertEquals(LCounter, SizeUInt(aValue));
        Inc(LCounter);
        Result := True;
      end
  ));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LIntArr := specialize TArray<Integer>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed',
    LIntArr.ForEach(
      function(const aValue: Integer): Boolean
      begin
        AssertEquals(LCounter, SizeUInt(aValue));
        Inc(LCounter);
        Result := True;
      end
  ));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LIntArr.ForEach(
    function(const aValue: Integer): Boolean
    begin
      AssertEquals(LCounter, SizeUInt(aValue));
      Inc(LCounter);
      Result := LCounter < 2;
    end
  ));
  AssertEquals('Count should be 2', 2, LCounter);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertTrue(
  'ForEach should succeed', 
  LStrArr.ForEach(
    function(const aValue: string): Boolean
    begin
      AssertEquals(LCounter, SizeUInt(StrToInt(aValue)));
      Inc(LCounter);
      Result := True;
    end
  ));
  AssertEquals('Count should be 4', 4, LCounter);

  { 空数组 }
  LStrArr := specialize TArray<string>.Create;
  LCounter  := 0;
  AssertTrue('ForEach should succeed', LStrArr.ForEach(
    function(const aValue: string): Boolean
    begin
      AssertEquals(LCounter, SizeUInt(StrToInt(aValue)));
      Inc(LCounter);
      Result := True;
    end
  ));
  AssertEquals('Count should be 0', 0, LCounter);

  { 跳出遍历 }
  LStrArr := specialize TArray<string>.Create(['0','1','2','3']);
  LCounter  := 0;
  AssertFalse('ForEach should succeed', LStrArr.ForEach(
    function(const aValue: string): Boolean
    begin
      AssertEquals(LCounter, SizeUInt(StrToInt(aValue)));
      Inc(LCounter);
      Result := LCounter < 2;
    end
  ));
  AssertEquals('Count should be 2', 2, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_ForEach4;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基础测试 }

  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5]);

  { 从头开始遍历 }
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=0, GlobalFunc) should succeed', LIntArr.ForEach(0, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=0, GlobalFunc) should iterate 6 times', 6, LCounter);

  { 从中间开始遍历 }
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=2, GlobalFunc) should succeed', LIntArr.ForEach(2, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=2, GlobalFunc) should iterate 4 times', 4, LCounter);

  { 从末尾开始遍历 }
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=5, GlobalFunc) should succeed', LIntArr.ForEach(5, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=5, GlobalFunc) should iterate 1 time', 1, LCounter);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 索引越界，不应执行 }
  LCounter := 0;
  AssertException(
    'Failed to ForEach: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.ForEach(6, @GlobalForEach_IntCounter, @LCounter);
    end);
  AssertEquals('ForEach(StartIndex=6, GlobalFunc) should without iterating', 0, LCounter);
  {$ENDIF}

  { 托管类型 (string) }
  LStrArr := specialize TArray<string>.Create(['0', '1', '2', '3', '4', '5']);
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=0, GlobalFunc, String) should succeed', LStrArr.ForEach(0, @GlobalForEach_StrCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=0, GlobalFunc, String) should iterate 6 times', 6, LCounter);

  LCounter := 0;
  AssertTrue('ForEach(StartIndex=3, GlobalFunc, String) should succeed', LStrArr.ForEach(3, @GlobalForEach_StrCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=3, GlobalFunc, String) should iterate 3 times', 3, LCounter);
end;

procedure TTestCase_Array.Test_ForEach5;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基础测试 }

  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5]);

  { 从头开始遍历 }
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=0, Method) should succeed', LIntArr.ForEach(0, @ForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=0, Method) should iterate 6 times', 6, LCounter);

  { 从中间开始遍历 }
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=2, Method) should succeed', LIntArr.ForEach(2, @ForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=2, Method) should iterate 4 times', 4, LCounter);

  { 托管类型 (string) }
  LStrArr := specialize TArray<string>.Create(['0', '1', '2', '3', '4', '5']);
  LCounter := 0;
  AssertTrue('ForEach(StartIndex=0, Method, String) should succeed', LStrArr.ForEach(0, @ForEach_StrCounter, @LCounter));
  AssertEquals('ForEach(StartIndex=0, Method, String) should iterate 6 times', 6, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_ForEach6;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 基本测试 }
  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5]);
  LCounter := 0;
  AssertTrue('ForEach(StartIndex, RefFunc) should succeed',
    LIntArr.ForEach(2,
      function(const aValue: Integer): Boolean
      begin
        Inc(LCounter);
        Result := True;
      end
    )
  );
  AssertEquals('ForEach(StartIndex, RefFunc) should iterate 4 times', 4, LCounter);

  { 托管类型 (string) }
  LStrArr := specialize TArray<string>.Create(['0', '1', '2', '3', '4', '5']);
  LCounter := 0;
  AssertTrue('ForEach(StartIndex, RefFunc, String) should succeed',
    LStrArr.ForEach(3,
      function(const aValue: string): Boolean
      begin
        Inc(LCounter);
        Result := True;
      end
    )
  );
  AssertEquals('ForEach(StartIndex, RefFunc, String) should iterate 3 times', 3, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_ForEach7;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5, 6, 7]);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 范围越界 }
  LCounter := 0;
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure
    begin
      LIntArr.ForEach(0, 9, @GlobalForEach_IntCounter, @LCounter);
    end);
  AssertEquals('ForEach(Range, GlobalFunc) should without iterating', 0, LCounter);
  {$ENDIF}

  { 完整遍历 }
  LCounter := 0;
  AssertTrue('ForEach(Range, GlobalFunc) full scan', LIntArr.ForEach(0, 8, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc) full scan count', 8, LCounter);

  { 部分遍历 }
  LCounter := 0;
  AssertTrue('ForEach(Range, GlobalFunc) partial scan', LIntArr.ForEach(2, 4, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc) partial scan count', 4, LCounter);

  { 遍历到结尾 }
  LCounter := 0;
  AssertTrue('ForEach(Range, GlobalFunc) to the end', LIntArr.ForEach(5, 3, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc) to the end count', 3, LCounter);

  { 提前中断 }
  LCounter := 0;
  AssertFalse('ForEach(Range, GlobalFunc) should stop early', LIntArr.ForEach(1, 6, @GlobalForEach_IntCounter_Stop, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc) early stop count', 3, LCounter);

  { 遍历数量为0 }
  LCounter := 0;
  AssertTrue('ForEach(Range, GlobalFunc) with count=0', LIntArr.ForEach(1, 0, @GlobalForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc) with count=0 count', 0, LCounter);

  { 托管类型 (string) }
  LStrArr := specialize TArray<string>.Create(['0', '1', '2', '3', '4']);
  LCounter := 0;
  AssertTrue('ForEach(Range, GlobalFunc, String) partial scan', LStrArr.ForEach(1, 3, @GlobalForEach_StrCounter, @LCounter));
  AssertEquals('ForEach(Range, GlobalFunc, String) partial scan count', 3, LCounter);
end;

procedure TTestCase_Array.Test_ForEach8;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 值类型 (Integer) }
  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5, 6, 7]);

  { 部分遍历 }
  LCounter := 0;
  AssertTrue('ForEach(Range, Method) partial scan', LIntArr.ForEach(2, 4, @Self.ForEach_IntCounter, @LCounter));
  AssertEquals('ForEach(Range, Method) partial scan count', 4, LCounter);

  { 提前中断 }
  LCounter := 0;
  AssertFalse('ForEach(Range, Method) should stop early', LIntArr.ForEach(1, 6, @Self.ForEach_IntCounter_Stop, @LCounter));
  AssertEquals('ForEach(Range, Method) early stop count', 3, LCounter);

  { 托管类型 (string) }
  LStrArr := specialize TArray<string>.Create(['0', '1', '2', '3', '4']);
  LCounter := 0;
  AssertTrue('ForEach(Range, Method, String) partial scan', LStrArr.ForEach(1, 3, @Self.ForEach_StrCounter, @LCounter));
  AssertEquals('ForEach(Range, Method, String) partial scan count', 3, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_ForEach9;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LCounter: SizeUInt;
begin
  { 值类型 (Integer) }
  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5, 6, 7]);

  { 部分遍历 }
  LCounter := 0;
  AssertTrue('ForEach(Range, RefFunc) partial scan',
    LIntArr.ForEach(2, 4,
      function(const aValue: Integer): Boolean
      begin
        Inc(LCounter);
        Result := True;
      end
    )
  );
  AssertEquals('ForEach(Range, RefFunc) partial scan count', 4, LCounter);

  { 提前中断 }
  LCounter := 0;
  AssertFalse('ForEach(Range, RefFunc) should stop early',
    LIntArr.ForEach(1, 6,
      function(const aValue: Integer): Boolean
      begin
        Inc(LCounter);
        Result := LCounter < 3;
      end
    )
  );
  AssertEquals('ForEach(Range, RefFunc) early stop count', 3, LCounter);

  { 托管类型 (string) }
  LStrArr  := specialize TArray<string>.Create(['0', '1', '2', '3', '4']);
  LCounter := 0;
  AssertTrue('ForEach(Range, RefFunc, String) partial scan',
    LStrArr.ForEach(1, 3,
      function(const aValue: string): Boolean
      begin
        Inc(LCounter);
        Result := True;
      end
    )
  );
  AssertEquals('ForEach(Range, RefFunc, String) partial scan count', 3, LCounter);
end;
{$ENDIF}

function make_custom_compare_data(const aA, aB, aC, aD: Integer): custom_data_t;
begin
  Result.a := aA;
  Result.b := aB;
  Result.c := aC;
  Result.d := aD;
end;

procedure TTestCase_Array.Test_Contains1;
var
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4));

  { 成功测试 }
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3));

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000,10001,10002,10003]);

  { 失败测试 }
  AssertFalse('Contains should fail', LUInt16Arr.Contains(10004));

  { 成功测试 }
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10000));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10001));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10002));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10003));

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([100000000,100000001,100000002,100000003]);

  { 失败测试 }
  AssertFalse('Contains should fail', LUInt32Arr.Contains(100000004));

  { 成功测试 }
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000000));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000001));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000002));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000003));

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000,10000000000000001,10000000000000002,10000000000000003]);

  { 失败测试 }
  AssertFalse('Contains should fail', LUInt64Arr.Contains(10000000000000004));

  { 成功测试 }
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000000));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000001));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000002));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000003));

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  AssertFalse('Contains should fail', LStrArr.Contains('E'));

  { 成功测试 }
  AssertTrue('Contains should succeed', LStrArr.Contains('A'));
  AssertTrue('Contains should succeed', LStrArr.Contains('B'));
  AssertTrue('Contains should succeed', LStrArr.Contains('C'));
  AssertTrue('Contains should succeed', LStrArr.Contains('D'));

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 失败测试 }
  AssertFalse('Contains should fail', LCustomArr.Contains(make_custom_compare_data(100,200,300,400)));

  { 成功测试 }
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(1,2,3,4)));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(5,6,7,8)));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(9,10,11,12)));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(13,14,15,16)));
end;


procedure TTestCase_Array.Test_Contains2;
var
  LCounter:   Integer;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, @custom_equals_u8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000,10001,10002,10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(10004, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10000, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10001, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10002, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10003, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(10004, @custom_equals_u16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([100000000,100000001,100000002,100000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(100000004, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000000, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000001, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000002, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000003, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(100000004, @custom_equals_u32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000,10000000000000001,10000000000000002,10000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(10000000000000004, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000000, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000001, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000002, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000003, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(10000000000000004, @custom_equals_u64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', @custom_equals_string, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400),
    @custom_equals_custom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 
    @custom_equals_custom, 
    @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 
    @custom_equals_custom, 
    @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12),
    @custom_equals_custom,
    @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16),
    @custom_equals_custom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(17,18,19,20),
    @custom_equals_custom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);
end;

procedure TTestCase_Array.Test_Contains3;
var
  LCounter:   Integer;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, @DoEqualsU8, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000,10001,10002,10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(10004, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10000, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10001, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10002, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(10003, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(10004, @DoEqualsU16, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([100000000,100000001,100000002,100000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(100000004, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000000, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000001, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000002, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(100000003, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(100000004, @DoEqualsU32, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000,10000000000000001,10000000000000002,10000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(10000000000000004, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000000, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000001, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000002, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(10000000000000003, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(10000000000000004, @DoEqualsU64, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', @DoEqualsString, @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400),
    @DoEqualsCustom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 
    @DoEqualsCustom, 
    @LCounter));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 
    @DoEqualsCustom, 
    @LCounter));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12),
    @DoEqualsCustom,
    @LCounter));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16),
    @DoEqualsCustom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(17,18,19,20),
    @DoEqualsCustom,
    @LCounter));
  AssertEquals('Data should be 4', 4, LCounter);
end;

procedure TTestCase_Array.Test_Contains4;
var
  LCounter:   Integer;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(
  4,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  0,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  1,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  2,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  3,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000,10001,10002,10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(
  10004,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  10000,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  10001,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  10002,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := equals_u16(aLeft, aRight);
    inc(LCounter);
  end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  10003,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([100000000,100000001,100000002,100000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(
  100000004,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  100000000,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  100000001,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  100000002,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  100000003,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000,10000000000000001,10000000000000002,10000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(
  10000000000000004,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  10000000000000000,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  10000000000000001,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  10000000000000002,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  10000000000000003,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains(
  'E',
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'A',
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'B',
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'C',
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'D',
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Data should be 4', 4, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400),
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Data should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Data should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Data should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12),
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Data should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16),
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Data should be 4', 4, LCounter);
end;

procedure TTestCase_Array.Test_Contains5;
var
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空数组 }
  LUInt8Arr := specialize TArray<UInt8>.Create;
  AssertException(
    'exception should be raised: count is 0',
    ERangeOutOfIndex,
    procedure
    begin 
    LUInt8Arr.Contains(0, 0);
    end);

  { 异常测试: 索引越界 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin 
      LUInt8Arr.Contains(0, 4);
    end);

  { 异常测试: 范围越界 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LUInt8Arr.Contains(1, 4); 
    end);
  {$ENDIF}

  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3));
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0));
  AssertFalse('Contains should fail', LUInt8Arr.Contains(0, 1));
  AssertFalse('Contains should fail', LUInt8Arr.Contains(1, 2));
  AssertFalse('Contains should fail', LUInt8Arr.Contains(2, 3));
  
  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3));
  AssertFalse('Contains should fail', LUInt16Arr.Contains(4, 0));
  AssertFalse('Contains should fail', LUInt16Arr.Contains(0, 1));
  AssertFalse('Contains should fail', LUInt16Arr.Contains(1, 2));
  AssertFalse('Contains should fail', LUInt16Arr.Contains(2, 3));

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3));
  AssertFalse('Contains should fail', LUInt32Arr.Contains(4, 0));
  AssertFalse('Contains should fail', LUInt32Arr.Contains(0, 1));
  AssertFalse('Contains should fail', LUInt32Arr.Contains(1, 2));
  AssertFalse('Contains should fail', LUInt32Arr.Contains(2, 3));

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3));
  AssertFalse('Contains should fail', LUInt64Arr.Contains(4, 0));
  AssertFalse('Contains should fail', LUInt64Arr.Contains(0, 1));
  AssertFalse('Contains should fail', LUInt64Arr.Contains(1, 2));
  AssertFalse('Contains should fail', LUInt64Arr.Contains(2, 3));

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0));
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1));
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2));
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3));
  AssertFalse('Contains should fail', LStrArr.Contains('E', 0));
  AssertFalse('Contains should fail', LStrArr.Contains('A', 1));
  AssertFalse('Contains should fail', LStrArr.Contains('B', 2));
  AssertFalse('Contains should fail', LStrArr.Contains('C', 3));

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(1,2,3,4), 0));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(5,6,7,8), 1));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(9,10,11,12), 2));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(13,14,15,16), 3));
  AssertFalse('Contains should fail',   LCustomArr.Contains(make_custom_compare_data(17,18,19,20), 0));
  AssertFalse('Contains should fail',   LCustomArr.Contains(make_custom_compare_data(1,2,3,4), 1));
  AssertFalse('Contains should fail',   LCustomArr.Contains(make_custom_compare_data(5,6,7,8), 2));
  AssertFalse('Contains should fail',   LCustomArr.Contains(make_custom_compare_data(9,10,11,12), 3));
end;

procedure TTestCase_Array.Test_Contains6;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(4, 0, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(4, 0, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(4, 0, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', 0, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3, @custom_equals_string, @LCounter)); 
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);
  
  { 失败测试}
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400), 0, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
end;

procedure TTestCase_Array.Test_Contains7;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(4, 0, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(4, 0, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(4, 0, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', 0, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3, @DoEqualsString, @LCounter)); 
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);
  
  { 失败测试}
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400), 0, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Contains8;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(
  4, 0, 
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  0, 0, 
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  1, 1, 
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  2, 2, 
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  3, 3, 
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(
  4, 0, 
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  0, 0, 
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  1, 1, 
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  2, 2, 
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  3, 3, 
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(
  4, 0, 
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  0, 0, 
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  1, 1, 
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  2, 2, 
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  3, 3, 
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(
  4, 0, 
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  0, 0, 
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  1, 1, 
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  2, 2, 
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  3, 3, 
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains(
  'E', 0, 
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'A', 0, 
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'B', 1, 
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'C', 2, 
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'D', 3, 
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);
  
  { 失败测试}
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(100,200,300,400), 0, 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, 
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Contains9;
var
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 异常测试: 0 数量 }
  AssertException(
    'exception should be raised: count is 0',
    EIsZero, 
    procedure
    begin
      LUInt8Arr.Contains(0, 0, 0)
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex, 
    procedure
    begin
      LUInt8Arr.Contains(0, 4, 1)
    end);

  { 测试: 范围越界 }
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds, 
    procedure
    begin
      LUInt8Arr.Contains(0, 1, 4)
    end);
  {$ENDIF}

  { 错误测试: 不存在 }
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0, 4));


  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0, 1));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1, 1));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2, 1));
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3, 1));

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0, 1));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1, 1));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2, 1));
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3, 1));

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0, 1));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1, 1));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2, 1));
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3, 1));

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0, 1));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1, 1));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2, 1));
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3, 1));

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0, 1));
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1, 1));
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2, 1));
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3, 1));

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(1,2,3,4), 0, 1));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(5,6,7,8), 1, 1));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(9,10,11,12), 2, 1));
  AssertTrue('Contains should succeed', LCustomArr.Contains(make_custom_compare_data(13,14,15,16), 3, 1));
end;

procedure TTestCase_Array.Test_Contains10;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3, 1, @custom_equals_u8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(4, 0, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3, 1, @custom_equals_u16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(4, 0, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3, 1, @custom_equals_u32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(4, 0, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3, 1, @custom_equals_u64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', 0, 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0, 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1, 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2, 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3, 1, @custom_equals_string, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(17,18,19,20), 0, 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, 1, @custom_equals_custom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
end;

procedure TTestCase_Array.Test_Contains11;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(4, 0, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(0, 0, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(1, 1, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(2, 2, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(3, 3, 1, @DoEqualsU8, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(4, 0, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(0, 0, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(1, 1, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(2, 2, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(3, 3, 1, @DoEqualsU16, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(4, 0, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(0, 0, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(1, 1, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(2, 2, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(3, 3, 1, @DoEqualsU32, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(4, 0, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(0, 0, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(1, 1, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(2, 2, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(3, 3, 1, @DoEqualsU64, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains('E', 0, 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('A', 0, 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('B', 1, 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('C', 2, 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains('D', 3, 1, @DoEqualsString, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(17,18,19,20), 0, 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, 1, @DoEqualsCustom, @LCounter));
  AssertEquals('Counter should be 1', 1, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Contains12;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt8Arr.Contains(
  4, 0, 1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  0, 0, 1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  1, 1, 1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  2, 2, 1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt8Arr.Contains(
  3, 3, 1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt16Arr.Contains(
  4, 0, 1,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  0, 0, 1,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  1, 1, 1,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  2, 2, 1,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt16Arr.Contains(
  3, 3, 1,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);


  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt32Arr.Contains(
  4, 0, 1,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  0, 0, 1,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  1, 1, 1,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  2, 2, 1,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt32Arr.Contains(
  3, 3, 1,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LUInt64Arr.Contains(
  4, 0, 1,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  0, 0, 1,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  1, 1, 1,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  2, 2, 1,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LUInt64Arr.Contains(
  3, 3, 1,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LStrArr.Contains(
  'E', 0, 1,
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'A', 0, 1,
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'B', 1, 1,
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'C', 2, 1,
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LStrArr.Contains(
  'D', 3, 1,
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('Contains should fail', LCustomArr.Contains(
    make_custom_compare_data(17,18,19,20), 0, 1,
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(1,2,3,4), 0, 1,
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(5,6,7,8), 1, 1,
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(9,10,11,12), 2, 1,
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('Contains should succeed', LCustomArr.Contains(
    make_custom_compare_data(13,14,15,16), 3, 1,
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end));
  AssertEquals('Counter should be 1', 1, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Append_Memory;
const
  INT1: array[0..3] of Integer = (0,1,2,3);
  INT2: array[0..3] of Integer = (4,5,6,7);
  STR1: array[0..3] of string = ('A','B','C','D');
  STR2: array[0..3] of string = ('E','F','G','H');
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 追加 nil }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
  'AppendFromMemory should fail: append nil memory',
  ENil,
  procedure
  begin
    LIntArr.Append(nil, 1);
  end);

  { 异常测试: 追加容器自身范围内存 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'AppendFromMemory should fail: append container memory',
  EOverlap,
  procedure
  begin
    LIntArr.Append(LIntArr.Memory, 4);
  end);

  { 追加空内存 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
  'AppendFromMemory should fail: append empty memory',
  ENil,
  procedure
  begin
    LIntArr.Append(nil, 0);
  end);
  {$ENDIF}

  { 追加非空内存 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertTrue('AppendFromMemory should succeed: append non-empty memory', LIntArr.Append(@INT1[0], 4));
  AssertEquals('Size should match source array', 4, LIntArr.GetCount);
  AssertEquals('First element should be 0',  0, LIntArr.Get(0));
  AssertEquals('Second element should be 1', 1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',  2, LIntArr.Get(2));
  AssertEquals('Fourth element should be 3', 3, LIntArr.Get(3));
  AssertTrue('Memory should be allocated', LIntArr.Memory <> nil);

  { 继续追击非空内存 }
  AssertTrue('AppendFromMemory should succeed: append non-empty memory', LIntArr.Append(@INT2[0], 4));
  AssertEquals('Size should match source array', 8, LIntArr.GetCount);
  AssertEquals('First element should be 0',   0, LIntArr.Get(0));
  AssertEquals('Second element should be 1',  1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',   2, LIntArr.Get(2));
  AssertEquals('Fourth element should be 3',  3, LIntArr.Get(3));
  AssertEquals('Fifth element should be 4',   4, LIntArr.Get(4));
  AssertEquals('Sixth element should be 5',   5, LIntArr.Get(5));
  AssertEquals('Seventh element should be 6', 6, LIntArr.Get(6));
  AssertEquals('Eighth element should be 7',  7, LIntArr.Get(7));

  { 追加空内存 }
  AssertTrue('AppendFromMemory should succeed: append empty memory', LIntArr.Append(nil, 0));
  AssertEquals('Size should match source array', 8, LIntArr.GetCount);
  AssertEquals('First element should be 0',   0, LIntArr.Get(0));
  AssertEquals('Second element should be 1',  1, LIntArr.Get(1));
  AssertEquals('Third element should be 2',   2, LIntArr.Get(2));
  AssertEquals('Fourth element should be 3',  3, LIntArr.Get(3));
  AssertEquals('Fifth element should be 4',   4, LIntArr.Get(4));
  AssertEquals('Sixth element should be 5',   5, LIntArr.Get(5));
  AssertEquals('Seventh element should be 6', 6, LIntArr.Get(6));
  AssertEquals('Eighth element should be 7',  7, LIntArr.Get(7));

  ///
  /// 托管类型
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 追加 nil }
  LStrArr := specialize TArray<string>.Create;
  AssertException(
  'AppendFromMemory should fail: append nil memory',
  ENil,
  procedure
  begin
    LStrArr.Append(nil, 1);
  end);

  { 异常测试: 追加容器自身范围内存 }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);
  AssertException(
  'AppendFromMemory should fail: append container memory',
  EOverlap,
  procedure
  begin
    LStrArr.Append(LStrArr.Memory, 4);
  end);

  { 追加空内存 }
  LStrArr := specialize TArray<string>.Create;
  AssertException(
  'AppendFromMemory should fail: append empty memory',
  ENil,
  procedure
  begin
    LStrArr.Append(nil, 0);
  end);
  {$ENDIF}


  { 追加非空内存 }
  LStrArr := specialize TArray<string>.Create;
  AssertTrue('AppendFromMemory should succeed: append non-empty memory', LStrArr.Append(@STR1[0], 4));
  AssertEquals('Size should match source array', 4, LStrArr.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr.Get(3));
  AssertTrue('Memory should be allocated', LStrArr.Memory <> nil);

  { 继续追加非空内存 }
  AssertTrue('AppendFromMemory should succeed: append non-empty memory', LStrArr.Append(@STR2[0], 4));
  AssertEquals('Size should match source array', 8, LStrArr.GetCount);
  AssertEquals('First element should be A',   'A', LStrArr.Get(0));
  AssertEquals('Second element should be B',  'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',   'C', LStrArr.Get(2));
  AssertEquals('Fourth element should be D',  'D', LStrArr.Get(3));
  AssertEquals('Fifth element should be E',   'E', LStrArr.Get(4));
  AssertEquals('Sixth element should be F',   'F', LStrArr.Get(5));
  AssertEquals('Seventh element should be G', 'G', LStrArr.Get(6));
  AssertEquals('Eighth element should be H',  'H', LStrArr.Get(7));

  AssertEquals('Size should match source array', 8, LStrArr.GetCount);
  AssertEquals('First element should be A',   'A', LStrArr.Get(0));
  AssertEquals('Second element should be B',  'B', LStrArr.Get(1));
  AssertEquals('Third element should be C',   'C', LStrArr.Get(2));
  AssertEquals('Fourth element should be D',  'D', LStrArr.Get(3));
  AssertEquals('Fifth element should be E',   'E', LStrArr.Get(4));
  AssertEquals('Sixth element should be F',   'F', LStrArr.Get(5));
  AssertEquals('Seventh element should be G', 'G', LStrArr.Get(6));
  AssertEquals('Eighth element should be H',  'H', LStrArr.Get(7));
end;

procedure TTestCase_Array.Test_Append_Collection;
var
  LIntArr1:  specialize IArray<Integer>;
  LIntArr2:  specialize IArray<Integer>;
  LIntArr3:  specialize IArray<Integer>;
  LIntVec:   specialize IVec<Integer>;
  LInt64Arr: specialize IArray<Int64>;
  LStrArr1:  specialize IArray<string>;
  LStrArr2:  specialize IArray<string>;
  LStrArr3:  specialize IArray<string>;
  LStrVec:   specialize IVec<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 追加自身 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'Append should fail: append to self',
  ESelf,
  procedure
  begin
    LIntArr1.Append(LIntArr1 as TCollection);
  end);

  { 异常测试: 追加不兼容的泛型容器 }
  LInt64Arr := specialize TArray<Int64>.Create([1,2,3,4]);
  LIntArr1  := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertException(
  'Append should fail: append to incompatible generic',
  ENotCompatible,
  procedure
  begin
    LIntArr1.Append(LInt64Arr as TCollection);
  end);

  { 异常测试: 追加nil容器 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertException(
  'Append should fail: append to nil container',
  ENil,
  procedure
  begin
    LIntArr1.Append(LIntArr2 as TCollection);
  end);
  {$ENDIF}

  { 空数组追加空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue('Append should succeed: allow empty array', LIntArr1.Append(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 空数组追加非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3]);
  AssertTrue('Append should succeed', LIntArr1.Append(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));

  { 非空数组追加空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3]);
  AssertTrue('Append should succeed: allow empty array', LIntArr2.Append(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr2.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr2.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr2.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr2.Get(2));

  { 非空数组追加非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  LIntArr2 := specialize TArray<Integer>.Create([4,5,6]);
  AssertTrue('Append should succeed', LIntArr1.Append(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 6, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr1.Get(3));
  AssertEquals('Fifth element should be 5',  5, LIntArr1.Get(4));
  AssertEquals('Sixth element should be 6',  6, LIntArr1.Get(5));

  { 再追加一次 }
  LIntArr3 := specialize TArray<Integer>.Create([8,9,10]);
  AssertTrue('Append should succeed', LIntArr1.Append(LIntArr3 as TCollection));
  AssertEquals('Size should match source array', 9, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 4', 4, LIntArr1.Get(3));
  AssertEquals('Fifth element should be 5',  5, LIntArr1.Get(4));
  AssertEquals('Sixth element should be 6',  6, LIntArr1.Get(5));
  AssertEquals('Seventh element should be 8', 8, LIntArr1.Get(6));
  AssertEquals('Eighth element should be 9',  9, LIntArr1.Get(7));
  AssertEquals('Ninth element should be 10', 10, LIntArr1.Get(8));

  // From Vec

  { 空数组从空Vec追加 }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('Append should succeed: allow empty collection', LIntArr1.Append(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 空数组从非空Vec追加 }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3]);
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('Append should succeed', LIntArr1.Append(LIntVec as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  { 非空数组从空Vec追加 }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  AssertTrue('Append should succeed: append to empty collection', LIntArr1.Append(LIntVec as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr1.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr1.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr1.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr1.Get(2));
  AssertTrue('Memory should be allocated', LIntArr1.Memory <> nil);

  { 非空数组从非空Vec追加 }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3]);
  LIntArr1 := specialize TArray<Integer>.Create([4,5,6]);
  AssertTrue('Append should succeed', LIntArr1.Append(LIntVec as TCollection));
  AssertEquals('Size should match source array', 6, LIntArr1.GetCount);
  AssertEquals('First element should be 4',  4, LIntArr1.Get(0));
  AssertEquals('Second element should be 5', 5, LIntArr1.Get(1));
  AssertEquals('Third element should be 6',  6, LIntArr1.Get(2));
  AssertEquals('Fourth element should be 1', 1, LIntArr1.Get(3));
  AssertEquals('Fifth element should be 2',  2, LIntArr1.Get(4));
  AssertEquals('Sixth element should be 3',  3, LIntArr1.Get(5));

  { 托管类型 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 失败测试: 追加自身 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertException(
  'Append should fail: append to self',
  ESelf,
  procedure
  begin
    LStrArr1.Append(LStrArr1 as TCollection);
  end);

  { 失败测试: 追加不兼容的泛型容器 }
  LInt64Arr := specialize TArray<Int64>.Create([1,2,3,4]);
  LStrArr1  := specialize TArray<string>.Create(['E','F','G','H']);
  AssertException(
  'Append should fail: append to incompatible generic',
  ENotCompatible,
  procedure
  begin
    LStrArr1.Append(LInt64Arr as TCollection);
  end);

  { 失败测试: 追加nil容器 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := nil;
  AssertException(
  'Append should fail: append to nil container',
  ENil,
  procedure
  begin
    LStrArr1.Append(LStrArr2 as TCollection);
  end);
  {$ENDIF}
  { 空数组追加空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue('Append should succeed: allow empty array', LStrArr1.Append(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 空数组追加非空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('Append should succeed', LStrArr1.Append(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组追加空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('Append should succeed: allow empty array', LStrArr2.Append(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr2.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr2.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr2.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr2.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr2.Get(3));
  AssertTrue('Memory should be allocated', LStrArr2.Memory <> nil);

  { 非空数组追加非空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue('Append should succeed', LStrArr1.Append(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 8, LStrArr1.GetCount);
  AssertEquals('First element should be A',   'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B',  'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',   'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D',  'D', LStrArr1.Get(3));
  AssertEquals('Fifth element should be E',   'E', LStrArr1.Get(4));
  AssertEquals('Sixth element should be F',   'F', LStrArr1.Get(5));
  AssertEquals('Seventh element should be G', 'G', LStrArr1.Get(6));
  AssertEquals('Eighth element should be H',  'H', LStrArr1.Get(7));

  { 再追加一次 }
  LStrArr3 := specialize TArray<string>.Create(['I','J','K','L']);
  AssertTrue('Append should succeed', LStrArr1.Append(LStrArr3 as TCollection));
  AssertEquals('Size should match source array', 12, LStrArr1.GetCount);
  AssertEquals('First element should be A',    'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B',   'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',    'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D',   'D', LStrArr1.Get(3));
  AssertEquals('Fifth element should be E',    'E', LStrArr1.Get(4));
  AssertEquals('Sixth element should be F',    'F', LStrArr1.Get(5));
  AssertEquals('Seventh element should be G',  'G', LStrArr1.Get(6));
  AssertEquals('Eighth element should be H',   'H', LStrArr1.Get(7));
  AssertEquals('Ninth element should be I',    'I', LStrArr1.Get(8));
  AssertEquals('Tenth element should be J',    'J', LStrArr1.Get(9));
  AssertEquals('Eleventh element should be K', 'K', LStrArr1.Get(10));
  AssertEquals('Twelfth element should be L',  'L', LStrArr1.Get(11));

  // From Vec

  { 空数组从空Vec追加 }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue('Append should succeed: allow empty collection', LStrArr1.Append(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 空数组从非空Vec追加 }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create;
  AssertTrue('Append should succeed', LStrArr1.Append(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组从空Vec追加 }
  LStrVec  := specialize TVec<string>.Create;
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('Append should succeed: append to empty collection', LStrArr1.Append(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组从非空Vec追加 }
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  LStrArr1 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue('Append should succeed', LStrArr1.Append(LStrVec as TCollection));
  AssertEquals('Size should match source array', 8, LStrArr1.GetCount);
  AssertEquals('First element should be E',  'E', LStrArr1.Get(0));
  AssertEquals('Second element should be F', 'F', LStrArr1.Get(1));
  AssertEquals('Third element should be G',  'G', LStrArr1.Get(2));
  AssertEquals('Fourth element should be H', 'H', LStrArr1.Get(3));
  AssertEquals('Fifth element should be A',  'A', LStrArr1.Get(4));
  AssertEquals('Sixth element should be B',  'B', LStrArr1.Get(5));
  AssertEquals('Seventh element should be C', 'C', LStrArr1.Get(6));
  AssertEquals('Eighth element should be D',  'D', LStrArr1.Get(7));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

end;

procedure TTestCase_Array.Test_AppendTo_Collection;
var
  LIntArr1:  specialize IArray<Integer>;
  LIntArr2:  specialize IArray<Integer>;
  LIntVec:   specialize IVec<Integer>;
  LInt64Arr: specialize IArray<Int64>;
  LStrArr1:  specialize IArray<string>;
  LStrArr2:  specialize IArray<string>;
  LStrVec:   specialize IVec<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 追加到自身容器 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertException(
  'AppendTo should fail: append to self',
  ESelf,
  procedure
  begin
    LIntArr1.AppendTo(LIntArr1 as TCollection);
  end);

  { 异常测试: 追加到不兼容的泛型容器 }
  LInt64Arr := specialize TArray<Int64>.Create([1,2,3,4]);
  LIntArr1  := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertException(
  'AppendTo should fail: append to incompatible generic',
  ENotCompatible,
  procedure
  begin
    LIntArr1.AppendTo(LInt64Arr as TCollection);
  end);

  { 异常测试: 追加到nil容器 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertException(
  'AppendTo should fail: append to nil container',
  ENil,
  procedure
  begin
    LIntArr1.AppendTo(LIntArr2 as TCollection);
  end);
  {$ENDIF}

  { 空数组追加到非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3]);
  AssertTrue('AppendTo should succeed: append to empty array', LIntArr1.AppendTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr2.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr2.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr2.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr2.Get(2));

  { 空数组追加到空数组 }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue('AppendTo should succeed: allow empty array', LIntArr1.AppendTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertTrue('Memory should be nil', LIntArr1.Memory = nil);

  { 非空数组追加到空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  LIntArr2 := specialize TArray<Integer>.Create;
  AssertTrue('AppendTo should succeed', LIntArr1.AppendTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 3, LIntArr2.GetCount);
  AssertEquals('First element should be 1',  1, LIntArr2.Get(0));
  AssertEquals('Second element should be 2', 2, LIntArr2.Get(1));
  AssertEquals('Third element should be 3',  3, LIntArr2.Get(2));

  { 非空数组追加非空数组 }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  LIntArr2 := specialize TArray<Integer>.Create([4,5,6]);
  AssertTrue('AppendTo should succeed', LIntArr1.AppendTo(LIntArr2 as TCollection));
  AssertEquals('Size should match source array', 6, LIntArr2.GetCount);
  AssertEquals('First element should be 4',  4, LIntArr2.Get(0));
  AssertEquals('Second element should be 5', 5, LIntArr2.Get(1));
  AssertEquals('Third element should be 6',  6, LIntArr2.Get(2));
  AssertEquals('Fourth element should be 1', 1, LIntArr2.Get(3));
  AssertEquals('Fifth element should be 2',  2, LIntArr2.Get(4));
  AssertEquals('Sixth element should be 3',  3, LIntArr2.Get(5));

  // to Vec

  { 空数组追加到空Vec }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntVec  := specialize TVec<Integer>.Create;
  AssertTrue('AppendTo should succeed: allow empty collection', LIntArr1.AppendTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 0, LIntVec.GetCount);

  { 空数组追加到非空Vec }
  LIntArr1 := specialize TArray<Integer>.Create;
  LIntVec  := specialize TVec<Integer>.Create([1,2,3]);
  AssertTrue('AppendTo should succeed: allow empty collection', LIntArr1.AppendTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 3, LIntVec.GetCount);
  AssertEquals('First element should be 1',  1, LIntVec[0]);
  AssertEquals('Second element should be 2', 2, LIntVec[1]);
  AssertEquals('Third element should be 3',  3, LIntVec[2]);

  { 非空数组追加到空Vec }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  LIntVec  := specialize TVec<Integer>.Create;
  AssertTrue('AppendTo should succeed', LIntArr1.AppendTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 3, LIntVec.GetCount);
  AssertEquals('First element should be 1',  1, LIntVec[0]);
  AssertEquals('Second element should be 2', 2, LIntVec[1]);
  AssertEquals('Third element should be 3',  3, LIntVec[2]);

  { 非空数组追加到非空Vec }
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3]);
  LIntVec  := specialize TVec<Integer>.Create([4,5,6]);
  AssertTrue('AppendTo should succeed', LIntArr1.AppendTo(LIntVec as TCollection));
  AssertEquals('Size should match source array', 6, LIntVec.GetCount);
  AssertEquals('First element should be 4',  4, LIntVec[0]);
  AssertEquals('Second element should be 5', 5, LIntVec[1]);
  AssertEquals('Third element should be 6',  6, LIntVec[2]);
  AssertEquals('Fourth element should be 1', 1, LIntVec[3]);
  AssertEquals('Fifth element should be 2',  2, LIntVec[4]);
  AssertEquals('Sixth element should be 3',  3, LIntVec[5]);

  { 托管类型 }

  { 空数组追加到空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue('AppendTo should succeed: allow empty array', LStrArr1.AppendTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertTrue('Memory should be nil', LStrArr1.Memory = nil);

  { 空数组追加到非空数组 }
  LStrArr1 := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['A','B','C','D']);
  AssertTrue('AppendTo should succeed: allow empty array', LStrArr1.AppendTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr2.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr2.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr2.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr2.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr2.Get(3));
  AssertTrue('Memory should be allocated', LStrArr2.Memory <> nil);

  { 非空数组追加到空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create;
  AssertTrue('AppendTo should succeed', LStrArr1.AppendTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 4, LStrArr1.GetCount);
  AssertEquals('First element should be A',  'A', LStrArr1.Get(0));
  AssertEquals('Second element should be B', 'B', LStrArr1.Get(1));
  AssertEquals('Third element should be C',  'C', LStrArr1.Get(2));
  AssertEquals('Fourth element should be D', 'D', LStrArr1.Get(3));
  AssertTrue('Memory should be allocated', LStrArr1.Memory <> nil);

  { 非空数组追加非空数组 }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrArr2 := specialize TArray<string>.Create(['E','F','G','H']);
  AssertTrue('AppendTo should succeed', LStrArr1.AppendTo(LStrArr2 as TCollection));
  AssertEquals('Size should match source array', 8, LStrArr2.GetCount);
  AssertEquals('First element should be E',   'E', LStrArr2.Get(0));
  AssertEquals('Second element should be F',  'F', LStrArr2.Get(1));
  AssertEquals('Third element should be G',   'G', LStrArr2.Get(2));
  AssertEquals('Fourth element should be H',  'H', LStrArr2.Get(3));
  AssertEquals('Fifth element should be A',   'A', LStrArr2.Get(4));
  AssertEquals('Sixth element should be B',   'B', LStrArr2.Get(5));
  AssertEquals('Seventh element should be C', 'C', LStrArr2.Get(6));
  AssertEquals('Eighth element should be D',  'D', LStrArr2.Get(7));
  AssertTrue('Memory should be allocated', LStrArr2.Memory <> nil);

  // to Vec

  { 空数组追加到空Vec }
  LStrArr1 := specialize TArray<string>.Create;
  LStrVec  := specialize TVec<string>.Create;
  AssertTrue('AppendTo should succeed: allow empty collection', LStrArr1.AppendTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 0, LStrVec.GetCount);

  { 空数组追加到非空Vec }
  LStrArr1 := specialize TArray<string>.Create;
  LStrVec  := specialize TVec<string>.Create(['A','B','C','D']);
  AssertTrue('AppendTo should succeed: allow empty collection', LStrArr1.AppendTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrVec.GetCount);
  AssertEquals('First element should be A',  'A', LStrVec[0]);
  AssertEquals('Second element should be B', 'B', LStrVec[1]);
  AssertEquals('Third element should be C',  'C', LStrVec[2]);
  AssertEquals('Fourth element should be D', 'D', LStrVec[3]);

  { 非空数组追加到空Vec }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrVec  := specialize TVec<string>.Create;
  AssertTrue('AppendTo should succeed', LStrArr1.AppendTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 4, LStrVec.GetCount);
  AssertEquals('First element should be A',  'A', LStrVec[0]);
  AssertEquals('Second element should be B', 'B', LStrVec[1]);
  AssertEquals('Third element should be C',  'C', LStrVec[2]);
  AssertEquals('Fourth element should be D', 'D', LStrVec[3]);

  { 非空数组追加到非空Vec }
  LStrArr1 := specialize TArray<string>.Create(['A','B','C','D']);
  LStrVec  := specialize TVec<string>.Create(['E','F','G','H']);
  AssertTrue('AppendTo should succeed', LStrArr1.AppendTo(LStrVec as TCollection));
  AssertEquals('Size should match source array', 8, LStrVec.GetCount);
  AssertEquals('First element should be E',   'E', LStrVec[0]);
  AssertEquals('Second element should be F',  'F', LStrVec[1]);
  AssertEquals('Third element should be G',   'G', LStrVec[2]);
  AssertEquals('Fourth element should be H',  'H', LStrVec[3]);
  AssertEquals('Fifth element should be A',   'A', LStrVec[4]);
  AssertEquals('Sixth element should be B',   'B', LStrVec[5]);
  AssertEquals('Seventh element should be C', 'C', LStrVec[6]);
  AssertEquals('Eighth element should be D',  'D', LStrVec[7]);
end;

procedure TTestCase_Array.Test_WriteToArrayMemory;
var
  LIntArr:  specialize IArray<Integer>;
  LStrArr:  specialize IArray<string>;
  LIntData: array[0..7] of Integer;
  LStrData: array[0..7] of string;
begin
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);

  { 失败测试: 目标内存为nil }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('WriteToArrayMemory should fail for nil pointer', LIntArr.WriteToArrayMemory(nil, 4));

  { 失败测试: 数量为0 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('WriteToArrayMemory should fail for element count is 0', LIntArr.WriteToArrayMemory(@LIntData[0], 0));

  { 失败测试: 范围越界 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('WriteToArrayMemory should fail for range out of bounds', LIntArr.WriteToArrayMemory(@LIntData[0], 5));

  { 满写 }
  FillChar(LIntData[0],8 * sizeof(Integer),0);
  LIntArr.WriteToArrayMemory(@LIntData[0], 4);
  AssertEquals('WriteToArrayMemory should store values correctly', 1, LIntData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 2, LIntData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', 3, LIntData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', 4, LIntData[3]);

  { 写入前两个 }
  FillChar(LIntData[0], 8 * sizeof(Integer), 0);
  LIntArr.WriteToArrayMemory(@LIntData[0], 2);
  AssertEquals('WriteToArrayMemory should store values correctly', 1, LIntData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 2, LIntData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', 0, LIntData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', 0, LIntData[3]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);
  
  { 满写 }
  Initialize(LStrData);
  FillChar(LStrData[0], 8 * sizeof(string), 0);
  LStrArr.WriteToArrayMemory(@LStrData[0], 4);
  AssertEquals('WriteToArrayMemory should store values correctly', 'A', LStrData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'B', LStrData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'C', LStrData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'D', LStrData[3]);

  { 写入前两个 }
  FillChar(LStrData[0], 8 * sizeof(string), 0);
  LStrArr.WriteToArrayMemory(@LStrData[0], 2);
  AssertEquals('WriteToArrayMemory should store values correctly', 'A', LStrData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'B', LStrData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', '', LStrData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', '', LStrData[3]);
  
end;

procedure TTestCase_Array.Test_WriteMemory;
const
  DATA_INT: array[0..3] of Integer = (100, 200, 300, 400);
  DATA_STR: array[0..3] of string = ('100', '200', '300', '400');
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;
  
  { 错误测试,写入空数组 }
  AssertFalse('WriteMemory should fail for empty array', LIntArr.Write(0, @DATA_INT[0], 4));
 
  { 错误测试,索引越界 }
  LIntArr.Resize(2);
  AssertFalse('WriteMemory should fail for range out of bounds', LIntArr.Write(2, @DATA_INT[0], 4));
  AssertFalse('WriteMemory should fail for range out of bounds', LIntArr.Write(3, @DATA_INT[0], 4));

  { 错误测试,范围越界 }
  LIntArr.Resize(4);
  AssertFalse('WriteMemory should fail for range out of bounds', LIntArr.Write(1, @DATA_INT[0], 4));
  
  { 错误测试,元素个数为0 }
  AssertFalse('WriteMemory should fail for element count is 0', LIntArr.Write(0, @DATA_INT[0], 0));
 
  { 错误测试,目标内存为nil }
  AssertFalse('WriteMemory should fail for nil pointer', LIntArr.Write(0, nil, 4));

  { 正确测试,写入数据 }
  LIntArr.Resize(4);
  AssertTrue('WriteMemory should succeed', LIntArr.Write(0, @DATA_INT[0], 4));
  AssertEquals('WriteMemory should succeed', 4, LIntArr.GetCount);
  AssertEquals('WriteMemory should store values correctly', 100, LIntArr.Get(0));
  AssertEquals('WriteMemory should store values correctly', 200, LIntArr.Get(1));
  AssertEquals('WriteMemory should store values correctly', 300, LIntArr.Get(2));
  AssertEquals('WriteMemory should store values correctly', 400, LIntArr.Get(3));

  { 正确测试,写入数据 }
  LIntArr.Resize(2);
  AssertTrue('WriteMemory should succeed', LIntArr.Write(0, @DATA_INT[2], 2));
  AssertEquals('WriteMemory should succeed', 2, LIntArr.GetCount);
  AssertEquals('WriteMemory should store values correctly', 300, LIntArr.Get(0));
  AssertEquals('WriteMemory should store values correctly', 400, LIntArr.Get(1));

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create;

  { 错误测试,写入空数组 }
  AssertFalse('WriteMemory should fail for empty array', LStrArr.Write(0, @DATA_STR[0], 4));
  
  { 错误测试,索引越界 }
  LStrArr.Resize(2);
  AssertFalse('WriteMemory should fail for index out of bounds', LStrArr.Write(2, @DATA_STR[0], 4));
  AssertFalse('WriteMemory should fail for index out of bounds', LStrArr.Write(3, @DATA_STR[0], 4));

  { 错误测试,范围越界 }
  LStrArr.Resize(4);
  AssertFalse('WriteMemory should fail for range out of bounds', LStrArr.Write(1, @DATA_STR[0], 4));
  
  { 错误测试,元素个数为0 }
  AssertFalse('WriteMemory should fail for element count is 0', LStrArr.Write(0, @DATA_STR[0], 0));
  
  { 错误测试,目标内存为nil }
  AssertFalse('WriteMemory should fail for nil pointer', LStrArr.Write(0, nil, 4));

  { 正确测试,写入数据 }
  LStrArr.Resize(4);
  AssertTrue('WriteMemory should succeed', LStrArr.Write(0, @DATA_STR[0], 4));
  AssertEquals('WriteMemory should succeed', 4, LStrArr.GetCount);
  AssertEquals('WriteMemory should store values correctly', '100', LStrArr.Get(0));
  AssertEquals('WriteMemory should store values correctly', '200', LStrArr.Get(1));
  AssertEquals('WriteMemory should store values correctly', '300', LStrArr.Get(2));
  AssertEquals('WriteMemory should store values correctly', '400', LStrArr.Get(3));

  { 正确测试,写入数据 }
  LStrArr.Resize(2);
  AssertTrue('WriteMemory should succeed', LStrArr.Write(0, @DATA_STR[2], 2));
  AssertEquals('WriteMemory should succeed', 2, LStrArr.GetCount);
  AssertEquals('WriteMemory should store values correctly', '300', LStrArr.Get(0));
  AssertEquals('WriteMemory should store values correctly', '400', LStrArr.Get(1));
end;

procedure TTestCase_Array.Test_WriteArray;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;

  { 错误测试,写入空数组 }
  AssertFalse('WriteArray should fail for empty array', LIntArr.Write(0, [100, 200, 300, 400]));

  { 错误测试,索引越界 }
  LIntArr.Resize(2);
  AssertFalse('WriteArray should fail for index out of bounds', LIntArr.Write(2, [100, 200, 300, 400]));
  AssertFalse('WriteArray should fail for index out of bounds', LIntArr.Write(3, [100, 200, 300, 400]));

  { 错误测试,范围越界 }
  LIntArr.Resize(4);
  AssertFalse('WriteArray should fail for range out of bounds', LIntArr.Write(1, [100, 200, 300, 400]));
  
  { 错误测试,元素个数为0 }
  AssertFalse('WriteArray should fail for element count is 0', LIntArr.Write(0, []));
  
  { 正确测试,写入数据 }
  LIntArr.Resize(4);
  AssertTrue('WriteArray should succeed', LIntArr.Write(0, [100, 200, 300, 400]));
  AssertEquals('WriteArray should succeed', 4, LIntArr.GetCount);
  AssertEquals('WriteArray should store values correctly', 100, LIntArr.Get(0));
  AssertEquals('WriteArray should store values correctly', 200, LIntArr.Get(1));
  AssertEquals('WriteArray should store values correctly', 300, LIntArr.Get(2));
  AssertEquals('WriteArray should store values correctly', 400, LIntArr.Get(3));

  { 正确测试,写入数据 }
  LIntArr.Resize(2);
  AssertTrue('WriteArray should succeed', LIntArr.Write(0, [300, 400]));
  AssertEquals('WriteArray should succeed', 2, LIntArr.GetCount);
  AssertEquals('WriteArray should store values correctly', 300, LIntArr.Get(0));
  AssertEquals('WriteArray should store values correctly', 400, LIntArr.Get(1));

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create;
  
  { 错误测试,写入空数组 }
  AssertFalse('WriteArray should fail for empty array', LStrArr.Write(0, ['100', '200', '300', '400']));
  
  { 错误测试,索引越界 }
  LStrArr.Resize(2);
  AssertFalse('WriteArray should fail for index out of bounds', LStrArr.Write(2, ['100', '200', '300', '400']));
  AssertFalse('WriteArray should fail for index out of bounds', LStrArr.Write(3, ['100', '200', '300', '400']));

  { 错误测试,范围越界 }
  LStrArr.Resize(4);
  AssertFalse('WriteArray should fail for range out of bounds', LStrArr.Write(1, ['100', '200', '300', '400']));
  
  { 错误测试,元素个数为0 }
  AssertFalse('WriteArray should fail for element count is 0', LStrArr.Write(0, []));

  { 正确测试,写入数据 }
  LStrArr.Resize(4);
  AssertTrue('WriteArray should succeed', LStrArr.Write(0, ['100', '200', '300', '400']));
  AssertEquals('WriteArray should succeed', 4, LStrArr.GetCount);
  AssertEquals('WriteArray should store values correctly', '100', LStrArr.Get(0));
  AssertEquals('WriteArray should store values correctly', '200', LStrArr.Get(1));
  AssertEquals('WriteArray should store values correctly', '300', LStrArr.Get(2));
  AssertEquals('WriteArray should store values correctly', '400', LStrArr.Get(3));

  { 正确测试,写入数据 }
  LStrArr.Resize(2);
  AssertTrue('WriteArray should succeed', LStrArr.Write(0, ['300', '400']));
  AssertEquals('WriteArray should succeed', 2, LStrArr.GetCount);
  AssertEquals('WriteArray should store values correctly', '300', LStrArr.Get(0));
  AssertEquals('WriteArray should store values correctly', '400', LStrArr.Get(1));
end;

procedure TTestCase_Array.Test_WriteCollection;
var
  LIntArr:  specialize IArray<Integer>;
  LIntArr2: specialize IArray<Integer>;
  LIntVec:  specialize IVec<Integer>;
  LStrArr:  specialize IArray<string>;
  LStrArr2: specialize IArray<string>;
  LStrVec:  specialize IVec<string>;
begin
  { 失败测试: 源容器为nil }
  LIntArr  := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertFalse('Write should fail for source collection is nil', LIntArr.Write(0, LIntArr2 as TCollection, 4));

  { 失败测试: 源容器为自己 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for source collection is self', LIntArr.Write(0, LIntArr as TCollection, 4));

  { 失败测试: 数量为0 }
  LIntArr  := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertFalse('Write should fail for element count is 0', LIntArr.Write(0, LIntArr as TCollection, 0));

  { 失败测试: 范围越界 空数组 }
  LIntArr  := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(1, LIntArr2 as TCollection, 4));

  { 失败测试: 范围越界 不足空间 }
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(1, LIntArr2 as TCollection, 4));

  { 失败测试: 范围越界 不足空间 }
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4,5]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(0, LIntArr2 as TCollection, 5));

  { 写入 Array }
  LIntArr2 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntArr2 as TCollection, 4);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);
  AssertEquals('Write should store values correctly', 2, LIntArr[2]);
  AssertEquals('Write should store values correctly', 3, LIntArr[3]);

  { 小型数据写入 Vec 4个元素到0 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntArr := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntVec as TCollection, 4);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);
  AssertEquals('Write should store values correctly', 2, LIntArr[2]);
  AssertEquals('Write should store values correctly', 3, LIntArr[3]);

  { 小型数据写入 Array 2个元素到0 }
  LIntArr2 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntArr2 as TCollection, 2);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);

  { 小型数据写入 Array 2个元素到2 }
  LIntArr2 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr.Write(2, LIntArr2 as TCollection, 2);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[2]);
  AssertEquals('Write should store values correctly', 1, LIntArr[3]);

  { 小型数据写入 Vec 2个元素到0 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntArr := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntVec as TCollection, 2);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);

  { 小型数据写入 Vec 2个元素到2 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntArr := specialize TArray<Integer>.Create(4);
  LIntArr.Write(2, LIntVec as TCollection, 2);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[2]);
  AssertEquals('Write should store values correctly', 1, LIntArr[3]);

  { 托管元素 }

  { 失败测试: 源容器为nil }
  LStrArr  := specialize TArray<string>.Create(['1','2','3','4']);
  LStrArr2 := nil;
  AssertFalse('Write should fail for source collection is nil', LStrArr.Write(0, LStrArr2 as TCollection, 4));

  { 失败测试: 源容器为自己 }
  LStrArr := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for source collection is self', LStrArr.Write(0, LStrArr as TCollection, 4));

  { 失败测试: 数量为0 }
  LStrArr  := specialize TArray<string>.Create(['1','2','3','4']);
  LStrArr2 := specialize TArray<string>.Create(['5','6','7','8']);
  AssertFalse('Write should fail for element count is 0', LStrArr.Write(0, LStrArr2 as TCollection, 0));

  { 失败测试: 范围越界 空数组 }
  LStrArr  := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(1, LStrArr2 as TCollection, 4));

  { 失败测试: 范围越界 不足空间 }
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(1, LStrArr2 as TCollection, 4));

  { 失败测试: 范围越界 不足空间 }
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4','5']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(0, LStrArr2 as TCollection, 5));


  { 写入 Array }
  LStrArr2 := specialize TArray<string>.Create(['0','1','2','3']);
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>, 4);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);
  AssertEquals('Write should store values correctly', '2', LStrArr[2]);
  AssertEquals('Write should store values correctly', '3', LStrArr[3]);

  { 小型数据写入 Vec 4个元素到0 }
  LStrVec := specialize TVec<string>.Create(['0','1','2','3']);
  LStrArr := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrVec as specialize TGenericCollection<string>, 4);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);
  AssertEquals('Write should store values correctly', '2', LStrArr[2]);
  AssertEquals('Write should store values correctly', '3', LStrArr[3]);

    { 小型数据写入 Array 2个元素到0 }
  LStrArr2 := specialize TArray<string>.Create(['0','1','2','3']);
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>, 2);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);

  { 小型数据写入 Array 2个元素到2 }
  LStrArr2 := specialize TArray<string>.Create(['0','1','2','3']);
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr.Write(2, LStrArr2 as specialize TGenericCollection<string>, 2);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[2]);
  AssertEquals('Write should store values correctly', '1', LStrArr[3]);

  { 小型数据写入 Vec 2个元素到0 }
  LStrVec := specialize TVec<string>.Create(['0','1','2','3']);
  LStrArr := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrVec as specialize TGenericCollection<string>, 2);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);

  { 小型数据写入 Vec 2个元素到2 }
  LStrVec := specialize TVec<string>.Create(['0','1','2','3']);
  LStrArr := specialize TArray<string>.Create(4);
  LStrArr.Write(2, LStrVec as specialize TGenericCollection<string>, 2);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[2]);
  AssertEquals('Write should store values correctly', '1', LStrArr[3]);
end;

procedure TTestCase_Array.Test_WriteCollection2;
var
  LIntArr:  specialize IArray<Integer>;
  LIntArr2: specialize IArray<Integer>;
  LIntVec:  specialize IVec<Integer>;
  LStrArr:  specialize IArray<string>;
  LStrArr2: specialize IArray<string>;
  LStrVec:  specialize IVec<string>;
begin
  { 失败测试: 源容器为nil }
  LIntArr  := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntArr2 := nil;
  AssertFalse('Write should fail for source collection is nil', LIntArr.Write(0, LIntArr2 as specialize TGenericCollection<Integer>));

  { 失败测试: 源容器为自己 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for source collection is self', LIntArr.Write(0, LIntArr as specialize TGenericCollection<Integer>));

  { 失败测试: 范围越界 空数组 }
  LIntArr  := specialize TArray<Integer>.Create;
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(0, LIntArr2 as specialize TGenericCollection<Integer>));

  { 失败测试: 范围越界 不足空间 }
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(1, LIntArr2 as specialize TGenericCollection<Integer>));

  { 失败测试: 范围越界 不足空间 }
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr2 := specialize TArray<Integer>.Create([1,2,3,4,5]);
  AssertFalse('Write should fail for range out of bounds', LIntArr.Write(0, LIntArr2 as specialize TGenericCollection<Integer>));

  { 写入 Array }
  LIntArr2 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr  := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntArr2 as specialize TGenericCollection<Integer>);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);
  AssertEquals('Write should store values correctly', 2, LIntArr[2]);
  AssertEquals('Write should store values correctly', 3, LIntArr[3]);

  { 写入 Vec }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntArr := specialize TArray<Integer>.Create(4);
  LIntArr.Write(0, LIntVec as specialize TGenericCollection<Integer>);
  AssertEquals('Write should succeed', 4, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[0]);
  AssertEquals('Write should store values correctly', 1, LIntArr[1]);
  AssertEquals('Write should store values correctly', 2, LIntArr[2]);
  AssertEquals('Write should store values correctly', 3, LIntArr[3]);

  { 写入 Array 到索引4 }
  LIntArr2 := specialize TArray<Integer>.Create([0,1,2,3]);
  LIntArr  := specialize TArray<Integer>.Create(8);
  LIntArr.Write(4, LIntArr2 as specialize TGenericCollection<Integer>);
  AssertEquals('Write should succeed', 8, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[4]);
  AssertEquals('Write should store values correctly', 1, LIntArr[5]);
  AssertEquals('Write should store values correctly', 2, LIntArr[6]);
  AssertEquals('Write should store values correctly', 3, LIntArr[7]);

  { 写入 Vec 到索引4 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntArr := specialize TArray<Integer>.Create(8);
  LIntArr.Write(4, LIntVec as specialize TGenericCollection<Integer>);
  AssertEquals('Write should succeed', 8, LIntArr.GetCount);
  AssertEquals('Write should store values correctly', 0, LIntArr[4]);
  AssertEquals('Write should store values correctly', 1, LIntArr[5]);
  AssertEquals('Write should store values correctly', 2, LIntArr[6]);
  AssertEquals('Write should store values correctly', 3, LIntArr[7]);

  ///
  /// 托管元素
  ///

  { 失败测试: 源容器为nil }
  LStrArr  := specialize TArray<string>.Create(['1','2','3','4']);
  LStrArr2 := nil;
  AssertFalse('Write should faiAWl for source collection is nil', LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>));

  { 失败测试: 源容器为自己 }
  LStrArr := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for source collection is self', LStrArr.Write(0, LStrArr as specialize TGenericCollection<string>));

  { 失败测试: 范围越界 空数组 }
  LStrArr  := specialize TArray<string>.Create;
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>));

  { 失败测试: 范围越界 不足空间 }
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(1, LStrArr2 as specialize TGenericCollection<string>));

  { 失败测试: 范围越界 不足空间 }
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr2 := specialize TArray<string>.Create(['1','2','3','4','5']);
  AssertFalse('Write should fail for range out of bounds', LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>));

  { 写入 Array }
  LStrArr2 := specialize TArray<string>.Create(['0','1','2','3']);
  LStrArr  := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrArr2 as specialize TGenericCollection<string>);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);
  AssertEquals('Write should store values correctly', '2', LStrArr[2]);
  AssertEquals('Write should store values correctly', '3', LStrArr[3]);

  { 写入 Vec }
  LStrVec := specialize TVec<string>.Create(['0','1','2','3']);
  LStrArr := specialize TArray<string>.Create(4);
  LStrArr.Write(0, LStrVec as specialize TGenericCollection<string>);
  AssertEquals('Write should succeed', 4, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[0]);
  AssertEquals('Write should store values correctly', '1', LStrArr[1]);
  AssertEquals('Write should store values correctly', '2', LStrArr[2]);
  AssertEquals('Write should store values correctly', '3', LStrArr[3]);

  { 写入 Array 到索引4 }
  LStrArr2 := specialize TArray<string>.Create(['0','1','2','3']);
  LStrArr  := specialize TArray<string>.Create(8);
  LStrArr.Write(4, LStrArr2 as specialize TGenericCollection<string>);
  AssertEquals('Write should succeed', 8, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[4]);
  AssertEquals('Write should store values correctly', '1', LStrArr[5]);
  AssertEquals('Write should store values correctly', '2', LStrArr[6]);
  AssertEquals('Write should store values correctly', '3', LStrArr[7]);

  { 写入 Vec 到索引4 }
  LStrVec := specialize TVec<string>.Create(['0','1','2','3']);
  LStrArr := specialize TArray<string>.Create(8);
  LStrArr.Write(4, LStrVec as specialize TGenericCollection<string>);
  AssertEquals('Write should succeed', 8, LStrArr.GetCount);
  AssertEquals('Write should store values correctly', '0', LStrArr[4]);
  AssertEquals('Write should store values correctly', '1', LStrArr[5]);
  AssertEquals('Write should store values correctly', '2', LStrArr[6]);
  AssertEquals('Write should store values correctly', '3', LStrArr[7]);
end;

procedure TTestCase_Array.Test_ReadMemory;
const
  DATA_INT: array[0..3] of Integer = (100,200,300,400);
  DATA_STR: array[0..3] of string = ('100','200','300','400');
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
  LInts:   array[0..3] of Integer;
  LStrs:   array[0..3] of string;
begin
  Initialize(LInts);
  Initialize(LStrs);

  LIntArr := specialize TArray<Integer>.Create(DATA_INT);

  { 失败测试,索引越界 }
  AssertFalse('ReadMemory should fail for index out of bounds', LIntArr.Read(4, @LInts[0], 4));

  { 失败测试,范围越界 }
  AssertFalse('ReadMemory should fail for range out of bounds', LIntArr.Read(1, @LInts[0], 4));

  { 失败测试,元素个数为0 }
  AssertFalse('ReadMemory should fail for element count is 0', LIntArr.Read(0, @LInts[0], 0));

  { 正确测试,读取数据 }
  AssertTrue('ReadMemory should succeed', LIntArr.Read(0, @LInts[0], 4));
  AssertEquals('ReadMemory should store values correctly', 100, LInts[0]);
  AssertEquals('ReadMemory should store values correctly', 200, LInts[1]);
  AssertEquals('ReadMemory should store values correctly', 300, LInts[2]);
  AssertEquals('ReadMemory should store values correctly', 400, LInts[3]);

  { 读取最后两个元素 }
  AssertTrue('ReadMemory should succeed', LIntArr.Read(2, @LInts[0], 2));
  AssertEquals('ReadMemory should store values correctly', 300, LInts[0]);
  AssertEquals('ReadMemory should store values correctly', 400, LInts[1]);
  
  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(DATA_STR);

  { 错误测试,索引越界 }
  AssertFalse('ReadMemory should fail for index out of bounds', LStrArr.Read(4, @LStrs[0], 4));

  { 错误测试,范围越界 }
  AssertFalse('ReadMemory should fail for range out of bounds', LStrArr.Read(1, @LStrs[0], 4));

  { 错误测试,元素个数为0 }
  AssertFalse('ReadMemory should fail for element count is 0', LStrArr.Read(0, @LStrs[0], 0));

  { 正确测试,读取数据 }
  AssertTrue('ReadMemory should succeed', LStrArr.Read(0, @LStrs[0], 4));
  AssertEquals('ReadMemory should store values correctly', '100', LStrs[0]);
  AssertEquals('ReadMemory should store values correctly', '200', LStrs[1]);
  AssertEquals('ReadMemory should store values correctly', '300', LStrs[2]);
  AssertEquals('ReadMemory should store values correctly', '400', LStrs[3]);

  { 读取最后两个元素 }
  AssertTrue('ReadMemory should succeed', LStrArr.Read(2, @LStrs[0], 2));
  AssertEquals('ReadMemory should store values correctly', '300', LStrs[0]);
  AssertEquals('ReadMemory should store values correctly', '400', LStrs[1]);
end;

procedure TTestCase_Array.Test_ReadArray;
const
  DATA_INT: array[0..3] of Integer = (100, 200, 300, 400);
  DATA_STR: array[0..3] of string = ('100', '200', '300', '400');
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
  LInts:   array of Integer;
  LStrs:   array of string;
begin
  Initialize(LInts);
  Initialize(LStrs);

  LIntArr := specialize TArray<Integer>.Create(DATA_INT);

  { 错误测试,索引越界 }
  AssertFalse('ReadArray should fail for index out of bounds', LIntArr.Read(4, LInts, 4));

  { 错误测试,范围越界 }
  AssertFalse('ReadArray should fail for range out of bounds', LIntArr.Read(1, LInts, 4));

  { 错误测试,元素个数为0 }
  AssertFalse('ReadArray should fail for element count is 0', LIntArr.Read(0, LInts, 0));

  { 正确测试,读取数据 }
  AssertTrue('ReadArray should succeed', LIntArr.Read(0, LInts, 4));
  AssertEquals('ReadArray should succeed', 4, Length(LInts));
  AssertEquals('ReadArray should store values correctly', 100, LInts[0]);
  AssertEquals('ReadArray should store values correctly', 200, LInts[1]);
  AssertEquals('ReadArray should store values correctly', 300, LInts[2]);
  AssertEquals('ReadArray should store values correctly', 400, LInts[3]);

  { 读取最后两个元素 }
  AssertTrue('ReadArray should succeed', LIntArr.Read(2, LInts, 2));
  AssertEquals('ReadArray should succeed', 2, Length(LInts));
  AssertEquals('ReadArray should store values correctly', 300, LInts[0]);
  AssertEquals('ReadArray should store values correctly', 400, LInts[1]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(DATA_STR);

  { 错误测试,索引越界 }
  AssertFalse('ReadArray should fail for index out of bounds', LStrArr.Read(4, LStrs, 4));

  { 错误测试,范围越界 }
  AssertFalse('ReadArray should fail for range out of bounds', LStrArr.Read(1, LStrs, 4));

  { 错误测试,元素个数为0 }
  AssertFalse('ReadArray should fail for element count is 0', LStrArr.Read(0, LStrs, 0));

  { 正确测试,读取数据 }
  AssertTrue('ReadArray should succeed', LStrArr.Read(0, LStrs, 4));
  AssertEquals('ReadArray should succeed', 4, Length(LStrs));
  AssertEquals('ReadArray should store values correctly', '100', LStrs[0]);
  AssertEquals('ReadArray should store values correctly', '200', LStrs[1]);
  AssertEquals('ReadArray should store values correctly', '300', LStrs[2]);
  AssertEquals('ReadArray should store values correctly', '400', LStrs[3]);

  { 读取最后两个元素 }
  AssertTrue('ReadArray should succeed', LStrArr.Read(2, LStrs, 2));
  AssertEquals('ReadArray should succeed', 2, Length(LStrs));
  AssertEquals('ReadArray should store values correctly', '300', LStrs[0]);
  AssertEquals('ReadArray should store values correctly', '400', LStrs[1]);
end;

procedure TTestCase_Array.Test_Clone;
var
  LIntArr:   specialize IArray<Integer>;
  LIntClone: specialize IArray<Integer>;
  LStrArr:   specialize IArray<string>;
  LStrClone: specialize IArray<string>;
begin
  LIntArr := specialize TArray<Integer>.Create;

  { 空数组克隆 }
  LIntClone := LIntArr.Clone as specialize IArray<Integer>;
  AssertEquals('Cloned empty array should remain empty', 0, LIntClone.GetCount);
  AssertTrue('Cloned empty array should have the same allocator', LIntArr.GetAllocator = LIntClone.GetAllocator);
  AssertTrue('Cloned empty array should have the same count', LIntArr.GetCount = LIntClone.GetCount);
  AssertTrue('A cloned empty array should have the same collection memory as nil', LIntClone.GetMemory = nil);

  { 克隆测试 }
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4, 5]);
  LIntClone := LIntArr.Clone as specialize IArray<Integer>;
  AssertEquals('Cloned array size should match original', LIntArr.GetCount, LIntClone.GetCount);
  AssertTrue('Cloned array should have a different collection memory', LIntArr.GetMemory <> LIntClone.GetMemory);
  AssertEquals('Cloned array should have the same elements', LIntArr[0], LIntClone[0]);
  AssertEquals('Cloned array should have the same elements', LIntArr[1], LIntClone[1]);
  AssertEquals('Cloned array should have the same elements', LIntArr[2], LIntClone[2]);
  AssertEquals('Cloned array should have the same elements', LIntArr[3], LIntClone[3]);
  AssertEquals('Cloned array should have the same elements', LIntArr[4], LIntClone[4]);

  LIntArr.Put(0, 99);
  AssertTrue('Cloned array should be independent', LIntArr[0] <> LIntClone[0]);

  LIntArr.Put(1, 100);
  AssertTrue('Cloned array should be independent', LIntArr[1] <> LIntClone[1]);

  LIntArr.Put(2, 101);
  AssertTrue('Cloned array should be independent', LIntArr[2] <> LIntClone[2]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create;

  { 空数组克隆 }
  LStrClone := LStrArr.Clone as specialize IArray<string>;
  AssertEquals('Cloned empty array should remain empty', 0, LStrClone.GetCount);
  AssertTrue('Cloned empty array should have the same allocator', LStrArr.GetAllocator = LStrClone.GetAllocator);
  AssertTrue('Cloned empty array should have the same count', LStrArr.GetCount = LStrClone.GetCount);
  AssertTrue('A cloned empty array should have the same collection memory as nil', LStrClone.GetMemory = nil);

  { 克隆测试 }
  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD','FAFAFA']);
  LStrClone := LStrArr.Clone as specialize IArray<string>;
  AssertEquals('Cloned array size should match original', LStrArr.GetCount, LStrClone.GetCount);
  AssertTrue('Cloned array should have a different collection memory', LStrArr.GetMemory <> LStrClone.GetMemory);
  AssertEquals('Cloned array should have the same elements', LStrArr[0], LStrClone[0]);
  AssertEquals('Cloned array should have the same elements', LStrArr[1], LStrClone[1]);
  AssertEquals('Cloned array should have the same elements', LStrArr[2], LStrClone[2]);

  LStrArr.Put(0, 'TEST');
  AssertTrue('Cloned array should be independent', LStrArr[0] <> LStrClone[0]);

  LStrArr.Put(1, 'TEST2');
  AssertTrue('Cloned array should be independent', LStrArr[1] <> LStrClone[1]);

  LStrArr.Put(2, 'TEST3');
  AssertTrue('Cloned array should be independent', LStrArr[2] <> LStrClone[2]);

end;

type
  TTestStruct = record
    A: UInt64;
    B: UInt64;
    C: UInt64;
    D: UInt64;
  end;

function MakeTestStruct(aA, aB, aC,aD: UInt64): TTestStruct;
begin
  Result.A := aA;
  Result.B := aB;
  Result.C := aC;
  Result.D := aD;
end;

procedure TTestCase_Array.Test_Fill3;
var
  LU8Arr:         specialize IArray<UInt8>;
  LU16Arr:        specialize IArray<UInt16>;
  LIntArr:        specialize IArray<Integer>;
  LU64Arr:        specialize IArray<UInt64>;
  LStrArr:        specialize IArray<string>;
  LArrTestStruct: specialize IArray<TTestStruct>;
begin
  LU8Arr := specialize TArray<UInt8>.Create([0, 0, 0, 0, 0]);
  LU8Arr.Fill(1, 3, 255);
  AssertEquals(0, LU8Arr[0]);
  AssertEquals(255, LU8Arr[1]);
  AssertEquals(255, LU8Arr[2]);
  AssertEquals(255, LU8Arr[3]);
  AssertEquals(0, LU8Arr[4]);

  LU16Arr := specialize TArray<UInt16>.Create([0, 0, 0, 0, 0]);
  LU16Arr.Fill(1, 3, 65535);
  AssertEquals(0, LU16Arr[0]);
  AssertEquals(65535, LU16Arr[1]);
  AssertEquals(65535, LU16Arr[2]);
  AssertEquals(65535, LU16Arr[3]);
  AssertEquals(0, LU16Arr[4]);

  LIntArr := specialize TArray<Integer>.Create([0, 0, 0, 0, 0]);
  LIntArr.Fill(1, 3, 2147483647);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(2147483647, LIntArr[1]);
  AssertEquals(2147483647, LIntArr[2]);
  AssertEquals(2147483647, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);

  LU64Arr := specialize TArray<UInt64>.Create([0, 0, 0, 0, 0]);
  LU64Arr.Fill(1, 3, 18446744073709551615);
  AssertEquals(0, LU64Arr[0]);
  AssertEquals(18446744073709551615, LU64Arr[1]);
  AssertEquals(18446744073709551615, LU64Arr[2]);
  AssertEquals(18446744073709551615, LU64Arr[3]);
  AssertEquals(0, LU64Arr[4]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD','FAFAFA']);
  LStrArr.Fill(0, 2, 'TEST');
  AssertEquals('TEST', LStrArr[0]);
  AssertEquals('TEST', LStrArr[1]);
  AssertEquals('FAFAFA', LStrArr[2]);

  { 大结构填充 }

  LArrTestStruct := specialize TArray<TTestStruct>.Create(5);
  LArrTestStruct.Fill(0, 5, MakeTestStruct(100000, 200000, 300000, 400000));
  AssertTrue(100000 = LArrTestStruct[0].A);
  AssertTrue(200000 = LArrTestStruct[0].B);
  AssertTrue(300000 = LArrTestStruct[0].C);
  AssertTrue(400000 = LArrTestStruct[0].D);
  AssertTrue(100000 = LArrTestStruct[1].A);
  AssertTrue(200000 = LArrTestStruct[1].B);
  AssertTrue(300000 = LArrTestStruct[1].C);
  AssertTrue(400000 = LArrTestStruct[1].D);
  AssertTrue(100000 = LArrTestStruct[2].A);
  AssertTrue(200000 = LArrTestStruct[2].B);
  AssertTrue(300000 = LArrTestStruct[2].C);
  AssertTrue(400000 = LArrTestStruct[2].D);
  AssertTrue(100000 = LArrTestStruct[3].A);
  AssertTrue(200000 = LArrTestStruct[3].B);
  AssertTrue(300000 = LArrTestStruct[3].C);
  AssertTrue(400000 = LArrTestStruct[3].D);
  AssertTrue(100000 = LArrTestStruct[4].A);
  AssertTrue(200000 = LArrTestStruct[4].B);
  AssertTrue(300000 = LArrTestStruct[4].C);
  AssertTrue(400000 = LArrTestStruct[4].D);
end;

procedure TTestCase_Array.Test_Fill2;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空容器 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LIntArr.Fill(0, 999);
    end);

  { 异常测试: 越界 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Fill(9, 999);
    end);
  {$ENDIF}

  LIntArr := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 从0开始,填充999 }
  LIntArr.Fill(0, 999);
  AssertEquals('New size should be 8', 8, LIntArr.GetCount);
  AssertEquals('element 0 should be 999', 999, LIntArr[0]);
  AssertEquals('element 1 should be 999', 999, LIntArr[1]);
  AssertEquals('element 2 should be 999', 999, LIntArr[2]);
  AssertEquals('element 3 should be 999', 999, LIntArr[3]);
  AssertEquals('element 4 should be 999', 999, LIntArr[4]);
  AssertEquals('element 5 should be 999', 999, LIntArr[5]);
  AssertEquals('element 6 should be 999', 999, LIntArr[6]);
  AssertEquals('element 7 should be 999', 999, LIntArr[7]);

  { 从1开始,填充128 }
  LIntArr.Fill(1, 128);
  AssertEquals('New size should be 8', 8, LIntArr.GetCount);
  AssertEquals('element 0 should be 999', 999, LIntArr[0]);
  AssertEquals('element 1 should be 128', 128, LIntArr[1]);
  AssertEquals('element 2 should be 128', 128, LIntArr[2]);
  AssertEquals('element 3 should be 128', 128, LIntArr[3]);
  AssertEquals('element 4 should be 128', 128, LIntArr[4]);
  AssertEquals('element 5 should be 128', 128, LIntArr[5]);
  AssertEquals('element 6 should be 128', 128, LIntArr[6]);
  AssertEquals('element 7 should be 128', 128, LIntArr[7]);

  { 填充最后一个为1024 }
  LIntArr.Fill(7, 1024);
  AssertEquals('New size should be 8', 8, LIntArr.GetCount);
  AssertEquals('element 0 should be 999',  999, LIntArr[0]);
  AssertEquals('element 1 should be 128',  128, LIntArr[1]);
  AssertEquals('element 2 should be 128',  128, LIntArr[2]);
  AssertEquals('element 3 should be 128',  128, LIntArr[3]);
  AssertEquals('element 4 should be 128',  128, LIntArr[4]);
  AssertEquals('element 5 should be 128',  128, LIntArr[5]);
  AssertEquals('element 6 should be 128',  128, LIntArr[6]);
  AssertEquals('element 7 should be 1024', 1024, LIntArr[7]);

  { 托管元素 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LStrArr := specialize TArray<String>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LStrArr.Fill(0, '999');
    end);

  { 异常测试: 越界 }
  LStrArr := specialize TArray<String>.Create(['11','22','33','44','55','66','77','88']);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Fill(9, '999');
    end);
  {$ENDIF}

  LStrArr := specialize TArray<String>.Create(['11','22','33','44','55','66','77','88']);

  { 从0开始,填充999 }
  LStrArr.Fill(0, '999');
  AssertEquals('New size should be 8', 8, LStrArr.GetCount);
  AssertEquals('element 0 should be 999', '999', LStrArr[0]);
  AssertEquals('element 1 should be 999', '999', LStrArr[1]);
  AssertEquals('element 2 should be 999', '999', LStrArr[2]);
  AssertEquals('element 3 should be 999', '999', LStrArr[3]);
  AssertEquals('element 4 should be 999', '999', LStrArr[4]);
  AssertEquals('element 5 should be 999', '999', LStrArr[5]);
  AssertEquals('element 6 should be 999', '999', LStrArr[6]);
  AssertEquals('element 7 should be 999', '999', LStrArr[7]);

  { 从1开始,填充128 }
  LStrArr.Fill(1, '128');
  AssertEquals('New size should be 8', 8, LStrArr.GetCount);
  AssertEquals('element 0 should be 999', '999', LStrArr[0]);
  AssertEquals('element 1 should be 128', '128', LStrArr[1]);
  AssertEquals('element 2 should be 128', '128', LStrArr[2]);
  AssertEquals('element 3 should be 128', '128', LStrArr[3]);
  AssertEquals('element 4 should be 128', '128', LStrArr[4]);
  AssertEquals('element 5 should be 128', '128', LStrArr[5]);
  AssertEquals('element 6 should be 128', '128', LStrArr[6]);
  AssertEquals('element 7 should be 128', '128', LStrArr[7]);

  { 填充最后一个为1024 }
  LStrArr.Fill(7, '1024');
  AssertEquals('New size should be 8', 8, LStrArr.GetCount);
  AssertEquals('element 0 should be 999',  '999',  LStrArr[0]);
  AssertEquals('element 1 should be 128',  '128',  LStrArr[1]);
  AssertEquals('element 2 should be 128',  '128',  LStrArr[2]);
  AssertEquals('element 3 should be 128',  '128',  LStrArr[3]);
  AssertEquals('element 4 should be 128',  '128',  LStrArr[4]);
  AssertEquals('element 5 should be 128',  '128',  LStrArr[5]);
  AssertEquals('element 6 should be 128',  '128',  LStrArr[6]);
  AssertEquals('element 7 should be 1024', '1024', LStrArr[7]);
end;

procedure TTestCase_Array.Test_Fill1;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LIntArr.Fill(999);
    end);
  {$ENDIF}

  LIntArr := specialize TArray<Integer>.Create([0, 0, 0, 0, 0]);
  LIntArr.Fill(3);
  AssertEquals(3, LIntArr[0]);
  AssertEquals(3, LIntArr[1]);
  AssertEquals(3, LIntArr[2]);
  AssertEquals(3, LIntArr[3]);
  AssertEquals(3, LIntArr[4]);

  { 托管元素 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LStrArr := specialize TArray<String>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LStrArr.Fill('999');
    end);
  {$ENDIF}

  LStrArr := specialize TArray<String>.Create(['11','22']);
  LStrArr.Fill('999');
  AssertEquals('New size should be 2', 2, LStrArr.GetCount);
  AssertEquals('element 0 should be 999', '999', LStrArr[0]);
  AssertEquals('element 1 should be 999', '999', LStrArr[1]);

end;

procedure TTestCase_Array.Test_Zero3;
var
  LU8Arr:    specialize IArray<UInt8>;
  LU16Arr:   specialize IArray<UInt16>;
  LU32Arr:   specialize IArray<UInt32>;
  LU64Arr:   specialize IArray<UInt64>;
  LStrArr:   specialize IArray<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 索引越界 }
  LU8Arr := specialize TArray<UInt8>.Create;
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LU8Arr.Zero(1, 3);
    end);

  { 异常测试: 数量为0 }
  LU8Arr := specialize TArray<UInt8>.Create([1, 1, 1, 1, 1]);
  AssertException(
    'exception should be raised: aElementCount is 0',
    EIsZero,
    procedure
    begin
      LU8Arr.Zero(1, 0);
    end);

  { 异常测试: 索引越界 }
  LU8Arr := specialize TArray<UInt8>.Create([1, 1, 1, 1, 1]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LU8Arr.Zero(5, 3);
    end);
  {$ENDIF}

  { U8 写零 }
  LU8Arr := specialize TArray<UInt8>.Create([1, 1, 1, 1, 1]);
  LU8Arr.Zero(1, 3);
  AssertEquals(1, LU8Arr[0]);
  AssertEquals(0, LU8Arr[1]);
  AssertEquals(0, LU8Arr[2]);
  AssertEquals(0, LU8Arr[3]);
  AssertEquals(1, LU8Arr[4]);

  { U16 写零 }
  LU16Arr := specialize TArray<UInt16>.Create([1, 1, 1, 1, 1]);
  LU16Arr.Zero(1, 3);
  AssertEquals(1, LU16Arr[0]);
  AssertEquals(0, LU16Arr[1]);
  AssertEquals(0, LU16Arr[2]);
  AssertEquals(0, LU16Arr[3]);
  AssertEquals(1, LU16Arr[4]);

  { U32 写零 }
  LU32Arr := specialize TArray<UInt32>.Create([1, 1, 1, 1, 1]);
  LU32Arr.Zero(1, 3);
  AssertEquals(1, LU32Arr[0]);
  AssertEquals(0, LU32Arr[1]);
  AssertEquals(0, LU32Arr[2]);
  AssertEquals(0, LU32Arr[3]);
  AssertEquals(1, LU32Arr[4]);

  { U64 写零 }
  LU64Arr := specialize TArray<UInt64>.Create([1, 1, 1, 1, 1]);
  LU64Arr.Zero(1, 3);
  AssertEquals(1, LU64Arr[0]);
  AssertEquals(0, LU64Arr[1]);
  AssertEquals(0, LU64Arr[2]);
  AssertEquals(0, LU64Arr[3]);
  AssertEquals(1, LU64Arr[4]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD','FAFAFA']);
  LStrArr.Zero(0, 2);
  AssertEquals('', LStrArr[0]);
  AssertEquals('', LStrArr[1]);
  AssertEquals('FAFAFA', LStrArr[2]);
end;

procedure TTestCase_Array.Test_Zero2;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LIntArr.Zero(2);
    end);

  { 异常测试: 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Zero(5);
    end);
  {$ENDIF}

  { 全部写0 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Zero(0);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(0, LIntArr[1]);
  AssertEquals(0, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);

  { 部分写0 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Zero(1);
  AssertEquals(1, LIntArr[0]);
  AssertEquals(0, LIntArr[1]);
  AssertEquals(0, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);

  { 最后一个元素写0 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Zero(4);
  AssertEquals(1, LIntArr[0]);
  AssertEquals(1, LIntArr[1]);
  AssertEquals(1, LIntArr[2]);
  AssertEquals(1, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);

  { 托管类型 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LStrArr := specialize TArray<String>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LStrArr.Zero(2);
    end);

  { 异常测试: 索引越界 }
  LStrArr := specialize TArray<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Zero(3);
    end);
  {$ENDIF}

  { 全部写0 }
  LStrArr := specialize TArray<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  LStrArr.Zero(0);
  AssertEquals('', LStrArr[0]);
  AssertEquals('', LStrArr[1]);
  AssertEquals('', LStrArr[2]);

  { 部分写0 }
  LStrArr := specialize TArray<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  LStrArr.Zero(1);
  AssertEquals('HELLO', LStrArr[0]);
  AssertEquals('', LStrArr[1]);
  AssertEquals('', LStrArr[2]);

  { 最后一个元素写0 }
  LStrArr := specialize TArray<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  LStrArr.Zero(2);
  AssertEquals('HELLO', LStrArr[0]);
  AssertEquals('WORLD', LStrArr[1]);
  AssertEquals('', LStrArr[2]);
end;

procedure TTestCase_Array.Test_Zero1;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LIntArr := specialize TArray<Integer>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LIntArr.Zero;
    end);
  {$ENDIF}

  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Zero;
  AssertEquals(0, LIntArr[0]);
  AssertEquals(0, LIntArr[1]);
  AssertEquals(0, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);

  { 托管类型 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LStrArr := specialize TArray<String>.Create;
  AssertException(
    'exception should be raised: collection is empty',
    EEmpty,
    procedure
    begin
      LStrArr.Zero;
    end);
  {$ENDIF}

  LStrArr := specialize TArray<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  LStrArr.Zero;
  AssertEquals('', LStrArr[0]);
  AssertEquals('', LStrArr[1]);
  AssertEquals('', LStrArr[2]);
end;

procedure TTestCase_Array.Test_Swap;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
  i:       SizeUInt;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4]);

  { 异常测试: 数量为0 }
  AssertException(
    'exception should be raised: element count is 0',
    EIsZero,
    procedure
    begin
      LIntArr.Swap(0, 3, 0);
    end);

  { 异常测试: 索引相同 }
  AssertException(
    'exception should be raised: same index',
    ESame,
    procedure
    begin
      LIntArr.Swap(1, 1, 1);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Swap(1, 4, 1);
    end);

  { 异常测试: 范围越界 }
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure
    begin
      LIntArr.Swap(0, 3, 2);
    end);

  { 异常测试: 正向重叠 }
  AssertException(
    'exception should be raised: overlap',
    EOverlap,
    procedure
    begin
      LIntArr.Swap(0, 1, 2);
    end);

  { 异常测试: 反向重叠 }
  AssertException(
    'exception should be raised: overlap',
    EOverlap,
    procedure
    begin
      LIntArr.Swap(1, 0, 2);
    end);
  {$ENDIF}

  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4]);

  { 交换首尾两个元素 }
  LIntArr.Swap(0, 3, 1);
  AssertEquals(4, LIntArr[0]);
  AssertEquals(2, LIntArr[1]);
  AssertEquals(3, LIntArr[2]);
  AssertEquals(1, LIntArr[3]);

  { 反转前两个元素 }
  LIntArr.Swap(0, 2, 2);
  AssertEquals(3, LIntArr[0]);
  AssertEquals(1, LIntArr[1]);
  AssertEquals(4, LIntArr[2]);
  AssertEquals(2, LIntArr[3]);

  { 分块交换 }
  LIntArr := specialize TArray<Integer>.Create;
  LIntArr.Resize(65536);

  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := i;

  LIntArr.Swap(0, (LIntArr.Count div 2), LIntArr.Count div 2);

  for i := 0 to (LIntArr.Count div 2) - 1 do
    AssertEquals(Integer((LIntArr.Count div 2) + i), LIntArr[i]);

  for i := (LIntArr.Count div 2) to LIntArr.Count - 1 do
    AssertEquals(Integer(i - (LIntArr.Count div 2)), LIntArr[i]);

  { 托管元素 }

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD', 'FAFAFA', 'STUDIO']);

  { 异常测试: 数量为0 }
  AssertException(
    'exception should be raised: element count is 0',
    EIsZero,
    procedure
    begin
      LStrArr.Swap(0, 3, 0);
    end);

  { 异常测试: 索引相同 }
  AssertException(
    'exception should be raised: same index',
    ESame,
    procedure
    begin
      LStrArr.Swap(1, 1, 1);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Swap(1, 4, 1);
    end);

  { 异常测试: 范围越界 }
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure
    begin
      LStrArr.Swap(0, 3, 2);
    end);

  { 异常测试: 正向重叠 }
  AssertException(
    'exception should be raised: overlap',
    EOverlap,
    procedure
    begin
      LStrArr.Swap(0, 1, 2);
    end);

  { 异常测试: 反向重叠 }
  AssertException(
    'exception should be raised: overlap',
    EOverlap,
    procedure
    begin
      LStrArr.Swap(1, 0, 2);
    end);
  {$ENDIF}

  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD', 'FAFAFA', 'STUDIO']);

  { 交换首尾两个元素 }
  LStrArr.Swap(0, 3, 1);
  AssertEquals('STUDIO', LStrArr[0]);
  AssertEquals('WORLD',  LStrArr[1]);
  AssertEquals('FAFAFA', LStrArr[2]);
  AssertEquals('HELLO',  LStrArr[3]);

  { 反转前两个元素 }
  LStrArr.Swap(0, 2, 2);
  AssertEquals('FAFAFA', LStrArr[0]);
  AssertEquals('HELLO',  LStrArr[1]);
  AssertEquals('STUDIO', LStrArr[2]);
  AssertEquals('WORLD',  LStrArr[3]);

  { 分块交换 }
  LStrArr := specialize TArray<string>.Create;
  LStrArr.Resize(65536);

  for i := 0 to LStrArr.Count - 1 do
    LStrArr[i] := IntToStr(i);

  LStrArr.Swap(0, (LStrArr.Count div 2), LStrArr.Count div 2);

  for i := 0 to (LStrArr.Count div 2) - 1 do
    AssertEquals(IntToStr((LStrArr.Count div 2) + i), LStrArr[i]);

  for i := (LStrArr.Count div 2) to LStrArr.Count - 1 do
    AssertEquals(IntToStr(i - (LStrArr.Count div 2)), LStrArr[i]);

end;

procedure TTestCase_Array.Test_Swap2;
var
  LIntArr:           specialize IArray<Integer>;
  LStrArr:        specialize IArray<string>;
  LTestStructArr: specialize IArray<TTestStruct>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 索引相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4, 5]);
  AssertException(
    'exception should be raised: same index',
    ESame,
    procedure
    begin
      LIntArr.Swap(1, 1);
    end);

  { 异常测试: 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Swap(1, 4);
    end);
  {$ENDIF}

  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4, 5]);
  LIntArr.Swap(1, 3);
  AssertEquals(1, LIntArr[0]);
  AssertEquals(4, LIntArr[1]);
  AssertEquals(3, LIntArr[2]);
  AssertEquals(2, LIntArr[3]);
  AssertEquals(5, LIntArr[4]);

  { 托管类型 }

  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD','FAFAFA']);
  LStrArr.Swap(0, 2);
  AssertEquals('FAFAFA', LStrArr[0]);
  AssertEquals('WORLD', LStrArr[1]);
  AssertEquals('HELLO', LStrArr[2]);

  { 大结构交换 }

  LTestStructArr := specialize TArray<TTestStruct>.Create(4);
  LTestStructArr.Put(0, MakeTestStruct(1, 2, 3, 4));
  LTestStructArr.Put(1, MakeTestStruct(5, 6, 7, 8));
  LTestStructArr.Put(2, MakeTestStruct(9, 10, 11, 12));
  LTestStructArr.Put(3, MakeTestStruct(13, 14, 15, 16));

  LTestStructArr.Swap(1, 3);

  AssertEquals(1, LTestStructArr[0].A);
  AssertEquals(2, LTestStructArr[0].B);
  AssertEquals(3, LTestStructArr[0].C);
  AssertEquals(4, LTestStructArr[0].D);

  AssertEquals(13, LTestStructArr[1].A);
  AssertEquals(14, LTestStructArr[1].B);
  AssertEquals(15, LTestStructArr[1].C);
  AssertEquals(16, LTestStructArr[1].D);

  AssertEquals(9, LTestStructArr[2].A);
  AssertEquals(10, LTestStructArr[2].B);
  AssertEquals(11, LTestStructArr[2].C);
  AssertEquals(12, LTestStructArr[2].D);

  AssertEquals(5, LTestStructArr[3].A);
  AssertEquals(6, LTestStructArr[3].B);
  AssertEquals(7, LTestStructArr[3].C);
  AssertEquals(8, LTestStructArr[3].D);
end;

procedure TTestCase_Array.Test_Copy;
var
  LIntArr: specialize IArray<Integer>;
  LStrArr: specialize IArray<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 范围越界 }
  LIntArr := specialize TArray<Integer>.Create([1, 2, 3, 4]);
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(1, 3, 2);
  end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(3, 1, 2);
    end);

  { 异常测试: 索引相同 }
  AssertException(
    'exception should be raised: same index',
    ESame,
    procedure begin
      LIntArr.Copy(1, 1, 2);
    end);

  { 异常测试: 范围越界 }
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(0, 1, 4);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(0, 1, 5);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(1, 2, 3);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(2, 3, 2);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(1, 0, 4);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(2, 1, 3);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LIntArr.Copy(3, 2, 2);
    end);

  { 错误测试,元素数量为0 }
  AssertException(
    'exception should be raised: element count is 0',
    EIsZero,
    procedure begin
      LIntArr.Copy(1, 3, 0);
    end);

  {$ENDIF}

  { 正向拷贝 }
  LIntArr := specialize TArray<Integer>.Create([0, 1, 2, 3, 4, 5, 6, 7]);
  LIntArr.Copy(0, 3, 2);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(1, LIntArr[1]);
  AssertEquals(2, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(1, LIntArr[4]);
  AssertEquals(5, LIntArr[5]);
  AssertEquals(6, LIntArr[6]);
  AssertEquals(7, LIntArr[7]);

  { 正向边界拷贝 }
  LIntArr.Copy(0, 4, 4);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(1, LIntArr[1]);
  AssertEquals(2, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);
  AssertEquals(1, LIntArr[5]);
  AssertEquals(2, LIntArr[6]);
  AssertEquals(0, LIntArr[7]);

  { 反向拷贝 }
  LIntArr.Copy(3, 0, 3);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(0, LIntArr[1]);
  AssertEquals(1, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);
  AssertEquals(1, LIntArr[5]);
  AssertEquals(2, LIntArr[6]);
  AssertEquals(0, LIntArr[7]);

  { 反向边界拷贝 }
  LIntArr.Copy(4, 0, 4);
  AssertEquals(0, LIntArr[0]);
  AssertEquals(1, LIntArr[1]);
  AssertEquals(2, LIntArr[2]);
  AssertEquals(0, LIntArr[3]);
  AssertEquals(0, LIntArr[4]);
  AssertEquals(1, LIntArr[5]);
  AssertEquals(2, LIntArr[6]);
  AssertEquals(0, LIntArr[7]);

  { 托管类型 }

  { 错误测试,索引越界 }
  LStrArr := specialize TArray<string>.Create(['HELLO', 'WORLD', 'FAFAFA']);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}

  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure begin
      LStrArr.Copy(1, 3, 2);
    end);

  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure begin
      LStrArr.Copy(3, 1, 2);
    end);

  { 错误测试,索引相同 }
  AssertException(
    'exception should be raised: same index',
    ESame,
    procedure begin
      LStrArr.Copy(1, 1, 2);
    end);

  { 错误测试,范围越界 }
  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LStrArr.Copy(0, 1, 4);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LStrArr.Copy(0, 1, 5);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LStrArr.Copy(1, 2, 3);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure begin
      LStrArr.Copy(2, 3, 2);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LStrArr.Copy(1, 0, 4);
    end);

  AssertException(
    'exception should be raised: bounds out of range',
    ERangeOutOfBounds,
    procedure begin
      LStrArr.Copy(2, 1, 3);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure begin
      LStrArr.Copy(3, 2, 2);
    end);

  { 错误测试,元素数量为0 }
  AssertException(
    'exception should be raised: element count is 0',
    EIsZero,
    procedure begin
      LStrArr.Copy(1, 3, 0);
    end);

  {$ENDIF}

  { 正向拷贝 }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7']);
  LStrArr.Copy(0, 3, 2);
  AssertEquals('str0', LStrArr[0]);
  AssertEquals('str1', LStrArr[1]);
  AssertEquals('str2', LStrArr[2]);
  AssertEquals('str0', LStrArr[3]);
  AssertEquals('str1', LStrArr[4]);
  AssertEquals('str5', LStrArr[5]);
  AssertEquals('str6', LStrArr[6]);
  AssertEquals('str7', LStrArr[7]);

  { 正向边界拷贝 }
  LStrArr.Copy(0, 4, 4);
  AssertEquals('str0', LStrArr[0]);
  AssertEquals('str1', LStrArr[1]);
  AssertEquals('str2', LStrArr[2]);
  AssertEquals('str0', LStrArr[3]);
  AssertEquals('str0', LStrArr[4]);
  AssertEquals('str1', LStrArr[5]);
  AssertEquals('str2', LStrArr[6]);
  AssertEquals('str0', LStrArr[7]);

  { 反向拷贝 }
  LStrArr.Copy(3, 0, 3);
  AssertEquals('str0', LStrArr[0]);
  AssertEquals('str0', LStrArr[1]);
  AssertEquals('str1', LStrArr[2]);
  AssertEquals('str0', LStrArr[3]);
  AssertEquals('str0', LStrArr[4]);
  AssertEquals('str1', LStrArr[5]);
  AssertEquals('str2', LStrArr[6]);
  AssertEquals('str0', LStrArr[7]);

  { 反向边界拷贝 }
  LStrArr.Copy(4, 0, 4);
  AssertEquals('str0', LStrArr[0]);
  AssertEquals('str1', LStrArr[1]);
  AssertEquals('str2', LStrArr[2]);
  AssertEquals('str0', LStrArr[3]);
  AssertEquals('str0', LStrArr[4]);
  AssertEquals('str1', LStrArr[5]);
  AssertEquals('str2', LStrArr[6]);
  AssertEquals('str0', LStrArr[7]);

end;

procedure TTestCase_Array.Test_Find1;
var
  LUInt8Arr : specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3, 4, 5, 6, 7, 8, 9]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt8Arr.Find(10) > -1);

  { 成功测试 }
  AssertEquals(0,  LUInt8Arr.Find(0));
  AssertEquals(1,  LUInt8Arr.Find(1));
  AssertEquals(2,  LUInt8Arr.Find(2));
  AssertEquals(3,  LUInt8Arr.Find(3));
  AssertEquals(4,  LUInt8Arr.Find(4));
  AssertEquals(5,  LUInt8Arr.Find(5));
  AssertEquals(6,  LUInt8Arr.Find(6));
  AssertEquals(7,  LUInt8Arr.Find(7));
  AssertEquals(8,  LUInt8Arr.Find(8));
  AssertEquals(9,  LUInt8Arr.Find(9));

  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003, 10004, 10005, 10006, 10007, 10008, 10009]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt16Arr.Find(10010) > -1);

  { 成功测试 }
  AssertEquals(0,  LUInt16Arr.Find(10000));
  AssertEquals(1,  LUInt16Arr.Find(10001));
  AssertEquals(2,  LUInt16Arr.Find(10002));
  AssertEquals(3,  LUInt16Arr.Find(10003));
  AssertEquals(4,  LUInt16Arr.Find(10004));
  AssertEquals(5,  LUInt16Arr.Find(10005));
  AssertEquals(6,  LUInt16Arr.Find(10006));
  AssertEquals(7,  LUInt16Arr.Find(10007));
  AssertEquals(8,  LUInt16Arr.Find(10008));
  AssertEquals(9,  LUInt16Arr.Find(10009));

  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003, 1000000004, 1000000005, 1000000006, 1000000007, 1000000008, 1000000009]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt32Arr.Find(1000000010) > -1);

  { 成功测试 }
  AssertEquals(0,  LUInt32Arr.Find(1000000000));
  AssertEquals(1,  LUInt32Arr.Find(1000000001));
  AssertEquals(2,  LUInt32Arr.Find(1000000002));
  AssertEquals(3,  LUInt32Arr.Find(1000000003));
  AssertEquals(4,  LUInt32Arr.Find(1000000004));
  AssertEquals(5,  LUInt32Arr.Find(1000000005));
  AssertEquals(6,  LUInt32Arr.Find(1000000006));
  AssertEquals(7,  LUInt32Arr.Find(1000000007));
  AssertEquals(8,  LUInt32Arr.Find(1000000008));
  AssertEquals(9,  LUInt32Arr.Find(1000000009));

  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003, 10000000000000000004, 10000000000000000005, 10000000000000000006, 10000000000000000007, 10000000000000000008, 10000000000000000009]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000010) > -1);

  { 成功测试 }
  AssertEquals(0,  LUInt64Arr.Find(10000000000000000000));
  AssertEquals(1,  LUInt64Arr.Find(10000000000000000001));
  AssertEquals(2,  LUInt64Arr.Find(10000000000000000002));
  AssertEquals(3,  LUInt64Arr.Find(10000000000000000003));
  AssertEquals(4,  LUInt64Arr.Find(10000000000000000004));
  AssertEquals(5,  LUInt64Arr.Find(10000000000000000005));
  AssertEquals(6,  LUInt64Arr.Find(10000000000000000006));
  AssertEquals(7,  LUInt64Arr.Find(10000000000000000007));
  AssertEquals(8,  LUInt64Arr.Find(10000000000000000008));
  AssertEquals(9,  LUInt64Arr.Find(10000000000000000009));

  LStrArr := specialize TArray<string>.Create(
    ['str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7', 'str8', 'str9']);

  { 失败测试 }
  AssertFalse('find should fail', LStrArr.Find('str10') > -1);

  { 成功测试 }
  AssertEquals(0,  LStrArr.Find('str0'));
  AssertEquals(1,  LStrArr.Find('str1'));
  AssertEquals(2,  LStrArr.Find('str2'));
  AssertEquals(3,  LStrArr.Find('str3'));
  AssertEquals(4,  LStrArr.Find('str4'));
  AssertEquals(5,  LStrArr.Find('str5'));
  AssertEquals(6,  LStrArr.Find('str6'));
  AssertEquals(7,  LStrArr.Find('str7'));
  AssertEquals(8,  LStrArr.Find('str8'));
  AssertEquals(9,  LStrArr.Find('str9'));

  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  AssertFalse('find should fail', LCustomArr.Find(make_custom_compare_data(17, 18, 19, 20)) > -1);

  { 成功测试 }
  AssertEquals(0,  LCustomArr.Find(make_custom_compare_data(1, 2, 3, 4)));
  AssertEquals(1,  LCustomArr.Find(make_custom_compare_data(5, 6, 7, 8)));
  AssertEquals(2,  LCustomArr.Find(make_custom_compare_data(9, 10, 11, 12)));
  AssertEquals(3,  LCustomArr.Find(make_custom_compare_data(13, 14, 15, 16)));
end;

procedure TTestCase_Array.Test_Find2;
var
  LCounter:   SizeUInt;
  LUInt8Arr : specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, @custom_equals_u8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, @custom_equals_u8, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, @custom_equals_u8, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, @custom_equals_u8, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, @custom_equals_u8, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(10004, @custom_equals_u16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10000, @custom_equals_u16, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10001, @custom_equals_u16, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10002, @custom_equals_u16, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10003, @custom_equals_u16, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(1000000004, @custom_equals_u32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000000, @custom_equals_u32, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000001, @custom_equals_u32, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000002, @custom_equals_u32, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000003, @custom_equals_u32, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create(
    [10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000004, @custom_equals_u64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000000, @custom_equals_u64, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000001, @custom_equals_u64, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000002, @custom_equals_u64, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000003, @custom_equals_u64, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('str4', @custom_equals_string, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str0', @custom_equals_string, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str1', @custom_equals_string, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str2', @custom_equals_string, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str3', @custom_equals_string, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20), @custom_equals_custom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4), @custom_equals_custom, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8), @custom_equals_custom, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12), @custom_equals_custom, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16), @custom_equals_custom, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);
end;

procedure TTestCase_Array.Test_Find3;
var
  LCounter:   SizeUInt;
  LUInt8Arr : specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, @DoEqualsU8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, @DoEqualsU8, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, @DoEqualsU8, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, @DoEqualsU8, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, @DoEqualsU8, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(10004, @DoEqualsU16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10000, @DoEqualsU16, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10001, @DoEqualsU16, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10002, @DoEqualsU16, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10003, @DoEqualsU16, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(1000000004, @DoEqualsU32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000000, @DoEqualsU32, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000001, @DoEqualsU32, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000002, @DoEqualsU32, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000003, @DoEqualsU32, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create(
    [10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000004, @DoEqualsU64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000000, @DoEqualsU64, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000001, @DoEqualsU64, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000002, @DoEqualsU64, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000003, @DoEqualsU64, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('str4', @DoEqualsString, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str0', @DoEqualsString, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str1', @DoEqualsString, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str2', @DoEqualsString, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str3', @DoEqualsString, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20), @DoEqualsCustom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4), @DoEqualsCustom, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8), @DoEqualsCustom, @LCounter) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12), @DoEqualsCustom, @LCounter) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16), @DoEqualsCustom, @LCounter) = 3);
  AssertEquals('counter should be 4', 4, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Find4;
var
  LCounter:   SizeUInt;
  LUInt8Arr : specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试}
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(
  4,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  0,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  1,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  2,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  3,
  function(const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(
  10004,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10000,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10001,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10002,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10003,
  function(const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(
  1000000004,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000000,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000001,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000002,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000003,
  function(const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create(
    [10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(
  10000000000000000004,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000000,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000001,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000002,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000003,
  function(const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);


  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find(
  'str4',
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str0',
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str1',
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str2',
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str3',
  function(const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20),
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4),
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8),
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 1);
  AssertEquals('counter should be 2', 2, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12),
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 2);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16),
    function(const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 3);
  AssertEquals('counter should be 4', 4, LCounter);
end;
{$endif}

procedure TTestCase_Array.Test_Find5;
var
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0) =  0);
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1) =  1);
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2) =  2);
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3) =  3);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt16Arr.Find(10004, 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt16Arr.Find(10000, 0) =  0);
  AssertTrue('find should succeed', LUInt16Arr.Find(10001, 1) =  1);
  AssertTrue('find should succeed', LUInt16Arr.Find(10002, 2) =  2);
  AssertTrue('find should succeed', LUInt16Arr.Find(10003, 3) =  3);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt32Arr.Find(1000000004, 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000000, 0) =  0);
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000001, 1) =  1);
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000002, 2) =  2);
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000003, 3) =  3);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);
  
  { 失败测试 }
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000004, 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000000, 0) =  0);
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000001, 1) =  1);
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000002, 2) =  2);
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000003, 3) =  3);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  AssertFalse('find should fail', LStrArr.Find('str4', 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LStrArr.Find('str0', 0) =  0);
  AssertTrue('find should succeed', LStrArr.Find('str1', 1) =  1);
  AssertTrue('find should succeed', LStrArr.Find('str2', 2) =  2);
  AssertTrue('find should succeed', LStrArr.Find('str3', 3) =  3);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  AssertFalse('find should fail', LCustomArr.Find(make_custom_compare_data(17, 18, 19, 20), 0) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(1, 2, 3, 4), 0)     =  0);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(5, 6, 7, 8), 1)     =  1);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(9, 10, 11, 12), 2)  =  2);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(13, 14, 15, 16), 3) =  3);
end;

procedure TTestCase_Array.Test_Find6;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0, @custom_equals_u8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0, @custom_equals_u8, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1, @custom_equals_u8, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2, @custom_equals_u8, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3, @custom_equals_u8, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(10004, 0, @custom_equals_u16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10000, 0, @custom_equals_u16, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10001, 1, @custom_equals_u16, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10002, 2, @custom_equals_u16, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10003, 3, @custom_equals_u16, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(1000000004, 0, @custom_equals_u32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000000, 0, @custom_equals_u32, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000001, 1, @custom_equals_u32, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000002, 2, @custom_equals_u32, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000003, 3, @custom_equals_u32, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000004, 0, @custom_equals_u64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000000, 0, @custom_equals_u64, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000001, 1, @custom_equals_u64, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000002, 2, @custom_equals_u64, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000003, 3, @custom_equals_u64, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('str4', 0, @custom_equals_string, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str0', 0, @custom_equals_string, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str1', 1, @custom_equals_string, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str2', 2, @custom_equals_string, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str3', 3, @custom_equals_string, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20), 0, @custom_equals_custom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4), 0, @custom_equals_custom, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8), 1, @custom_equals_custom, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12), 2, @custom_equals_custom, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16), 3, @custom_equals_custom, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);
end;

procedure TTestCase_Array.Test_Find7;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0, @DoEqualsU8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0, @DoEqualsU8, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1, @DoEqualsU8, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2, @DoEqualsU8, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3, @DoEqualsU8, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(10004, 0, @DoEqualsU16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10000, 0, @DoEqualsU16, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10001, 1, @DoEqualsU16, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10002, 2, @DoEqualsU16, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(10003, 3, @DoEqualsU16, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(1000000004, 0, @DoEqualsU32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000000, 0, @DoEqualsU32, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000001, 1, @DoEqualsU32, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000002, 2, @DoEqualsU32, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1000000003, 3, @DoEqualsU32, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(10000000000000000004, 0, @DoEqualsU64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000000, 0, @DoEqualsU64, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000001, 1, @DoEqualsU64, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000002, 2, @DoEqualsU64, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(10000000000000000003, 3, @DoEqualsU64, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('str4', 0, @DoEqualsString, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str0', 0, @DoEqualsString, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str1', 1, @DoEqualsString, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str2', 2, @DoEqualsString, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('str3', 3, @DoEqualsString, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20), 0, @DoEqualsCustom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4), 0, @DoEqualsCustom, @LCounter) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8), 1, @DoEqualsCustom, @LCounter) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12), 2, @DoEqualsCustom, @LCounter) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16), 3, @DoEqualsCustom, @LCounter) =  3);
  AssertEquals('counter should be 1', 1, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Find8;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0, 1, 2, 3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(
  4, 0,
  function (const aLeft, aRight: UInt8): boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  0, 0,
  function (const aLeft, aRight: UInt8): boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  1, 1,
  function (const aLeft, aRight: UInt8): boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  2, 2,
  function (const aLeft, aRight: UInt8): boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  3, 3,
  function (const aLeft, aRight: UInt8): boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([10000, 10001, 10002, 10003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(
  10004, 0,
  function (const aLeft, aRight: UInt16): boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10000, 0,
  function (const aLeft, aRight: UInt16): boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10001, 1,
  function (const aLeft, aRight: UInt16): boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10002, 2,
  function (const aLeft, aRight: UInt16): boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  10003, 3,
  function (const aLeft, aRight: UInt16): boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([1000000000, 1000000001, 1000000002, 1000000003]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(
  1000000004, 0,
  function (const aLeft, aRight: UInt32): boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000000, 0,
  function (const aLeft, aRight: UInt32): boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000001, 1,
  function (const aLeft, aRight: UInt32): boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000002, 2,
  function (const aLeft, aRight: UInt32): boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1000000003, 3,
  function (const aLeft, aRight: UInt32): boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([10000000000000000000, 10000000000000000001, 10000000000000000002, 10000000000000000003]);
  
  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(
  10000000000000000004, 0,
  function (const aLeft, aRight: UInt64): boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000000, 0,
  function (const aLeft, aRight: UInt64): boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000001, 1,
  function (const aLeft, aRight: UInt64): boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000002, 2,
  function (const aLeft, aRight: UInt64): boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  10000000000000000003, 3,
  function (const aLeft, aRight: UInt64): boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { string }
  LStrArr := specialize TArray<string>.Create(['str0', 'str1', 'str2', 'str3']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find(
  'str4', 0,
  function (const aLeft, aRight: string): boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str0', 0,
  function (const aLeft, aRight: string): boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str1', 1,
  function (const aLeft, aRight: string): boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str2', 2,
  function (const aLeft, aRight: string): boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'str3', 3,
  function (const aLeft, aRight: string): boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);

  { custom }
  LCustomArr := specialize TArray<custom_data_t>.Create(
    [
      make_custom_compare_data(1, 2, 3, 4),
      make_custom_compare_data(5, 6, 7, 8),
      make_custom_compare_data(9, 10, 11, 12),
      make_custom_compare_data(13, 14, 15, 16)
    ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17, 18, 19, 20), 0,
    function (const aLeft, aRight: custom_data_t): boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1, 2, 3, 4), 0,
    function (const aLeft, aRight: custom_data_t): boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) =  0);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5, 6, 7, 8), 1,
    function (const aLeft, aRight: custom_data_t): boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) =  1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9, 10, 11, 12), 2,
    function (const aLeft, aRight: custom_data_t): boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) =  2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13, 14, 15, 16), 3,
    function (const aLeft, aRight: custom_data_t): boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) =  3);
  AssertEquals('counter should be 1', 1, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Find9;
var
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0, 4) > -1);
  AssertFalse('find should fail', LUInt8Arr.Find(3, 0, 3) > -1);
  AssertFalse('find should fail', LUInt8Arr.Find(0, 1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0, 1) = 0);
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1, 1) = 1);
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2, 1) = 2);
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3, 1) = 3);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt16Arr.Find(4, 0, 4) > -1);
  AssertFalse('find should fail', LUInt16Arr.Find(3, 0, 3) > -1);
  AssertFalse('find should fail', LUInt16Arr.Find(0, 1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt16Arr.Find(0, 0, 1) = 0);
  AssertTrue('find should succeed', LUInt16Arr.Find(1, 1, 1) = 1);
  AssertTrue('find should succeed', LUInt16Arr.Find(2, 2, 1) = 2);
  AssertTrue('find should succeed', LUInt16Arr.Find(3, 3, 1) = 3);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt32Arr.Find(4, 0, 4) > -1);
  AssertFalse('find should fail', LUInt32Arr.Find(3, 0, 3) > -1);
  AssertFalse('find should fail', LUInt32Arr.Find(0, 1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt32Arr.Find(0, 0, 1) = 0);
  AssertTrue('find should succeed', LUInt32Arr.Find(1, 1, 1) = 1);
  AssertTrue('find should succeed', LUInt32Arr.Find(2, 2, 1) = 2);
  AssertTrue('find should succeed', LUInt32Arr.Find(3, 3, 1) = 3);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  AssertFalse('find should fail', LUInt64Arr.Find(4, 0, 4) > -1);
  AssertFalse('find should fail', LUInt64Arr.Find(3, 0, 3) > -1);
  AssertFalse('find should fail', LUInt64Arr.Find(0, 1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LUInt64Arr.Find(0, 0, 1) = 0);
  AssertTrue('find should succeed', LUInt64Arr.Find(1, 1, 1) = 1);
  AssertTrue('find should succeed', LUInt64Arr.Find(2, 2, 1) = 2);
  AssertTrue('find should succeed', LUInt64Arr.Find(3, 3, 1) = 3);

  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  AssertFalse('find should fail', LStrArr.Find('E', 0, 4) > -1);
  AssertFalse('find should fail', LStrArr.Find('D', 0, 3) > -1);
  AssertFalse('find should fail', LStrArr.Find('A', 1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LStrArr.Find('A', 0, 1) = 0);
  AssertTrue('find should succeed', LStrArr.Find('B', 1, 1) = 1);
  AssertTrue('find should succeed', LStrArr.Find('C', 2, 1) = 2);
  AssertTrue('find should succeed', LStrArr.Find('D', 3, 1) = 3);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  AssertFalse('find should fail', LCustomArr.Find(make_custom_compare_data(17,18,19,20), 0, 4) > -1);
  AssertFalse('find should fail', LCustomArr.Find(make_custom_compare_data(13,14,15,16), 0, 3) > -1);
  AssertFalse('find should fail', LCustomArr.Find(make_custom_compare_data(1,2,3,4),     1, 3) > -1);

  { 成功测试 }
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(1,2,3,4),     0, 1) = 0);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(5,6,7,8),     1, 1) = 1);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(9,10,11,12),  2, 1) = 2);
  AssertTrue('find should succeed', LCustomArr.Find(make_custom_compare_data(13,14,15,16), 3, 1) = 3);
end;

procedure TTestCase_Array.Test_Find10;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0, 4, @custom_equals_u8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(3, 0, 3, @custom_equals_u8, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(0, 1, 3, @custom_equals_u8, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0, 1, @custom_equals_u8, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1, 1, @custom_equals_u8, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2, 1, @custom_equals_u8, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3, 1, @custom_equals_u8, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(4, 0, 4, @custom_equals_u16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(3, 0, 3, @custom_equals_u16, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(0, 1, 3, @custom_equals_u16, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(0, 0, 1, @custom_equals_u16, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(1, 1, 1, @custom_equals_u16, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(2, 2, 1, @custom_equals_u16, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(3, 3, 1, @custom_equals_u16, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(4, 0, 4, @custom_equals_u32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(3, 0, 3, @custom_equals_u32, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(0, 1, 3, @custom_equals_u32, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(0, 0, 1, @custom_equals_u32, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1, 1, 1, @custom_equals_u32, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(2, 2, 1, @custom_equals_u32, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(3, 3, 1, @custom_equals_u32, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(4, 0, 4, @custom_equals_u64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(3, 0, 3, @custom_equals_u64, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(0, 1, 3, @custom_equals_u64, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(0, 0, 1, @custom_equals_u64, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(1, 1, 1, @custom_equals_u64, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(2, 2, 1, @custom_equals_u64, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(3, 3, 1, @custom_equals_u64, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
  
  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('E', 0, 4, @custom_equals_string, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('D', 0, 3, @custom_equals_string, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('A', 1, 3, @custom_equals_string, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('A', 0, 1, @custom_equals_string, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('B', 1, 1, @custom_equals_string, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('C', 2, 1, @custom_equals_string, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('D', 3, 1, @custom_equals_string, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17,18,19,20), 0, 4, @custom_equals_custom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 0, 3, @custom_equals_custom, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     1, 3, @custom_equals_custom, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     0, 1, @custom_equals_custom, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5,6,7,8),     1, 1, @custom_equals_custom, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9,10,11,12),  2, 1, @custom_equals_custom, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 3, 1, @custom_equals_custom, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
end;

procedure TTestCase_Array.Test_Find11;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(4, 0, 4, @DoEqualsU8, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(3, 0, 3, @DoEqualsU8, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(0, 1, 3, @DoEqualsU8, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(0, 0, 1, @DoEqualsU8, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(1, 1, 1, @DoEqualsU8, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(2, 2, 1, @DoEqualsU8, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(3, 3, 1, @DoEqualsU8, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(4, 0, 4, @DoEqualsU16, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(3, 0, 3, @DoEqualsU16, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(0, 1, 3, @DoEqualsU16, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(0, 0, 1, @DoEqualsU16, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(1, 1, 1, @DoEqualsU16, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(2, 2, 1, @DoEqualsU16, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(3, 3, 1, @DoEqualsU16, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(4, 0, 4, @DoEqualsU32, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(3, 0, 3, @DoEqualsU32, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(0, 1, 3, @DoEqualsU32, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(0, 0, 1, @DoEqualsU32, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(1, 1, 1, @DoEqualsU32, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(2, 2, 1, @DoEqualsU32, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(3, 3, 1, @DoEqualsU32, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(4, 0, 4, @DoEqualsU64, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(3, 0, 3, @DoEqualsU64, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(0, 1, 3, @DoEqualsU64, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(0, 0, 1, @DoEqualsU64, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(1, 1, 1, @DoEqualsU64, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(2, 2, 1, @DoEqualsU64, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(3, 3, 1, @DoEqualsU64, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
  
  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('E', 0, 4, @DoEqualsString, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('D', 0, 3, @DoEqualsString, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find('A', 1, 3, @DoEqualsString, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('A', 0, 1, @DoEqualsString, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('B', 1, 1, @DoEqualsString, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('C', 2, 1, @DoEqualsString, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find('D', 3, 1, @DoEqualsString, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17,18,19,20), 0, 4, @DoEqualsCustom, @LCounter) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 0, 3, @DoEqualsCustom, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     1, 3, @DoEqualsCustom, @LCounter) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     0, 1, @DoEqualsCustom, @LCounter) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5,6,7,8),     1, 1, @DoEqualsCustom, @LCounter) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9,10,11,12),  2, 1, @DoEqualsCustom, @LCounter) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 3, 1, @DoEqualsCustom, @LCounter) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Find12;
var
  LCounter:   SizeUInt;
  LUInt8Arr:  specialize IArray<UInt8>;
  LUInt16Arr: specialize IArray<UInt16>;
  LUInt32Arr: specialize IArray<UInt32>;
  LUInt64Arr: specialize IArray<UInt64>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { UInt8 }
  LUInt8Arr := specialize TArray<UInt8>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(
  4, 0, 4,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(
  3, 0, 3,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt8Arr.Find(
  0, 1, 3,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  0, 0, 1,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  1, 1, 1,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  2, 2, 1,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt8Arr.Find(
  3, 3, 1,
  function (const aLeft, aRight: UInt8): Boolean
  begin
    Result := custom_equals_u8(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt16 }
  LUInt16Arr := specialize TArray<UInt16>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(
  4, 0, 4,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(
  3, 0, 3,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt16Arr.Find(
  0, 1, 3,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  0, 0, 1,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  1, 1, 1,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  2, 2, 1,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt16Arr.Find(
  3, 3, 1,
  function (const aLeft, aRight: UInt16): Boolean
  begin
    Result := custom_equals_u16(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt32 }
  LUInt32Arr := specialize TArray<UInt32>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(
  4, 0, 4,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(
  3, 0, 3,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt32Arr.Find(
  0, 1, 3,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  0, 0, 1,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  1, 1, 1,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  2, 2, 1,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt32Arr.Find(
  3, 3, 1,
  function (const aLeft, aRight: UInt32): Boolean
  begin
    Result := custom_equals_u32(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { UInt64 }
  LUInt64Arr := specialize TArray<UInt64>.Create([0,1,2,3]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(
  4, 0, 4,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(
  3, 0, 3,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LUInt64Arr.Find(
  0, 1, 3,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  0, 0, 1,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  1, 1, 1,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  2, 2, 1,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LUInt64Arr.Find(
  3, 3, 1,
  function (const aLeft, aRight: UInt64): Boolean
  begin
    Result := custom_equals_u64(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
  
  { String }
  LStrArr := specialize TArray<string>.Create(['A','B','C','D']);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find(
  'E', 0, 4,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find(
  'D', 0, 3,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LStrArr.Find(
  'A', 1, 3,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'A', 0, 1,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'B', 1, 1,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'C', 2, 1,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LStrArr.Find(
  'D', 3, 1,
  function (const aLeft, aRight: string): Boolean
  begin
    Result := custom_equals_string(aLeft, aRight, @LCounter);
  end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);

  { Custom }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16) 
  ]);

  { 失败测试 }
  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(17,18,19,20), 0, 4,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) > -1);
  AssertEquals('counter should be 4', 4, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 0, 3,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  LCounter := 0;
  AssertFalse('find should fail', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     1, 3,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) > -1);
  AssertEquals('counter should be 3', 3, LCounter);

  { 成功测试 }
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(1,2,3,4),     0, 1,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 0);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(5,6,7,8),     1, 1,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 1);
  AssertEquals('counter should be 1', 1, LCounter);
  
  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(9,10,11,12),  2, 1,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 2);
  AssertEquals('counter should be 1', 1, LCounter);

  LCounter := 0;
  AssertTrue('find should succeed', LCustomArr.Find(
    make_custom_compare_data(13,14,15,16), 3, 1,
    function (const aLeft, aRight: custom_data_t): Boolean
    begin
      Result := custom_equals_custom(aLeft, aRight, @LCounter);
    end) = 3);
  AssertEquals('counter should be 1', 1, LCounter);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Sort1;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 1, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'date', 'elderberry']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort;
  specialize AssertArrayEquals<Single>(LFloatArr, [-0.9, 0.1, 0.9, 1.1, 1.18, 1.19, 1.2, 2.3, 3.3, 5.2, 5.3]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 0, 1, 2, 3, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'b', 'd', 'e', 'e']);

  { 边界条件测试: 空容器 }
  LIntArr := specialize TArray<Integer>.Create([]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, []);
  LStrArr := specialize TArray<string>.Create([]);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, []);

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort;
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort;
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort;
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := LIntArr.GetCount-1 downto 0 do
    LIntArr[i] := i;

  LIntArr.Sort;

  for i := 0 to LIntArr.GetCount - 1 do
    AssertEquals(i, LIntArr[i]);
end;

procedure TTestCase_Array.Test_Sort2;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 1, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'date', 'elderberry']);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 0, 1, 2, 3, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'b', 'd', 'e', 'e']);

  { 边界条件测试: 空容器 }
  LIntArr := specialize TArray<Integer>.Create([]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, []);
  LStrArr := specialize TArray<string>.Create([]);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, []);

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(@custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(@custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(@custom_compare_custom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

end;

procedure TTestCase_Array.Test_Sort3;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 1, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'date', 'elderberry']);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 0, 1, 2, 3, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'b', 'd', 'e', 'e']);

  { 边界条件测试: 空容器 }
  LIntArr := specialize TArray<Integer>.Create([]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, []);
  LStrArr := specialize TArray<string>.Create([]);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, []);

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(@DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(@DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(@DoCompareCustom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Sort4;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LCustomArr: specialize IArray<custom_data_t>;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 1, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'date', 'elderberry']);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [-1, 0, 0, 1, 2, 3, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'b', 'd', 'e', 'e']);

  { 边界条件测试: 空容器 }
  LIntArr := specialize TArray<Integer>.Create([]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);
  LStrArr := specialize TArray<string>.Create([]);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);
  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(
    function(const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(
    function(const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(
    function(const aLeft, aRight: custom_data_t): SizeInt
    begin
      Result := custom_compare_custom(aLeft, aRight, nil);
    end
  );
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(1,2,3,4),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Sort5;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, -1, 0, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(2);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'cherry', 'banana', 'date', 'elderberry']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(2);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, 3.3, -0.9, 0.1, 0.9, 1.18, 1.19, 1.2, 2.3, 5.2, 5.3]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(2);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 3, -1, 0, 0, 2, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(2);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'e', 'a', 'b', 'd', 'e']);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空容器 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(2);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(2);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(2);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(2);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(2);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(2);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, 2, -5, -1, 0]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(2);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'c', 'a', 'b']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(2);
  AssertEquals(Integer(LIntArr.GetCount - 1), LIntArr[0]);
  AssertEquals(Integer(LIntArr.GetCount - 2), LIntArr[1]);

  for i := 2 to LIntArr.Count - 1 do
    AssertEquals(i - 2, LIntArr[i]);
end;

procedure TTestCase_Array.Test_Sort6;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, -1, 0, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(2, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'cherry', 'banana', 'date', 'elderberry']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(2, @custom_compare_single, nil);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, 3.3, -0.9, 0.1, 0.9, 1.18, 1.19, 1.2, 2.3, 5.2, 5.3]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(2, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 3, -1, 0, 0, 2, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(2, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'e', 'a', 'b', 'd', 'e']);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空容器 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, @custom_compare_i32, nil);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(2, @custom_compare_string, nil);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(2, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(2, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(2, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(2, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(2, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, 2, -5, -1, 0]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(2, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'c', 'a', 'b']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, @custom_compare_custom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(2, @custom_compare_i32, nil);
  AssertEquals(Integer(LIntArr.Count - 1), LIntArr[0]);
  AssertEquals(Integer(LIntArr.Count - 2), LIntArr[1]);

  for i := 2 to LIntArr.Count - 1 do
    AssertEquals(Integer(i - 2), LIntArr[i]);
end;

procedure TTestCase_Array.Test_Sort7;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, -1, 0, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(2, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'cherry', 'banana', 'date', 'elderberry']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(2, @DoCompareSingle, nil);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, 3.3, -0.9, 0.1, 0.9, 1.18, 1.19, 1.2, 2.3, 5.2, 5.3]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(2, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 3, -1, 0, 0, 2, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(2, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'e', 'a', 'b', 'd', 'e']);

  { 异常测试: 空容器 索引越界 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, @DoCompareI32, nil);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(2, @DoCompareString, nil);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(2, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(2, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(2, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(2, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(2, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, 2, -5, -1, 0]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(2, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'c', 'a', 'b']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, @DoCompareCustom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(2, @DoCompareI32, nil);
  AssertEquals(Integer(LIntArr.Count - 1), LIntArr[0]);
  AssertEquals(Integer(LIntArr.Count - 2), LIntArr[1]);

  for i := 2 to LIntArr.Count - 1 do
    AssertEquals(Integer(i - 2), LIntArr[i]);
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Sort8;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, -1, 0, 2, 3, 5]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(2,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'cherry', 'banana', 'date', 'elderberry']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(2,
    function (const aLeft, aRight: Single): SizeInt
    begin
      Result := compare_single(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, 3.3, -0.9, 0.1, 0.9, 1.18, 1.19, 1.2, 2.3, 5.2, 5.3]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(2,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 3, -1, 0, 0, 2, 5, 5]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(2,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'e', 'a', 'b', 'd', 'e']);

  { 异常测试: 空容器 索引越界 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1,
        function (const aLeft, aRight: Integer): SizeInt
        begin
          Result := compare_i32(aLeft, aRight);
        end);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(2,
        function (const aLeft, aRight: string): SizeInt
        begin
          Result := compare_string(aLeft, aRight);
        end);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(2,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(2,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(2,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(2,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(2,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, 2, -5, -1, 0]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(2,
    function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'c', 'a', 'b']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1,
    function (const aLeft, aRight: custom_data_t): SizeInt
    begin
      Result := custom_compare_custom(aLeft, aRight, nil);
    end);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(2,
    function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  AssertEquals(Integer(LIntArr.Count - 1), LIntArr[0]);
  AssertEquals(Integer(LIntArr.Count - 2), LIntArr[1]);

  for i := 2 to LIntArr.Count - 1 do
    AssertEquals(Integer(i - 2), LIntArr[i]);
end;
{$ENDIF}

procedure TTestCase_Array.Test_Sort9;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, 3);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(1, 3);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'elderberry', 'date']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(1,3);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, -0.9, 0.9, 3.3, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(1, 3);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1, 5, 0]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(1, 3);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'e', 'e', 'd', 'a']);

  { 异常测试: 空容器 索引越界 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, 3);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(1, 3);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, 1);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, 1);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(1, 3);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(1, 3);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(1, 3);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(1, 3);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(1, 3);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, -1, 0, 2, -5]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(1, 3);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'a', 'b', 'c']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, 3);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(1, 3); // 1023,1022,1021,1020 -> 1023,1020,1021,1022
  AssertEquals(LIntArr[0], Integer(LIntArr.GetCount - 1));
  AssertEquals(LIntArr[1], Integer(LIntArr.GetCount - 1 - 3));
  AssertEquals(LIntArr[2], Integer(LIntArr.GetCount - 1 - 2 ));
  AssertEquals(LIntArr[3], Integer(LIntArr.GetCount - 1 - 1));
end;

procedure TTestCase_Array.Test_Sort10;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, 3, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(1, 3, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'elderberry', 'date']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(1,3, @custom_compare_single, nil);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, -0.9, 0.9, 3.3, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(1, 3, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1, 5, 0]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(1, 3, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'e', 'e', 'd', 'a']);

  { 异常测试: 空容器 索引越界 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, 3, @custom_compare_i32, nil);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(1, 3, @custom_compare_string, nil);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, 1, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, 1, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(1, 3, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(1, 3, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(1, 3, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(1, 3, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(1, 3, @custom_compare_i32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, -1, 0, 2, -5]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(1, 3, @custom_compare_string, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'a', 'b', 'c']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, 3, @custom_compare_custom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(1, 3, @custom_compare_i32, nil); // 1023,1022,1021,1020 -> 1023,1020,1021,1022
  AssertEquals(LIntArr[0], Integer(LIntArr.GetCount - 1));
  AssertEquals(LIntArr[1], Integer(LIntArr.GetCount - 1 - 3));
  AssertEquals(LIntArr[2], Integer(LIntArr.GetCount - 1 - 2 ));
  AssertEquals(LIntArr[3], Integer(LIntArr.GetCount - 1 - 1));
end;

procedure TTestCase_Array.Test_Sort11;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, 3, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1]);

  { 字符串排序 }
  LStrArr := specialize TArray<string>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(1, 3, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['apple', 'banana', 'cherry', 'elderberry', 'date']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(1,3, @DoCompareSingle, nil);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, -0.9, 0.9, 3.3, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(1, 3, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1, 5, 0]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(1, 3, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'e', 'e', 'd', 'a']);

  { 异常测试: 空容器 索引越界 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, 3, @DoCompareI32, nil);
    end
  );
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(1, 3, @DoCompareString, nil);
    end
  );
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, 1, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, 1, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(1, 3, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(1, 3, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(1, 3, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(1, 3, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(1, 3, @DoCompareI32, nil);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, -1, 0, 2, -5]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(1, 3, @DoCompareString, nil);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'a', 'b', 'c']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, 3, @DoCompareCustom, nil);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);
  
  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(1, 3, @DoCompareI32, nil); // 1023,1022,1021,1020 -> 1023,1020,1021,1022
  AssertEquals(LIntArr[0], Integer(LIntArr.GetCount - 1));
  AssertEquals(LIntArr[1], Integer(LIntArr.GetCount - 1 - 3));
  AssertEquals(LIntArr[2], Integer(LIntArr.GetCount - 1 - 2 ));
  AssertEquals(LIntArr[3], Integer(LIntArr.GetCount - 1 - 1));
end;

{$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
procedure TTestCase_Array.Test_Sort12;
var
  LIntArr:    specialize IArray<Integer>;
  LStrArr:    specialize IArray<string>;
  LFloatArr:  specialize IArray<Single>;
  LCustomArr: specialize IArray<custom_data_t>;
  i:          SizeInt;
begin
  { 基本排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1]);
  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1]);

  { 字符串排序 }
  LStrArr := specialize TArray<String>.Create(['apple', 'cherry', 'banana', 'elderberry', 'date']);
  LStrArr.Sort(1, 3,
  function (const aLeft, aRight: String): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<String>(LStrArr, ['apple', 'banana', 'cherry', 'elderberry', 'date']);

  { 浮点排序 }
  LFloatArr := specialize TArray<Single>.Create([1.1, 3.3, -0.9, 0.9, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);
  LFloatArr.Sort(1,3,
  function (const aLeft, aRight: Single): SizeInt
    begin
      Result := compare_single(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Single>(LFloatArr, [1.1, -0.9, 0.9, 3.3, 0.1, 2.3, 5.3, 5.2, 1.2, 1.19, 1.18]);

  { 重复元素排序 }
  LIntArr := specialize TArray<Integer>.Create([1, 3, 0, 2, 5, -1, 5, 0]);
  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 0, 2, 3, 5, -1, 5, 0]);

  LStrArr := specialize TArray<string>.Create(['a', 'e', 'b', 'e', 'd', 'a']);
  LStrArr.Sort(1, 3,
  function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'e', 'e', 'd', 'a']);

  { 异常测试: 空容器 }
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntArr := specialize TArray<Integer>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LIntArr.Sort(1, 3,
      function (const aLeft, aRight: Integer): SizeInt
        begin
          Result := compare_i32(aLeft, aRight);
        end);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, []);

  LStrArr := specialize TArray<string>.Create([]);
  AssertException(
    'exception should be raised: index out of range',
    ERangeOutOfIndex,
    procedure
    begin
      LStrArr.Sort(1, 3,
      function (const aLeft, aRight: string): SizeInt
        begin
          Result := compare_string(aLeft, aRight);
        end);
    end);
  specialize AssertArrayEquals<string>(LStrArr, []);
  {$ENDIF}

  { 边界条件测试: 单个元素 }
  LIntArr := specialize TArray<Integer>.Create([1]);
  LIntArr.Sort(0, 1,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1]);
  LStrArr := specialize TArray<string>.Create(['single']);
  LStrArr.Sort(0, 1,
  function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['single']);

  { 边界条件测试: 所有元素相同 }
  LIntArr := specialize TArray<Integer>.Create([1, 1, 1, 1, 1]);
  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [1, 1, 1, 1, 1]);

  LStrArr := specialize TArray<string>.Create(['a', 'a', 'a', 'a', 'a']);
  LStrArr.Sort(1, 3,
  function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'a', 'a', 'a', 'a']);

  { 边界条件测试: 已排序数组 }
  LIntArr := specialize TArray<Integer>.Create([-5, -1, 0, 2, 10]);
  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [-5, -1, 0, 2, 10]);

  LStrArr := specialize TArray<string>.Create(['a', 'b', 'c', 'd']);
  LStrArr.Sort(1, 3,
  function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['a', 'b', 'c', 'd']);

  { 边界条件测试: 逆序数组 }
  LIntArr := specialize TArray<Integer>.Create([10, 2, 0, -1, -5]);
  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end);
  specialize AssertArrayEquals<Integer>(LIntArr, [10, -1, 0, 2, -5]);

  LStrArr := specialize TArray<string>.Create(['d', 'c', 'b', 'a']);
  LStrArr.Sort(1, 3,
  function (const aLeft, aRight: string): SizeInt
    begin
      Result := compare_string(aLeft, aRight);
    end);
  specialize AssertArrayEquals<string>(LStrArr, ['d', 'a', 'b', 'c']);

  { 自定义结构 }
  LCustomArr := specialize TArray<custom_data_t>.Create([
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(13,14,15,16),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(5,6,7,8)
  ]);

  LCustomArr.Sort(1, 3,
  function (const aLeft, aRight: custom_data_t): SizeInt
    begin
      Result := custom_compare_custom(aLeft, aRight, nil);
    end);
  specialize AssertArrayEquals<custom_data_t>(LCustomArr, [
    make_custom_compare_data(10,20,30,40),
    make_custom_compare_data(5,6,7,8),
    make_custom_compare_data(9,10,11,12),
    make_custom_compare_data(13,14,15,16)
  ]);

  { 大数组排序(倒序) }
  LIntArr := specialize TArray<Integer>.Create(1024);

  for i := 0 to LIntArr.Count - 1 do
    LIntArr[i] := LIntArr.Count - 1 - i;

  LIntArr.Sort(1, 3,
  function (const aLeft, aRight: Integer): SizeInt
    begin
      Result := compare_i32(aLeft, aRight);
    end); // 1023,1022,1021,1020 -> 1023,1020,1021,1022
  AssertEquals(LIntArr[0], Integer(LIntArr.GetCount - 1));
  AssertEquals(LIntArr[1], Integer(LIntArr.GetCount - 1 - 3));
  AssertEquals(LIntArr[2], Integer(LIntArr.GetCount - 1 - 2 ));
  AssertEquals(LIntArr[3], Integer(LIntArr.GetCount - 1 - 1));
end;
{$ENDIF}

function custom_equals_bool(const aLeft, aRight: Boolean; aData: Pointer): Boolean;
begin
  Result := equals_bool(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_char(const aLeft, aRight: Char; aData: Pointer): Boolean;
begin
  Result := equals_char(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_wchar(const aLeft, aRight: WideChar; aData: Pointer): Boolean;
begin
  Result := equals_wchar(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_i8(const aLeft, aRight: Int8; aData: Pointer): Boolean;
begin
  Result := equals_i8(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_i16(const aLeft, aRight: Int16; aData: Pointer): Boolean;
begin
  Result := equals_i16(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_i32(const aLeft, aRight: Int32; aData: Pointer): Boolean;
begin
  Result := equals_i32(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_i64(const aLeft, aRight: Int64; aData: Pointer): Boolean;
begin
  Result := equals_i64(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_u8(const aLeft, aRight: UInt8; aData: Pointer): Boolean;
begin
  Result := equals_u8(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_u16(const aLeft, aRight: UInt16; aData: Pointer): Boolean;
begin
  Result := equals_u16(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_u32(const aLeft, aRight: UInt32; aData: Pointer): Boolean;
begin
  Result := equals_u32(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_u64(const aLeft, aRight: UInt64; aData: Pointer): Boolean;
begin
  Result := equals_u64(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_single(const aLeft, aRight: Single; aData: Pointer): Boolean;
begin
  Result := equals_single(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_double(const aLeft, aRight: Double; aData: Pointer): Boolean;
begin
  Result := equals_double(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_extended(const aLeft, aRight: Extended; aData: Pointer): Boolean;
begin
  Result := equals_extended(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_currency(const aLeft, aRight: Currency; aData: Pointer): Boolean;
begin
  Result := equals_currency(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_comp(const aLeft, aRight: Comp; aData: Pointer): Boolean;
begin
  Result := equals_comp(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_shortstring(const aLeft, aRight: ShortString; aData: Pointer): Boolean;
begin
  Result := equals_shortstring(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_ansistring(const aLeft, aRight: AnsiString; aData: Pointer): Boolean;
begin
  Result := equals_ansistring(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_widestring(const aLeft, aRight: WideString; aData: Pointer): Boolean;
begin
  Result := equals_widestring(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_unicodestring(const aLeft, aRight: UnicodeString; aData: Pointer): Boolean;
begin
  Result := equals_unicodestring(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_pointer(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
begin
  Result := equals_pointer(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_variant(const aLeft, aRight: Variant; aData: Pointer): Boolean;
begin
  Result := equals_variant(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_string(const aLeft, aRight: string; aData: Pointer): Boolean;
begin
  Result := equals_string(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_method(const aLeft, aRight: TMethod; aData: Pointer): Boolean;
begin
  Result := equals_method(aLeft, aRight);

  if aData <> nil then
    inc(PSizeUInt(aData)^);
end;

function custom_equals_bin(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
var
  LData: ptest_equals_data_t;
begin
  LData := ptest_equals_data_t(aData);
  Result := equals_bin(aLeft, aRight, LData^.size);

  if LData^.data <> nil then
    inc(PSIZEUINT(LData^.data)^);
end;

function custom_equals_dynarray(const aLeft, aRight: Pointer; aData: Pointer): Boolean;
var
  LData: ptest_equals_data_t;
begin
  LData := ptest_equals_data_t(aData);
  Result := equals_dynarray(aLeft, aRight, LData^.size);

  if LData^.data <> nil then
    inc(PSIZEUINT(LData^.data)^);
end;

function custom_equals_custom(const aLeft, aRight: custom_data_t; aData: Pointer): Boolean;
begin
  Result := (aLeft.a = aRight.a) and
            (aLeft.b = aRight.b) and
            (aLeft.c = aRight.c) and
            (aLeft.d = aRight.d);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_bool(const aLeft, aRight: Boolean; aData: Pointer): SizeInt;
begin
  Result := compare_bool(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_char(const aLeft, aRight: Char; aData: Pointer): SizeInt;
begin
  Result := compare_char(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_wchar(const aLeft, aRight: WideChar; aData: Pointer): SizeInt;
begin
  Result := compare_wchar(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_i8(const aLeft, aRight: Int8; aData: Pointer): SizeInt;
begin
  Result := compare_i8(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_i16(const aLeft, aRight: Int16; aData: Pointer): SizeInt;
begin
  Result := compare_i16(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_i32(const aLeft, aRight: Int32; aData: Pointer): SizeInt;
begin
  Result := compare_i32(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_i64(const aLeft, aRight: Int64; aData: Pointer): SizeInt;
begin
  Result := compare_i64(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_u8(const aLeft, aRight: UInt8; aData: Pointer): SizeInt;
begin
  Result := compare_u8(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_u16(const aLeft, aRight: UInt16; aData: Pointer): SizeInt;
begin
  Result := compare_u16(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_u32(const aLeft, aRight: UInt32; aData: Pointer): SizeInt;
begin
  Result := compare_u32(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_u64(const aLeft, aRight: UInt64; aData: Pointer): SizeInt;
begin
  Result := compare_u64(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_single(const aLeft, aRight: Single; aData: Pointer): SizeInt;
begin
  Result := compare_single(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_double(const aLeft, aRight: Double; aData: Pointer): SizeInt;
begin
  Result := compare_double(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_extended(const aLeft, aRight: Extended; aData: Pointer): SizeInt;
begin
  Result := compare_extended(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_currency(const aLeft, aRight: Currency; aData: Pointer): SizeInt;
begin
  Result := compare_currency(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_comp(const aLeft, aRight: Comp; aData: Pointer): SizeInt;
begin
  Result := compare_comp(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_shortstring(const aLeft, aRight: ShortString; aData: Pointer): SizeInt;
begin
  Result := compare_shortstring(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_ansistring(const aLeft, aRight: AnsiString; aData: Pointer): SizeInt;
begin
  Result := compare_ansistring(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_widestring(const aLeft, aRight: WideString; aData: Pointer): SizeInt;
begin
  Result := compare_widestring(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_unicodestring(const aLeft, aRight: UnicodeString; aData: Pointer): SizeInt;
begin
  Result := compare_unicodestring(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_pointer(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
begin
  Result := compare_pointer(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_variant(const aLeft, aRight: Variant; aData: Pointer): SizeInt;
begin
  Result := compare_variant(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_string(const aLeft, aRight: string; aData: Pointer): SizeInt;
begin
  Result := compare_string(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_method(const aLeft, aRight: TMethod; aData: Pointer): SizeInt;
begin
  Result := compare_method(aLeft, aRight);

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

function custom_compare_bin(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
var
  LData: ptest_compare_data_t;
begin
  LData := ptest_compare_data_t(aData);
  Result := compare_bin(aLeft, aRight, LData^.size);

  if LData^.data <> nil then
    inc(PSIZEUINT(LData^.data)^);
end;

function custom_compare_dynarray(const aLeft, aRight: Pointer; aData: Pointer): SizeInt;
var
  LData: ptest_compare_data_t;
begin
  LData := ptest_compare_data_t(aData);
  Result := compare_dynarray(aLeft, aRight, LData^.size);

  if LData^.data <> nil then
    inc(PSIZEUINT(LData^.data)^);
end;

function custom_compare_custom(const aLeft, aRight: custom_data_t; aData: Pointer): SizeInt;
begin
  Result := compare_bin(@aLeft, @aRight, SizeOf(custom_data_t));

  if aData <> nil then
    inc(PSIZEUINT(aData)^);
end;

generic procedure AssertArrayEquals<T>(const aActual: specialize IArray<T>; const aExpected: array of T);
var
  LLen:         SizeInt;
  i:            SizeInt;
  LElementSize: SizeUInt;
begin
  LLen := Length(aExpected);
  TTestCase.AssertEquals('Count mismatch', SizeUInt(LLen), aActual.GetCount);
  LElementSize := SizeOf(T);

  for i := 0 to LLen - 1 do
    TTestCase.AssertTrue('Element ' + IntToStr(i) + ' mismatch', equals_bin(@aExpected[i], aActual.GetPtr(i), LElementSize));
end;

initialization

  RegisterTest(TTestCase_Array);
end.

