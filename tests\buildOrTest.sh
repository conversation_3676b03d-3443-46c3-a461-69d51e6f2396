#!/bin/bash

# --- Configuration --- 
# Please set the path to your lazbuild executable
# 请设置您的 lazbuild 可执行文件路径
LAZBUILD="/path/to/your/lazarus/lazbuild"

# --- Script --- 
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &> /dev/null && pwd)
PROJECT="${SCRIPT_DIR}/fafafa.core.tests.lpi"
TEST_EXECUTABLE="${SCRIPT_DIR}/../bin/tests"
LPR_FILE="${SCRIPT_DIR}/fafafa.core.tests.lpr"

# Modify .lpr file to ensure recompilation
# echo "" >> "${LPR_FILE}"

# Build the project
echo "Building project: ${PROJECT}..."
"${LAZBUILD}" "${PROJECT}" --add-unit-path="${SCRIPT_DIR}/../src"

EXIT_CODE=$?
if [ ${EXIT_CODE} -ne 0 ]; then
    echo ""
    echo "Build failed with error code ${EXIT_CODE}."
    exit ${EXIT_CODE}
fi

echo ""
echo "Build successful."
echo ""

# Run tests if the 'test' parameter is provided
if [ "$1" = "test" ]; then
    echo "Running tests..."
    echo ""
    "${TEST_EXECUTABLE}"
else
    echo "To run tests, call this script with the 'test' parameter."
    echo "e.g., ./buildOrTest.sh test"
fi
