{**
 * fafafa.core.fs.pas
 *
 * @desc 基于 libuv 风格的文件系统操作模块 - 严格移植版本
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024
 *
 * 严格遵循 libuv 1.x 的文件系统 API 设计，提供高性能、跨平台的文件系统操作
 *}

unit fafafa.core.fs;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes, fafafa.core.base;

type
  { 文件描述符 - 对应 libuv 的 uv_file }
  TFileHandle = type Integer;

  { 文件打开标志 - 对应 libuv 的 flags 参数 }
  TFileOpenFlags = (
    fofReadOnly = 0,      // O_RDONLY
    fofWriteOnly = 1,     // O_WRONLY  
    fofReadWrite = 2,     // O_RDWR
    fofAppend = 8,        // O_APPEND
    fofCreate = 64,       // O_CREAT
    fofExclusive = 128,   // O_EXCL
    fofTruncate = 512,    // O_TRUNC
    fofDirectory = 65536, // O_DIRECTORY
    fofNoFollow = 131072, // O_NOFOLLOW
    fofSymlink = 262144   // O_SYMLINK
  );

  { 文件模式 - 对应 libuv 的 mode 参数 }
  TFileMode = Integer;

  { 文件状态信息 - 对应 libuv 的 uv_stat_t }
  TFileStat = record
    Device: UInt64;        // st_dev
    Inode: UInt64;         // st_ino
    Mode: UInt32;          // st_mode
    NLink: UInt32;         // st_nlink
    UID: UInt32;           // st_uid
    GID: UInt32;           // st_gid
    RDev: UInt64;          // st_rdev
    Size: Int64;           // st_size
    BlockSize: Int64;      // st_blksize
    Blocks: Int64;         // st_blocks
    AccessTime: TDateTime; // st_atime
    ModificationTime: TDateTime; // st_mtime
    CreationTime: TDateTime;     // st_ctime
  end;

  { 文件系统错误码 - 对应 libuv 的错误码 }
  TFileSystemError = (
    fsEUNKNOWN = -99,   // 未知错误
    fsEXDEV = -22,      // UV_EXDEV
    fsETXTBSY = -21,    // UV_ETXTBSY
    fsESPIPE = -20,     // UV_ESPIPE
    fsEROFS = -19,      // UV_EROFS
    fsENOTEMPTY = -18,  // UV_ENOTEMPTY
    fsENODEV = -17,     // UV_ENODEV
    fsENOBUFS = -16,    // UV_ENOBUFS
    fsENFILE = -15,     // UV_ENFILE
    fsEMFILE = -14,     // UV_EMFILE
    fsELOOP = -13,      // UV_ELOOP
    fsENAMETOOLONG = -12, // UV_ENAMETOOLONG
    fsEINTR = -11,      // UV_EINTR
    fsEBADF = -10,      // UV_EBADF
    fsEIO = -9,         // UV_EIO
    fsENOMEM = -8,      // UV_ENOMEM
    fsENOSPC = -7,      // UV_ENOSPC
    fsEISDIR = -6,      // UV_EISDIR
    fsENOTDIR = -5,     // UV_ENOTDIR
    fsEEXIST = -4,      // UV_EEXIST
    fsEACCES = -3,      // UV_EACCES
    fsENOENT = -2,      // UV_ENOENT
    fsEINVAL = -1,      // UV_EINVAL
    fsOK = 0
  );

  { 文件系统操作类型 - 对应 libuv 的 uv_fs_type }
  TFileSystemOperation = (
    fsOpUnknown = -1,
    fsOpOpen,
    fsOpClose,
    fsOpRead,
    fsOpWrite,
    fsOpUnlink,
    fsOpMkdir,
    fsOpRmdir,
    fsOpRename,
    fsOpCopyfile,
    fsOpStat,
    fsOpFstat,
    fsOpLstat,
    fsOpAccess,
    fsOpChmod,
    fsOpFchmod,
    fsOpUtime,
    fsOpFutime,
    fsOpSymlink,
    fsOpReadlink,
    fsOpReaddir,
    fsOpFsync,
    fsOpFdatasync,
    fsOpFtruncate,
    fsOpSendfile,
    fsOpRealpath,
    fsOpChown,
    fsOpFchown,
    fsOpLchown,
    fsOpStatfs,
    fsOpScandir,
    fsOpMkdtemp,
    fsOpMkstemp
  );

  { 文件系统请求 - 对应 libuv 的 uv_fs_t }
  TFileSystemRequest = record
    Operation: TFileSystemOperation;
    Path: string;
    NewPath: string;
    Handle: TFileHandle;
    Buffer: Pointer;
    BufferSize: SizeUInt;
    Offset: Int64;
    Mode: Integer;
    Flags: Integer;
    Result: Integer; // 对应 libuv 的 result 字段
    ErrorCode: Integer; // 对应 libuv 的 errorno
  end;

  { 文件系统操作回调 - 对应 libuv 的 uv_fs_cb }
  TFileSystemCallback = procedure(const aRequest: TFileSystemRequest);

  { 平台抽象层 }
  TPlatformFileSystem = class abstract
  public
    // 基础文件操作
    class function OpenFile(const aPath: string; aFlags: Integer; aMode: Integer): Integer; virtual; abstract;
    class function CloseFile(aHandle: TFileHandle): Integer; virtual; abstract;
    class function ReadFile(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; virtual; abstract;
    class function WriteFile(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer; virtual; abstract;
    
    // 文件系统操作
    class function DeleteFile(const aPath: string): Integer; virtual; abstract;
    class function CreateDirectory(const aPath: string; aMode: Integer): Integer; virtual; abstract;
    class function RemoveDirectory(const aPath: string): Integer; virtual; abstract;
    class function RenameFile(const aOldPath, aNewPath: string): Integer; virtual; abstract;
    class function CopyFile(const aSrc, aDest: string; aFlags: Integer): Integer; virtual; abstract;
    
    // 文件信息操作
    class function GetFileStat(const aPath: string; out aStat: TFileStat): Integer; virtual; abstract;
    class function GetFileStatByHandle(aHandle: TFileHandle; out aStat: TFileStat): Integer; virtual; abstract;
    class function GetLinkStat(const aPath: string; out aStat: TFileStat): Integer; virtual; abstract;
    class function CheckAccess(const aPath: string; aMode: Integer): Integer; virtual; abstract;
    
    // 权限和时间操作
    class function ChangeMode(const aPath: string; aMode: Integer): Integer; virtual; abstract;
    class function ChangeModeByHandle(aHandle: TFileHandle; aMode: Integer): Integer; virtual; abstract;
    class function SetFileTime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer; virtual; abstract;
    class function SetFileTimeByHandle(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer; virtual; abstract;
    
    // 符号链接操作
    class function CreateSymlink(const aTarget, aPath: string; aFlags: Integer): Integer; virtual; abstract;
    class function ReadSymlink(const aPath: string; out aTarget: string): Integer; virtual; abstract;
    
    // 目录操作
    class function ReadDirectory(const aPath: string; out aEntries: TStringArray): Integer; virtual; abstract;
    
    // 新增的 libuv 功能
    class function FileSync(aHandle: TFileHandle): Integer; virtual; abstract;
    class function FileDataSync(aHandle: TFileHandle): Integer; virtual; abstract;
    class function FileTruncate(aHandle: TFileHandle; aOffset: Int64): Integer; virtual; abstract;
    class function SendFile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer; virtual; abstract;
    class function RealPath(const aPath: string; out aRealPath: string): Integer; virtual; abstract;
    class function ChangeOwner(const aPath: string; aUID, aGID: Integer): Integer; virtual; abstract;
    class function ChangeOwnerByHandle(aHandle: TFileHandle; aUID, aGID: Integer): Integer; virtual; abstract;
    class function ChangeOwnerLink(const aPath: string; aUID, aGID: Integer): Integer; virtual; abstract;
    class function GetFileSystemStat(const aPath: string; out aStat: TFileStat): Integer; virtual; abstract;
    class function ScanDirectory(const aPath: string; out aEntries: TStringArray): Integer; virtual; abstract;
    class function MakeTempDirectory(const aTemplate: string; out aPath: string): Integer; virtual; abstract;
    class function MakeTempFile(const aTemplate: string; out aPath: string): Integer; virtual; abstract;
  end;

  { Windows 平台实现 - 在 fafafa.core.fs.windows.pas 中 }
  TWindowsFileSystem = class(TPlatformFileSystem);

  { Unix 平台实现 - 在 fafafa.core.fs.unix.pas 中 }
  TUnixFileSystem = class(TPlatformFileSystem);

{ 公共 API 函数 - 严格遵循 libuv 接口 }

{ 基础文件操作 }
function fs_open(const aPath: string; aFlags: Integer; aMode: Integer): Integer;
function fs_close(aHandle: TFileHandle): Integer;
function fs_read(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64 = -1): Integer;
function fs_write(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64 = -1): Integer;

{ 文件系统操作 }
function fs_unlink(const aPath: string): Integer;
function fs_mkdir(const aPath: string; aMode: Integer): Integer;
function fs_rmdir(const aPath: string): Integer;
function fs_rename(const aOldPath, aNewPath: string): Integer;
function fs_copyfile(const aSrc, aDest: string; aFlags: Integer = 0): Integer;

{ 文件信息操作 }
function fs_stat(const aPath: string; out aStat: TFileStat): Integer;
function fs_fstat(aHandle: TFileHandle; out aStat: TFileStat): Integer;
function fs_lstat(const aPath: string; out aStat: TFileStat): Integer;
function fs_access(const aPath: string; aMode: Integer): Integer;

{ 符号链接操作 }
function fs_symlink(const aTarget, aPath: string; aFlags: Integer = 0): Integer;
function fs_readlink(const aPath: string; out aTarget: string): Integer;

{ 权限和时间操作 }
function fs_chmod(const aPath: string; aMode: Integer): Integer;
function fs_fchmod(aHandle: TFileHandle; aMode: Integer): Integer;
function fs_utime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer;
function fs_futime(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer;

{ 目录操作 }
function fs_readdir(const aPath: string; out aEntries: TStringArray): Integer;

{ 新增的 libuv 功能 }
function fs_fsync(aHandle: TFileHandle): Integer;
function fs_fdatasync(aHandle: TFileHandle): Integer;
function fs_ftruncate(aHandle: TFileHandle; aOffset: Int64): Integer;
function fs_sendfile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer;
function fs_realpath(const aPath: string; out aRealPath: string): Integer;
function fs_chown(const aPath: string; aUID, aGID: Integer): Integer;
function fs_fchown(aHandle: TFileHandle; aUID, aGID: Integer): Integer;
function fs_lchown(const aPath: string; aUID, aGID: Integer): Integer;
function fs_statfs(const aPath: string; out aStat: TFileStat): Integer;
function fs_scandir(const aPath: string; out aEntries: TStringArray): Integer;
function fs_mkdtemp(const aTemplate: string; out aPath: string): Integer;
function fs_mkstemp(const aTemplate: string; out aPath: string): Integer;

{ 便利函数 - 基于底层C风格API构建的高级函数 }
function fs_read_file(const aPath: string): TBytes;
function fs_write_file(const aPath: string; const aData: TBytes): Boolean;
function fs_read_text(const aPath: string; aEncoding: TEncoding = nil): string;
function fs_write_text(const aPath: string; const aText: string; aEncoding: TEncoding = nil): Boolean;
function fs_exists(const aPath: string): Boolean;
function fs_is_file(const aPath: string): Boolean;
function fs_is_directory(const aPath: string): Boolean;
function fs_file_size(const aPath: string): Int64;

{ 工具函数 }
function fs_get_error_message(aErrorCode: Integer): string;
function fs_translate_system_error(aSystemError: Integer): Integer;

implementation

{ 公共 API 函数实现 - 严格遵循 libuv 返回值 }

function fs_open(const aPath: string; aFlags: Integer; aMode: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.OpenFile(aPath, aFlags, aMode);
    {$ELSE}
    Result := TUnixFileSystem.OpenFile(aPath, aFlags, aMode);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1; // 对应 libuv 的错误返回
  end;
end;

function fs_close(aHandle: TFileHandle): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.CloseFile(aHandle);
    {$ELSE}
    Result := TUnixFileSystem.CloseFile(aHandle);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_read(aHandle: TFileHandle; var aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ReadFile(aHandle, aBuffer, aSize, aOffset);
    {$ELSE}
    Result := TUnixFileSystem.ReadFile(aHandle, aBuffer, aSize, aOffset);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_write(aHandle: TFileHandle; const aBuffer; aSize: SizeUInt; aOffset: Int64): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.WriteFile(aHandle, aBuffer, aSize, aOffset);
    {$ELSE}
    Result := TUnixFileSystem.WriteFile(aHandle, aBuffer, aSize, aOffset);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_unlink(const aPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.DeleteFile(aPath);
    {$ELSE}
    Result := TUnixFileSystem.DeleteFile(aPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_mkdir(const aPath: string; aMode: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.CreateDirectory(aPath, aMode);
    {$ELSE}
    Result := TUnixFileSystem.CreateDirectory(aPath, aMode);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_rmdir(const aPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.RemoveDirectory(aPath);
    {$ELSE}
    Result := TUnixFileSystem.RemoveDirectory(aPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_rename(const aOldPath, aNewPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.RenameFile(aOldPath, aNewPath);
    {$ELSE}
    Result := TUnixFileSystem.RenameFile(aOldPath, aNewPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_copyfile(const aSrc, aDest: string; aFlags: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.CopyFile(aSrc, aDest, aFlags);
    {$ELSE}
    Result := TUnixFileSystem.CopyFile(aSrc, aDest, aFlags);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_stat(const aPath: string; out aStat: TFileStat): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.GetFileStat(aPath, aStat);
    {$ELSE}
    Result := TUnixFileSystem.GetFileStat(aPath, aStat);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_fstat(aHandle: TFileHandle; out aStat: TFileStat): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.GetFileStatByHandle(aHandle, aStat);
    {$ELSE}
    Result := TUnixFileSystem.GetFileStatByHandle(aHandle, aStat);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_lstat(const aPath: string; out aStat: TFileStat): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.GetLinkStat(aPath, aStat);
    {$ELSE}
    Result := TUnixFileSystem.GetLinkStat(aPath, aStat);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_access(const aPath: string; aMode: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.CheckAccess(aPath, aMode);
    {$ELSE}
    Result := TUnixFileSystem.CheckAccess(aPath, aMode);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_symlink(const aTarget, aPath: string; aFlags: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.CreateSymlink(aTarget, aPath, aFlags);
    {$ELSE}
    Result := TUnixFileSystem.CreateSymlink(aTarget, aPath, aFlags);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_readlink(const aPath: string; out aTarget: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ReadSymlink(aPath, aTarget);
    {$ELSE}
    Result := TUnixFileSystem.ReadSymlink(aPath, aTarget);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_chmod(const aPath: string; aMode: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ChangeMode(aPath, aMode);
    {$ELSE}
    Result := TUnixFileSystem.ChangeMode(aPath, aMode);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_fchmod(aHandle: TFileHandle; aMode: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ChangeModeByHandle(aHandle, aMode);
    {$ELSE}
    Result := TUnixFileSystem.ChangeModeByHandle(aHandle, aMode);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_utime(const aPath: string; aAccessTime, aModificationTime: TDateTime): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.SetFileTime(aPath, aAccessTime, aModificationTime);
    {$ELSE}
    Result := TUnixFileSystem.SetFileTime(aPath, aAccessTime, aModificationTime);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_futime(aHandle: TFileHandle; aAccessTime, aModificationTime: TDateTime): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.SetFileTimeByHandle(aHandle, aAccessTime, aModificationTime);
    {$ELSE}
    Result := TUnixFileSystem.SetFileTimeByHandle(aHandle, aAccessTime, aModificationTime);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_readdir(const aPath: string; out aEntries: TStringArray): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ReadDirectory(aPath, aEntries);
    {$ELSE}
    Result := TUnixFileSystem.ReadDirectory(aPath, aEntries);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 新增的 libuv 功能实现 }

function fs_fsync(aHandle: TFileHandle): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.FileSync(aHandle);
    {$ELSE}
    Result := TUnixFileSystem.FileSync(aHandle);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_fdatasync(aHandle: TFileHandle): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.FileDataSync(aHandle);
    {$ELSE}
    Result := TUnixFileSystem.FileDataSync(aHandle);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_ftruncate(aHandle: TFileHandle; aOffset: Int64): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.FileTruncate(aHandle, aOffset);
    {$ELSE}
    Result := TUnixFileSystem.FileTruncate(aHandle, aOffset);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_sendfile(aOutHandle, aInHandle: TFileHandle; aInOffset: Int64; aLength: SizeUInt): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.SendFile(aOutHandle, aInHandle, aInOffset, aLength);
    {$ELSE}
    Result := TUnixFileSystem.SendFile(aOutHandle, aInHandle, aInOffset, aLength);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_realpath(const aPath: string; out aRealPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.RealPath(aPath, aRealPath);
    {$ELSE}
    Result := TUnixFileSystem.RealPath(aPath, aRealPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_chown(const aPath: string; aUID, aGID: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ChangeOwner(aPath, aUID, aGID);
    {$ELSE}
    Result := TUnixFileSystem.ChangeOwner(aPath, aUID, aGID);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_fchown(aHandle: TFileHandle; aUID, aGID: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ChangeOwnerByHandle(aHandle, aUID, aGID);
    {$ELSE}
    Result := TUnixFileSystem.ChangeOwnerByHandle(aHandle, aUID, aGID);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_lchown(const aPath: string; aUID, aGID: Integer): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ChangeOwnerLink(aPath, aUID, aGID);
    {$ELSE}
    Result := TUnixFileSystem.ChangeOwnerLink(aPath, aUID, aGID);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_statfs(const aPath: string; out aStat: TFileStat): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.GetFileSystemStat(aPath, aStat);
    {$ELSE}
    Result := TUnixFileSystem.GetFileSystemStat(aPath, aStat);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_scandir(const aPath: string; out aEntries: TStringArray): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.ScanDirectory(aPath, aEntries);
    {$ELSE}
    Result := TUnixFileSystem.ScanDirectory(aPath, aEntries);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_mkdtemp(const aTemplate: string; out aPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.MakeTempDirectory(aTemplate, aPath);
    {$ELSE}
    Result := TUnixFileSystem.MakeTempDirectory(aTemplate, aPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

function fs_mkstemp(const aTemplate: string; out aPath: string): Integer;
begin
  try
    {$IFDEF WINDOWS}
    Result := TWindowsFileSystem.MakeTempFile(aTemplate, aPath);
    {$ELSE}
    Result := TUnixFileSystem.MakeTempFile(aTemplate, aPath);
    {$ENDIF}
  except
    on E: Exception do
      Result := -1;
  end;
end;

{ 工具函数 }

function fs_get_error_message(aErrorCode: Integer): string;
begin
  case aErrorCode of
    0: Result := 'Success';
    -1: Result := 'Invalid argument';
    -2: Result := 'File not found';
    -3: Result := 'Permission denied';
    -4: Result := 'File already exists';
    -5: Result := 'Not a directory';
    -6: Result := 'Is a directory';
    -7: Result := 'No space left on device';
    -8: Result := 'Out of memory';
    -9: Result := 'I/O error';
    -10: Result := 'Bad file descriptor';
    -11: Result := 'Operation interrupted';
    -12: Result := 'File name too long';
    -13: Result := 'Symbolic link loop';
    -14: Result := 'Too many open files';
    -15: Result := 'System file table full';
    -16: Result := 'No buffer space available';
    -17: Result := 'No such device';
    -18: Result := 'Directory not empty';
    -19: Result := 'Read-only file system';
    -20: Result := 'Invalid seek';
    -21: Result := 'Text file busy';
    -22: Result := 'Cross-device link';
    else
      Result := 'Unknown error';
  end;
end;

function fs_translate_system_error(aSystemError: Integer): Integer;
begin
  // 将系统错误码转换为 libuv 错误码
  case aSystemError of
    0: Result := 0;
    22: Result := -1;  // EINVAL
    2: Result := -2;   // ENOENT
    13: Result := -3;  // EACCES
    17: Result := -4;  // EEXIST
    20: Result := -5;  // ENOTDIR
    21: Result := -6;  // EISDIR
    28: Result := -7;  // ENOSPC
    12: Result := -8;  // ENOMEM
    5: Result := -9;   // EIO
    9: Result := -10;  // EBADF
    4: Result := -11;  // EINTR
    36: Result := -12; // ENAMETOOLONG
    40: Result := -13; // ELOOP
    24: Result := -14; // EMFILE
    23: Result := -15; // ENFILE
    55: Result := -16; // ENOBUFS
    19: Result := -17; // ENODEV
    39: Result := -18; // ENOTEMPTY
    30: Result := -19; // EROFS
    29: Result := -20; // ESPIPE
    26: Result := -21; // ETXTBSY
    18: Result := -22; // EXDEV
    else
      Result := -99;
  end;
end;

{ 便利函数实现 - 基于底层C风格API构建 }

function fs_read_file(const aPath: string): TBytes;
var
  Handle: TFileHandle;
  Stat: TFileStat;
  BytesRead: Integer;
begin
  SetLength(Result, 0);

  // 获取文件大小
  if fs_stat(aPath, Stat) <> 0 then
    Exit;

  if Stat.Size <= 0 then
    Exit;

  // 打开文件
  Handle := fs_open(aPath, Integer(fofReadOnly), 0);
  if Handle < 0 then
    Exit;

  try
    // 分配缓冲区
    SetLength(Result, Stat.Size);

    // 读取文件内容
    BytesRead := fs_read(Handle, Result[0], Stat.Size, 0);
    if BytesRead < 0 then
    begin
      SetLength(Result, 0);
      Exit;
    end;

    // 调整实际读取的大小
    if BytesRead < Stat.Size then
      SetLength(Result, BytesRead);
  finally
    fs_close(Handle);
  end;
end;

function fs_write_file(const aPath: string; const aData: TBytes): Boolean;
var
  Handle: TFileHandle;
  BytesWritten: Integer;
begin
  Result := False;

  if Length(aData) = 0 then
  begin
    Result := True;
    Exit;
  end;

  // 创建或截断文件
  Handle := fs_open(aPath, Integer(fofWriteOnly) or Integer(fofCreate) or Integer(fofTruncate), 644);
  if Handle < 0 then
    Exit;

  try
    BytesWritten := fs_write(Handle, aData[0], Length(aData), 0);
    Result := (BytesWritten = Length(aData));
  finally
    fs_close(Handle);
  end;
end;

function fs_read_text(const aPath: string; aEncoding: TEncoding): string;
var
  Data: TBytes;
begin
  Result := '';
  Data := fs_read_file(aPath);

  if Length(Data) = 0 then
    Exit;

  if aEncoding = nil then
    aEncoding := TEncoding.UTF8;

  Result := aEncoding.GetString(Data);
end;

function fs_write_text(const aPath: string; const aText: string; aEncoding: TEncoding): Boolean;
var
  Data: TBytes;
begin
  if aEncoding = nil then
    aEncoding := TEncoding.UTF8;

  Data := aEncoding.GetBytes(aText);
  Result := fs_write_file(aPath, Data);
end;

function fs_exists(const aPath: string): Boolean;
begin
  Result := fs_access(aPath, 0) = 0;
end;

function fs_is_file(const aPath: string): Boolean;
var
  Stat: TFileStat;
begin
  Result := False;
  if fs_stat(aPath, Stat) = 0 then
  begin
    // 检查是否为普通文件 (S_IFREG = $8000)
    Result := (Stat.Mode and $F000) = $8000;
  end;
end;

function fs_is_directory(const aPath: string): Boolean;
var
  Stat: TFileStat;
begin
  Result := False;
  if fs_stat(aPath, Stat) = 0 then
  begin
    // 检查是否为目录 (S_IFDIR = $4000)
    Result := (Stat.Mode and $F000) = $4000;
  end;
end;

function fs_file_size(const aPath: string): Int64;
var
  Stat: TFileStat;
begin
  Result := -1;
  if fs_stat(aPath, Stat) = 0 then
    Result := Stat.Size;
end;

{ 平台实现将在后续文件中添加 }

end.