unit fafafa.core.collections.vec;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.base,
  fafafa.core.math,
  fafafa.core.mem.utils,
  fafafa.core.collections.base,
  fafafa.core.collections.arr,
  fafafa.core.collections.stack,
  fafafa.core.mem.allocator;

type

  { IVec 向量接口 }
  generic IVec<T> = interface(specialize IArray<T>)
  ['{C205D988-F671-4E47-8573-9AF2C85AC749}']

    {**
     * GetCapacity
     *
     * @desc 获取向量的容量
     *
     * @return 返回向量的容量
     *}
    function GetCapacity: SizeUint;

    {**
     * SetCapacity
     *
     * @desc 设置向量的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @remark 如果失败会抛出异常
     *}
    procedure SetCapacity(aCapacity: SizeUint);

    {**
     * GetGrowStrategy
     *
     * @desc 获取容器当前的容量增长策略.
     *
     * @return 返回向量的增长策略
     *
     * @remark
     *   增长策略决定了当容器容量不足需要扩容时, 其内部存储空间应如何扩展.
     *   这是一个影响性能和内存使用效率的关键参数.
     *}
    function GetGrowStrategy: TGrowthStrategy;

    {**
     * SetGrowStrategy
     *
     * @desc 设置容器的容量增长策略.
     *
     * @params
     *   aGrowStrategy 要设置的增长策略.
     *
     * @remark
     *   通过改变增长策略,可以调整容器在自动扩容时的行为,
     *   从而在内存使用和重新分配次数 (影响性能) 之间进行权衡.
     *
     *   如果设置为 nil,容器将恢复使用默认的 `TGoldenRatioGrowStrategy` (黄金比例增长) 增长策略.
     *   默认策略在内存占用与分配性能之间提供了较好的平衡，适用于大多数场景.
     *
     *   用户可创建自定义策略并设置到容器中，但该策略对象的生命周期由用户负责管理.
     *   框架内置的常用增长策略包括:
     *     TCustomGrowthStrategy    自定义回调增长策略.通过回调函数精细控制增长行为.
     *     TDoublingGrowStrategy    指数增长策略(容量 * 2).广泛用于大多数动态容器
     *     TFixedGrowStrategy       固定线性增长(每次 += fixedSize).内存利用率高,适用于定长批量增长场景.
     *     TFactorGrowStrategy      因子增长(容量 *= factor).可调整增长幅度.
     *     TPowerOfTwoGrowStrategy  容量扩展至不小于所需容量的最小 2 的幂次.适用于哈希表、位运算容器
     *     TGoldenRatioGrowStrategy 黄金比例增长(容量 *= 1.618).空间浪费小,适合高增长频率场景.这是框架默认的增长策略.
     *     TAlignedWrapperStrategy  对齐包装策略.可包裹任意增长策略,对齐容量至指定字节边界(需为 2 的幂),提升 CPU 预取效率.
     *     TExactGrowStrategy       精确增长策略.始终将容量扩展到恰好满足需求,不浪费空间.但分配频繁,除非对分配行为有严格控制,否则不推荐使用.
     *}
    procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);

    {**
     * TryReserve
     *
     * @desc 尝试预留额外的容量 (Count + aAdditional)
     *
     * @params
     *   aAdditional 要预留的额外容量(增加的元素数量,该接口用于确保容量足够)
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    function TryReserve(aAdditional: SizeUint): Boolean;

    {**
     * Reserve
     *
     * @desc 预留额外的容量
     *
     * @params
     *   aAdditional 要预留的额外容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    procedure Reserve(aAdditional: SizeUint);

    {**
     * TryReserveExact
     *
     * @desc 尝试预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   向量预留的空间等于请求的空间,不会按照增长策略进行分配
     *}
    function TryReserveExact(aAdditional: SizeUint): Boolean;

    {**
     * ReserveExact
     *
     * @desc 预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   向量预留的容量空间等于请求的空间,不会按照增长策略进行分配
     *}
    procedure ReserveExact(aAdditional: SizeUint);

    {**
     * Shrink
     *
     * @desc 收缩向量容量到实际使用的大小,释放多余的内存空间。
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   收缩后的容量等于当前元素数量
     *}
    procedure Shrink;

    {**
     * ShrinkTo
     *
     * @desc 收缩向量容量到指定大小,释放多余的内存空间。
     *
     * @params
     *   aCapacity 要收缩到的容量大小
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   如果指定的容量小于当前元素数量则会抛出异常(因为会截断元素)
     *   如果指定收缩的容量大于当前容量,什么也不会发生
     *}
    procedure ShrinkTo(aCapacity: SizeUint);

    {**
     * Truncate
     *
     * @desc 截断向量到指定数量,丢弃截断的元素。
     *
     * @params
     *   aCount 要截断到的数量
     *
     * @remark
     *   如果指定的数量大于当前元素数量,不会进行任何操作
     *   不会释放内存空间(不影响容量),只是修改元素数量
     *   如果需要截断元素数量并缩减容量,请使用 Truncate + Shrink
     *}
    procedure Truncate(aCount: SizeUint);

    {**
     * ResizeExact
     *
     * @desc 精确重置向量大小
     *
     * @params
     *   aNewSize 要重置的元素空间和容量大小
     *
     * @remark
     *   此接口精确改变容器大小和容量,如果新大小小于当前大小,会丢弃多余元素
     *   与 Resize 不同, Resize 按照增长策略进行扩容,而 ResizeExact 会严格按照新大小设置容量
     *
     * @exceptions
     *   EAlloc 内存分配/调整失败.
     *}
    procedure ResizeExact(aNewSize: SizeUint);



    { Insert 系列接口 }

    {**
     * Insert
     *
     * @desc 在指定位置插入多个元素(从指针拷贝)
     *
     * @params
     *   aIndex        要插入的位置
     *   aSrc          要插入的指针
     *   aElementCount 要插入的元素数量
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入一个元素
     *
     * @params
     *   aIndex   要插入的位置
     *   aElement 要插入的元素
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aElement: T); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入多个元素
     *
     * @params
     *   aIndex 要插入的位置
     *   aSrc   要插入的元素数组
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aSrc: array of T); overload;

    {**
     * Insert
     *
     * @desc 在指定位置插入集合元素(拷贝)
     *
     * @params
     *   aIndex        要插入的位置
     *   aSrc          要插入的泛型容器
     *   aElementCount 要插入的元素数量
     *
     * @remark
     *   如果插入失败会抛出异常
     *   索引必须小于等于当前元素数量(<= Count)
     *   插入成功后指定索引处原来的元素会向后移动(低效)
     *}
    procedure Insert(aIndex: SizeUInt; const aSrc: TCollection; aElementCount: SizeUInt); overload;



    { Write 系列接口 }

    {**
     * Write
     *
     * @desc 从指针内存写入多个元素到指定位置
     *
     * @params
     *   aIndex        要写入的元素位置
     *   aSrc          要写入的指针
     *   aElementCount 要写入的元素数量
     *
     * @remark 
     *   超出容量会自动扩容
     *   目标索引边界是Count位置,不得超越
     *
     * @exceptions
     *   ENil              `aSrc` 为 `nil`.
     *   ERangeOutOfIndex  索引越界.
     *}
    procedure Write(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入一个动态数组的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   包含源数据的动态数组.
     *
     * @remark
     *   写入的元素数量由 Length(aSrc) 决定.
     *   如果 aIndex + Length(aSrc) 超出当前 Count, 容器将被扩容.
     *   如果 aSrc 为空, 此操作不产生任何效果.
     *
     * @exceptions
     *   EAlloc           如果内存分配失败.
     *   ERangeOutOfIndex 索引越界
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: array of T); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入另一个容器的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   提供源数据的容器.
     *
     * @remark
     *   写入的元素数量由 `aSrc.GetCount` 决定.
     *   如果 `aIndex + aSrc.GetCount` 超出当前 Count, 容器将被扩容.
     *
     * @exceptions
     *   ENil             `aSrc` 为 `nil`.
     *   ERangeOutOfIndex 索引越界
     *   ESelf            `aSrc` 是容器自身.
     *   ENotCompatible   `aSrc` 与当前容器不兼容.
     *   EAlloc           如果内存分配失败.
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection); overload;

    {**
     * Write
     *
     * @desc 在容器内指定位置写入另一个容器的全部内容, 如有必要则自动扩容.
     *
     * @params
     *   aIndex 容器内开始写入的起始索引 (0-based).
     *   aSrc   提供源数据的容器.
     *   aCount 要写入的元素数量
     *
     * @remark
     *   写入的元素数量由 `aCount` 决定.
     *   如果 aIndex + aCount 超出当前 Count, 容器将被扩容.
     *   如果 aCount 为 0, 此操作不产生任何效果.
     *
     * @exceptions
     *   ENil           如果 aCollection 为 nil.
     *   ESelf          如果 aCollection 是容器自身.
     *   ENotCompatible 如果 aCollection 与当前容器不兼容.
     *   EAlloc         如果内存分配失败.
     *}
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload;

    {**
     * WriteExact
     *
     * @desc 精确将指定内存指针处的多个元素复制到容器中的指定位置(拷贝)。
     *
     * @params
     *   aIndex        要写入的索引
     *   aSrc          要写入的内存指针
     *   aElementCount 要写入的元素数量
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aPtr 必须为有效指针
     *   aElementCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt);

    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素数组
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aArray 必须为有效数组
     *   aElementCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: array of T);
    
    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素容器
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aCollection 必须为有效容器
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection); overload;

    {**
     * WriteExact
     *
     * @desc 精确写入元素数组
     *
     * @params
     *   aIndex 要写入的索引
     *   aSrc   要写入的元素容器
     *   aCount 要写入的元素数量
     *
     * @return 返回是否写入成功
     *
     * @remark
     *   aIndex 必须为有效索引
     *   aCollection 必须为有效容器
     *   aCount 必须大于 0
     *   此接口在遇到扩容时不再遵循增长策略,而是精确的扩容到正好容纳元素的容量
     *}
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);



    { 栈操作系列接口 }

    {**
     * Push
     *
     * @desc 在末尾添加多个元素(从指针拷贝)
     *
     * @params
     *   aSrc          指针
     *   aElementCount 元素数量
     *
     * @remark 指针内存应为泛型元素数组内存
     *}
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 在末尾添加数组元素(拷贝)
     *
     * @params
     *   aElements 元素数组
     *}
    procedure Push(const aSrc: array of T); overload;

    {**
     * Push
     *
     * @desc 在末尾添加容器指定数量的元素(拷贝)
     *
     * @params
     *   aSrc 要添加的集合
     *   aCount      要添加的元素数量
     *
     *
     * @remark 如果容器为空会抛出异常
     *}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 在末尾添加一个元素
     *
     * @params
     *   aElement 元素
     *}
    procedure Push(const aElement: T); overload;


    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出多个元素拷贝到指定指针内存
     *
     * @params
     *   aPtr          指针
     *   aElementCount 元素数量
     *
     * @return 如果成功弹出返回 True,否则返回 False
     *}
    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出多个元素拷贝到指定数组
     *
     * @params
     *   aElements     元素数组
     *   aElementCount 元素数量
     *
     * @return 如果成功移除返回 True,否则返回 False
     *}
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试从末尾弹出单个元素拷贝到指定元素内存变量
     *
     * @params
     *   aDst 目标变量
     *
     * @return 如果成功移除返回 True,否则返回 False
      *}
      function TryPop(var aDst: T): Boolean; overload;

    {**
     * Pop
     *
     * @desc 从末尾移除一个元素
     *
     * @return 返回移除的元素
     *
     * @remark 如果容器为空会抛出异常
     *}
    function Pop: T; overload;

    {**
     * TryPeekCopy
     *
     * @desc 尝试拷贝末尾多个元素到指定指针内存
     *
     * @params
     *   aPtr          指针
     *   aElementCount 元素数量
     *
     * @return 如果成功读取返回 True,否则返回 False
     *}
    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 尝试拷贝末尾多个元素到指定数组内存
     *
     * @params
     *   aDst          元素数组
     *   aElementCount 元素数量
     *
     * @return 如果成功获取返回 True,否则返回 False
     *
     * @remark 数组会被修改长度为指定大小
     *}
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 尝试获取末尾一个元素(拷贝到指定元素内存变量)
     *
     * @params
     *   aElement 元素
     *
     * @return 如果成功获取返回 True,否则返回 False
     *}
    function TryPeek(var aElement: T): Boolean; overload;

    {**
     * PeekRange
     *
     * @desc 获取从末尾开始指定数量的元素的指针(容器内的指针)
     *
     * @params
     *   aElementCount 元素数量
     *
     * @return 返回指针
     *
     * @remark 如果容器为空或者元素数量大于容器数量会返回 nil
     *}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PElement; overload;

    {**
     * Peek
     *
     * @desc 获取末尾一个元素
     *
     * @return 返回末尾元素
     *
     * @remark 如果容器为空会抛出异常
     *}
    function Peek: T; overload;


    {**
     * Delete
     *
     * @desc 删除指定位置的元素(低效操作)
     *
     * @params
     *   aIndex        要删除的元素位置
     *   aElementCount 要删除的元素数量
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 DeleteSwap
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure Delete(aIndex, aElementCount: SizeUInt); overload;

    {**
     * Delete
     *
     * @desc 删除指定位置的元素(低效操作)
     *
     * @params
     *   aIndex 要删除的元素位置
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 DeleteSwap
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure Delete(aIndex: SizeUInt); overload;

    {**
     * DeleteSwap
     *
     * @desc 删除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex        要删除的元素位置
     *   aElementCount 要删除的元素数量
     *
     * @remark
     *   此函数是一种高效的删除操作,可以避免元素的移动,但会破坏元素的顺序,适合不敏感的场合
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure DeleteSwap(aIndex, aElementCount: SizeUInt); overload;

    {**
     * DeleteSwap
     *
     * @desc 删除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要删除的元素位置
     *
     * @remark
     *   此函数是一种高效的删除操作,可以避免元素的移动,但会破坏元素的顺序,适合不敏感的场合
     *   如果删除失败会抛出异常
     *   如果删除的元素数量大于容器数量会抛出异常
     *   如果删除的元素数量小于0会抛出异常
     *}
    procedure DeleteSwap(aIndex: SizeUInt); overload;


    { 关于 Delete 和 Remove 的说明 "擦掉"和"移走" }
    

    {**
     * RemoveCopy
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定指针
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aPtr          保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 RemoveCopySwap
     *   请确保aPtr指向的内存空间足够容纳aElementCount个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveCopy(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveCopy
     *
     * @desc 移除指定位置的元素并拷贝到指定指针
     *
     * @params
     *   aIndex 要移除的元素位置
     *   aPtr   保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果指定位置后有元素,则这些元素会向前移动,在大量数据下非常低效,如果不介意元素顺序,请使用 RemoveCopySwap
     *   请确保aPtr指向的内存空间足够容纳1个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveCopy(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveArray
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定数组
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aElements     保存元素的数组变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   aElements指向的数组数量会被自动调整
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;

    {**
     * Remove
     *
     * @desc 移除指定位置的元素并拷贝到指定元素变量
     *
     * @params
     *   aIndex   要移除的元素位置
     *   aElement 保存元素的元素变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function Remove(aIndex: SizeUInt; var aElement: T): Boolean; overload;

    {**
     * Remove
     *
     * @desc 移除指定位置的元素
     *
     * @params
     *   aIndex 要移除的元素位置
     *
     * @return 返回移除的元素
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function Remove(aIndex: SizeUInt): T; overload;



    {**
     * RemoveCopySwap
     *
     * @desc 移除指定位置指定数量的元素并拷贝到指定指针,并用尾部元素填充删除数据的位置(交换)
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aPtr          保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   请确保aPtr指向的内存空间足够容纳aElementCount个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveCopySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;

    {**
     * RemoveCopySwap
     *
     * @desc 移除指定位置的元素并拷贝到指定指针,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要移除的元素位置
     *   aPtr   保存元素的内存指针
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   请确保aPtr指向的内存空间足够容纳1个元素
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveCopySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;


    {**
     * RemoveArraySwap
     *
     * @desc 移除指定位置指定数量的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex        要移除的元素位置
     *   aElementCount 要移除的元素数量
     *   aElements     保存元素的数组变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   aElements指向的数组数量会被自动调整
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;

    {**
     * RemoveSwap
     *
     * @desc 移除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex   要移除的元素位置 autocompleted
     *   aElement 保存元素的元素变量
     *
     * @return 返回是否移除成功
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean; overload;

    {**
     * RemoveSwap
     *
     * @desc 移除指定位置的元素,并用最后一个元素填充删除的位置(交换)
     *
     * @params
     *   aIndex 要移除的元素位置
     *
     * @return 返回移除的元素
     *
     * @remark
     *   如果移除失败会抛出异常
     *   如果移除的元素数量大于容器数量会抛出异常
     *   如果移除的元素数量小于0会抛出异常
     *}
    function RemoveSwap(aIndex: SizeUInt): T; overload;

    property Capacity:     SizeUint        read GetCapacity     write SetCapacity;
    property GrowStrategy: TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;
  end;



  { TVec 向量数组实现 }

  generic TVec<T> = class(specialize TGenericCollection<T>, specialize IVec<T>, specialize IStack<T>)
  const
    VEC_DEFAULT_CAPACITY = 0;
  type
    TVecBuf = specialize TArray<T>;
  private
    FBuf:          TVecBuf;
    FCount:        SizeUInt;
    FGrowStrategy: TGrowthStrategy;

  { 迭代器回调 }
  protected
    function  DoIterGetCurrent(aIter: PPtrIter): Pointer; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function  DoIterMoveNext(aIter: PPtrIter): Boolean; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  protected
    function  IsOverlap(const aSrc: Pointer; aCount: SizeUInt): Boolean; override;
    function  GetDefaultGrowStrategy: TGrowthStrategy; virtual;
    procedure EnsureCapacity(aRequiredCapacity: SizeUInt);
    procedure EnsureCapacityExact(aRequiredCapacity: SizeUInt);
    function  CalculateGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

  { 基类虚方法实现 }
  protected
    procedure DoFill(const aElement: T); override;
    procedure DoZero; override;
    procedure DoReverse; override;
  public
    constructor Create(aAllocator: TAllocator; aData: Pointer); override; overload;

    constructor Create(aCapacity: SizeUInt); overload;
    constructor Create(aCapacity: SizeUInt; aAllocator: TAllocator); overload;
    constructor Create(aCapacity: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(aCapacity: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer); overload;

    constructor Create(const aSrc: TCollection; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(const aSrc: TCollection; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer); overload;

    constructor Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer); overload;

    constructor Create(const aSrc: array of T; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy); overload;
    constructor Create(const aSrc: array of T; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer); overload;

    destructor  Destroy; override;

    { ICollection }
    function  PtrIter: TPtrIter; override;
    function  GetCount: SizeUInt; override;
    procedure Clear; override;
    procedure SerializeToArrayBuffer(aDst: Pointer; aCount: SizeUInt); override;
    procedure LoadFromUnChecked(const aSrc: Pointer; aCount: SizeUInt); override; overload;
    procedure AppendUnChecked(const aSrc: Pointer; aCount: SizeUInt); override;
    procedure AppendToUnChecked(const aDst: TCollection); override;

    { IGenericCollection }
    procedure SaveToUnChecked(aDst: TCollection); override;

    { IArray }
    function  GetMemory: PElement; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function  Get(aIndex: SizeUInt): T; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function  GetUnChecked(aIndex: SizeUInt): T; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Put(aIndex: SizeUInt; const aValue: T); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure PutUnChecked(aIndex: SizeUInt; const aValue: T); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function  GetPtr(aIndex: SizeUInt): PElement; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function  GetPtrUnChecked(aIndex: SizeUInt): PElement; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Resize(aNewSize: SizeUInt);
    procedure Ensure(aCount: SizeUInt);

    { IArray - OverWrite 系列 }
    procedure OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: TCollection); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWrite(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure OverWriteUnChecked(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IArray - Read 系列 }
    procedure Read(aIndex: SizeUInt; aDst: Pointer; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Read(aIndex: SizeUInt; var aDst: specialize TGenericArray<T>; aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IArray - Swap 系列 }
    procedure Swap(aIndex1, aIndex2: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure SwapUnChecked(aIndex1, aIndex2: SizeUInt); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Swap(aIndex1, aIndex2, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Swap(aIndex1, aIndex2, aCount, aSwapBufferSize: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IArray - Copy 系列 }
    procedure Copy(aSrcIndex, aDstIndex, aCount: SizeUInt); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure CopyUnChecked(aSrcIndex, aDstIndex, aCount: SizeUInt); {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IArray - Fill 系列 }
    procedure Fill(aIndex: SizeUInt; const aValue: T); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Fill(aIndex, aCount: SizeUInt; const aValue: T); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IArray - Zero 系列 }
    procedure Zero; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Zero(aIndex, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}

    { IGenericCollection - Find 系列 }
    function Find(const aValue: T): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function Find(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - FindIF 系列 }
    function FindIF(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIF(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIF(aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - 额外的Zero、Reverse、ForEach方法 }
    procedure Zero(aIndex: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Reverse(aStartIndex: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Reverse(aStartIndex, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - FindLastIF 系列方法 }
    function FindLastIF(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIF(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIF(aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - FindLastIFNot 系列方法 }
    function FindLastIFNot(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIFNot(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIFNot(aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - CountOf 系列方法 }
    function CountOf(const aElement: T; aStartIndex: SizeUInt): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function CountOf(const aElement: T; aStartIndex, aCount: SizeUInt): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - CountIf 系列方法 }
    function CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeUInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - Replace 系列方法 }
    procedure Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IGenericCollection - ReplaceIF 系列方法 }
    procedure ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - Sort 系列方法 }
    procedure Sort; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Sort(aComparer: specialize TCompareRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure Sort(aStartIndex: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure Sort(aStartIndex, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - BinarySearch 系列方法 }
    function BinarySearch(const aElement: T): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearch(const aElement: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - BinarySearchInsert 系列方法 }
    function BinarySearchInsert(const aElement: T): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aElement: T; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - Shuffle 系列方法 }
    procedure Shuffle; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aRandomGenerator: TRandomGeneratorFunc; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aRandomGenerator: TRandomGeneratorMethod; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Shuffle(aRandomGenerator: TRandomGeneratorRefFunc); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure Shuffle(aStartIndex: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorFunc; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorMethod; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorRefFunc); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    procedure Shuffle(aStartIndex, aCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorFunc; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    procedure Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorMethod; aData: Pointer); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    procedure Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorRefFunc); overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - IsSorted 系列方法 }
    function IsSorted: Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function IsSorted(aComparer: specialize TCompareRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function IsSorted(aStartIndex: SizeUInt): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function IsSorted(aStartIndex, aCount: SizeUInt): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - FindIFNot 系列方法 }
    function FindIFNot(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIFNot(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIFNot(aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - FindLast 系列方法 }
    function FindLast(const aValue: T): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLast(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function FindLast(const aValue: T; aStartIndex: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function FindLast(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IArray - Contains 系列方法（带范围参数） }
    function Contains(const aValue: T; aStartIndex: SizeUInt): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
    function Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE} inline;{$ENDIF}
    {$ENDIF}

    { IVec - 容量管理 }
    function  GetCapacity: SizeUint;
    procedure SetCapacity(aCapacity: SizeUint);
    function  GetGrowStrategy: TGrowthStrategy;
    procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
    function  TryReserve(aAdditional: SizeUint): Boolean;
    procedure Reserve(aAdditional: SizeUint);
    function  TryReserveExact(aAdditional: SizeUint): Boolean;
    procedure ReserveExact(aAdditional: SizeUint);
    procedure Shrink;
    procedure ShrinkTo(aCapacity: SizeUint);
    procedure Truncate(aCount: SizeUint);
    procedure ResizeExact(aNewSize: SizeUint);

    { IVec - Insert 系列 }
    procedure Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt); overload;
    procedure Insert(aIndex: SizeUInt; const aElement: T); overload;
    procedure Insert(aIndex: SizeUInt; const aSrc: array of T); overload;
    procedure Insert(aIndex: SizeUInt; const aSrc: TCollection; aElementCount: SizeUInt); overload;

    { IVec - Write 系列 }
    procedure Write(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt); overload;
    procedure Write(aIndex:SizeUInt; const aSrc: array of T); overload;
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection); overload;
    procedure Write(aIndex:SizeUInt; const aSrc: TCollection; aCount: SizeUInt); overload;
    procedure WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt);
    procedure WriteExact(aIndex: SizeUint; const aSrc: array of T);
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection); overload;
    procedure WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);

    { IVec - 栈操作系列 }
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;
    procedure Push(const aSrc: array of T); overload;
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;
    procedure Push(const aElement: T); overload;
    function  TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;
    function  TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
    function  TryPop(var aDst: T): Boolean; overload;
    function  Pop: T; overload;
    function  TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;
    function  TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
    function  TryPeek(var aElement: T): Boolean; overload;
    function  PeekRange(aElementCount: SizeUInt): PElement; overload;
    function  Peek: T; overload;

    { IVec - Delete/Remove 系列 }
    procedure Delete(aIndex, aElementCount: SizeUInt); overload;
    procedure Delete(aIndex: SizeUInt); overload;
    procedure DeleteSwap(aIndex, aElementCount: SizeUInt); overload;
    procedure DeleteSwap(aIndex: SizeUInt); overload;
    function  RemoveCopy(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveCopy(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
    function  Remove(aIndex: SizeUInt; var aElement: T): Boolean; overload;
    function  Remove(aIndex: SizeUInt): T; overload;
    function  RemoveCopySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveCopySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean; overload;
    function  RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean; overload;
    function  RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean; overload;
    function  RemoveSwap(aIndex: SizeUInt): T; overload;

    { 属性 }
    property Capacity:                SizeUint        read GetCapacity     write SetCapacity;
    property GrowStrategy:            TGrowthStrategy read GetGrowStrategy write SetGrowStrategy;
    property Items[aIndex: SizeUint]: T               read Get             write Put; default;
    property Ptr[aIndex: SizeUint]:   PElement        read GetPtr;
    property Memory:                  PElement        read GetMemory;

  end;



implementation

{ TVec<T> }

constructor TVec.Create(aAllocator: TAllocator; aData: Pointer);
begin
  Create(VEC_DEFAULT_CAPACITY, aAllocator,GetDefaultGrowStrategy, aData);
end;

constructor TVec.Create(aCapacity: SizeUInt);
begin
  Create(aCapacity, GetRtlAllocator(), GetDefaultGrowStrategy, nil);
end;

constructor TVec.Create(aCapacity: SizeUInt; aAllocator: TAllocator);
begin
  Create(aCapacity, aAllocator, GetDefaultGrowStrategy, nil);
end;

constructor TVec.Create(aCapacity: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aCapacity, aAllocator, aGrowStrategy, nil);
end;

constructor TVec.Create(aCapacity: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer);
begin
  inherited Create(aAllocator, aData);
  SetGrowStrategy(aGrowStrategy);
  FBuf   := TVecBuf.Create(aCapacity, aAllocator, aData);
  FCount := 0;
end;

constructor TVec.Create(const aSrc: TCollection; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aSrc, aAllocator, aGrowStrategy, nil);
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(const aSrc: TCollection; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer);
begin
  Create(aSrc, aAllocator, aGrowStrategy, aData);
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aSrc, aElementCount, aAllocator, aGrowStrategy, nil);
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer);
begin
  Create(aSrc, aElementCount, aAllocator, aGrowStrategy, aData);
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(const aSrc: array of T; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy);
begin
  Create(aSrc, aAllocator, aGrowStrategy, nil);
  SetGrowStrategy(aGrowStrategy);
end;

constructor TVec.Create(const aSrc: array of T; aAllocator: TAllocator; aGrowStrategy: TGrowthStrategy; aData: Pointer);
begin
  Create(aSrc, aAllocator, aGrowStrategy, aData);
  SetGrowStrategy(aGrowStrategy);
end;

destructor TVec.Destroy;
begin
  FBuf.Free;
  inherited Destroy;
end;

{ 迭代器回调 }

function TVec.DoIterGetCurrent(aIter: PPtrIter): Pointer;
begin
  {$PUSH}{$WARN 4055 OFF}
  Result := FBuf.GetPtrUnChecked(SizeUInt(aIter^.Data));
  {$POP}
end;

function TVec.DoIterMoveNext(aIter: PPtrIter): Boolean;
begin
  if aIter^.Started then
  begin
    {$PUSH}{$WARN 4055 OFF}
    Inc(SizeUInt(aIter^.Data));
    Result := SizeUInt(aIter^.Data) < FCount;
    {$POP}
  end
  else
  begin
    aIter^.Started := True;
    aIter^.Data    := Pointer(0);
    Result         := FCount > 0;
  end;
end;

{ 内部辅助方法 }

function TVec.GetDefaultGrowStrategy: TGrowthStrategy;
begin
  Result := TGoldenRatioGrowStrategy.GetGlobal;
end;

function TVec.IsOverlap(const aSrc: Pointer; aCount: SizeUInt): Boolean;
begin
  Result := fafafa.core.mem.utils.IsOverlap(FBuf.GetMemory, FCount * FBuf.GetElementSize,
                                            aSrc, aCount * FBuf.GetElementSize);
end;

procedure TVec.EnsureCapacity(aRequiredCapacity: SizeUInt);
var
  LNewCapacity: SizeUInt;
begin
  if FBuf.GetCount >= aRequiredCapacity then
    Exit;

  LNewCapacity := CalculateGrowSize(FBuf.GetCount, aRequiredCapacity);
  FBuf.Resize(LNewCapacity);
end;

procedure TVec.EnsureCapacityExact(aRequiredCapacity: SizeUInt);
begin
  if FBuf.GetCount >= aRequiredCapacity then
    Exit;

  FBuf.Resize(aRequiredCapacity);
end;

function TVec.CalculateGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
var
  LStrategy: TGrowthStrategy;
begin
  LStrategy := FGrowStrategy;
  if LStrategy = nil then
    LStrategy := GetDefaultGrowStrategy;

  Result := LStrategy.GetGrowSize(aCurrentSize, aRequiredSize);
end;

{ 基类虚方法实现 - 直接委托给TArray }

procedure TVec.DoFill(const aElement: T);
begin
  FBuf.Fill(aElement);
end;

procedure TVec.DoZero;
begin
  FBuf.Zero;
end;

procedure TVec.DoReverse;
begin
  FBuf.Reverse;
end;

{ ICollection - 直接委托给TArray }

function TVec.PtrIter: TPtrIter;
begin
  Result.Init(Self, @DoIterGetCurrent, @DoIterMoveNext, Pointer(0));
end;

function TVec.GetCount: SizeUInt;
begin
  Result := FCount;
end;

procedure TVec.Clear;
begin
  FCount := 0;
end;

procedure TVec.SerializeToArrayBuffer(aDst: Pointer; aCount: SizeUInt);
begin
  if aCount = 0 then
    exit;

  if aCount > FCount then
    raise fafafa.core.base.EOutOfRange.Create('TVec.SerializeToArrayBuffer: aCount out of bounds');

  FBuf.SerializeToArrayBuffer(aDst, aCount);
end;

procedure TVec.LoadFromUnChecked(const aSrc: Pointer; aCount: SizeUInt);
begin
  FBuf.LoadFromUnChecked(aSrc, aCount);
  FCount := aCount;
end;

procedure TVec.AppendUnChecked(const aSrc: Pointer; aCount: SizeUInt);
var
  LIndex: SizeUInt;
begin
  if aCount = 0 then
    exit;

  if aSrc = nil then
    raise fafafa.core.base.EInvalidArgument.Create('TVec.AppendUnChecked: aSrc is nil');

  if IsOverlap(aSrc, aCount) then
  begin
    if PtrUInt(aSrc) mod FBuf.GetElementSize <> 0 then
      raise fafafa.core.base.EInvalidArgument.Create('TVec.AppendUnChecked: aSrc is not aligned');

    LIndex := (PtrUInt(aSrc) - PtrUInt(FBuf.GetMemory)) div FBuf.GetElementSize;

    if (LIndex >= FCount) or (aCount > (FCount - LIndex)) then
      raise fafafa.core.base.EOutOfRange.Create('TVec.AppendUnChecked: bounds out of range');

    Reserve(aCount);
    FBuf.CopyUnChecked(LIndex, FCount, aCount);
  end
  else
  begin
    Reserve(aCount);
    FBuf.OverWriteUnChecked(FCount, aSrc, aCount);
  end;

  Inc(FCount, aCount);
end;

procedure TVec.AppendToUnChecked(const aDst: TCollection);
begin
  if FCount = 0 then
    exit;

  aDst.AppendUnChecked(GetMemory, FCount);
end;

{ IGenericCollection - 直接委托给TArray }

procedure TVec.SaveToUnChecked(aDst: TCollection);
begin
  if FCount = 0 then
    aDst.Clear
  else
    aDst.LoadFromUnChecked(GetMemory, FCount);
end;

{ IArray - 直接委托给TArray }

function TVec.GetMemory: PElement;
begin
  Result := FBuf.GetMemory;
end;

function TVec.Get(aIndex: SizeUInt): T;
begin
  if aIndex >= FCount then
    raise fafafa.core.base.EOutOfRange.Create('TVec.Get: aIndex out of bounds');

  Result := GetUnChecked(aIndex);
end;

function TVec.GetUnChecked(aIndex: SizeUInt): T;
begin
  Result := FBuf.GetUnChecked(aIndex);
end;

procedure TVec.Put(aIndex: SizeUInt; const aValue: T);
begin
  if aIndex >= FCount then
    raise fafafa.core.base.EOutOfRange.Create('TVec.Put: aIndex out of bounds');

  PutUnChecked(aIndex, aValue);
end;

procedure TVec.PutUnChecked(aIndex: SizeUInt; const aValue: T);
begin
  FBuf.PutUnChecked(aIndex, aValue);
end;

function TVec.GetPtr(aIndex: SizeUInt): PElement;
begin
  if aIndex >= FCount then
    raise fafafa.core.base.EOutOfRange.Create('TVec.GetPtr: aIndex out of bounds');

  Result := GetPtrUnChecked(aIndex);
end;

function TVec.GetPtrUnChecked(aIndex: SizeUInt): PElement;
begin
  Result := FBuf.GetPtrUnChecked(aIndex);
end;

procedure TVec.Resize(aNewSize: SizeUInt);
begin
  if aNewSize > GetCapacity then
    EnsureCapacity(aNewSize);

  FCount := aNewSize;
end;

procedure TVec.Ensure(aCount: SizeUInt);
begin
  EnsureCapacity(FCount + aCount);
end;

{ IVec - 容量管理（TVec的核心增值功能）}

function TVec.GetCapacity: SizeUint;
begin
  Result := FBuf.GetCount; // TArray的Count就是其容量
end;

procedure TVec.SetCapacity(aCapacity: SizeUint);
begin
  FBuf.Resize(aCapacity);
end;

function TVec.GetGrowStrategy: TGrowthStrategy;
begin
  Result := FGrowStrategy;
  if Result = nil then
    Result := GetDefaultGrowStrategy;
end;

procedure TVec.SetGrowStrategy(aGrowStrategy: TGrowthStrategy);
begin
  FGrowStrategy := aGrowStrategy;
end;

function TVec.TryReserve(aAdditional: SizeUint): Boolean;
var
  LRequiredCapacity: SizeUInt;
  LNewCapacity: SizeUInt;
begin
  Result := True;

  if IsAddOverflow(FBuf.GetCount, aAdditional) then
  begin
    Result := False;
    Exit;
  end;

  LRequiredCapacity := FBuf.GetCount + aAdditional;
  if GetCapacity >= LRequiredCapacity then
    Exit;

  try
    LNewCapacity := CalculateGrowSize(GetCapacity, LRequiredCapacity);
    FBuf.Resize(LNewCapacity);
  except
    Result := False;
  end;
end;

procedure TVec.Reserve(aAdditional: SizeUint);
begin
  if not TryReserve(aAdditional) then
    raise EOutOfMemory.Create('TVec.Reserve: Failed to reserve additional capacity');
end;

function TVec.TryReserveExact(aAdditional: SizeUint): Boolean;
var
  LRequiredCapacity: SizeUInt;
begin
  Result := True;

  if IsAddOverflow(FBuf.GetCount, aAdditional) then
  begin
    Result := False;
    Exit;
  end;

  LRequiredCapacity := FBuf.GetCount + aAdditional;
  if GetCapacity >= LRequiredCapacity then
    Exit;

  try
    FBuf.Resize(LRequiredCapacity); // 精确扩容，不使用增长策略
  except
    Result := False;
  end;
end;

procedure TVec.ReserveExact(aAdditional: SizeUint);
begin
  if not TryReserveExact(aAdditional) then
    raise EOutOfMemory.Create('TVec.ReserveExact: Failed to reserve exact additional capacity');
end;

procedure TVec.Shrink;
begin
  FBuf.Resize(FCount); // 收缩容量到实际元素数量
end;

procedure TVec.ShrinkTo(aCapacity: SizeUint);
begin
  if aCapacity < FBuf.GetCount then
    raise EInvalidArgument.Create('TVec.ShrinkTo: Cannot shrink to capacity smaller than current count');

  if aCapacity < GetCapacity then
    FBuf.Resize(aCapacity);
end;

procedure TVec.Truncate(aCount: SizeUint);
begin
  if aCount < FBuf.GetCount then
    FBuf.Resize(aCount); // 截断到指定数量
end;

procedure TVec.ResizeExact(aNewSize: SizeUint);
begin
  EnsureCapacityExact(aNewSize); // 精确调整容量，不使用增长策略
  FCount := aNewSize;  // 更新TVec的元素数量
end;

{ IVec - 栈操作系列（TVec的增值功能）}

procedure TVec.Push(const aElement: T);
begin
  EnsureCapacity(FCount + 1);
  FBuf.PutUnChecked(FCount, aElement);
  Inc(FCount);
end;

procedure TVec.Push(const aSrc: Pointer; aElementCount: SizeUInt);
begin
  if (aSrc = nil) or (aElementCount = 0) then
    Exit;

  EnsureCapacity(FCount + aElementCount);
  FBuf.OverWriteUnChecked(FCount, aSrc, aElementCount);
  Inc(FCount, aElementCount);
end;

procedure TVec.Push(const aSrc: array of T);
begin
  Push(@aSrc[0], Length(aSrc));
end;

procedure TVec.Push(const aSrc: TCollection; aElementCount: SizeUInt);
begin
  if (aSrc = nil) or (aElementCount = 0) then
    Exit;

  if aElementCount > aSrc.GetCount then
    raise EInvalidArgument.Create('TVec.Push: aElementCount exceeds source collection count');

  EnsureCapacity(FCount + aElementCount);
  aSrc.SerializeToArrayBuffer(FBuf.GetPtrUnChecked(FCount), aElementCount);
  Inc(FCount, aElementCount);
end;

function TVec.TryPop(var aDst: T): Boolean;
begin
  Result := FCount > 0;

  if Result then
  begin
    Dec(FCount);
    aDst := FBuf.GetUnChecked(FCount);
  end;
end;

function TVec.TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean;
begin
  Result := (aDst <> nil) and (aElementCount > 0) and (FCount >= aElementCount);

  if Result then
  begin
    FBuf.Read(FCount - aElementCount, aDst, aElementCount);
    Dec(FCount, aElementCount);
  end;
end;

function TVec.TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
begin
  Result := (aElementCount > 0) and (FCount >= aElementCount);

  if Result then
  begin
    SetLength(aDst, aElementCount);
    FBuf.Read(FCount - aElementCount, aDst, aElementCount);
    Dec(FCount, aElementCount);
  end;
end;

function TVec.Pop: T;
begin
  if not TryPop(Result) then
    raise EEmptyCollection.Create('TVec.Pop: Cannot pop from empty vector');
end;

function TVec.TryPeek(var aElement: T): Boolean;
begin
  Result := FCount > 0;

  if Result then
    aElement := FBuf.GetUnChecked(FCount - 1);
end;

function TVec.Peek: T;
begin
  if not TryPeek(Result) then
    raise EEmptyCollection.Create('TVec.Peek: Cannot peek empty vector');
end;

function TVec.TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean;
begin
  Result := (aDst <> nil) and (aElementCount > 0) and (FCount >= aElementCount);

  if Result then
    FBuf.Read(FCount - aElementCount, aDst, aElementCount);
end;

function TVec.TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean;
begin
  Result := (aElementCount > 0) and (FCount >= aElementCount);

  if Result then
  begin
    SetLength(aDst, aElementCount);
    FBuf.Read(FCount - aElementCount, aDst, aElementCount);
  end;
end;

function TVec.PeekRange(aElementCount: SizeUInt): PElement;
var
  LCount: SizeUInt;
begin
  LCount := FBuf.GetCount;

  if (aElementCount = 0) or (LCount < aElementCount) then
    Result := nil
  else
    Result := FBuf.GetPtrUnChecked(LCount - aElementCount);
end;

{ IVec - Insert 系列（需要动态扩容的特殊逻辑）}

procedure TVec.Insert(aIndex: SizeUInt; const aElement: T);
var
  LCount: SizeUInt;
begin
  LCount := FBuf.GetCount;

  if aIndex > LCount then
    raise EOutOfRange.Create('TVec.Insert: Index out of range');

  EnsureCapacity(LCount + 1);
  FBuf.Resize(LCount + 1);

  // 移动元素为新元素腾出空间
  if aIndex < LCount then
    FBuf.CopyUnChecked(aIndex, aIndex + 1, LCount - aIndex);

  FBuf.PutUnChecked(aIndex, aElement);
end;

procedure TVec.Insert(aIndex: SizeUint; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LCount: SizeUInt;
begin
  if (aSrc = nil) or (aElementCount = 0) then
    Exit;

  LCount := FBuf.GetCount;

  if aIndex > LCount then
    raise EOutOfRange.Create('TVec.Insert: Index out of range');

  EnsureCapacity(LCount + aElementCount);
  FBuf.Resize(LCount + aElementCount);

  // 移动元素为新元素腾出空间
  if aIndex < LCount then
    FBuf.CopyUnChecked(aIndex, aIndex + aElementCount, LCount - aIndex);

  FBuf.OverWriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.Insert(aIndex: SizeUInt; const aSrc: array of T);
begin
  Insert(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.Insert(aIndex: SizeUInt; const aSrc: TCollection; aElementCount: SizeUInt);
var
  LCount: SizeUInt;
begin
  if (aSrc = nil) or (aElementCount = 0) then
    Exit;

  if aElementCount > aSrc.GetCount then
    raise EInvalidArgument.Create('TVec.Insert: aElementCount exceeds source collection count');

  LCount := FBuf.GetCount;

  if aIndex > LCount then
    raise EOutOfRange.Create('TVec.Insert: Index out of range');

  EnsureCapacity(LCount + aElementCount);
  FBuf.Resize(LCount + aElementCount);

  // 移动元素为新元素腾出空间
  if aIndex < LCount then
    FBuf.CopyUnChecked(aIndex, aIndex + aElementCount, LCount - aIndex);

  aSrc.SerializeToArrayBuffer(FBuf.GetPtrUnChecked(aIndex), aElementCount);
end;

{ IVec - Write 系列（需要动态扩容的特殊逻辑）}

procedure TVec.Write(aIndex: SizeUInt; const aSrc: Pointer; aElementCount: SizeUInt);
var
  LRequiredSize: SizeUInt;
begin
  if (aSrc = nil) or (aElementCount = 0) then
    Exit;

  if IsAddOverflow(aIndex, aElementCount) then
    raise EOverflow.Create('TVec.Write: Index + ElementCount overflow');

  LRequiredSize := aIndex + aElementCount;

  if LRequiredSize > FBuf.GetCount then
  begin
    EnsureCapacity(LRequiredSize);
    FBuf.Resize(LRequiredSize);
  end;

  FBuf.OverWriteUnChecked(aIndex, aSrc, aElementCount);
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: array of T);
begin
  Write(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: TCollection);
begin
  if aSrc = nil then
    Exit;

  Write(aIndex, aSrc, aSrc.GetCount);
end;

procedure TVec.Write(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
var
  LRequiredSize: SizeUInt;
begin
  if (aSrc = nil) or (aCount = 0) then
    Exit;

  if aCount > aSrc.GetCount then
    raise EInvalidArgument.Create('TVec.Write: aCount exceeds source collection count');

  if IsAddOverflow(aIndex, aCount) then
    raise EOverflow.Create('TVec.Write: Index + Count overflow');

  LRequiredSize := aIndex + aCount;

  if LRequiredSize > FBuf.GetCount then
  begin
    EnsureCapacity(LRequiredSize);
    FBuf.Resize(LRequiredSize);
  end;

  aSrc.SerializeToArrayBuffer(FBuf.GetPtrUnChecked(aIndex), aCount);
end;

{ IVec - WriteExact 系列（精确扩容，不使用增长策略）}

procedure TVec.WriteExact(aIndex: SizeUint; const aPtr: Pointer; aElementCount: SizeUInt);
var
  LRequiredSize: SizeUInt;
begin
  if (aPtr = nil) or (aElementCount = 0) then
    Exit;

  if IsAddOverflow(aIndex, aElementCount) then
    raise EOverflow.Create('TVec.WriteExact: Index + ElementCount overflow');

  LRequiredSize := aIndex + aElementCount;

  if LRequiredSize > FBuf.GetCount then
    FBuf.Resize(LRequiredSize); // 精确扩容

  FBuf.OverWriteUnChecked(aIndex, aPtr, aElementCount);
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: array of T);
begin
  WriteExact(aIndex, @aSrc[0], Length(aSrc));
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: TCollection);
begin
  if aSrc = nil then
    Exit;

  WriteExact(aIndex, aSrc, aSrc.GetCount);
end;

procedure TVec.WriteExact(aIndex: SizeUint; const aSrc: TCollection; aCount: SizeUInt);
var
  LRequiredSize: SizeUInt;
begin
  if (aSrc = nil) or (aCount = 0) then
    Exit;

  if aCount > aSrc.GetCount then
    raise EInvalidArgument.Create('TVec.WriteExact: aCount exceeds source collection count');

  if IsAddOverflow(aIndex, aCount) then
    raise EOverflow.Create('TVec.WriteExact: Index + Count overflow');

  LRequiredSize := aIndex + aCount;

  if LRequiredSize > FBuf.GetCount then
    FBuf.Resize(LRequiredSize); // 精确扩容

  aSrc.SerializeToArrayBuffer(FBuf.GetPtrUnChecked(aIndex), aCount);
end;

{ IVec - Delete/Remove 系列（直接委托给TArray的成熟实现）}

procedure TVec.Delete(aIndex, aElementCount: SizeUInt);
begin
  // TArray没有Delete方法，我们需要手动实现
  if aIndex >= FBuf.GetCount then
    raise EOutOfRange.Create('TVec.Delete: Index out of range');

  if aIndex + aElementCount > FBuf.GetCount then
    aElementCount := FBuf.GetCount - aIndex;

  if aElementCount = 0 then
    Exit;

  // 移动后续元素
  if aIndex + aElementCount < FBuf.GetCount then
    FBuf.CopyUnChecked(aIndex + aElementCount, aIndex, FBuf.GetCount - aIndex - aElementCount);

  // 调整大小
  FBuf.Resize(FBuf.GetCount - aElementCount);
end;

procedure TVec.Delete(aIndex: SizeUInt);
begin
  Delete(aIndex, 1);
end;

procedure TVec.DeleteSwap(aIndex, aElementCount: SizeUInt);
var
  LCount, LTailCount: SizeUInt;
begin
  LCount := FBuf.GetCount;

  if aIndex >= LCount then
    raise EOutOfRange.Create('TVec.DeleteSwap: Index out of range');

  if aIndex + aElementCount > LCount then
    aElementCount := LCount - aIndex;

  if aElementCount = 0 then
    Exit;

  // 计算尾部元素数量
  LTailCount := LCount - aIndex - aElementCount;

  // 如果尾部元素数量小于要删除的数量，只移动尾部元素
  if LTailCount < aElementCount then
  begin
    if LTailCount > 0 then
      FBuf.CopyUnChecked(LCount - LTailCount, aIndex, LTailCount);
  end
  else
  begin
    // 移动尾部的aElementCount个元素到删除位置
    FBuf.CopyUnChecked(LCount - aElementCount, aIndex, aElementCount);
  end;

  FBuf.Resize(LCount - aElementCount);
end;

procedure TVec.DeleteSwap(aIndex: SizeUInt);
begin
  DeleteSwap(aIndex, 1);
end;

function TVec.RemoveCopy(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := (aPtr <> nil) and (aIndex < FBuf.GetCount) and (aElementCount > 0);

  if not Result then
    Exit;

  if aIndex + aElementCount > FBuf.GetCount then
    aElementCount := FBuf.GetCount - aIndex;

  // 复制要删除的元素
  FBuf.Read(aIndex, aPtr, aElementCount);

  // 删除元素
  Delete(aIndex, aElementCount);
end;

function TVec.RemoveCopy(aIndex: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := RemoveCopy(aIndex, 1, aPtr);
end;

function TVec.RemoveArray(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
begin
  Result := (aIndex < FBuf.GetCount) and (aElementCount > 0);

  if not Result then
    Exit;

  if aIndex + aElementCount > FBuf.GetCount then
    aElementCount := FBuf.GetCount - aIndex;

  // 设置数组大小并复制元素
  SetLength(aElements, aElementCount);
  FBuf.Read(aIndex, aElements, aElementCount);

  // 删除元素
  Delete(aIndex, aElementCount);
end;

function TVec.Remove(aIndex: SizeUInt; var aElement: T): Boolean;
begin
  Result := aIndex < FBuf.GetCount;

  if Result then
  begin
    aElement := FBuf.GetUnChecked(aIndex);
    Delete(aIndex, 1);
  end;
end;

function TVec.Remove(aIndex: SizeUInt): T;
begin
  if not Remove(aIndex, Result) then
    raise EOutOfRange.Create('TVec.Remove: Index out of range');
end;

function TVec.RemoveCopySwap(aIndex, aElementCount: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := (aPtr <> nil) and (aIndex < FBuf.GetCount) and (aElementCount > 0);

  if not Result then
    Exit;

  if aIndex + aElementCount > FBuf.GetCount then
    aElementCount := FBuf.GetCount - aIndex;

  // 复制要删除的元素
  FBuf.Read(aIndex, aPtr, aElementCount);

  // 使用swap删除
  DeleteSwap(aIndex, aElementCount);
end;

function TVec.RemoveCopySwap(aIndex: SizeUInt; aPtr: Pointer): Boolean;
begin
  Result := RemoveCopySwap(aIndex, 1, aPtr);
end;

function TVec.RemoveArraySwap(aIndex, aElementCount: SizeUInt; var aElements: specialize TGenericArray<T>): Boolean;
begin
  Result := (aIndex < FBuf.GetCount) and (aElementCount > 0);

  if not Result then
    Exit;

  if aIndex + aElementCount > FBuf.GetCount then
    aElementCount := FBuf.GetCount - aIndex;

  // 设置数组大小并复制元素
  SetLength(aElements, aElementCount);
  FBuf.Read(aIndex, aElements, aElementCount);

  // 使用swap删除
  DeleteSwap(aIndex, aElementCount);
end;

function TVec.RemoveSwap(aIndex: SizeUInt; var aElement: T): Boolean;
begin
  Result := aIndex < FBuf.GetCount;

  if Result then
  begin
    aElement := FBuf.GetUnChecked(aIndex);
    DeleteSwap(aIndex, 1);
  end;
end;

function TVec.RemoveSwap(aIndex: SizeUInt): T;
begin
  if not RemoveSwap(aIndex, Result) then
    raise EOutOfRange.Create('TVec.RemoveSwap: Index out of range');
end;

{ IArray - OverWrite 系列实现 }
procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: Pointer; aCount: SizeUInt);
begin
  FBuf.OverWrite(aIndex, aSrc, aCount);
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: Pointer; aCount: SizeUInt);
begin
  FBuf.OverWriteUnChecked(aIndex, aSrc, aCount);
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: array of T);
begin
  FBuf.OverWrite(aIndex, aSrc);
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: array of T);
begin
  FBuf.OverWriteUnChecked(aIndex, aSrc);
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: TCollection);
begin
  FBuf.OverWrite(aIndex, aSrc);
end;

procedure TVec.OverWrite(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  FBuf.OverWrite(aIndex, aSrc, aCount);
end;

procedure TVec.OverWriteUnChecked(aIndex: SizeUInt; const aSrc: TCollection; aCount: SizeUInt);
begin
  FBuf.OverWriteUnChecked(aIndex, aSrc, aCount);
end;

{ IArray - Read 系列实现 }
procedure TVec.Read(aIndex: SizeUInt; aDst: Pointer; aCount: SizeUInt);
begin
  FBuf.Read(aIndex, aDst, aCount);
end;

procedure TVec.Read(aIndex: SizeUInt; var aDst: specialize TGenericArray<T>; aCount: SizeUInt);
begin
  FBuf.Read(aIndex, aDst, aCount);
end;

{ IArray - Swap 系列实现 }
procedure TVec.Swap(aIndex1, aIndex2: SizeUInt);
begin
  FBuf.Swap(aIndex1, aIndex2);
end;

procedure TVec.SwapUnChecked(aIndex1, aIndex2: SizeUInt);
begin
  FBuf.SwapUnChecked(aIndex1, aIndex2);
end;

procedure TVec.Swap(aIndex1, aIndex2, aCount: SizeUInt);
begin
  FBuf.Swap(aIndex1, aIndex2, aCount);
end;

procedure TVec.Swap(aIndex1, aIndex2, aCount, aSwapBufferSize: SizeUInt);
begin
  FBuf.Swap(aIndex1, aIndex2, aCount, aSwapBufferSize);
end;

{ IArray - Copy 系列实现 }
procedure TVec.Copy(aSrcIndex, aDstIndex, aCount: SizeUInt);
begin
  FBuf.Copy(aSrcIndex, aDstIndex, aCount);
end;

procedure TVec.CopyUnChecked(aSrcIndex, aDstIndex, aCount: SizeUInt);
begin
  FBuf.CopyUnChecked(aSrcIndex, aDstIndex, aCount);
end;

{ IArray - Fill 系列实现 }
procedure TVec.Fill(aIndex: SizeUInt; const aValue: T);
begin
  FBuf.Fill(aIndex, aValue);
end;

procedure TVec.Fill(aIndex, aCount: SizeUInt; const aValue: T);
begin
  FBuf.Fill(aIndex, aCount, aValue);
end;

{ IArray - Zero 系列实现 }
procedure TVec.Zero;
begin
  FBuf.Zero;
end;

procedure TVec.Zero(aIndex, aCount: SizeUInt);
begin
  FBuf.Zero(aIndex, aCount);
end;

{ IGenericCollection - Find 系列实现 }
function TVec.Find(const aValue: T): SizeInt;
begin
  Result := FBuf.Find(aValue);
end;

function TVec.Find(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aEquals, aData);
end;

function TVec.Find(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.Find(aValue, aEquals);
end;
{$ENDIF}

function TVec.Find(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex);
end;

function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aEquals, aData);
end;

function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aEquals);
end;
{$ENDIF}

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aCount);
end;

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aCount, aEquals, aData);
end;

function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aCount, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.Find(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.Find(aValue, aStartIndex, aCount, aEquals);
end;
{$ENDIF}

{ IGenericCollection - FindIF 系列实现 }
function TVec.FindIF(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aPredicate, aData);
end;

function TVec.FindIF(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIF(aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIF(aPredicate);
end;
{$ENDIF}

function TVec.FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aPredicate, aData);
end;

function TVec.FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIF(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IGenericCollection - 额外的Zero、Reverse、ForEach方法实现 }
procedure TVec.Zero(aIndex: SizeUInt);
begin
  FBuf.Zero(aIndex);
end;

procedure TVec.Reverse(aStartIndex: SizeUInt);
begin
  FBuf.Reverse(aStartIndex);
end;

procedure TVec.Reverse(aStartIndex, aCount: SizeUInt);
begin
  FBuf.Reverse(aStartIndex, aCount);
end;

function TVec.ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aPredicate, aData);
end;

function TVec.ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.ForEach(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.ForEach(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): Boolean;
begin
  Result := FBuf.ForEach(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IGenericCollection - FindLastIF 系列方法实现 }
function TVec.FindLastIF(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aPredicate, aData);
end;

function TVec.FindLastIF(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIF(aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIF(aPredicate);
end;
{$ENDIF}

function TVec.FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aPredicate, aData);
end;

function TVec.FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIF(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIF(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIF(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IGenericCollection - FindLastIFNot 系列方法实现 }
function TVec.FindLastIFNot(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aPredicate, aData);
end;

function TVec.FindLastIFNot(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIFNot(aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aPredicate);
end;
{$ENDIF}

function TVec.FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aPredicate, aData);
end;

function TVec.FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLastIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLastIFNot(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IGenericCollection - CountOf 系列方法实现 }
function TVec.CountOf(const aElement: T; aStartIndex: SizeUInt): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex);
end;

function TVec.CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aEquals, aData);
end;

function TVec.CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.CountOf(const aElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aEquals);
end;
{$ENDIF}

function TVec.CountOf(const aElement: T; aStartIndex, aCount: SizeUInt): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aCount);
end;

function TVec.CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aCount, aEquals, aData);
end;

function TVec.CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aCount, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.CountOf(const aElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeUInt;
begin
  Result := FBuf.CountOf(aElement, aStartIndex, aCount, aEquals);
end;
{$ENDIF}

{ IGenericCollection - CountIf 系列方法实现 }
function TVec.CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aPredicate, aData);
end;

function TVec.CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.CountIf(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.CountIf(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeUInt;
begin
  Result := FBuf.CountIf(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IGenericCollection - Replace 系列方法实现 }
procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex);
end;

procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aEquals, aData);
end;

procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aEquals);
end;
{$ENDIF}

procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aCount);
end;

procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aCount, aEquals, aData);
end;

procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aCount, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Replace(const aElement, aNewElement: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>);
begin
  FBuf.Replace(aElement, aNewElement, aStartIndex, aCount, aEquals);
end;
{$ENDIF}

{ IGenericCollection - ReplaceIF 系列方法实现 }
procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aPredicate, aData);
end;

procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aPredicate);
end;
{$ENDIF}

procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aCount, aPredicate, aData);
end;

procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.ReplaceIF(const aNewElement: T; aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>);
begin
  FBuf.ReplaceIF(aNewElement, aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IArray - Sort 系列方法实现 }
procedure TVec.Sort;
begin
  FBuf.Sort;
end;

procedure TVec.Sort(aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  FBuf.Sort(aComparer, aData);
end;

procedure TVec.Sort(aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  FBuf.Sort(aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aComparer: specialize TCompareRefFunc<T>);
begin
  FBuf.Sort(aComparer);
end;
{$ENDIF}

procedure TVec.Sort(aStartIndex: SizeUInt);
begin
  FBuf.Sort(aStartIndex);
end;

procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  FBuf.Sort(aStartIndex, aComparer, aData);
end;

procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  FBuf.Sort(aStartIndex, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin
  FBuf.Sort(aStartIndex, aComparer);
end;
{$ENDIF}

procedure TVec.Sort(aStartIndex, aCount: SizeUInt);
begin
  FBuf.Sort(aStartIndex, aCount);
end;

procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer);
begin
  FBuf.Sort(aStartIndex, aCount, aComparer, aData);
end;

procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer);
begin
  FBuf.Sort(aStartIndex, aCount, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Sort(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>);
begin
  FBuf.Sort(aStartIndex, aCount, aComparer);
end;
{$ENDIF}

{ IArray - BinarySearch 系列方法实现 }
function TVec.BinarySearch(const aElement: T): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement);
end;

function TVec.BinarySearch(const aElement: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aComparer, aData);
end;

function TVec.BinarySearch(const aElement: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aElement: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aComparer);
end;
{$ENDIF}

function TVec.BinarySearch(const aElement: T; aStartIndex: SizeUInt): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex);
end;

function TVec.BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aComparer, aData);
end;

function TVec.BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aComparer);
end;
{$ENDIF}

function TVec.BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aCount);
end;

function TVec.BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aCount, aComparer, aData);
end;

function TVec.BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aCount, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearch(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearch(aElement, aStartIndex, aCount, aComparer);
end;
{$ENDIF}

{ IArray - BinarySearchInsert 系列方法实现 }
function TVec.BinarySearchInsert(const aElement: T): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement);
end;

function TVec.BinarySearchInsert(const aElement: T; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aComparer, aData);
end;

function TVec.BinarySearchInsert(const aElement: T; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearchInsert(const aElement: T; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aComparer);
end;
{$ENDIF}

function TVec.BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex);
end;

function TVec.BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aComparer, aData);
end;

function TVec.BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearchInsert(const aElement: T; aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aComparer);
end;
{$ENDIF}

function TVec.BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aCount);
end;

function TVec.BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aCount, aComparer, aData);
end;

function TVec.BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aCount, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.BinarySearchInsert(const aElement: T; aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): SizeInt;
begin
  Result := FBuf.BinarySearchInsert(aElement, aStartIndex, aCount, aComparer);
end;
{$ENDIF}

{ IArray - Shuffle 系列方法实现 }
procedure TVec.Shuffle;
begin
  FBuf.Shuffle;
end;

procedure TVec.Shuffle(aRandomGenerator: TRandomGeneratorFunc; aData: Pointer);
begin
  FBuf.Shuffle(aRandomGenerator, aData);
end;

procedure TVec.Shuffle(aRandomGenerator: TRandomGeneratorMethod; aData: Pointer);
begin
  FBuf.Shuffle(aRandomGenerator, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Shuffle(aRandomGenerator: TRandomGeneratorRefFunc);
begin
  FBuf.Shuffle(aRandomGenerator);
end;
{$ENDIF}

procedure TVec.Shuffle(aStartIndex: SizeUInt);
begin
  FBuf.Shuffle(aStartIndex);
end;

procedure TVec.Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorFunc; aData: Pointer);
begin
  FBuf.Shuffle(aStartIndex, aRandomGenerator, aData);
end;

procedure TVec.Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorMethod; aData: Pointer);
begin
  FBuf.Shuffle(aStartIndex, aRandomGenerator, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Shuffle(aStartIndex: SizeUInt; aRandomGenerator: TRandomGeneratorRefFunc);
begin
  FBuf.Shuffle(aStartIndex, aRandomGenerator);
end;
{$ENDIF}

procedure TVec.Shuffle(aStartIndex, aCount: SizeUInt);
begin
  FBuf.Shuffle(aStartIndex, aCount);
end;

procedure TVec.Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorFunc; aData: Pointer);
begin
  FBuf.Shuffle(aStartIndex, aCount, aRandomGenerator, aData);
end;

procedure TVec.Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorMethod; aData: Pointer);
begin
  FBuf.Shuffle(aStartIndex, aCount, aRandomGenerator, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
procedure TVec.Shuffle(aStartIndex, aCount: SizeUInt; aRandomGenerator: TRandomGeneratorRefFunc);
begin
  FBuf.Shuffle(aStartIndex, aCount, aRandomGenerator);
end;
{$ENDIF}

{ IArray - IsSorted 系列方法实现 }
function TVec.IsSorted: Boolean;
begin
  Result := FBuf.IsSorted;
end;

function TVec.IsSorted(aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aComparer, aData);
end;

function TVec.IsSorted(aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.IsSorted(aComparer: specialize TCompareRefFunc<T>): Boolean;
begin
  Result := FBuf.IsSorted(aComparer);
end;
{$ENDIF}

function TVec.IsSorted(aStartIndex: SizeUInt): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex);
end;

function TVec.IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aComparer, aData);
end;

function TVec.IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.IsSorted(aStartIndex: SizeUInt; aComparer: specialize TCompareRefFunc<T>): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aComparer);
end;
{$ENDIF}

function TVec.IsSorted(aStartIndex, aCount: SizeUInt): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aCount);
end;

function TVec.IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aCount, aComparer, aData);
end;

function TVec.IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aCount, aComparer, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.IsSorted(aStartIndex, aCount: SizeUInt; aComparer: specialize TCompareRefFunc<T>): Boolean;
begin
  Result := FBuf.IsSorted(aStartIndex, aCount, aComparer);
end;
{$ENDIF}

{ IArray - FindIFNot 系列方法实现 }
function TVec.FindIFNot(aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aPredicate, aData);
end;

function TVec.FindIFNot(aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIFNot(aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIFNot(aPredicate);
end;
{$ENDIF}

function TVec.FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aPredicate, aData);
end;

function TVec.FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIFNot(aStartIndex: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aPredicate);
end;
{$ENDIF}

function TVec.FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aCount, aPredicate, aData);
end;

function TVec.FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aCount, aPredicate, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindIFNot(aStartIndex, aCount: SizeUInt; aPredicate: specialize TPredicateRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindIFNot(aStartIndex, aCount, aPredicate);
end;
{$ENDIF}

{ IArray - FindLast 系列方法实现 }
function TVec.FindLast(const aValue: T): SizeInt;
begin
  Result := FBuf.FindLast(aValue);
end;

function TVec.FindLast(const aValue: T; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aEquals, aData);
end;

function TVec.FindLast(const aValue: T; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLast(const aValue: T; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aEquals);
end;
{$ENDIF}

function TVec.FindLast(const aValue: T; aStartIndex: SizeUInt): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex);
end;

function TVec.FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aEquals, aData);
end;

function TVec.FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLast(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aEquals);
end;
{$ENDIF}

function TVec.FindLast(const aValue: T; aStartIndex, aCount: SizeUInt): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aCount);
end;

function TVec.FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aCount, aEquals, aData);
end;

function TVec.FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aCount, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.FindLast(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): SizeInt;
begin
  Result := FBuf.FindLast(aValue, aStartIndex, aCount, aEquals);
end;
{$ENDIF}

{ IArray - Contains 系列方法实现（带范围参数） }
function TVec.Contains(const aValue: T; aStartIndex: SizeUInt): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex);
end;

function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aEquals, aData);
end;

function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.Contains(const aValue: T; aStartIndex: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aEquals);
end;
{$ENDIF}

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aCount);
end;

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsFunc<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aCount, aEquals, aData);
end;

function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsMethod<T>; aData: Pointer): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aCount, aEquals, aData);
end;

{$IFDEF FAFAFA_CORE_ANONYMOUS_REFERENCES}
function TVec.Contains(const aValue: T; aStartIndex, aCount: SizeUInt; aEquals: specialize TEqualsRefFunc<T>): Boolean;
begin
  Result := FBuf.Contains(aValue, aStartIndex, aCount, aEquals);
end;
{$ENDIF}

end.