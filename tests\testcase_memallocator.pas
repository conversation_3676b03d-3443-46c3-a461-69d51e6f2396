unit testcase_memAllocator;

{$mode objfpc}{$H+}
{$I ../src/fafafa.collections.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testutils, testregistry,
  fafafa.collections;

type

  { TTestCase_memAllocator }

  TTestCase_memAllocator= class(TTestCase)
  published
    procedure Test_Create;
    procedure Test_GetMem;
    procedure Test_AllocMem;
    procedure Test_ReallocMem;
    procedure Test_FreeMem;
    procedure Test_Copy;
    procedure Test_CopyUnChecked;
    procedure Test_CopyNonOverlap;
    procedure Test_CopyNonOverlapUnChecked;
    procedure Test_Fill;
    procedure Test_Fill8;
    procedure Test_Fill16;
    procedure Test_Fill32;
    procedure Test_Fill64;
    procedure Test_zero;
    procedure Test_IsOverlap;
    procedure Test_RtlMemAllocator;
  end;

implementation

function getMem_cb(aSize: PtrUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function allocMem_cb(aSize: PtrUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function reallocMem_cb(aPtr: Pointer; aSize: PtrUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure freeMem_cb(aPtr: Pointer);
begin
  Freemem(aPtr);
end;

function createAllocator: TMemAllocator;
begin
  Result := TMemAllocator.Create(@getMem_cb, @allocMem_cb, @reallocMem_cb, @freeMem_cb);
end;

procedure TTestCase_memAllocator.Test_Create;
var
  LAllocator: IMemAllocator;
begin
  LAllocator := TMemAllocator.Create(@getMem_cb, @allocMem_cb, @reallocMem_cb, @freeMem_cb);
  AssertNotNull('Allocator should be created successfully', LAllocator);
end;

procedure TTestCase_memAllocator.Test_GetMem;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);
    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_AllocMem;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.AllocMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);
    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_ReallocMem;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
  LP:         PByte;
  i:          Integer;
begin
  LAllocator := createAllocator;
  try
    { 1. 边界情况: aPtr = nil, 行为应等同于 GetMem/AllocMem }
    LMem := LAllocator.ReallocMem(nil, 4);
    AssertNotNull('ReallocMem(nil, 4) should allocate new memory', LMem);
    LAllocator.FreeMem(LMem);
    LMem := nil;

    { 2. 核心功能: 重新分配到更大的尺寸 }
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Failed to GetMem(4) for larger reallocation test', LMem);
    LP   := PByte(LMem);
    for i := 0 to 3 do
      LP[i] := i + 1; // 填充数据 {1, 2, 3, 4}

    LMem := LAllocator.ReallocMem(LMem, 8);
    AssertNotNull('ReallocMem to a larger size should succeed', LMem);
    LP   := PByte(LMem);
    // 验证原始数据是否保留
    AssertEquals('Data should be preserved after reallocating to a larger size (Byte 0)', 1, LP[0]);
    AssertEquals('Data should be preserved after reallocating to a larger size (Byte 1)', 2, LP[1]);
    AssertEquals('Data should be preserved after reallocating to a larger size (Byte 2)', 3, LP[2]);
    AssertEquals('Data should be preserved after reallocating to a larger size (Byte 3)', 4, LP[3]);
    LAllocator.FreeMem(LMem);
    LMem := nil;

    { 3. 核心功能: 重新分配到更小的尺寸 }
    LMem := LAllocator.GetMem(8);
    AssertNotNull('Failed to GetMem(8) for smaller reallocation test', LMem);
    LP   := PByte(LMem);
    for i := 0 to 7 do
      LP[i] := i + 1; // 填充数据 {1, 2, 3, 4, 5, 6, 7, 8}

    LMem := LAllocator.ReallocMem(LMem, 4);
    AssertNotNull('ReallocMem to a smaller size should succeed', LMem);
    LP   := PByte(LMem);
    // 验证原始数据是否保留
    AssertEquals('Data should be preserved after reallocating to a smaller size (Byte 0)', 1, LP[0]);
    AssertEquals('Data should be preserved after reallocating to a smaller size (Byte 1)', 2, LP[1]);
    AssertEquals('Data should be preserved after reallocating to a smaller size (Byte 2)', 3, LP[2]);
    AssertEquals('Data should be preserved after reallocating to a smaller size (Byte 3)', 4, LP[3]);
    LAllocator.FreeMem(LMem);
    LMem := nil;

    { 4. 边界情况: 重新分配到尺寸 0, 行为应等同于 FreeMem }
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Failed to GetMem(4) for size-zero reallocation test', LMem);
    LMem := LAllocator.ReallocMem(LMem, 0);
    AssertNull('ReallocMem to size 0 should return nil, effectively freeing the memory', LMem);
    // LMem 此时应为 nil, 无需再次释放

  finally
    // 确保在测试结束时 allocator 被释放
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_FreeMem;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.AllocMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);
    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Copy;
var
  LAllocator: TMemAllocator;
  LMem1:      Pointer;
  LMem2:      Pointer;
  LP:         PByte;
  i:          Integer;
begin
  LAllocator := RtlMemAllocator;
  LMem1      := LAllocator.GetMem(4);
  LMem2      := LAllocator.GetMem(4);
  try
    LP    := PByte(LMem1);
    LP[0] := 0;
    LP[1] := 1;
    LP[2] := 2;
    LP[3] := 3;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aSrc = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Copy(nil, LMem2, 4);
      end);

    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Copy(LMem1, nil, 4);
      end);

    { 异常测试: aSize = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Copy(LMem1, LMem2, 0);
      end);
  {$ENDIF}
  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;

  { 基础测试 }
  LMem1 := LAllocator.GetMem(4);
  LMem2 := LAllocator.GetMem(4);
  try
    LP := PByte(LMem1);
    LP[0] := 0;
    LP[1] := 1;
    LP[2] := 2;
    LP[3] := 3;
    LP := PByte(LMem2);
    LP[0] := 4;
    LP[1] := 5;
    LP[2] := 6;
    LP[3] := 7;

    LAllocator.Copy(LMem1, LMem2, 4);
    AssertEquals(PByte(LMem2)[0], 0);
    AssertEquals(PByte(LMem2)[1], 1);
    AssertEquals(PByte(LMem2)[2], 2);
    AssertEquals(PByte(LMem2)[3], 3);

    { 正向重叠测试 }

    LP := PByte(LMem1);
    LP[0] := 1;
    LP[1] := 2;
    LP[2] := 3;
    LP[3] := 4;
    LAllocator.Copy(LMem1, @LP[1], 2);

    AssertEquals(PByte(LMem1)[0], 1);
    AssertEquals(PByte(LMem1)[1], 1);
    AssertEquals(PByte(LMem1)[2], 2);
    AssertEquals(PByte(LMem1)[3], 4);

    { 反向重叠测试 }

    LP := PByte(LMem1);
    LP[0] := 1;
    LP[1] := 2;
    LP[2] := 3;
    LP[3] := 4;

    LAllocator.Copy(@LP[2], PByte(LMem1) + 1, 2);
    AssertEquals(PByte(LMem1)[0], 1);
    AssertEquals(PByte(LMem1)[1], 3);
    AssertEquals(PByte(LMem1)[2], 4);
    AssertEquals(PByte(LMem1)[3], 4);
  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;

  { 大块内存测试 }

  LMem1 := LAllocator.GetMem(256);
  PByte(LMem1)[0] := 1;
  PByte(LMem1)[1] := 2;
  PByte(LMem1)[2] := 3;
  PByte(LMem1)[3] := 4;

  LMem2 := LAllocator.GetMem(256);
  try
    for i := 0 to 255 do
      PByte(LMem1)[i] := i;

    LAllocator.Copy(LMem1, LMem2, 255);

    for i := 0 to 255 do
      AssertEquals(PByte(LMem2)[i], i);

  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;
end;

procedure TTestCase_memAllocator.Test_CopyUnChecked;
var
  LAllocator: TMemAllocator;
  LMem1, LMem2: Pointer;
  LP: PByte;
  i: Integer;
begin
  { 基本测试 }

  LAllocator := createAllocator;
  try
    LMem1 := LAllocator.GetMem(4);
    LMem2 := LAllocator.GetMem(4);
    try
      LP := PByte(LMem1);
      LP[0] := 0;
      LP[1] := 1;
      LP[2] := 2;
      LP[3] := 3;

      LP := PByte(LMem2);
      LP[0] := 4;
      LP[1] := 5;
      LP[2] := 6;
      LP[3] := 7;

      LAllocator.CopyUnChecked(LMem1, LMem2, 4);

      AssertEquals(LP[0], 0);
      AssertEquals(LP[1], 1);
      AssertEquals(LP[2], 2);
      AssertEquals(LP[3], 3);

      { 正向重叠测试 }

      LP := PByte(LMem1);
      LP[0] := 1;
      LP[1] := 2;
      LP[2] := 3;
      LP[3] := 4;
      LAllocator.CopyUnChecked(LMem1, @LP[1], 2);

      AssertEquals(PByte(LMem1)[0], 1);
      AssertEquals(PByte(LMem1)[1], 1);
      AssertEquals(PByte(LMem1)[2], 2);
      AssertEquals(PByte(LMem1)[3], 4);

      { 反向重叠测试 }

      LP := PByte(LMem1);
      LP[0] := 1;
      LP[1] := 2;
      LP[2] := 3;
      LP[3] := 4;

      LAllocator.CopyUnChecked(@LP[2], PByte(LMem1) + 1, 2);
      AssertEquals(PByte(LMem1)[0], 1);
      AssertEquals(PByte(LMem1)[1], 3);
      AssertEquals(PByte(LMem1)[2], 4);
      AssertEquals(PByte(LMem1)[3], 4);
    finally
      LAllocator.FreeMem(LMem1);
      LAllocator.FreeMem(LMem2);
    end;

    { 大块内存测试 }

    LMem1 := LAllocator.GetMem(256);
    LMem2 := LAllocator.GetMem(256);
    try
      PByte(LMem1)[0] := 1;
      PByte(LMem1)[1] := 2;
      PByte(LMem1)[2] := 3;
      PByte(LMem1)[3] := 4;

      for i := 0 to 255 do
        PByte(LMem1)[i] := i;

      LAllocator.CopyUnChecked(LMem1, LMem2, 255);

      for i := 0 to 255 do
        AssertEquals(PByte(LMem2)[i], i);

    finally
      LAllocator.FreeMem(LMem1);
      LAllocator.FreeMem(LMem2);
    end;

  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_CopyNonOverlap;
var
  LAllocator: TMemAllocator;
  LMem1:      Pointer;
  LMem2:      Pointer;
  LP:         PByte;
  i:          Integer;
begin
  LAllocator := RtlMemAllocator;
  LMem1      := LAllocator.GetMem(4);
  LMem2      := LAllocator.GetMem(4);
  try
    LP    := PByte(LMem1);
    LP[0] := 0;
    LP[1] := 1;
    LP[2] := 2;
    LP[3] := 3;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aSrc = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.CopyNonOverlap(nil, LMem2, 4);
      end);

    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.CopyNonOverlap(LMem1, nil, 4);
      end);

    { 异常测试: aSize = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.CopyNonOverlap(LMem1, LMem2, 0);
      end);
  {$ENDIF}
  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;

  { 基础测试 }
  LMem1 := LAllocator.GetMem(4);
  LMem2 := LAllocator.GetMem(4);
  try
    LP := PByte(LMem1);
    LP[0] := 0;
    LP[1] := 1;
    LP[2] := 2;
    LP[3] := 3;
    LP := PByte(LMem2);
    LP[0] := 4;
    LP[1] := 5;
    LP[2] := 6;
    LP[3] := 7;

    LAllocator.CopyNonOverlap(LMem1, LMem2, 4);
    AssertEquals(PByte(LMem2)[0], 0);
    AssertEquals(PByte(LMem2)[1], 1);
    AssertEquals(PByte(LMem2)[2], 2);
    AssertEquals(PByte(LMem2)[3], 3);

  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;

  { 大块内存测试 }

  LMem1 := LAllocator.GetMem(256);
  PByte(LMem1)[0] := 1;
  PByte(LMem1)[1] := 2;
  PByte(LMem1)[2] := 3;
  PByte(LMem1)[3] := 4;

  LMem2 := LAllocator.GetMem(256);
  try
    for i := 0 to 255 do
      PByte(LMem1)[i] := i;

    LAllocator.CopyNonOverlap(LMem1, LMem2, 255);

    for i := 0 to 255 do
      AssertEquals(PByte(LMem2)[i], i);

  finally
    LAllocator.FreeMem(LMem1);
    LAllocator.FreeMem(LMem2);
  end;
end;

procedure TTestCase_memAllocator.Test_CopyNonOverlapUnChecked;
var
  LAllocator: TMemAllocator;
  LMem1, LMem2: Pointer;
  LP: PByte;
  i: Integer;
begin
  { 基本测试 }

  LAllocator := createAllocator;
  try
    LMem1 := LAllocator.GetMem(4);
    LMem2 := LAllocator.GetMem(4);
    try
      LP := PByte(LMem1);
      LP[0] := 0;
      LP[1] := 1;
      LP[2] := 2;
      LP[3] := 3;

      LP := PByte(LMem2);
      LP[0] := 4;
      LP[1] := 5;
      LP[2] := 6;
      LP[3] := 7;

      LAllocator.CopyNonOverlapUnChecked(LMem1, LMem2, 4);

      AssertEquals(LP[0], 0);
      AssertEquals(LP[1], 1);
      AssertEquals(LP[2], 2);
      AssertEquals(LP[3], 3);

    finally
      LAllocator.FreeMem(LMem1);
      LAllocator.FreeMem(LMem2);
    end;

    { 大块内存测试 }

    LMem1 := LAllocator.GetMem(256);
    LMem2 := LAllocator.GetMem(256);
    try
      PByte(LMem1)[0] := 1;
      PByte(LMem1)[1] := 2;
      PByte(LMem1)[2] := 3;
      PByte(LMem1)[3] := 4;

      for i := 0 to 255 do
        PByte(LMem1)[i] := i;

      LAllocator.CopyNonOverlapUnChecked(LMem1, LMem2, 255);

      for i := 0 to 255 do
        AssertEquals(PByte(LMem2)[i], i);

    finally
      LAllocator.FreeMem(LMem1);
      LAllocator.FreeMem(LMem2);
    end;

  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Fill;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Fill(nil, 4, 0);
      end);

    { 异常测试: aCount = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Fill(LMem, 0, 0);
      end);
    {$ENDIF}

    LAllocator.Fill(LMem, 4, 0);
    AssertTrue(PByte(LMem)[0] = 0);
    AssertTrue(PByte(LMem)[1] = 0);
    AssertTrue(PByte(LMem)[2] = 0);
    AssertTrue(PByte(LMem)[3] = 0);

    LAllocator.Fill(LMem, 4, 1);
    AssertTrue(PByte(LMem)[0] = 1);
    AssertTrue(PByte(LMem)[1] = 1);
    AssertTrue(PByte(LMem)[2] = 1);
    AssertTrue(PByte(LMem)[3] = 1);

    LAllocator.FreeMem(LMem); 
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Fill8;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Fill8(nil, 4, 0);
      end);

    { 异常测试: aCount = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Fill8(LMem, 0, 0);
      end);
    {$ENDIF}

    LAllocator.Fill8(LMem, 4, 0);
    AssertTrue(PByte(LMem)[0] = 0);
    AssertTrue(PByte(LMem)[1] = 0);
    AssertTrue(PByte(LMem)[2] = 0);
    AssertTrue(PByte(LMem)[3] = 0);

    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Fill16;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(8);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Fill16(nil, 4, 0);
      end);

    { 异常测试: aCount = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Fill16(LMem, 0, 0);
      end);
    {$ENDIF}

    LAllocator.Fill16(LMem, 4, 0);
    AssertTrue(PWord(LMem)[0] = 0);
    AssertTrue(PWord(LMem)[1] = 0);
    AssertTrue(PWord(LMem)[2] = 0);
    AssertTrue(PWord(LMem)[3] = 0);

    LAllocator.Fill16(LMem, 4, 65535);
    AssertTrue(PWord(LMem)[0] = 65535);
    AssertTrue(PWord(LMem)[1] = 65535);
    AssertTrue(PWord(LMem)[2] = 65535);
    AssertTrue(PWord(LMem)[3] = 65535);

    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Fill32;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(16);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }  
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Fill32(nil, 4, 0);
      end);

    { 异常测试: aCount = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Fill32(LMem, 0, 0);
      end);
    {$ENDIF}

    LAllocator.Fill32(LMem, 4, 0);
    AssertTrue(PLongWord(LMem)[0] = 0);
    AssertTrue(PLongWord(LMem)[1] = 0);
    AssertTrue(PLongWord(LMem)[2] = 0);
    AssertTrue(PLongWord(LMem)[3] = 0);

    LAllocator.Fill32(LMem, 4, 4294967295);
    AssertTrue(PLongWord(LMem)[0] = 4294967295);
    AssertTrue(PLongWord(LMem)[1] = 4294967295);
    AssertTrue(PLongWord(LMem)[2] = 4294967295);
    AssertTrue(PLongWord(LMem)[3] = 4294967295);

    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_Fill64;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(32);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Fill64(nil, 4, 0);
      end);

    { 异常测试: aCount = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Fill64(LMem, 0, 0);
      end);
    {$ENDIF}

    LAllocator.Fill64(LMem, 4, 0);
    AssertTrue(PInt64(LMem)[0] = 0);
    AssertTrue(PInt64(LMem)[1] = 0);
    AssertTrue(PInt64(LMem)[2] = 0);
    AssertTrue(PInt64(LMem)[3] = 0);

    LAllocator.Fill64(LMem, 4, 9223372036854775807);
    AssertTrue(PInt64(LMem)[0] = 9223372036854775807);
    AssertTrue(PInt64(LMem)[1] = 9223372036854775807);
    AssertTrue(PInt64(LMem)[2] = 9223372036854775807);
    AssertTrue(PInt64(LMem)[3] = 9223372036854775807);

    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_zero;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := createAllocator;
  try
    LMem := LAllocator.GetMem(4);
    AssertNotNull('Memory should be allocated successfully', LMem);

    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    { 异常测试: aDst = nil }
    AssertException(
      'exception should be raised: ENil',
      ENil,
      procedure
      begin
        LAllocator.Zero(nil, 4);
      end);

    { 异常测试: aSize = 0 }
    AssertException(
      'exception should be raised: EIsZero',
      EIsZero,
      procedure
      begin
        LAllocator.Zero(LMem, 0);
      end);
    {$ENDIF}

    LAllocator.Zero(LMem, 4);
    AssertTrue(PByte(LMem)[0] = 0);
    AssertTrue(PByte(LMem)[1] = 0);
    AssertTrue(PByte(LMem)[2] = 0);
    AssertTrue(PByte(LMem)[3] = 0);

    PByte(LMem)[0] := 1;
    PByte(LMem)[1] := 2;
    PByte(LMem)[2] := 3;
    PByte(LMem)[3] := 4;

    LAllocator.Zero(LMem, 4);
    AssertTrue(PByte(LMem)[0] = 0);
    AssertTrue(PByte(LMem)[1] = 0);
    AssertTrue(PByte(LMem)[2] = 0);
    AssertTrue(PByte(LMem)[3] = 0);

    LAllocator.FreeMem(LMem);
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_memAllocator.Test_RtlMemAllocator;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
begin
  LAllocator := RtlMemAllocator;
  LMem := LAllocator.AllocMem(4);
  AssertNotNull('Memory should be allocated successfully', LMem);
  LAllocator.FreeMem(LMem);
end;

procedure TTestCase_memAllocator.Test_IsOverlap;
var
  LAllocator: TMemAllocator;
  LMem:       Pointer;
  LPtr1, LPtr2, LPtr3: Pointer;
begin
  LAllocator := RtlMemAllocator;
  LMem       := LAllocator.GetMem(100); // Allocate a 100-byte block for testing
  try
    LPtr1 := LMem;
    LPtr2 := PByte(LMem) + 10;
    LPtr3 := PByte(LMem) + 30;

    // Case 1: No overlap
    AssertFalse('Case 1.1: No overlap, block1 before block2', LAllocator.IsOverlap(LPtr1, SizeUInt(10), LPtr2, SizeUInt(20)));
    AssertFalse('Case 1.2: No overlap, block2 before block1', LAllocator.IsOverlap(LPtr2, SizeUInt(20), LPtr1, SizeUInt(10)));

    // Case 2: Touching boundaries (not overlapping)
    AssertFalse('Case 2.1: Touching boundaries, block1 ends where block2 begins', LAllocator.IsOverlap(LPtr1, SizeUInt(10), LPtr2, SizeUInt(20)));
    AssertFalse('Case 2.2: Touching boundaries, block2 ends where block1 begins', LAllocator.IsOverlap(LPtr2, SizeUInt(20), LPtr1, SizeUInt(10)));

    // Case 3: Partial overlap
    AssertTrue('Case 3.1: Partial overlap, block1 overlaps beginning of block2', LAllocator.IsOverlap(LPtr1, SizeUInt(15), LPtr2, SizeUInt(20)));
    AssertTrue('Case 3.2: Partial overlap, block2 overlaps beginning of block1', LAllocator.IsOverlap(LPtr2, SizeUInt(20), LPtr1, SizeUInt(15)));
    AssertTrue('Case 3.3: Partial overlap, block1 overlaps end of block2', LAllocator.IsOverlap(LPtr2, SizeUInt(15), LPtr1, SizeUInt(20)));
    AssertTrue('Case 3.4: Partial overlap, block2 overlaps end of block1', LAllocator.IsOverlap(LPtr1, SizeUInt(20), LPtr2, SizeUInt(15)));

    // Case 4: Complete overlap (one block inside another)
    AssertTrue('Case 4.1: Complete overlap, block2 is inside block1', LAllocator.IsOverlap(LPtr1, SizeUInt(30), LPtr2, SizeUInt(10)));
    AssertTrue('Case 4.2: Complete overlap, block1 is inside block2', LAllocator.IsOverlap(LPtr2, SizeUInt(10), LPtr1, SizeUInt(30)));

    // Case 5: Identical blocks
    AssertTrue('Case 5.1: Identical blocks', LAllocator.IsOverlap(LPtr1, SizeUInt(10), LPtr1, SizeUInt(10)));

    // Case 6: Overlap with a third block
    AssertTrue('Case 6.1: Overlap with a third block', LAllocator.IsOverlap(LPtr1, SizeUInt(25), LPtr2, SizeUInt(10)));
    AssertFalse('Case 6.2: No overlap with a third block', LAllocator.IsOverlap(LPtr1, SizeUInt(5), LPtr3, SizeUInt(10)));

    // Case 7: Edge cases with zero size
    AssertFalse('Case 7.1: Zero-size block1 should not overlap', LAllocator.IsOverlap(LPtr1, SizeUInt(0), LPtr2, SizeUInt(10)));
    AssertFalse('Case 7.2: Zero-size block2 should not overlap', LAllocator.IsOverlap(LPtr1, SizeUInt(10), LPtr2, SizeUInt(0)));
    AssertFalse('Case 7.3: Two zero-size blocks at the same address are not considered overlapping', LAllocator.IsOverlap(LPtr1, SizeUInt(0), LPtr1, SizeUInt(0)));

  finally
    LAllocator.FreeMem(LMem);
  end;
end;

initialization

  RegisterTest(TTestCase_memAllocator);
end.

