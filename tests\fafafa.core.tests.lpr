program fafafa.core.tests;

{$mode objfpc}{$H+}

uses
  Classes,
  consoletestrunner,
  // Add your test units here
  test_math,
  test_base,
  test_mem_utils,
  test_mem_allocator,
  test_collections.elementManager,
  test_collections.arr,
  //test_collections.deque,       // TDeque测试
  test_collections.vec;         // 完整的TVec测试
  // test_performance;             // 性能基准测试 - 暂停开发，专注于核心实现优化
  // test_fs_basic;                // 文件系统基础测试 - 暂时注释，需要进一步调试

var
  Application: TTestRunner;

begin
  DefaultRunAllTests := True;
  DefaultFormat := fPlain;
  Application := TTestRunner.Create(nil);
  Application.Initialize;
  Application.Title := 'fafafa.core Test Suite';
  Application.Run;
  Application.Free;
end. 
 
 
 
 
