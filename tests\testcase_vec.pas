unit testcase_vec;

{$mode objfpc}{$H+}
{$I ../src/fafafa.collections.settings.inc}

interface

uses
  Classes, SysUtils, fpcunit, testregistry,
  fafafa.collections;

type

  { TTestCase_vec }

  TTestCase_vec= class(TTestCase)
  published
    { 构造函数 }
    procedure Test_Create;
    procedure Test_Create_Allocator;
    procedure Test_Create_Capacity;
    procedure Test_Create_Capacity_GrowSize;
    procedure Test_Create_Allocator_Capacity;
    procedure Test_Create_Allocator_Capacity_GrowSize;
    procedure Test_Create_Array;
    procedure Test_Create_Array_Allocator;
    procedure Test_Create_Array_Allocator_GrowSize;
    procedure Test_Create_Collection;
    procedure Test_Create_Collection_Allocator;
    procedure Test_Create_Collection_Allocator_GrowSize;

    { ICollection }
    procedure Test_GetAllocator;
    procedure Test_GetCount;
    procedure Test_IsEmpty;
    procedure Test_GetData;
    procedure Test_SetData;
    procedure Test_Clear;
    procedure Test_Clone;
    procedure Test_LoadFromCollection;
    procedure Test_SaveToCollection;
    procedure Test_LoadFromMemory;
    procedure Test_AppendFromMemory;
    procedure Test_AppendFromCollection;
    procedure Test_AppendToCollection;
    procedure Test_WriteToArrayMemory;

    { IGenericCollection }
    procedure Test_GetEnumerator;
    procedure Test_GetElementSize;
    procedure Test_GetIsManagedType;
    procedure Test_GetElementAllocator;
    procedure Test_LoadFromArray;
    procedure Test_AppendFromArray;
    procedure Test_ToArray;

    { IArray }
    procedure Test_Get;
    procedure Test_Put;
    procedure Test_GetMemory;
    procedure Test_GetPtr;
    procedure Test_Resize;
    procedure Test_Ensure;
    procedure Test_WriteFromMemory;
    procedure Test_WriteFromArray;
    procedure Test_ReadToMemory;
    procedure Test_ReadToArray;
    procedure Test_Fill1;
    procedure Test_Fill2;
    procedure Test_Fill3;
    procedure Test_Zero;
    procedure Test_Zero2;
    procedure Test_Zero3;
    procedure Test_Swap;
    procedure Test_Swap2;
    procedure Test_Copy;

    { IStack }
    procedure Test_PushFromMemory;
    procedure Test_PushFromMemory2;
    procedure Test_Push;
    procedure Test_PushFromArray;
    procedure Test_PopToMemory;
    procedure Test_PopToMemory2;
    procedure Test_Pop;
    procedure Test_Pop2;
    procedure Test_PopToArray;
    procedure Test_PeekMemory;
    procedure Test_PeekMemory2;
    procedure Test_PeekReadMemory;
    procedure Test_PeekReadMemory2;
    procedure Test_Peek;
    procedure Test_Peek2;
    procedure Test_PeekToArray;

    { IVec }
    procedure Test_GetCapacity;
    procedure Test_SetCapacity;
    procedure Test_SetCapacity2;
    procedure Test_GetGrowSize;
    procedure Test_SetGrowSize;
    procedure Test_ResizeExact;
    procedure Test_TryReserve;
    procedure Test_Reserve;
    procedure Test_TryReserveExact;
    procedure Test_ReserveExact;
    procedure Test_Shrink;
    procedure Test_ShrinkTo;
    procedure Test_Truncate;
    procedure Test_InsertFromMemory;
    procedure Test_InsertFromMemory2;
    procedure Test_Insert;
    procedure Test_InsertFromArray;
    procedure Test_InsertFromCollection;
    procedure Test_InsertFromCollection2;

    procedure Test_WriteFromMemoryExact;
    procedure Test_WriteFromArrayExact;
    procedure Test_WriteFromArrayExact_TArray;
    procedure Test_ReadToMemoryExact;
    procedure Test_ReadToArrayExact;
    procedure Test_Peek_Element;
    procedure Test_PushPtr_ElementCount;
    procedure Test_PushPtr;
    procedure Test_Push_Elements;
    procedure Test_PopPtr_ElementCount;
    procedure Test_PopPtr;
    procedure Test_Pop_Array;
    procedure Test_PeekPtr_ElementCount;
    procedure Test_PeekPtr;
    procedure Test_Peek_Read_ptr_ElementCount;
    procedure Test_Peek_Read_ptr;
    procedure Test_Delete_Index_ElementCount;
    procedure Test_Delete_Index;
    procedure Test_DeleteSwap_Index_ElementCount;
    procedure Test_DeleteSwap_Index;
    procedure Test_RemovePtr_Index_ElementCount_Ptr;
    procedure Test_RemovePtr_Index_Ptr;
    procedure Test_RemovePtrSwap_Index_ElementCount_Ptr;
    procedure Test_RemovePtrSwap_Index_Ptr;
    procedure Test_Remove_Index_ElementCount_Elements;
    procedure Test_Remove_Index_Element;
    procedure Test_Remove_Index;
    procedure Test_RemoveSwap_Index_ElementCount_Elements;
    procedure Test_RemoveSwap_Index_Element;
    procedure Test_RemoveSwap_Index;
    procedure Test_FromTArray;
    procedure Test_WriteCollection;

  end;

implementation

function TestGetMem(aSize: SizeUInt): Pointer;
begin
  Result := GetMem(aSize);
end;

function TestAllocMem(aSize: SizeUInt): Pointer;
begin
  Result := AllocMem(aSize);
end;

function TestReallocMem(aPtr: Pointer; aSize: SizeUInt): Pointer;
begin
  Result := ReallocMem(aPtr, aSize);
end;

procedure TestFreeMem(aPtr: Pointer);
begin
  FreeMem(aPtr);
end;

procedure TTestCase_vec.Test_Create_Allocator_Capacity_GrowSize;
var
  LAllocator: TMemAllocator;
  LIntVec:    specialize IVec<Integer>;
  LStrVec:    specialize IVec<String>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntVec := specialize TVec<Integer>.Create(LAllocator, 12, 24);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 12);
    AssertEquals(Format('Initial grow size mismatch: expected 24, got %d.', [LIntVec.GetGrowSize]), LIntVec.GetGrowSize, 24);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);

    ///
    /// 托管元素
    ///

    LStrVec := specialize TVec<String>.Create(LAllocator, 12, 24);
    AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 12);
    AssertEquals(Format('Initial grow size mismatch: expected 24, got %d.', [LStrVec.GetGrowSize]), LStrVec.GetGrowSize, 24);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);

    LIntVec := nil;
    LStrVec := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Allocator_Capacity;
var
  LAllocator: TMemAllocator;
  LIntVec:    specialize IVec<Integer>;
  LStrVec:    specialize IVec<String>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntVec       := specialize TVec<Integer>.Create(LAllocator, 12);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 12);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);

    ///
    /// 托管元素
    ///

    LStrVec := specialize TVec<String>.Create(LAllocator, 12);
    AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 12);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);

    LIntVec := nil;
    LStrVec := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Allocator;
var
  LAllocator: TMemAllocator;
  LIntVec:    specialize IVec<Integer>;
  LStrVec:    specialize IVec<String>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntVec       := specialize TVec<Integer>.Create(LAllocator);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory = nil));
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected %d, got %d.', [VEC_DEFAULT_CAPACITY,LIntVec.GetCapacity]), LIntVec.GetCapacity, VEC_DEFAULT_CAPACITY);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);

    ///
    /// 托管类型
    ///

    LStrVec := specialize TVec<String>.Create(LAllocator);
    AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
    AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected %d, got %d.', [VEC_DEFAULT_CAPACITY,LStrVec.GetCapacity]), LStrVec.GetCapacity, VEC_DEFAULT_CAPACITY);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);

    LIntVec := nil;
    LStrVec := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Capacity_GrowSize;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(12, 24);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 12);
  AssertEquals(Format('Initial grow size mismatch: expected 24, got %d.', [LIntVec.GetGrowSize]), LIntVec.GetGrowSize, 24);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(12, 24);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 12);
  AssertEquals(Format('Initial grow size mismatch: expected 24, got %d.', [LStrVec.GetGrowSize]), LStrVec.GetGrowSize, 24);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);

end;

procedure TTestCase_vec.Test_Create_Capacity;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(12);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 12);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(12);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 12, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 12);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);
end;

procedure TTestCase_vec.Test_Create;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory = nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected %d, got %d.', [VEC_DEFAULT_CAPACITY,LIntVec.GetCapacity]), LIntVec.GetCapacity, VEC_DEFAULT_CAPACITY);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create;
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory = nil));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected %d, got %d.', [VEC_DEFAULT_CAPACITY,LStrVec.GetCapacity]), LStrVec.GetCapacity, VEC_DEFAULT_CAPACITY);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);
end;

procedure TTestCase_vec.Test_Create_Array_Allocator_GrowSize;
var
  LAllocator: TMemAllocator;
  LIntVec:    specialize IVec<Integer>;
  LStrVec:    specialize IVec<String>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8],LAllocator,32);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected 32, got %d.', [LIntVec.GetGrowSize]), LIntVec.GetGrowSize, 32);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec.Items[0]]), 1, LIntVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec.Items[1]]), 2, LIntVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec.Items[2]]), 3, LIntVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Items[3]]), 4, LIntVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec.Items[4]]), 5, LIntVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec.Items[5]]), 6, LIntVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec.Items[6]]), 7, LIntVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Items[7]]), 8, LIntVec.Items[7]);

    ///
    /// 托管类型
    ///

    LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8'],LAllocator,32);
    AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected 32, got %d.', [LStrVec.GetGrowSize]), LStrVec.GetGrowSize, 32);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec.Items[0]]), '1', LStrVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec.Items[1]]), '2', LStrVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec.Items[2]]), '3', LStrVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec.Items[3]]), '4', LStrVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec.Items[4]]), '5', LStrVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec.Items[5]]), '6', LStrVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec.Items[6]]), '7', LStrVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %s.', [LStrVec.Items[7]]), '8', LStrVec.Items[7]);

    LIntVec := nil;
    LStrVec := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Array_Allocator;
var
  LAllocator: TMemAllocator;
  LIntVec:    specialize IVec<Integer>;
  LStrVec:    specialize IVec<String>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8],LAllocator);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec.Items[0]]), 1, LIntVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec.Items[1]]), 2, LIntVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec.Items[2]]), 3, LIntVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Items[3]]), 4, LIntVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec.Items[4]]), 5, LIntVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec.Items[5]]), 6, LIntVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec.Items[6]]), 7, LIntVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Items[7]]), 8, LIntVec.Items[7]);

    ///
    /// 托管类型
    ///

    LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8'],LAllocator);
    AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec.Items[0]]), '1', LStrVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec.Items[1]]), '2', LStrVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec.Items[2]]), '3', LStrVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec.Items[3]]), '4', LStrVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec.Items[4]]), '5', LStrVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec.Items[5]]), '6', LStrVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec.Items[6]]), '7', LStrVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %s.', [LStrVec.Items[7]]), '8', LStrVec.Items[7]);

    LIntVec := nil;
    LStrVec := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Array;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 8);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec.Items[0]]), 1, LIntVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec.Items[1]]), 2, LIntVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec.Items[2]]), 3, LIntVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Items[3]]), 4, LIntVec.Items[3]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec.Items[4]]), 5, LIntVec.Items[4]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec.Items[5]]), 6, LIntVec.Items[5]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec.Items[6]]), 7, LIntVec.Items[6]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Items[7]]), 8, LIntVec.Items[7]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 8);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec.Items[0]]), '1', LStrVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec.Items[1]]), '2', LStrVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec.Items[2]]), '3', LStrVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec.Items[3]]), '4', LStrVec.Items[3]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec.Items[4]]), '5', LStrVec.Items[4]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec.Items[5]]), '6', LStrVec.Items[5]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec.Items[6]]), '7', LStrVec.Items[6]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %s.', [LStrVec.Items[7]]), '8', LStrVec.Items[7]);

end;

procedure TTestCase_vec.Test_Create_Collection_Allocator_GrowSize;
var
  LAllocator: TMemAllocator;
  LIntArray:  specialize IArray<Integer>;
  LIntVec:    specialize IVec<Integer>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LIntArray := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
    LIntVec   := specialize TVec<Integer>.Create(LIntArray as specialize TGenericCollection<Integer>, LAllocator, 32);
    AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected 32, got %d.', [LIntVec.GetGrowSize]), LIntVec.GetGrowSize, 32);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec.Items[0]]), 1, LIntVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec.Items[1]]), 2, LIntVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec.Items[2]]), 3, LIntVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Items[3]]), 4, LIntVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec.Items[4]]), 5, LIntVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec.Items[5]]), 6, LIntVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec.Items[6]]), 7, LIntVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Items[7]]), 8, LIntVec.Items[7]);

    LIntArray := nil;
    LIntVec   := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Collection_Allocator;
var
  LAllocator: TMemAllocator;
  LArray:     specialize IArray<Integer>;
  LVec:       specialize IVec<Integer>;
begin
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  try
    LArray := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
    LVec   := specialize TVec<Integer>.Create(LArray as specialize TGenericCollection<Integer>, LAllocator);
    AssertNotNull('Vector creation failed: LVec is nil.', LVec);
    AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LVec.Memory <> nil));
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LVec.Count]), 8, LVec.Count);
    AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LVec.GetCapacity]), LVec.GetCapacity, 8);
    AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LVec.GetGrowSize]), LVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
    AssertTrue('Allocator should be correctly assigned, but it is not.', LVec.GetAllocator = LAllocator);
    AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LVec.Items[0]]), 1, LVec.Items[0]);
    AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LVec.Items[1]]), 2, LVec.Items[1]);
    AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LVec.Items[2]]), 3, LVec.Items[2]);
    AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LVec.Items[3]]), 4, LVec.Items[3]);
    AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LVec.Items[4]]), 5, LVec.Items[4]);
    AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LVec.Items[5]]), 6, LVec.Items[5]);
    AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LVec.Items[6]]), 7, LVec.Items[6]);
    AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LVec.Items[7]]), 8, LVec.Items[7]);

    LArray := nil;
    LVec   := nil;
  finally
    LAllocator.Free;
  end;
end;

procedure TTestCase_vec.Test_Create_Collection;
var
  LIntArray:  specialize IArray<Integer>;
  LIntVec:    specialize IVec<Integer>;
  LIntVec2:   specialize IVec<Integer>;
  LStrArray:  specialize IArray<String>;
  LStrVec:    specialize IVec<String>;
  LStrVec2:   specialize IVec<String>;
  LAllocator: TMemAllocator;
begin
  { 从泛型数组构造 }
  LIntArray := specialize TArray<Integer>.Create([11,22,33,44,55,66,77,88]);
  LIntVec   := specialize TVec<Integer>.Create(LIntArray as specialize TGenericCollection<Integer>);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 8);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 11, got %d.', [LIntVec.Items[0]]), 11, LIntVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 22, got %d.', [LIntVec.Items[1]]), 22, LIntVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 33, got %d.', [LIntVec.Items[2]]), 33, LIntVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 44, got %d.', [LIntVec.Items[3]]), 44, LIntVec.Items[3]);
  AssertEquals(Format('Initial element count mismatch: expected 55, got %d.', [LIntVec.Items[4]]), 55, LIntVec.Items[4]);
  AssertEquals(Format('Initial element count mismatch: expected 66, got %d.', [LIntVec.Items[5]]), 66, LIntVec.Items[5]);
  AssertEquals(Format('Initial element count mismatch: expected 77, got %d.', [LIntVec.Items[6]]), 77, LIntVec.Items[6]);
  AssertEquals(Format('Initial element count mismatch: expected 88, got %d.', [LIntVec.Items[7]]), 88, LIntVec.Items[7]);

  { 从泛型Vec构造 }
  LIntVec2 := specialize TVec<Integer>.Create([100,101,102,103]);
  LIntVec  := specialize TVec<Integer>.Create(LIntVec2 as specialize TGenericCollection<Integer>);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Count]), 4, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = RtlMemAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %d.', [LIntVec.Items[0]]), 100, LIntVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %d.', [LIntVec.Items[1]]), 101, LIntVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %d.', [LIntVec.Items[2]]), 102, LIntVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %d.', [LIntVec.Items[3]]), 103, LIntVec.Items[3]);

  { 从泛型Vec构造, 指定分配器 }
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LIntVec2   := specialize TVec<Integer>.Create([100,101,102,103]);
  LIntVec    := specialize TVec<Integer>.Create(LIntVec2 as specialize TGenericCollection<Integer>, LAllocator);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Count]), 4, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %d.', [LIntVec.Items[0]]), 100, LIntVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %d.', [LIntVec.Items[1]]), 101, LIntVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %d.', [LIntVec.Items[2]]), 102, LIntVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %d.', [LIntVec.Items[3]]), 103, LIntVec.Items[3]);

  LIntVec2 := nil;
  LIntVec  := nil;
  LAllocator.Free;

  { 从泛型Vec构造, 指定源容器分配器 }
  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LIntVec2   := specialize TVec<Integer>.Create([100,101,102,103], LAllocator);
  LIntVec    := specialize TVec<Integer>.Create(LIntVec2 as specialize TGenericCollection<Integer>);
  AssertNotNull('Vector creation failed: LVec is nil.', LIntVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec.Count]), 4, LIntVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LIntVec.GetCapacity]), LIntVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LIntVec.GetGrowSize]), LIntVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LIntVec.GetAllocator = LAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %d.', [LIntVec.Items[0]]), 100, LIntVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %d.', [LIntVec.Items[1]]), 101, LIntVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %d.', [LIntVec.Items[2]]), 102, LIntVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %d.', [LIntVec.Items[3]]), 103, LIntVec.Items[3]);
  LIntVec2 := nil;
  LIntVec  := nil;
  LAllocator.Free;

  ///
  /// 托管类型
  ///

  { 从泛型数组构造 }
  LStrArray := specialize TArray<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec   := specialize TVec<String>.Create(LStrArray as specialize TGenericCollection<String>);
  AssertNotNull('Vector creation failed: LVecStr is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 8);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);

  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertEquals(Format('Initial element count mismatch: expected 11, got %s.', [LStrVec.Items[0]]), '11', LStrVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 22, got %s.', [LStrVec.Items[1]]), '22', LStrVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 33, got %s.', [LStrVec.Items[2]]), '33', LStrVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 44, got %s.', [LStrVec.Items[3]]), '44', LStrVec.Items[3]);
  AssertEquals(Format('Initial element count mismatch: expected 55, got %s.', [LStrVec.Items[4]]), '55', LStrVec.Items[4]);
  AssertEquals(Format('Initial element count mismatch: expected 66, got %s.', [LStrVec.Items[5]]), '66', LStrVec.Items[5]);
  AssertEquals(Format('Initial element count mismatch: expected 77, got %s.', [LStrVec.Items[6]]), '77', LStrVec.Items[6]);
  AssertEquals(Format('Initial element count mismatch: expected 88, got %s.', [LStrVec.Items[7]]), '88', LStrVec.Items[7]);

  { 从泛型Vec构造 }
  LStrVec2 := specialize TVec<String>.Create(['100','101','102','103']);
  LStrVec  := specialize TVec<String>.Create(LStrVec2 as specialize TGenericCollection<String>);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LStrVec.Count]), 4, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = RtlMemAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %s.', [LStrVec.Items[0]]), '100', LStrVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %s.', [LStrVec.Items[1]]), '101', LStrVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %s.', [LStrVec.Items[2]]), '102', LStrVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %s.', [LStrVec.Items[3]]), '103', LStrVec.Items[3]);

  { 从泛型Vec构造, 指定分配器 }

  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LStrVec2   := specialize TVec<String>.Create(['100','101','102','103'], LAllocator);
  LStrVec    := specialize TVec<String>.Create(LStrVec2 as specialize TGenericCollection<String>);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LStrVec.Count]), 4, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %s.', [LStrVec.Items[0]]), '100', LStrVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %s.', [LStrVec.Items[1]]), '101', LStrVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %s.', [LStrVec.Items[2]]), '102', LStrVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %s.', [LStrVec.Items[3]]), '103', LStrVec.Items[3]);
  LStrVec2 := nil;
  LStrVec  := nil;
  LAllocator.Free;

  { 从泛型Vec构造, 指定源容器分配器 }

  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LStrVec2   := specialize TVec<String>.Create(['100','101','102','103'], LAllocator);
  LStrVec    := specialize TVec<String>.Create(LStrVec2 as specialize TGenericCollection<String>);
  AssertNotNull('Vector creation failed: LVec is nil.', LStrVec);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVec.Memory <> nil));
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LStrVec.Count]), 4, LStrVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 4, got %d.', [LStrVec.GetCapacity]), LStrVec.GetCapacity, 4);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LStrVec.GetGrowSize]), LStrVec.GetGrowSize, VEC_DEFAULT_GROW_SIZE);
  AssertTrue('Allocator should be correctly assigned, but it is not.', LStrVec.GetAllocator = LAllocator);
  AssertEquals(Format('Initial element count mismatch: expected 100, got %s.', [LStrVec.Items[0]]), '100', LStrVec.Items[0]);
  AssertEquals(Format('Initial element count mismatch: expected 101, got %s.', [LStrVec.Items[1]]), '101', LStrVec.Items[1]);
  AssertEquals(Format('Initial element count mismatch: expected 102, got %s.', [LStrVec.Items[2]]), '102', LStrVec.Items[2]);
  AssertEquals(Format('Initial element count mismatch: expected 103, got %s.', [LStrVec.Items[3]]), '103', LStrVec.Items[3]);
  LStrVec2 := nil;
  LStrVec  := nil;
  LAllocator.Free;
end;

procedure TTestCase_vec.Test_GetAllocator;
var
  LAllocator: TMemAllocator;
  LVec:       specialize IVec<Integer>;
begin
  LAllocator := RtlMemAllocator;
  LVec := specialize TVec<Integer>.Create(LAllocator);
  AssertTrue('Allocator should match the one used in creation', LAllocator = LVec.GetAllocator);

  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LVec := specialize TVec<Integer>.Create(LAllocator);
  AssertTrue('Allocator should match the one used in creation', LAllocator = LVec.GetAllocator);

  LVec := nil;
  LAllocator.Free;
end;

procedure TTestCase_vec.Test_GetCount;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);

  { 变化一下 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8,9,10]);
  AssertEquals(Format('Initial element count mismatch: expected 10, got %d.', [LIntVec.Count]), 10, LIntVec.Count);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);

  { 变化一下 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88','99','100']);
  AssertEquals(Format('Initial element count mismatch: expected 10, got %d.', [LStrVec.Count]), 10, LStrVec.Count);
end;

procedure TTestCase_vec.Test_IsEmpty;
var
  LVec: specialize IVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  AssertTrue('Vector should be empty', LVec.IsEmpty);

  { 失败测试: 非空 }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('Vector should not be empty', LVec.IsEmpty);

  LVec := nil;
end;

procedure TTestCase_vec.Test_GetData;
var
  LVec: specialize IVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  AssertNull('Newly created vector should have null extra data', LVec.GetData);
  LVec.Data := Pointer(888);
  AssertTrue('Extra data should be correctly set', LVec.GetData = Pointer(888));
end;

procedure TTestCase_vec.Test_SetData;
var
  LVec: specialize IVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create;
  AssertNull('Newly created vector should have null extra data', LVec.GetData);
  LVec.SetData(Pointer(888));
  AssertTrue('Extra data should be correctly set', LVec.GetData = Pointer(888));
end;

procedure TTestCase_vec.Test_Clear;
var
  LVec: specialize IVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LVec.Count]), 8, LVec.Count);
  LVec.Clear;
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LVec.Count]), 0, LVec.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LVec.GetCapacity]), 8, LVec.GetCapacity);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [VEC_DEFAULT_GROW_SIZE,LVec.GetGrowSize]), VEC_DEFAULT_GROW_SIZE, LVec.GetGrowSize);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LVec.Memory <> nil));
end;

procedure TTestCase_vec.Test_Clone;
var
  LIntVec:      specialize IVec<Integer>;
  LIntVecClone: specialize IVec<Integer>;
  LStrVec:      specialize IVec<String>;
  LStrVecClone: specialize IVec<String>;
begin
  { 空vec克隆 }
  LIntVec      := specialize TVec<Integer>.Create;
  LIntVecClone := LIntVec.Clone as specialize TVec<Integer>;
  AssertNotNull('Vector creation failed: LVecClone is nil.', LIntVecClone);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVecClone.Count]), LIntVec.Count, LIntVecClone.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 0, got %d.', [LIntVecClone.GetCapacity]), 0, LIntVecClone.GetCapacity);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [LIntVec.GetGrowSize, LIntVecClone.GetGrowSize]), LIntVec.GetGrowSize, LIntVecClone.GetGrowSize);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is not nil.', (LIntVecClone.Memory = nil));

  { 克隆测试 }
  LIntVec      := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVecClone := LIntVec.Clone as specialize TVec<Integer>;
  AssertNotNull('Vector creation failed: LVecClone is nil.', LIntVecClone);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVecClone.Count]), LIntVec.Count, LIntVecClone.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LIntVecClone.GetCapacity]), LIntVec.GetCapacity, LIntVecClone.GetCapacity);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [LIntVec.GetGrowSize,LIntVecClone.GetGrowSize]), LIntVec.GetGrowSize, LIntVecClone.GetGrowSize);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LIntVecClone.Memory <> nil));
  AssertTrue('The cloned Vec memory should not be the same block', LIntVec.GetMemory <> LIntVecClone.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVecClone[0]]), LIntVec[0], LIntVecClone[0]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVecClone[1]]), LIntVec[1], LIntVecClone[1]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVecClone[2]]), LIntVec[2], LIntVecClone[2]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVecClone[3]]), LIntVec[3], LIntVecClone[3]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVecClone[4]]), LIntVec[4], LIntVecClone[4]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVecClone[5]]), LIntVec[5], LIntVecClone[5]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVecClone[6]]), LIntVec[6], LIntVecClone[6]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVecClone[7]]), LIntVec[7], LIntVecClone[7]);

  { 修改克隆的vec }
  LIntVecClone[0] := 100;
  AssertTrue('The cloned Vec should be independent', LIntVec[0] <> LIntVecClone[0]);

  LIntVecClone[1] := 200;
  AssertTrue('The cloned Vec should be independent', LIntVec[1] <> LIntVecClone[1]);

  ///
  /// 托管类型
  ///

  LStrVec      := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVecClone := LStrVec.Clone as specialize TVec<String>;
  AssertNotNull('Vector creation failed: LVecClone is nil.', LStrVecClone);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVecClone.Count]), LStrVec.Count, LStrVecClone.Count);
  AssertEquals(Format('Initial capacity mismatch: expected 8, got %d.', [LStrVecClone.GetCapacity]), LStrVec.GetCapacity, LStrVecClone.GetCapacity);
  AssertEquals(Format('Initial grow size mismatch: expected %d, got %d.', [LStrVec.GetGrowSize, LStrVecClone.GetGrowSize]), LStrVec.GetGrowSize, LStrVecClone.GetGrowSize);
  AssertTrue('Memory should be allocated after vector initialization, but Memory is nil.', (LStrVecClone.Memory <> nil));
  AssertTrue('The cloned Vec memory should not be the same block', LStrVec.GetMemory <> LStrVecClone.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 11, got %s.', [LStrVecClone[0]]), LStrVec[0], LStrVecClone[0]);
  AssertEquals(Format('Initial element count mismatch: expected 22, got %s.', [LStrVecClone[1]]), LStrVec[1], LStrVecClone[1]);
  AssertEquals(Format('Initial element count mismatch: expected 33, got %s.', [LStrVecClone[2]]), LStrVec[2], LStrVecClone[2]);
  AssertEquals(Format('Initial element count mismatch: expected 44, got %s.', [LStrVecClone[3]]), LStrVec[3], LStrVecClone[3]);
  AssertEquals(Format('Initial element count mismatch: expected 55, got %s.', [LStrVecClone[4]]), LStrVec[4], LStrVecClone[4]);
  AssertEquals(Format('Initial element count mismatch: expected 66, got %s.', [LStrVecClone[5]]), LStrVec[5], LStrVecClone[5]);
  AssertEquals(Format('Initial element count mismatch: expected 77, got %s.', [LStrVecClone[6]]), LStrVec[6], LStrVecClone[6]);
  AssertEquals(Format('Initial element count mismatch: expected 88, got %s.', [LStrVecClone[7]]), LStrVec[7], LStrVecClone[7]);
  
  { 修改克隆的vec }
  LStrVecClone[0] := '100';
  AssertTrue('The cloned Vec should be independent', LStrVec[0] <> LStrVecClone[0]);

  LStrVecClone[1] := '200';
  AssertTrue('The cloned Vec should be independent', LStrVec[1] <> LStrVecClone[1]);
end;

procedure TTestCase_vec.Test_LoadFromCollection;
var
  LIntVec:   specialize IVec<Integer>;
  LIntVec2:  specialize IVec<Integer>;
  LIntArr:   specialize IArray<Integer>;
  LInt64Arr: specialize IArray<Int64>;
  LStrVec:   specialize IVec<String>;
  LStrVec2:  specialize IVec<String>;
  LStrArr:   specialize IArray<String>;
begin
  { 失败测试: 加载自身 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('LoadFromCollection should fail: load self', LIntVec.LoadFromCollection(LIntVec as TCollection));

  { 失败测试: 加载nil容器 }
  LIntVec2 := nil;
  AssertFalse('LoadFromCollection should fail: load nil collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));

  { 失败测试: 不兼容的泛型 }
  LInt64Arr := specialize makeArray<Int64>([0,1,2,3]);
  AssertFalse('LoadFromCollection should fail: incompatible generic', LIntVec.LoadFromCollection(LInt64Arr as TCollection));

  { 从空vec加载空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntVec2 := specialize TVec<Integer>.Create;
  AssertTrue('LoadFromCollection should fail: load empty collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LIntVec.GetCapacity);

  { 从空vec加载非空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntVec2 := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  AssertTrue('LoadFromCollection should fail: load empty collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec[0]]), 0, LIntVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec[1]]), 1, LIntVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec[2]]), 2, LIntVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec[3]]), 3, LIntVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec[4]]), 4, LIntVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec[5]]), 5, LIntVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec[6]]), 6, LIntVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec[7]]), 7, LIntVec[7]);

  { 从非空vec加载空Vec }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec2 := specialize TVec<Integer>.Create;
  AssertTrue('LoadFromCollection should fail: load empty collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec.Count]), 0, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LIntVec.GetCapacity);

  { 从非空vec加载非空Vec }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec2 := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 8, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec[0]]), 0, LIntVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec[1]]), 1, LIntVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec[2]]), 2, LIntVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec[3]]), 3, LIntVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec[4]]), 4, LIntVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec[5]]), 5, LIntVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec[6]]), 6, LIntVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec[7]]), 7, LIntVec[7]);

  { 从非空vec加载非空Vec,不同长度 }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec2 := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7,8,9]);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LIntVec.LoadFromCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec.Count]), 10, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 10', 10, LIntVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec[0]]), 0, LIntVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec[1]]), 1, LIntVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec[2]]), 2, LIntVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec[3]]), 3, LIntVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec[4]]), 4, LIntVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec[5]]), 5, LIntVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec[6]]), 6, LIntVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec[7]]), 7, LIntVec[7]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec[8]]), 8, LIntVec[8]);
  AssertEquals(Format('Initial element count mismatch: expected 9, got %d.', [LIntVec[9]]), 9, LIntVec[9]);

  { 从Array加载Vec }
  LIntArr :=  specialize makeArray<Integer>([10,11,12,13,14,15,16,17,18,19]);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LIntVec.LoadFromCollection(LIntArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 10, got %d.', [LIntVec.Count]), 10, LIntVec.Count);
  AssertEquals('The cloned Vec capacity should be 10', 10, LIntVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 10, got %d.', [LIntVec[0]]), 10, LIntVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 11, got %d.', [LIntVec[1]]), 11, LIntVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 12, got %d.', [LIntVec[2]]), 12, LIntVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 13, got %d.', [LIntVec[3]]), 13, LIntVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 14, got %d.', [LIntVec[4]]), 14, LIntVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 15, got %d.', [LIntVec[5]]), 15, LIntVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 16, got %d.', [LIntVec[6]]), 16, LIntVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 17, got %d.', [LIntVec[7]]), 17, LIntVec[7]);
  AssertEquals(Format('Initial element count mismatch: expected 18, got %d.', [LIntVec[8]]), 18, LIntVec[8]);
  AssertEquals(Format('Initial element count mismatch: expected 19, got %d.', [LIntVec[9]]), 19, LIntVec[9]);

  ///
  /// 托管类型
  ///

  { 失败测试: 加载自身 }
  LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  AssertFalse('LoadFromCollection should fail: load self', LStrVec.LoadFromCollection(LStrVec as TCollection));

  { 失败测试: 加载nil容器 }
  LStrVec2 := nil;
  AssertFalse('LoadFromCollection should fail: load nil collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));

  { 失败测试: 不兼容的泛型 }
  LInt64Arr := specialize makeArray<Int64>([0,1,2,3]);
  AssertFalse('LoadFromCollection should fail: incompatible generic', LStrVec.LoadFromCollection(LInt64Arr as TCollection));

  { 从空Vec加载空Vec }
  LStrVec  := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('LoadFromCollection should fail: load empty collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LStrVec.GetCapacity);

  { 从空Vec加载非空Vec }
  LStrVec  := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrVec[0]]), '0', LStrVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec[1]]), '1', LStrVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec[2]]), '2', LStrVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec[3]]), '3', LStrVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec[4]]), '4', LStrVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec[5]]), '5', LStrVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec[6]]), '6', LStrVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec[7]]), '7', LStrVec[7]);

  { 从非空Vec加载空Vec }
  LStrVec  := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('LoadFromCollection should fail: load empty collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec.Count]), 0, LStrVec.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LStrVec.GetCapacity);

  { 从非空Vec加载非空Vec }
  LStrVec  := specialize TVec<String>.Create(['00','11','22','33','44','55','66','77']);
  LStrVec2 := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertTrue('The cloned Vec memory should not be the same block', LStrVec.GetMemory <> LStrVec2.GetMemory);
  AssertEquals('The cloned Vec capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrVec[0]]), '0', LStrVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec[1]]), '1', LStrVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec[2]]), '2', LStrVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec[3]]), '3', LStrVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec[4]]), '4', LStrVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec[5]]), '5', LStrVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec[6]]), '6', LStrVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec[7]]), '7', LStrVec[7]);

  { 从非空Vec加载非空Vec,不同长度 }
  LStrVec  := specialize TVec<String>.Create(['00','11','22','33','44','55','66','77']);
  LStrVec2 := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7','8','9']);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LStrVec.LoadFromCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 10, LStrVec.Count);
  AssertTrue('The cloned Vec memory should not be the same block', LStrVec.GetMemory <> LStrVec2.GetMemory);
  AssertEquals('The cloned Vec capacity should be 10', 10, LStrVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrVec[0]]), '0', LStrVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec[1]]), '1', LStrVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec[2]]), '2', LStrVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec[3]]), '3', LStrVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec[4]]), '4', LStrVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec[5]]), '5', LStrVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec[6]]), '6', LStrVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec[7]]), '7', LStrVec[7]);
  AssertEquals(Format('Initial element count mismatch: expected 8, got %s.', [LStrVec[8]]), '8', LStrVec[8]);
  AssertEquals(Format('Initial element count mismatch: expected 9, got %s.', [LStrVec[9]]), '9', LStrVec[9]);

  { 从Array加载Vec }
  LStrArr := specialize makeArray<String>(['ni','hao','ma','?','ni','ma','de','.']);
  AssertTrue('LoadFromCollection should succeed: load non-empty collection', LStrVec.LoadFromCollection(LStrArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec.Count]), 8, LStrVec.Count);
  AssertTrue('The cloned Vec memory should not be the same block', LStrVec.GetMemory <> LStrArr.GetMemory);
  AssertEquals('The cloned Vec capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals(Format('Initial element count mismatch: expected ni, got %s.',  [LStrVec[0]]), 'ni', LStrVec[0]);
  AssertEquals(Format('Initial element count mismatch: expected hao, got %s.', [LStrVec[1]]), 'hao', LStrVec[1]);
  AssertEquals(Format('Initial element count mismatch: expected ma, got %s.',  [LStrVec[2]]), 'ma', LStrVec[2]);
  AssertEquals(Format('Initial element count mismatch: expected ?, got %s.',   [LStrVec[3]]), '?', LStrVec[3]);
  AssertEquals(Format('Initial element count mismatch: expected ni, got %s.',  [LStrVec[4]]), 'ni', LStrVec[4]);
  AssertEquals(Format('Initial element count mismatch: expected ma, got %s.',  [LStrVec[5]]), 'ma', LStrVec[5]);
  AssertEquals(Format('Initial element count mismatch: expected de, got %s.',  [LStrVec[6]]), 'de', LStrVec[6]);
  AssertEquals(Format('Initial element count mismatch: expected ., got %s.',   [LStrVec[7]]), '.', LStrVec[7]);

end;

procedure TTestCase_vec.Test_SaveToCollection;
var
  LIntVec:   specialize IVec<Integer>;
  LIntVec2:  specialize IVec<Integer>;
  LIntArr:   specialize IArray<Integer>;
  LInt64Arr: specialize IArray<Int64>;
  LStrVec:   specialize IVec<String>;
  LStrVec2:  specialize IVec<String>;
  LStrArr:   specialize IArray<String>;
begin
  { 失败测试: 保存到不兼容的泛型容器 }
  LIntVec   := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LInt64Arr := specialize TArray<Int64>.Create;
  AssertFalse('SaveToCollection should fail: save to incompatible generic collection', LIntVec.SaveToCollection(LInt64Arr as TCollection));

  { 空Vec保存到空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntVec2 := specialize TVec<Integer>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec2.Count]), 0, LIntVec2.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LIntVec2.GetCapacity);
  AssertNull('The cloned Vec memory should be nil', LIntVec2.GetMemory);

  { 空Vec保存到非空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntVec2 := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec2.Count]), 0, LIntVec2.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LIntVec2.GetCapacity);
  AssertNull('The cloned Vec memory should be nil', LIntVec2.GetMemory);

  { 非空Vec保存到空Vec }
  LIntVec  := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LIntVec2 := specialize TVec<Integer>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntVec2.Count]), 8, LIntVec2.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LIntVec2.GetCapacity);
  AssertNotNull('The cloned Vec memory should not be nil', LIntVec2.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec2[0]]), 0, LIntVec2[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec2[1]]), 1, LIntVec2[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec2[2]]), 2, LIntVec2[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec2[3]]), 3, LIntVec2[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec2[4]]), 4, LIntVec2[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec2[5]]), 5, LIntVec2[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec2[6]]), 6, LIntVec2[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec2[7]]), 7, LIntVec2[7]);

  { 非空Vec保存到非空Vec }
  LIntVec  := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LIntVec2 := specialize TVec<Integer>.Create([10,11,12,13,14,15,16,17,18,19]);
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec2.Count]), 8, LIntVec2.Count);
  AssertEquals('The cloned Vec capacity should be 0', 8, LIntVec2.GetCapacity);
  AssertNotNull('The cloned Vec memory should not be nil', LIntVec2.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntVec2[0]]), 0, LIntVec2[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntVec2[1]]), 1, LIntVec2[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntVec2[2]]), 2, LIntVec2[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntVec2[3]]), 3, LIntVec2[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntVec2[4]]), 4, LIntVec2[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntVec2[5]]), 5, LIntVec2[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntVec2[6]]), 6, LIntVec2[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntVec2[7]]), 7, LIntVec2[7]);

  { 保存Vec到空Array }
  LIntVec  := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LIntArr  := specialize TArray<Integer>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntArr.Count]), 8, LIntArr.Count);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntArr[0]]), 0, LIntArr[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntArr[1]]), 1, LIntArr[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntArr[2]]), 2, LIntArr[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntArr[3]]), 3, LIntArr[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntArr[4]]), 4, LIntArr[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntArr[5]]), 5, LIntArr[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntArr[6]]), 6, LIntArr[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntArr[7]]), 7, LIntArr[7]);

  { 保存Vec到非空Array }
  LIntVec  := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LIntArr  := specialize TArray<Integer>.Create([10,11,12,13,14,15,16,17,18,19]);
  AssertTrue('SaveToCollection should succeed: save empty collection', LIntVec.SaveToCollection(LIntArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LIntArr.Count]), 8, LIntArr.Count);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LIntArr[0]]), 0, LIntArr[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LIntArr[1]]), 1, LIntArr[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LIntArr[2]]), 2, LIntArr[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LIntArr[3]]), 3, LIntArr[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LIntArr[4]]), 4, LIntArr[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LIntArr[5]]), 5, LIntArr[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LIntArr[6]]), 6, LIntArr[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LIntArr[7]]), 7, LIntArr[7]);

  ///
  /// 托管类型
  ///

  { 失败测试: 保存到不兼容的泛型容器 }
  LStrVec  := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  LInt64Arr := specialize TArray<Int64>.Create;
  AssertFalse('SaveToCollection should fail: save to incompatible generic collection', LStrVec.SaveToCollection(LInt64Arr as TCollection));

  { 空Vec保存到空Vec }
  LStrVec  := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec2.Count]), 0, LStrVec2.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LStrVec2.GetCapacity);
  AssertNull('The cloned Vec memory should be nil', LStrVec2.GetMemory);

  { 空Vec保存到非空Vec }
  LStrVec  := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 0, got %d.', [LStrVec2.Count]), 0, LStrVec2.Count);
  AssertEquals('The cloned Vec capacity should be 0', 0, LStrVec2.GetCapacity);
  AssertNull('The cloned Vec memory should be nil', LStrVec2.GetMemory);

  { 非空Vec保存到空Vec }
  LStrVec  := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec2.Count]), 8, LStrVec2.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LStrVec2.GetCapacity);
  AssertNotNull('The cloned Vec memory should not be nil', LStrVec2.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrVec2[0]]), '0', LStrVec2[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec2[1]]), '1', LStrVec2[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec2[2]]), '2', LStrVec2[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec2[3]]), '3', LStrVec2[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec2[4]]), '4', LStrVec2[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec2[5]]), '5', LStrVec2[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec2[6]]), '6', LStrVec2[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec2[7]]), '7', LStrVec2[7]);

  { 非空Vec保存到非空Vec }
  LStrVec  := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  LStrVec2 := specialize TVec<String>.Create(['10','11','12','13','14','15','16','17','18','19']);
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrVec2 as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrVec2.Count]), 8, LStrVec2.Count);
  AssertEquals('The cloned Vec capacity should be 8', 8, LStrVec2.GetCapacity);
  AssertNotNull('The cloned Vec memory should not be nil', LStrVec2.GetMemory);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrVec2[0]]), '0', LStrVec2[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrVec2[1]]), '1', LStrVec2[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrVec2[2]]), '2', LStrVec2[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrVec2[3]]), '3', LStrVec2[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrVec2[4]]), '4', LStrVec2[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrVec2[5]]), '5', LStrVec2[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrVec2[6]]), '6', LStrVec2[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrVec2[7]]), '7', LStrVec2[7]);

  { 保存Vec到空Array }
  LStrVec  := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  LStrArr  := specialize TArray<String>.Create;
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrArr.Count]), 8, LStrArr.Count);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrArr[0]]), '0', LStrArr[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrArr[1]]), '1', LStrArr[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrArr[2]]), '2', LStrArr[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrArr[3]]), '3', LStrArr[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrArr[4]]), '4', LStrArr[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrArr[5]]), '5', LStrArr[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrArr[6]]), '6', LStrArr[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrArr[7]]), '7', LStrArr[7]);

  { 保存Vec到非空Array }
  LStrVec  := specialize TVec<String>.Create(['0','1','2','3','4','5','6','7']);
  LStrArr  := specialize TArray<String>.Create(['10','11','12','13','14','15','16','17','18','19']);
  AssertTrue('SaveToCollection should succeed: save empty collection', LStrVec.SaveToCollection(LStrArr as TCollection));
  AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LStrArr.Count]), 8, LStrArr.Count);
  AssertEquals(Format('Initial element count mismatch: expected 0, got %s.', [LStrArr[0]]), '0', LStrArr[0]);
  AssertEquals(Format('Initial element count mismatch: expected 1, got %s.', [LStrArr[1]]), '1', LStrArr[1]);
  AssertEquals(Format('Initial element count mismatch: expected 2, got %s.', [LStrArr[2]]), '2', LStrArr[2]);
  AssertEquals(Format('Initial element count mismatch: expected 3, got %s.', [LStrArr[3]]), '3', LStrArr[3]);
  AssertEquals(Format('Initial element count mismatch: expected 4, got %s.', [LStrArr[4]]), '4', LStrArr[4]);
  AssertEquals(Format('Initial element count mismatch: expected 5, got %s.', [LStrArr[5]]), '5', LStrArr[5]);
  AssertEquals(Format('Initial element count mismatch: expected 6, got %s.', [LStrArr[6]]), '6', LStrArr[6]);
  AssertEquals(Format('Initial element count mismatch: expected 7, got %s.', [LStrArr[7]]), '7', LStrArr[7]);
end;

procedure TTestCase_vec.Test_GetEnumerator;
var
  LVec:           specialize TVec<Integer>;
  LEnumerator:    specialize TGenericEnumerator<Integer>;
  LVecStr:        specialize TVec<String>;
  LEnumeratorStr: specialize TGenericEnumerator<String>;
  i:              Integer;
  LItem:          Integer;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    LEnumerator := LVec.GetEnumerator;
    try
      AssertNotNull('Enumerator is nil.', LEnumerator);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 1, got %d.', [LEnumerator.Current]), 1, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 2, got %d.', [LEnumerator.Current]), 2, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 3, got %d.', [LEnumerator.Current]), 3, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 4, got %d.', [LEnumerator.Current]), 4, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 5, got %d.', [LEnumerator.Current]), 5, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 6, got %d.', [LEnumerator.Current]), 6, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 7, got %d.', [LEnumerator.Current]), 7, LEnumerator.Current);
      AssertTrue('Enumerator is not moved.', LEnumerator.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 8, got %d.', [LEnumerator.Current]), 8, LEnumerator.Current);
      AssertFalse('Enumerator is not moved.', LEnumerator.MoveNext);
    finally
      LEnumerator.Free;
    end;
  finally
    LVec.Free;
  end;

  { 字符串测试 }
  LVecStr := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  try
    LEnumeratorStr := LVecStr.GetEnumerator;
    try
      AssertNotNull('Enumerator is nil.', LEnumeratorStr);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 11, got %s.', [LEnumeratorStr.Current]), '11', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 22, got %s.', [LEnumeratorStr.Current]), '22', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 33, got %s.', [LEnumeratorStr.Current]), '33', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 44, got %s.', [LEnumeratorStr.Current]), '44', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 55, got %s.', [LEnumeratorStr.Current]), '55', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 66, got %s.', [LEnumeratorStr.Current]), '66', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 77, got %s.', [LEnumeratorStr.Current]), '77', LEnumeratorStr.Current);
      AssertTrue('Enumerator is not moved.', LEnumeratorStr.MoveNext);
      AssertEquals(Format('Initial element count mismatch: expected 88, got %s.', [LEnumeratorStr.Current]), '88', LEnumeratorStr.Current);
      AssertFalse('Enumerator is not moved.', LEnumeratorStr.MoveNext);
    finally
      LEnumeratorStr.Free;
    end;
  finally
    LVecStr.Free;
  end;

  { for 迭代器 }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    i := 1;
    for LItem in LVec do
    begin
      AssertEquals(Format('Initial element count mismatch: expected %d, got %d.', [i,LItem]), i, LItem);
      Inc(i);
    end;
  finally
    LVec.Free;
  end;
end;

procedure TTestCase_vec.Test_GetElementSize;
var
  LIntVec:      specialize IVec<Integer>;
  LStrVec:      specialize IVec<String>;
  LObjVec:      specialize IVec<TObject>;
  LInt64Vec:    specialize IVec<Int64>;
  LSingleVec:   specialize IVec<Single>;
  LDoubleVec:   specialize IVec<Double>;
  LExtendedVec: specialize IVec<Extended>;
begin
  LIntVec := specialize TVec<Integer>.Create;
  AssertEquals('Element size should be 4 for Integer', sizeof(Integer), LIntVec.GetElementSize);

  LStrVec := specialize TVec<String>.Create;
  AssertEquals('Element size should be 1 for String', sizeof(String), LStrVec.GetElementSize);

  LObjVec := specialize TVec<TObject>.Create;
  AssertEquals('Element size should be 4 for TObject', sizeof(TObject), LObjVec.GetElementSize);
  
  LInt64Vec := specialize TVec<Int64>.Create;
  AssertEquals('Element size should be 8 for Int64', sizeof(Int64), LInt64Vec.GetElementSize);

  LSingleVec := specialize TVec<Single>.Create;
  AssertEquals('Element size should be 4 for Single', sizeof(Single), LSingleVec.GetElementSize);
  
  LDoubleVec := specialize TVec<Double>.Create;
  AssertEquals('Element size should be 8 for Double', sizeof(Double), LDoubleVec.GetElementSize);

  LExtendedVec := specialize TVec<Extended>.Create;
  AssertEquals('Element size should be 10 for Extended', sizeof(Extended), LExtendedVec.GetElementSize);
end;

procedure TTestCase_vec.Test_GetIsManagedType;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<string>;
begin
  LIntVec := specialize TVec<Integer>.Create;
  AssertFalse('Integer should not be managed type', LIntVec.GetIsManagedType);

  LStrVec := specialize TVec<string>.Create;
  AssertTrue('String should be managed type', LStrVec.GetIsManagedType);
end;

procedure TTestCase_vec.Test_GetElementAllocator;
var
  LVec: specialize IVec<Integer>;
  LAllocator: TMemAllocator;
begin
  LVec := specialize TVec<Integer>.Create;
  AssertNotNull('Element allocator should not be nil', LVec.GetElementAllocator);
  AssertTrue('Element allocator should be the same', LVec.GetElementAllocator.GetMemAllocator = RtlMemAllocator());

  LAllocator := TMemAllocator.Create(@TestGetMem, @TestAllocMem, @TestReallocMem, @TestFreeMem);
  LVec := specialize TVec<Integer>.Create(LAllocator);
  AssertTrue('Allocator should match the one used in creation', LVec.GetElementAllocator.GetMemAllocator = LAllocator);

  LVec := nil;
  LAllocator.Free;
end;

procedure TTestCase_vec.Test_Get;
var
  LIntVec:  specialize IVec<Integer>;
  LStrVec:  specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('Get should return correct element', 1, LIntVec.Get(0));
  AssertEquals('Get should return correct element', 2, LIntVec.Get(1));
  AssertEquals('Get should return correct element', 3, LIntVec.Get(2));
  AssertEquals('Get should return correct element', 4, LIntVec.Get(3));
  AssertEquals('Get should return correct element', 5, LIntVec.Get(4));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(Exception,
    procedure
    begin
      LIntVec.Get(8);
    end,
    'Index out of bounds');
  {$ENDIF}

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(['Hello', 'World', 'Test']);
  AssertEquals('Get should return correct element', 'Hello', LStrVec.Get(0));
  AssertEquals('Get should return correct element', 'World', LStrVec.Get(1));
  AssertEquals('Get should return correct element', 'Test',  LStrVec.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(Exception,
    procedure
    begin
      LStrVec.Get(3);
    end,
    'Index out of bounds');
  {$ENDIF}
end;

procedure TTestCase_vec.Test_Put;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Put(0, 100);
  LIntVec.Put(1, 200);
  LIntVec.Put(2, 300);
  AssertEquals('Put should store value correctly', 100, LIntVec.Get(0));
  AssertEquals('Put should store value correctly', 200, LIntVec.Get(1));
  AssertEquals('Put should store value correctly', 300, LIntVec.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(Exception,
    procedure
    begin
      LIntVec.Put(8, 400);
    end,
    'Index out of bounds');
  {$ENDIF}

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(['Hello', 'World', 'Test']);
  LStrVec.Put(0, 'Hello');
  LStrVec.Put(1, 'World');
  LStrVec.Put(2, 'Test');
  AssertEquals('Put should store value correctly', 'Hello', LStrVec.Get(0));
  AssertEquals('Put should store value correctly', 'World', LStrVec.Get(1));
  AssertEquals('Put should store value correctly', 'Test',  LStrVec.Get(2));

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 超出范围 }
  AssertException(Exception,
    procedure
    begin
      LStrVec.Put(3, 'Test2');
    end,
    'Index out of bounds');
  {$ENDIF}
end;

procedure TTestCase_vec.Test_GetPtr;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<string>;
  LIntPtr: PInteger;
  LStrPtr: PString;
begin
  LIntVec := specialize TVec<Integer>.Create([10, 20, 30]);
  LIntPtr := LIntVec.GetPtr(0);
  AssertNotNull('Pointer to first element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 10, LIntPtr^);

  LIntPtr := LIntVec.GetPtr(1);
  AssertNotNull('Pointer to second element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 20, LIntPtr^);

  LIntPtr := LIntVec.GetPtr(2);
  AssertNotNull('Pointer to third element should not be null', LIntPtr);
  AssertEquals('Value should be stored correctly', 30, LIntPtr^);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 访问越界 }
  AssertException(Exception,
    procedure
    begin
      LIntVec.GetPtr(3);
    end,
    'Index out of bounds');
  {$ENDIF}

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<string>.Create(['Hello', 'World', 'Test']);
  LStrPtr := LStrVec.GetPtr(0);
  AssertNotNull('Pointer to first element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'Hello', LStrPtr^);

  LStrPtr := LStrVec.GetPtr(1);
  AssertNotNull('Pointer to second element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'World', LStrPtr^);

  LStrPtr := LStrVec.GetPtr(2);
  AssertNotNull('Pointer to third element should not be null', LStrPtr);
  AssertEquals('Value should be stored correctly', 'Test', LStrPtr^);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 访问越界 }
  AssertException(Exception,
    procedure
    begin
      LStrVec.GetPtr(3);
    end,
    'Index out of bounds');
  {$ENDIF}
end;

procedure TTestCase_vec.Test_GetMemory;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<string>;
  LIntPtr: PInteger;
  LStrPtr: PString;
begin
  LIntVec := specialize TVec<Integer>.Create(5);
  LIntVec.Resize(5);
  LIntPtr := LIntVec.GetMemory;
  AssertNotNull('Collection memory should not be null', LIntPtr);
  LIntPtr[0] := 100;
  LIntPtr[1] := 200;
  LIntPtr[2] := 300;
  AssertEquals('Value should be stored correctly', 100, LIntVec.Get(0));
  AssertEquals('Value should be stored correctly', 200, LIntVec.Get(1));
  AssertEquals('Value should be stored correctly', 300, LIntVec.Get(2));

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<string>.Create(5);
  LStrVec.Resize(5);
  LStrPtr := LStrVec.GetMemory;
  AssertNotNull('Collection memory should not be null', LStrPtr);
  LStrPtr[0] := 'Hello';
  LStrPtr[1] := 'World';
  LStrPtr[2] := 'Test';
  AssertEquals('Value should be stored correctly', 'Hello', LStrVec.Get(0));
  AssertEquals('Value should be stored correctly', 'World', LStrVec.Get(1));
  AssertEquals('Value should be stored correctly', 'Test',  LStrVec.Get(2));
end;

procedure TTestCase_vec.Test_Resize;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Resize(10);
  AssertEquals('New size should be 10', 10, LIntVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  { 缩小 }
  LIntVec.Resize(5);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  { 无变化 }
  LIntVec.Resize(5);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  { 0 }
  LIntVec.Resize(0);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LIntVec.GetCapacity);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec.Resize(10);
  AssertEquals('New size should be 10', 10, LStrVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 11', '11', LStrVec[0]);
  AssertEquals('element 1 should be 22', '22', LStrVec[1]);
  AssertEquals('element 2 should be 33', '33', LStrVec[2]);
  AssertEquals('element 3 should be 44', '44', LStrVec[3]);
  AssertEquals('element 4 should be 55', '55', LStrVec[4]);
  AssertEquals('element 5 should be 66', '66', LStrVec[5]);
  AssertEquals('element 6 should be 77', '77', LStrVec[6]);
  AssertEquals('element 7 should be 88', '88', LStrVec[7]);

  { 缩小 }
  LStrVec.Resize(5);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 11', '11', LStrVec[0]);
  AssertEquals('element 1 should be 22', '22', LStrVec[1]);
  AssertEquals('element 2 should be 33', '33', LStrVec[2]);
  AssertEquals('element 3 should be 44', '44', LStrVec[3]);
  AssertEquals('element 4 should be 55', '55', LStrVec[4]);

  { 无变化 }
  LStrVec.Resize(5);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 11', '11', LStrVec[0]);
  AssertEquals('element 1 should be 22', '22', LStrVec[1]);
  AssertEquals('element 2 should be 33', '33', LStrVec[2]);
  AssertEquals('element 3 should be 44', '44', LStrVec[3]);
  AssertEquals('element 4 should be 55', '55', LStrVec[4]);

  { 0 }
  LStrVec.Resize(0);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LStrVec.GetCapacity);
end;

procedure TTestCase_vec.Test_Ensure;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Ensure(10);
  AssertEquals('New size should be 10', 10, LIntVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LIntVec.GetCapacity);

  { 缩小会被忽略 }
  AssertTrue('Ensure should succeed', LIntVec.Ensure(3));
  AssertEquals('Ensured size should be correct', 10, LIntVec.GetCount);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec.Ensure(10);
  AssertEquals('New size should be 10', 10, LStrVec.GetCount);
  AssertEquals(format('capacity should be %d',[VEC_DEFAULT_GROW_SIZE+8]), VEC_DEFAULT_GROW_SIZE+8, LStrVec.GetCapacity);

  { 缩小会被忽略 }
  AssertTrue('Ensure should succeed', LStrVec.Ensure(3));
  AssertEquals('Ensured size should be correct', 10, LStrVec.GetCount);
end;

procedure TTestCase_vec.Test_LoadFromArray;
var
  LVec:    specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
begin
  LVec := specialize TVec<Integer>.Create;
  LVec.LoadFromArray([10,11,12,13]);
  AssertEquals('New size should be 4', 4, LVec.GetCount);
  AssertEquals('capacity should be 4', 4, LVec.GetCapacity);
  AssertEquals('element 0 should be 10', 10, LVec[0]);
  AssertEquals('element 1 should be 11', 11, LVec[1]);
  AssertEquals('element 2 should be 12', 12, LVec[2]);
  AssertEquals('element 3 should be 13', 13, LVec[3]);

  { 装6个新元素 }
  LVec.LoadFromArray([100,101,102,103,104,105]);
  AssertEquals('New size should be 6', 6, LVec.GetCount);
  AssertEquals('capacity should be 6', 6, LVec.GetCapacity);
  AssertEquals('element 0 should be 100', 100, LVec[0]);
  AssertEquals('element 1 should be 101', 101, LVec[1]);
  AssertEquals('element 2 should be 102', 102, LVec[2]);
  AssertEquals('element 3 should be 103', 103, LVec[3]);
  AssertEquals('element 4 should be 104', 104, LVec[4]);
  AssertEquals('element 5 should be 105', 105, LVec[5]);

  { 装0个新元素 }
  LVec.LoadFromArray([]);
  AssertEquals('New size should be 0', 0, LVec.GetCount);
  AssertEquals('capacity should be 0', 0, LVec.GetCapacity);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LVecStr.LoadFromArray(['10','11','12','13']);
  AssertEquals('New size should be 4', 4, LVecStr.GetCount);
  AssertEquals('capacity should be 4', 4, LVecStr.GetCapacity);
  AssertEquals('element 0 should be 10', '10', LVecStr[0]);
  AssertEquals('element 1 should be 11', '11', LVecStr[1]);
  AssertEquals('element 2 should be 12', '12', LVecStr[2]);
  AssertEquals('element 3 should be 13', '13', LVecStr[3]);

  { 装6个新元素 }
  LVecStr.LoadFromArray(['100','101','102','103','104','105']);
  AssertEquals('New size should be 6', 6, LVecStr.GetCount);
  AssertEquals('capacity should be 6', 6, LVecStr.GetCapacity);
  AssertEquals('element 0 should be 100', '100', LVecStr[0]);
  AssertEquals('element 1 should be 101', '101', LVecStr[1]);
  AssertEquals('element 2 should be 102', '102', LVecStr[2]);
  AssertEquals('element 3 should be 103', '103', LVecStr[3]);
  AssertEquals('element 4 should be 104', '104', LVecStr[4]);
  AssertEquals('element 5 should be 105', '105', LVecStr[5]);
  
  { 装0个新元素 }
  LVecStr.LoadFromArray([]);
  AssertEquals('New size should be 0', 0, LVecStr.GetCount);
  AssertEquals('capacity should be 0', 0, LVecStr.GetCapacity);
end;

procedure TTestCase_vec.Test_AppendFromArray;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;
  LIntVec.AppendFromArray([1,2,3,4]);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('capacity should be 4', 4, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);

  LIntVec.AppendFromArray([5,6,7,8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  { 装0个新元素 }
  LIntVec.AppendFromArray([]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 0 should be 5', 5, LIntVec[4]);
  AssertEquals('element 1 should be 6', 6, LIntVec[5]);
  AssertEquals('element 2 should be 7', 7, LIntVec[6]);
  AssertEquals('element 3 should be 8', 8, LIntVec[7]);
  
  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;
  LStrVec.AppendFromArray(['1','2','3','4']);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('capacity should be 4', 4, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 1', '1', LStrVec[0]);
  AssertEquals('element 1 should be 2', '2', LStrVec[1]);
  AssertEquals('element 2 should be 3', '3', LStrVec[2]);
  AssertEquals('element 3 should be 4', '4', LStrVec[3]);

  LStrVec.AppendFromArray(['5','6','7','8']);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 1', '1', LStrVec[0]);
  AssertEquals('element 1 should be 2', '2', LStrVec[1]);
  AssertEquals('element 2 should be 3', '3', LStrVec[2]);
  AssertEquals('element 3 should be 4', '4', LStrVec[3]);
  AssertEquals('element 0 should be 5', '5', LStrVec[4]);
  AssertEquals('element 1 should be 6', '6', LStrVec[5]);
  AssertEquals('element 2 should be 7', '7', LStrVec[6]);
  AssertEquals('element 3 should be 8', '8', LStrVec[7]);

  LStrVec.AppendFromArray([]);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 1', '1', LStrVec[0]);
  AssertEquals('element 1 should be 2', '2', LStrVec[1]);
  AssertEquals('element 2 should be 3', '3', LStrVec[2]);
  AssertEquals('element 3 should be 4', '4', LStrVec[3]);
  AssertEquals('element 0 should be 5', '5', LStrVec[4]);
  AssertEquals('element 1 should be 6', '6', LStrVec[5]);
  AssertEquals('element 2 should be 7', '7', LStrVec[6]);
  AssertEquals('element 3 should be 8', '8', LStrVec[7]);
end;

procedure TTestCase_vec.Test_ToArray;
var
  LIntVec:  specialize IVec<Integer>;
  LStrVec:  specialize IVec<String>;
  LIntData: array of Integer;
  LStrData: array of string;
begin
  Initialize(LIntData);
  Initialize(LStrData);

  { 空Vec }
  LIntVec  := specialize TVec<Integer>.Create;
  LIntData := LIntVec.ToArray;
  AssertEquals('Size should match source array', 0, Length(LIntData));

  { 非空Vec }
  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntData := LIntVec.ToArray;
  AssertEquals('Size should match source array', 4, Length(LIntData));
  AssertEquals('First element should be 1',  1, LIntData[0]);
  AssertEquals('Second element should be 2', 2, LIntData[1]);
  AssertEquals('Third element should be 3',  3, LIntData[2]);
  AssertEquals('Fourth element should be 4', 4, LIntData[3]);

  ///
  /// 托管类型
  ///

  { 空Vec }
  LStrVec  := specialize TVec<String>.Create;
  LStrData := LStrVec.ToArray;
  AssertEquals('Size should match source array', 0, Length(LStrData));

  { 非空Vec }
  LStrVec  := specialize TVec<String>.Create(['A','B','C','D']);
  LStrData := LStrVec.ToArray;
  AssertEquals('Size should match source array', 4, Length(LStrData));
  AssertEquals('First element should be A',  'A', LStrData[0]);
  AssertEquals('Second element should be B', 'B', LStrData[1]);
  AssertEquals('Third element should be C',  'C', LStrData[2]);
  AssertEquals('Fourth element should be D', 'D', LStrData[3]);
end;

procedure TTestCase_vec.Test_LoadFromMemory;
var
  LIntVec:  specialize IVec<Integer>;
  LIntData: array[0..7] of Integer = (10,11,12,13,14,15,16,17);
  LIntPtr:  PInteger;
  LStrVec:  specialize IVec<String>;
  LStrData: array[0..7] of String = ('A','B','C','D','E','F','G','H');
  LStrPtr:  PString;
begin
  { 加载空内存 }
  LIntVec := specialize TVec<Integer>.Create();
  LIntVec.LoadFromMemory(nil, 0);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('capacity should be 0', 0, LIntVec.GetCapacity);

  { 加载非空内存 }
  LIntVec := specialize TVec<Integer>.Create();
  LIntVec.LoadFromMemory(@LIntData[0], SizeUInt(Length(LIntData)));
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 10', 10, LIntVec[0]);
  AssertEquals('element 1 should be 11', 11, LIntVec[1]);
  AssertEquals('element 2 should be 12', 12, LIntVec[2]);
  AssertEquals('element 3 should be 13', 13, LIntVec[3]);
  AssertEquals('element 4 should be 14', 14, LIntVec[4]);
  AssertEquals('element 5 should be 15', 15, LIntVec[5]);
  AssertEquals('element 6 should be 16', 16, LIntVec[6]);
  AssertEquals('element 7 should be 17', 17, LIntVec[7]);

  { 通过内存指针初始化 }
  GetMem(LIntPtr, 3 * SizeOf(Integer));
  try
    LIntPtr[0] := 100;
    LIntPtr[1] := 200;
    LIntPtr[2] := 300;
    LIntVec.LoadFromMemory(LIntPtr, 3);
    AssertEquals('New size should be 3', 3, LIntVec.GetCount);
    AssertEquals('capacity should be 3', 3, LIntVec.GetCapacity);
    AssertEquals('element 0 should be 100', 100, LIntVec[0]);
    AssertEquals('element 1 should be 200', 200, LIntVec[1]);
    AssertEquals('element 2 should be 300', 300, LIntVec[2]);
  finally
    FreeMem(LIntPtr);
  end;

  ///
  /// 托管类型
  ///

  { 加载空内存 }
  LStrVec := specialize TVec<String>.Create();
  LStrVec.LoadFromMemory(nil, 0);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('capacity should be 0', 0, LStrVec.GetCapacity);

  { 加载非空内存 }
  LStrVec := specialize TVec<String>.Create();
  LStrVec.LoadFromMemory(@LStrData[0], SizeUInt(Length(LStrData)));
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be A', 'A', LStrVec[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec[3]);
  AssertEquals('element 4 should be E', 'E', LStrVec[4]);
  AssertEquals('element 5 should be F', 'F', LStrVec[5]);
  AssertEquals('element 6 should be G', 'G', LStrVec[6]);
  AssertEquals('element 7 should be H', 'H', LStrVec[7]);

  { 通过内存指针初始化 }
  GetMem(LStrPtr, 3 * SizeOf(String));
  try
    InitializeArray(LStrPtr, Typeinfo(String), 3);
    LStrPtr[0] := 'AA';
    LStrPtr[1] := 'BB';
    LStrPtr[2] := 'CC';
    LStrVec.LoadFromMemory(LStrPtr, 3);
    AssertEquals('New size should be 3', 3, LStrVec.GetCount);
    AssertEquals('capacity should be 3', 3, LStrVec.GetCapacity);
    AssertEquals('element 0 should be AA', 'AA', LStrVec[0]);
    AssertEquals('element 1 should be BB', 'BB', LStrVec[1]);
    AssertEquals('element 2 should be CC', 'CC', LStrVec[2]);
    FinalizeArray(LStrPtr, Typeinfo(String), 3);
  finally
    FreeMem(LStrPtr);
  end;
end;

procedure TTestCase_vec.Test_AppendFromMemory;
const
  INT1: array[0..3] of Integer = (0,1,2,3);
  INT2: array[0..3] of Integer = (4,5,6,7);
  STR1: array[0..3] of string = ('A','B','C','D');
  STR2: array[0..3] of string = ('E','F','G','H');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  { 空Vec追加空内存 }
  LIntVec := specialize TVec<Integer>.Create();
  LIntVec.AppendFromMemory(nil, 0);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);

  { 空Vec追加非空内存 }
  LIntVec := specialize TVec<Integer>.Create();
  LIntVec.AppendFromMemory(@INT1[0], 4);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('capacity should be 4', 4, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);

  { 非空Vec追加空内存 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntVec.AppendFromMemory(nil, 0);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('capacity should be 4', 4, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);

  { 非空Vec追加非空内存 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3]);
  LIntVec.AppendFromMemory(@INT2[0], 4);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);
  AssertEquals('element 6 should be 6', 6, LIntVec[6]);
  AssertEquals('element 7 should be 7', 7, LIntVec[7]);

  ///
  /// 托管类型
  ///

  { 空Vec追加空内存 }
  LStrVec := specialize TVec<String>.Create();
  LStrVec.AppendFromMemory(nil, 0);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);

  { 空Vec追加非空内存 }
  LStrVec := specialize TVec<String>.Create();
  LStrVec.AppendFromMemory(@STR1[0], 4);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('capacity should be 4', 4, LStrVec.GetCapacity);
  AssertEquals('element 0 should be A', 'A', LStrVec[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec[3]);

  { 非空Vec追加空内存 }
  LStrVec := specialize TVec<String>.Create(['A','B','C','D']);
  LStrVec.AppendFromMemory(nil, 0);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('capacity should be 4', 4, LStrVec.GetCapacity);
  AssertEquals('element 0 should be A', 'A', LStrVec[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec[3]);

  { 非空Vec追加非空内存 }
  LStrVec := specialize TVec<String>.Create(['A','B','C','D']);
  LStrVec.AppendFromMemory(@STR2[0], 4);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be A', 'A', LStrVec[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec[3]);
  AssertEquals('element 4 should be E', 'E', LStrVec[4]);
  AssertEquals('element 5 should be F', 'F', LStrVec[5]);
  AssertEquals('element 6 should be G', 'G', LStrVec[6]);
end;

procedure TTestCase_vec.Test_AppendFromCollection;
var
  LIntVec1:  specialize IVec<Integer>;
  LIntVec2:  specialize IVec<Integer>;
  LIntVec3:  specialize IVec<Integer>;
  LIntArr1:  specialize IArray<Integer>;
  LInt64Vec: specialize IVec<Int64>;
  LStrVec1:  specialize IVec<String>;
  LStrVec2:  specialize IVec<String>;
  LStrVec3:  specialize IVec<String>;
  LStrArr1:  specialize IArray<String>;
begin
  { 失败测试: 追加 nil }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntVec2 := nil;
  AssertFalse('AppendFromCollection should fail: append nil collection', LIntVec1.AppendFromCollection(LIntVec2 as TCollection));

  { 失败测试: 追加自身 }
  LIntVec1 := specialize TVec<Integer>.Create;
  AssertFalse('AppendFromCollection should fail: append self collection', LIntVec1.AppendFromCollection(LIntVec1 as TCollection));

  { 失败测试: 追加不兼容的泛型容器 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LInt64Vec := specialize TVec<Int64>.Create([1,2,3,4]);
  AssertFalse('AppendFromCollection should fail: append incompatible collection', LIntVec1.AppendFromCollection(LInt64Vec as TCollection));

  { 空Vec追加空Vec }
  LIntVec1 := specialize TVec<Integer>.Create();
  LIntVec2 := specialize TVec<Integer>.Create();
  AssertTrue('AppendFromCollection should succeed: append empty collection', LIntVec1.AppendFromCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 0', 0, LIntVec1.GetCount);

  { 空Vec追加非空Vec }
  LIntVec1 := specialize TVec<Integer>.Create();
  LIntVec2 := specialize TVec<Integer>.Create([1,2,3]);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LIntVec1.AppendFromCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);

  { 非空Vec追加空Vec }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntVec2 := specialize TVec<Integer>.Create();
  AssertTrue('AppendFromCollection should succeed: append empty collection', LIntVec1.AppendFromCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);

  { 非空Vec追加非空Vec }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntVec2 := specialize TVec<Integer>.Create([4,5,6]);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LIntVec1.AppendFromCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 6', 6, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec1[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec1[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec1[5]);

  { 再追加一次 }
  LIntVec3 := specialize TVec<Integer>.Create([8,9,10]);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LIntVec1.AppendFromCollection(LIntVec3 as TCollection));
  AssertEquals('New size should be 9', 9, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1,   LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2,   LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3,   LIntVec1[2]);
  AssertEquals('element 3 should be 4', 4,   LIntVec1[3]);
  AssertEquals('element 4 should be 5', 5,   LIntVec1[4]);
  AssertEquals('element 5 should be 6', 6,   LIntVec1[5]);
  AssertEquals('element 6 should be 8', 8,   LIntVec1[6]);
  AssertEquals('element 7 should be 9', 9,   LIntVec1[7]);
  AssertEquals('element 8 should be 10', 10, LIntVec1[8]);

  // From Array

  { 空Vec追加空数组 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('AppendFromCollection should succeed: append empty array', LIntVec1.AppendFromCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 0, LIntVec1.GetCount);

  { 空Vec追加非空数组 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue('AppendFromCollection should succeed: append non-empty array', LIntVec1.AppendFromCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 4, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec1[3]);

  { 非空Vec追加空数组 }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('AppendFromCollection should succeed: append empty array', LIntVec1.AppendFromCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 3, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);

  { 非空Vec追加非空数组 }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntArr1 := specialize TArray<Integer>.Create([4,5,6]);
  AssertTrue('AppendFromCollection should succeed: append non-empty array', LIntVec1.AppendFromCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 6, LIntVec1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec1[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec1[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec1[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec1[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec1[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec1[5]);

  ///
  /// 托管类型
  ///

  { 失败测试: 追加 nil }
  LStrVec1 := specialize TVec<String>.Create;
  LStrVec2 := nil;
  AssertFalse('AppendFromCollection should fail: append nil collection', LStrVec1.AppendFromCollection(LStrVec2 as TCollection));

  { 失败测试: 追加自身 }
  LStrVec1 := specialize TVec<String>.Create;
  AssertFalse('AppendFromCollection should fail: append self collection', LStrVec1.AppendFromCollection(LStrVec1 as TCollection));

  { 失败测试: 追加不兼容的泛型容器 }
  LStrVec1 := specialize TVec<String>.Create;
  LInt64Vec := specialize TVec<Int64>.Create([1,2,3,4]);
  AssertFalse('AppendFromCollection should fail: append incompatible collection', LStrVec1.AppendFromCollection(LInt64Vec as TCollection));

  { 空Vec追加空Vec }
  LStrVec1 := specialize TVec<String>.Create();
  LStrVec2 := specialize TVec<String>.Create();
  AssertTrue('AppendFromCollection should succeed: append empty collection', LStrVec1.AppendFromCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 0', 0, LStrVec1.GetCount);

  { 空Vec追加非空Vec }
  LStrVec1 := specialize TVec<String>.Create();
  LStrVec2 := specialize TVec<String>.Create(['A','B','C']);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LStrVec1.AppendFromCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);

  { 非空Vec追加空Vec }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrVec2 := specialize TVec<String>.Create();
  AssertTrue('AppendFromCollection should succeed: append empty collection', LStrVec1.AppendFromCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);

  { 非空Vec追加非空Vec }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrVec2 := specialize TVec<String>.Create(['D','E','F']);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LStrVec1.AppendFromCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 6', 6, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec1[3]);
  AssertEquals('element 4 should be E', 'E', LStrVec1[4]);
  AssertEquals('element 5 should be F', 'F', LStrVec1[5]);

  { 再追加一次 }
  LStrVec3 := specialize TVec<String>.Create(['G','H','I']);
  AssertTrue('AppendFromCollection should succeed: append non-empty collection', LStrVec1.AppendFromCollection(LStrVec3 as TCollection));
  AssertEquals('New size should be 9', 9, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec1[3]);
  AssertEquals('element 4 should be E', 'E', LStrVec1[4]);
  AssertEquals('element 5 should be F', 'F', LStrVec1[5]);
  AssertEquals('element 6 should be G', 'G', LStrVec1[6]);
  AssertEquals('element 7 should be H', 'H', LStrVec1[7]);
  AssertEquals('element 8 should be I', 'I', LStrVec1[8]);

  // From Array

  { 空Vec追加空数组 }
  LStrVec1 := specialize TVec<String>.Create;
  LStrArr1 := specialize TArray<String>.Create;
  AssertTrue('AppendFromCollection should succeed: append empty array', LStrVec1.AppendFromCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 0, LStrVec1.GetCount);

  { 空Vec追加非空数组 }
  LStrVec1 := specialize TVec<String>.Create;
  LStrArr1 := specialize TArray<String>.Create(['A','B','C','D']);
  AssertTrue('AppendFromCollection should succeed: append non-empty array', LStrVec1.AppendFromCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 4, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec1[3]);

  { 非空Vec追加空数组 }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrArr1 := specialize TArray<String>.Create;
  AssertTrue('AppendFromCollection should succeed: append empty array', LStrVec1.AppendFromCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 3, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);

  { 非空Vec追加非空数组 }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrArr1 := specialize TArray<String>.Create(['D','E','F']);
  AssertTrue('AppendFromCollection should succeed: append non-empty array', LStrVec1.AppendFromCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 6, LStrVec1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec1[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec1[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec1[2]);
  AssertEquals('element 3 should be D', 'D', LStrVec1[3]);
  AssertEquals('element 4 should be E', 'E', LStrVec1[4]);
end;

procedure TTestCase_vec.Test_AppendToCollection;
var
  LIntArr1:  specialize IArray<Integer>;
  LIntVec1:  specialize IVec<Integer>;
  LIntVec2:  specialize IVec<Integer>;
  LInt64Vec: specialize IVec<Int64>;
  LStrArr1:  specialize IArray<String>;
  LStrVec1:  specialize IVec<String>;
  LStrVec2:  specialize IVec<String>;
begin
  { 失败测试: 追加 nil }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntVec2 := nil;
  AssertFalse('AppendToCollection should fail: append nil collection', LIntVec1.AppendToCollection(LIntVec2 as TCollection));

  { 失败测试: 追加容器自身 }
  LIntVec1 := specialize TVec<Integer>.Create;
  AssertFalse('AppendToCollection should fail: append self collection', LIntVec1.AppendToCollection(LIntVec1 as TCollection));

  { 失败测试: 追加不兼容的泛型容器 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LInt64Vec := specialize TVec<Int64>.Create([1,2,3,4]);
  AssertFalse('AppendToCollection should fail: append incompatible collection', LIntVec1.AppendToCollection(LInt64Vec as TCollection));

  { 空Vec追加空Vec }
  LIntVec1 := specialize TVec<Integer>.Create();
  LIntVec2 := specialize TVec<Integer>.Create();
  AssertTrue('AppendToCollection should succeed: append empty collection', LIntVec1.AppendToCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 0', 0, LIntVec1.GetCount);

  { 空Vec追加非空Vec }
  LIntVec1 := specialize TVec<Integer>.Create();
  LIntVec2 := specialize TVec<Integer>.Create([1,2,3]);
  AssertTrue('AppendToCollection should succeed: append non-empty collection', LIntVec1.AppendToCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LIntVec2.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec2[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec2[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec2[2]);

  { 非空Vec追加空Vec }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntVec2 := specialize TVec<Integer>.Create();
  AssertTrue('AppendToCollection should succeed: append empty collection', LIntVec1.AppendToCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LIntVec2.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec2[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec2[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec2[2]);

  { 非空Vec追加非空Vec }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3]);
  LIntVec2 := specialize TVec<Integer>.Create([4,5,6]);
  AssertTrue('AppendToCollection should succeed: append non-empty collection', LIntVec1.AppendToCollection(LIntVec2 as TCollection));
  AssertEquals('New size should be 6', 6, LIntVec2.GetCount);
  AssertEquals('element 0 should be 4', 4, LIntVec2[0]);
  AssertEquals('element 1 should be 5', 5, LIntVec2[1]);
  AssertEquals('element 2 should be 6', 6, LIntVec2[2]);
  AssertEquals('element 3 should be 1', 1, LIntVec2[3]);
  AssertEquals('element 4 should be 2', 2, LIntVec2[4]);
  AssertEquals('element 5 should be 3', 3, LIntVec2[5]);

  // to Array

  { 空Vec追加到空数组 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('AppendToCollection should succeed: append empty array', LIntVec1.AppendToCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 0, LIntArr1.GetCount);
  AssertNull('Memory should be nil', LIntArr1.Memory);

  { 空Vec追加到非空数组 }
  LIntVec1 := specialize TVec<Integer>.Create;
  LIntArr1 := specialize TArray<Integer>.Create([1,2,3,4]);
  AssertTrue('AppendToCollection should succeed: append non-empty array', LIntVec1.AppendToCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntArr1[0]);
  AssertEquals('element 1 should be 2', 2, LIntArr1[1]);
  AssertEquals('element 2 should be 3', 3, LIntArr1[2]);
  AssertEquals('element 3 should be 4', 4, LIntArr1[3]);

  { 非空Vec追加到空数组 }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create;
  AssertTrue('AppendToCollection should succeed: append empty array', LIntVec1.AppendToCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 4, LIntArr1.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntArr1[0]);
  AssertEquals('element 1 should be 2', 2, LIntArr1[1]);
  AssertEquals('element 2 should be 3', 3, LIntArr1[2]);
  AssertEquals('element 3 should be 4', 4, LIntArr1[3]);

  { 非空Vec追加到非空数组 }
  LIntVec1 := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntArr1 := specialize TArray<Integer>.Create([5,6,7,8]);
  AssertTrue('AppendToCollection should succeed: append non-empty array', LIntVec1.AppendToCollection(LIntArr1 as TCollection));
  AssertEquals('Size should match source array', 8, LIntArr1.GetCount);
  AssertEquals('element 0 should be 5', 5, LIntArr1[0]);
  AssertEquals('element 1 should be 6', 6, LIntArr1[1]);
  AssertEquals('element 2 should be 7', 7, LIntArr1[2]);
  AssertEquals('element 3 should be 8', 8, LIntArr1[3]);
  AssertEquals('element 4 should be 1', 1, LIntArr1[4]);
  AssertEquals('element 5 should be 2', 2, LIntArr1[5]);
  AssertEquals('element 6 should be 3', 3, LIntArr1[6]);
  AssertEquals('element 7 should be 4', 4, LIntArr1[7]);

  ///
  /// 托管类型
  ///

  { 失败测试: 追加 nil }
  LStrVec1 := specialize TVec<String>.Create;
  LStrVec2 := nil;
  AssertFalse('AppendToCollection should fail: append nil collection', LStrVec1.AppendToCollection(LStrVec2 as TCollection));

  { 失败测试: 追加容器自身 }
  LStrVec1 := specialize TVec<String>.Create;
  AssertFalse('AppendToCollection should fail: append self collection', LStrVec1.AppendToCollection(LStrVec1 as TCollection));

  { 失败测试: 追加不兼容的泛型容器 }
  LStrVec1 := specialize TVec<String>.Create;
  LInt64Vec := specialize TVec<Int64>.Create([1,2,3,4]);
  AssertFalse('AppendToCollection should fail: append incompatible collection', LStrVec1.AppendToCollection(LInt64Vec as TCollection));

  { 空Vec追加到空Vec }
  LStrVec1 := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('AppendToCollection should succeed: append empty collection', LStrVec1.AppendToCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 0', 0, LStrVec1.GetCount);

  { 空Vec追加到非空Vec }
  LStrVec1 := specialize TVec<String>.Create;
  LStrVec2 := specialize TVec<String>.Create(['A','B','C']);
  AssertTrue('AppendToCollection should succeed: append non-empty collection', LStrVec1.AppendToCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LStrVec2.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec2[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec2[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec2[2]);

  { 非空Vec追加到空Vec }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrVec2 := specialize TVec<String>.Create;
  AssertTrue('AppendToCollection should succeed: append empty collection', LStrVec1.AppendToCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 3', 3, LStrVec2.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrVec2[0]);
  AssertEquals('element 1 should be B', 'B', LStrVec2[1]);
  AssertEquals('element 2 should be C', 'C', LStrVec2[2]);

  { 非空Vec追加到非空Vec }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrVec2 := specialize TVec<String>.Create(['D','E','F']);
  AssertTrue('AppendToCollection should succeed: append non-empty collection', LStrVec1.AppendToCollection(LStrVec2 as TCollection));
  AssertEquals('New size should be 6', 6, LStrVec2.GetCount);
  AssertEquals('element 0 should be D', 'D', LStrVec2[0]);
  AssertEquals('element 1 should be E', 'E', LStrVec2[1]);
  AssertEquals('element 2 should be F', 'F', LStrVec2[2]);
  AssertEquals('element 3 should be A', 'A', LStrVec2[3]);
  AssertEquals('element 4 should be B', 'B', LStrVec2[4]);
  AssertEquals('element 5 should be C', 'C', LStrVec2[5]);

  // to Array

  { 空Vec追加到空数组 }
  LStrVec1 := specialize TVec<String>.Create;
  LStrArr1 := specialize TArray<String>.Create;
  AssertTrue('AppendToCollection should succeed: append empty array', LStrVec1.AppendToCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 0, LStrArr1.GetCount);
  AssertNull('Memory should be nil', LStrArr1.Memory);

  { 空Vec追加到非空数组 }
  LStrVec1 := specialize TVec<String>.Create;
  LStrArr1 := specialize TArray<String>.Create(['A','B','C']);
  AssertTrue('AppendToCollection should succeed: append non-empty array', LStrVec1.AppendToCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 3, LStrArr1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrArr1[0]);
  AssertEquals('element 1 should be B', 'B', LStrArr1[1]);
  AssertEquals('element 2 should be C', 'C', LStrArr1[2]);

  { 非空Vec追加到空数组 }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrArr1 := specialize TArray<String>.Create;
  AssertTrue('AppendToCollection should succeed: append empty array', LStrVec1.AppendToCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 3, LStrArr1.GetCount);
  AssertEquals('element 0 should be A', 'A', LStrArr1[0]);
  AssertEquals('element 1 should be B', 'B', LStrArr1[1]);
  AssertEquals('element 2 should be C', 'C', LStrArr1[2]);

  { 非空Vec追加到非空数组 }
  LStrVec1 := specialize TVec<String>.Create(['A','B','C']);
  LStrArr1 := specialize TArray<String>.Create(['D','E','F']);
  AssertTrue('AppendToCollection should succeed: append non-empty array', LStrVec1.AppendToCollection(LStrArr1 as TCollection));
  AssertEquals('Size should match source array', 6, LStrArr1.GetCount);
  AssertEquals('element 0 should be D', 'D', LStrArr1[0]);
  AssertEquals('element 1 should be E', 'E', LStrArr1[1]);
  AssertEquals('element 2 should be F', 'F', LStrArr1[2]);
  AssertEquals('element 3 should be A', 'A', LStrArr1[3]);
  AssertEquals('element 4 should be B', 'B', LStrArr1[4]);
  AssertEquals('element 5 should be C', 'C', LStrArr1[5]);
end;

procedure TTestCase_vec.Test_WriteToArrayMemory;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LIntData: array[0..7] of Integer;
  LStrData: array[0..7] of string;
begin
  Initialize(LIntData);
  Initialize(LStrData);

  LIntVec  := specialize TVec<Integer>.Create([1,2,3,4]);

  { 失败测试: 目标 nil }
  AssertFalse('WriteToArrayMemory should fail', LIntVec.WriteToArrayMemory(nil, 4));
  
  { 失败测试: 目标数量0 }
  AssertFalse('WriteToArrayMemory should fail', LIntVec.WriteToArrayMemory(@LIntData[0], 0));

  { 失败测试: 范围越界 }
  AssertFalse('WriteToArrayMemory should fail', LIntVec.WriteToArrayMemory(@LIntData[0], 8));

  { 满写 }
  FillChar(LIntData[0],8 * sizeof(Integer),0);
  AssertTrue('WriteToArrayMemory should succeed', LIntVec.WriteToArrayMemory(@LIntData[0], 4));
  AssertEquals('WriteToArrayMemory should store values correctly', 1, LIntData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 2, LIntData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', 3, LIntData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', 4, LIntData[3]);

  { 写入前两个 }
  FillChar(LIntData[0], 8 * sizeof(Integer), 0);
  AssertTrue('WriteToArrayMemory should succeed', LIntVec.WriteToArrayMemory(@LIntData[0], 2));
  AssertEquals('WriteToArrayMemory should store values correctly', 1, LIntData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 2, LIntData[1]);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<String>.Create(['A','B','C','D']);

  { 失败测试: 目标 nil }
  AssertFalse('WriteToArrayMemory should fail', LStrVec.WriteToArrayMemory(nil, 4));

  { 失败测试: 目标数量0 }
  AssertFalse('WriteToArrayMemory should fail', LStrVec.WriteToArrayMemory(@LStrData[0], 0));

  { 失败测试: 范围越界 }
  AssertFalse('WriteToArrayMemory should fail', LStrVec.WriteToArrayMemory(@LStrData[0], 8));

  { 满写 }
  FillChar(LStrData[0], 8 * sizeof(string), 0);
  AssertTrue('WriteToArrayMemory should succeed', LStrVec.WriteToArrayMemory(@LStrData[0], 4));
  AssertEquals('WriteToArrayMemory should store values correctly', 'A', LStrData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'B', LStrData[1]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'C', LStrData[2]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'D', LStrData[3]);

  { 写入前两个 }
  FillChar(LStrData[0], 8 * sizeof(string), 0);
  AssertTrue('WriteToArrayMemory should succeed', LStrVec.WriteToArrayMemory(@LStrData[0], 2));
  AssertEquals('WriteToArrayMemory should store values correctly', 'A', LStrData[0]);
  AssertEquals('WriteToArrayMemory should store values correctly', 'B', LStrData[1]);
end;

procedure TTestCase_vec.Test_Fill3;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LIntVec.Fill(0, 0, 888);
    end);

  { 异常测试: 越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertException(
    'Failed to fill: out of range',
    exception,
    procedure
    begin
      LIntVec.Fill(0, 9, 888);
    end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 从0开始,填充8个元素 }
  LIntVec.Fill(0, 8, 888);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 888', 888, LIntVec[0]);
  AssertEquals('element 1 should be 888', 888, LIntVec[1]);
  AssertEquals('element 2 should be 888', 888, LIntVec[2]);
  AssertEquals('element 3 should be 888', 888, LIntVec[3]);
  AssertEquals('element 4 should be 888', 888, LIntVec[4]);
  AssertEquals('element 5 should be 888', 888, LIntVec[5]);
  AssertEquals('element 6 should be 888', 888, LIntVec[6]);
  AssertEquals('element 7 should be 888', 888, LIntVec[7]);

  { 从1开始,填充3个元素 }
  LIntVec.Fill(1, 3, 999);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 888', 888, LIntVec[0]);
  AssertEquals('element 1 should be 999', 999, LIntVec[1]);
  AssertEquals('element 2 should be 999', 999, LIntVec[2]);
  AssertEquals('element 3 should be 999', 999, LIntVec[3]);
  AssertEquals('element 4 should be 888', 888, LIntVec[4]);
  AssertEquals('element 5 should be 888', 888, LIntVec[5]);
  AssertEquals('element 6 should be 888', 888, LIntVec[6]);
  AssertEquals('element 7 should be 888', 888, LIntVec[7]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LStrVec := specialize TVec<String>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LStrVec.Fill(0, 0, '888');
    end);

  { 异常测试: 越界 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  AssertException(
    'Failed to fill: out of range',
    exception,
    procedure
    begin
      LStrVec.Fill(0, 9, '888');
    end);  
  {$ENDIF}

  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);

  { 从0开始,填充8个元素 }
  LStrVec.Fill(0, 8, '888');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 888', '888', LStrVec[0]);
  AssertEquals('element 1 should be 888', '888', LStrVec[1]);
  AssertEquals('element 2 should be 888', '888', LStrVec[2]);
  AssertEquals('element 3 should be 888', '888', LStrVec[3]);
  AssertEquals('element 4 should be 888', '888', LStrVec[4]);
  AssertEquals('element 5 should be 888', '888', LStrVec[5]);
  AssertEquals('element 6 should be 888', '888', LStrVec[6]);
  AssertEquals('element 7 should be 888', '888', LStrVec[7]);

  { 从1开始,填充3个元素 }
  LStrVec.Fill(1, 3, 'hello');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 888', '888', LStrVec[0]);
  AssertEquals('element 1 should be hello', 'hello', LStrVec[1]);
  AssertEquals('element 2 should be hello', 'hello', LStrVec[2]);
  AssertEquals('element 3 should be hello', 'hello', LStrVec[3]);
  AssertEquals('element 4 should be 888', '888', LStrVec[4]);
  AssertEquals('element 5 should be 888', '888', LStrVec[5]);
  AssertEquals('element 6 should be 888', '888', LStrVec[6]);
  AssertEquals('element 7 should be 888', '888', LStrVec[7]);

  { 全部填充成 fafafa }
  LStrVec.Fill(0, 8, 'fafafa');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be fafafa', 'fafafa', LStrVec[0]);
  AssertEquals('element 1 should be fafafa', 'fafafa', LStrVec[1]);
  AssertEquals('element 2 should be fafafa', 'fafafa', LStrVec[2]);
  AssertEquals('element 3 should be fafafa', 'fafafa', LStrVec[3]);
  AssertEquals('element 4 should be fafafa', 'fafafa', LStrVec[4]);
  AssertEquals('element 5 should be fafafa', 'fafafa', LStrVec[5]);
  AssertEquals('element 6 should be fafafa', 'fafafa', LStrVec[6]);
  AssertEquals('element 7 should be fafafa', 'fafafa', LStrVec[7]);
end;

procedure TTestCase_vec.Test_Fill2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LIntVec.Fill(0, 999);
    end);

  { 异常测试: 越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertException(
    'Failed to fill: out of range',
    exception,
    procedure
    begin
      LIntVec.Fill(9, 999);
    end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 从0开始,填充999 }
  LIntVec.Fill(0, 999);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 999', 999, LIntVec[0]);
  AssertEquals('element 1 should be 999', 999, LIntVec[1]);
  AssertEquals('element 2 should be 999', 999, LIntVec[2]);
  AssertEquals('element 3 should be 999', 999, LIntVec[3]);
  AssertEquals('element 4 should be 999', 999, LIntVec[4]);
  AssertEquals('element 5 should be 999', 999, LIntVec[5]);
  AssertEquals('element 6 should be 999', 999, LIntVec[6]);
  AssertEquals('element 7 should be 999', 999, LIntVec[7]);

  { 从1开始,填充128 }
  LIntVec.Fill(1, 128);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 999', 999, LIntVec[0]);
  AssertEquals('element 1 should be 128', 128, LIntVec[1]);
  AssertEquals('element 2 should be 128', 128, LIntVec[2]);
  AssertEquals('element 3 should be 128', 128, LIntVec[3]);
  AssertEquals('element 4 should be 128', 128, LIntVec[4]);
  AssertEquals('element 5 should be 128', 128, LIntVec[5]);
  AssertEquals('element 6 should be 128', 128, LIntVec[6]);
  AssertEquals('element 7 should be 128', 128, LIntVec[7]);

  { 填充最后一个为1024 }
  LIntVec.Fill(7, 1024);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 999',  999, LIntVec[0]);
  AssertEquals('element 1 should be 128',  128, LIntVec[1]);
  AssertEquals('element 2 should be 128',  128, LIntVec[2]);
  AssertEquals('element 3 should be 128',  128, LIntVec[3]);
  AssertEquals('element 4 should be 128',  128, LIntVec[4]);
  AssertEquals('element 5 should be 128',  128, LIntVec[5]);
  AssertEquals('element 6 should be 128',  128, LIntVec[6]);
  AssertEquals('element 7 should be 1024', 1024, LIntVec[7]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LStrVec := specialize TVec<String>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LStrVec.Fill(0, '999');
    end);

  { 异常测试: 越界 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  AssertException(
    'Failed to fill: index out of range',
    exception,
    procedure
    begin
      LStrVec.Fill(9, '999');
    end);
  {$ENDIF}

  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);

  { 从0开始,填充999 }
  LStrVec.Fill(0, '999');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 999', '999', LStrVec[0]);
  AssertEquals('element 1 should be 999', '999', LStrVec[1]);
  AssertEquals('element 2 should be 999', '999', LStrVec[2]);
  AssertEquals('element 3 should be 999', '999', LStrVec[3]);
  AssertEquals('element 4 should be 999', '999', LStrVec[4]);
  AssertEquals('element 5 should be 999', '999', LStrVec[5]);
  AssertEquals('element 6 should be 999', '999', LStrVec[6]);
  AssertEquals('element 7 should be 999', '999', LStrVec[7]);

  { 从1开始,填充128 }
  LStrVec.Fill(1, '128');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 999', '999', LStrVec[0]);
  AssertEquals('element 1 should be 128', '128', LStrVec[1]);
  AssertEquals('element 2 should be 128', '128', LStrVec[2]);
  AssertEquals('element 3 should be 128', '128', LStrVec[3]);
  AssertEquals('element 4 should be 128', '128', LStrVec[4]);
  AssertEquals('element 5 should be 128', '128', LStrVec[5]);
  AssertEquals('element 6 should be 128', '128', LStrVec[6]);
  AssertEquals('element 7 should be 128', '128', LStrVec[7]);

  { 填充最后一个为1024 }
  LStrVec.Fill(7, '1024');
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 999',  '999', LStrVec[0]);
  AssertEquals('element 1 should be 128',  '128', LStrVec[1]);
  AssertEquals('element 2 should be 128',  '128', LStrVec[2]);
  AssertEquals('element 3 should be 128',  '128', LStrVec[3]);
  AssertEquals('element 4 should be 128',  '128', LStrVec[4]);
  AssertEquals('element 5 should be 128',  '128', LStrVec[5]);
  AssertEquals('element 6 should be 128',  '128', LStrVec[6]);
  AssertEquals('element 7 should be 1024', '1024', LStrVec[7]);

end;

procedure TTestCase_vec.Test_Fill1;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LIntVec.Fill(0, 999);
    end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4]);
  LIntVec.Fill(999);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('capacity should be 4', 4, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 999', 999, LIntVec[0]);
  AssertEquals('element 1 should be 999', 999, LIntVec[1]);
  AssertEquals('element 2 should be 999', 999, LIntVec[2]);
  AssertEquals('element 3 should be 999', 999, LIntVec[3]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数量为0 }
  LStrVec := specialize TVec<String>.Create;
  AssertException(
    'Failed to fill: count is 0',
    exception,
    procedure
    begin
      LStrVec.Fill(0, '999');
    end);
  {$ENDIF}

  LStrVec := specialize TVec<String>.Create(['11','22']);
  LStrVec.Fill('fafafa');
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('capacity should be 2', 2, LStrVec.GetCapacity);
  AssertEquals('element 0 should be fafafa', 'fafafa', LStrVec[0]);
  AssertEquals('element 1 should be fafafa', 'fafafa', LStrVec[1]);

  { 托管元素测试 1个元素 }
  LStrVec := specialize TVec<String>.Create(['11']);
  LStrVec.Fill('fafafa');
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('capacity should be 1', 1, LStrVec.GetCapacity);
  AssertEquals('element 0 should be fafafa', 'fafafa', LStrVec[0]);
end;

procedure TTestCase_vec.Test_Zero3;
var
  LU8Vec:    specialize IVec<UInt8>;
  LU16Vec:   specialize IVec<UInt16>;
  LU32Vec:   specialize IVec<UInt32>;
  LU64Vec:   specialize IVec<UInt64>;
  LStrVec:   specialize IVec<string>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LU8Vec := specialize TVec<UInt8>.Create;
  AssertException(
    'Failed to zero: array is empty',
    exception,
    procedure
    begin
      LU8Vec.Zero(1, 3);
    end);

  { 异常测试: 数量为0 }
  LU8Vec := specialize TVec<UInt8>.Create([1, 1, 1, 1, 1]);
  AssertException(
    'Failed to zero: out of range',
    exception,
    procedure
    begin
      LU8Vec.Zero(1, 0);
    end);

  { 异常测试: 索引越界 }
  LU8Vec := specialize TVec<UInt8>.Create([1, 1, 1, 1, 1]);
  AssertException(
    'Failed to zero: index out of range',
    exception,
    procedure
    begin
      LU8Vec.Zero(5, 3);
    end);
  {$ENDIF}

  { U8 写零 }
  LU8Vec := specialize TVec<UInt8>.Create([1, 1, 1, 1, 1]);
  LU8Vec.Zero(1, 3);
  AssertEquals(1, LU8Vec[0]);
  AssertEquals(0, LU8Vec[1]);
  AssertEquals(0, LU8Vec[2]);
  AssertEquals(0, LU8Vec[3]);
  AssertEquals(1, LU8Vec[4]);

  { U16 写零 }
  LU16Vec := specialize TVec<UInt16>.Create([1, 1, 1, 1, 1]);
  LU16Vec.Zero(1, 3);
  AssertEquals(1, LU16Vec[0]);
  AssertEquals(0, LU16Vec[1]);
  AssertEquals(0, LU16Vec[2]);
  AssertEquals(0, LU16Vec[3]);
  AssertEquals(1, LU16Vec[4]);

  { U32 写零 }
  LU32Vec := specialize TVec<UInt32>.Create([1, 1, 1, 1, 1]);
  LU32Vec.Zero(1, 3);
  AssertEquals(1, LU32Vec[0]);
  AssertEquals(0, LU32Vec[1]);
  AssertEquals(0, LU32Vec[2]);
  AssertEquals(0, LU32Vec[3]);
  AssertEquals(1, LU32Vec[4]);

  { U64 写零 }
  LU64Vec := specialize TVec<UInt64>.Create([1, 1, 1, 1, 1]);
  LU64Vec.Zero(1, 3);
  AssertEquals(1, LU64Vec[0]);
  AssertEquals(0, LU64Vec[1]);
  AssertEquals(0, LU64Vec[2]);
  AssertEquals(0, LU64Vec[3]);
  AssertEquals(1, LU64Vec[4]);

  ///
  /// 托管类型
  ///

  LStrVec := specialize TVec<string>.Create(['HELLO', 'WORLD','FAFAFA']);
  LStrVec.Zero(0, 2);
  AssertEquals('', LStrVec[0]);
  AssertEquals('', LStrVec[1]);
  AssertEquals('FAFAFA', LStrVec[2]);
end;

procedure TTestCase_vec.Test_Zero2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertException(
    'Failed to zero: array is empty',
    exception,
    procedure
    begin
      LIntVec.Zero(4);
    end);

  { 异常测试: 索引越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertException(
    'Failed to zero: index out of range',
    exception,
    procedure
    begin
      LIntVec.Zero(8);
    end);
  {$ENDIF}

  { 全部写0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Zero(0);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 0', 0, LIntVec[1]);
  AssertEquals('element 2 should be 0', 0, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 0', 0, LIntVec[4]);
  AssertEquals('element 5 should be 0', 0, LIntVec[5]);
  AssertEquals('element 6 should be 0', 0, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  { 部分写0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Zero(1);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 0', 0, LIntVec[1]);
  AssertEquals('element 2 should be 0', 0, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 0', 0, LIntVec[4]);
  AssertEquals('element 5 should be 0', 0, LIntVec[5]);
  AssertEquals('element 6 should be 0', 0, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  { 最后一个元素写0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Zero(7);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LStrVec := specialize TVec<String>.Create;
  AssertException(
    'Failed to zero: array is empty',
    exception,
    procedure
    begin
      LStrVec.Zero(4);
    end);

  { 异常测试: 索引越界 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  AssertException(
    'Failed to zero: index out of range',
    exception,
    procedure
    begin
      LStrVec.Zero(8);
    end);
  {$ENDIF}

  { 全部写0 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec.Zero(0);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 0', '', LStrVec[0]);
  AssertEquals('element 1 should be 0', '', LStrVec[1]);
  AssertEquals('element 2 should be 0', '', LStrVec[2]);
  AssertEquals('element 3 should be 0', '', LStrVec[3]);
  AssertEquals('element 4 should be 0', '', LStrVec[4]);
  AssertEquals('element 5 should be 0', '', LStrVec[5]);
  AssertEquals('element 6 should be 0', '', LStrVec[6]);
  AssertEquals('element 7 should be 0', '', LStrVec[7]);

  { 部分写0 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec.Zero(1);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 11', '11', LStrVec[0]);
  AssertEquals('element 1 should be 0', '', LStrVec[1]);
  AssertEquals('element 2 should be 0', '', LStrVec[2]);
  AssertEquals('element 3 should be 0', '', LStrVec[3]);
  AssertEquals('element 4 should be 0', '', LStrVec[4]);
  AssertEquals('element 5 should be 0', '', LStrVec[5]);
  AssertEquals('element 6 should be 0', '', LStrVec[6]);
  AssertEquals('element 7 should be 0', '', LStrVec[7]);

  { 最后一个元素写0 }
  LStrVec := specialize TVec<String>.Create(['11','22','33','44','55','66','77','88']);
  LStrVec.Zero(7);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 11', '11', LStrVec[0]);
  AssertEquals('element 1 should be 22', '22', LStrVec[1]);
  AssertEquals('element 2 should be 33', '33', LStrVec[2]);
  AssertEquals('element 3 should be 44', '44', LStrVec[3]);
  AssertEquals('element 4 should be 55', '55', LStrVec[4]);
  AssertEquals('element 5 should be 66', '66', LStrVec[5]);
  AssertEquals('element 6 should be 77', '77', LStrVec[6]);
  AssertEquals('element 7 should be 0', '', LStrVec[7]);
end;

procedure TTestCase_vec.Test_Zero;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertException(
    'Failed to zero: array is empty',
    exception,
    procedure
    begin
      LIntVec.Zero;
    end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.Zero;
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 0', 0, LIntVec[1]);
  AssertEquals('element 2 should be 0', 0, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 0', 0, LIntVec[4]);
  AssertEquals('element 5 should be 0', 0, LIntVec[5]);
  AssertEquals('element 6 should be 0', 0, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 数组为空 }
  LStrVec := specialize TVec<String>.Create;
  AssertException(
    'Failed to zero: array is empty',
    exception,
    procedure
    begin
      LStrVec.Zero;
    end);
  {$ENDIF}

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrVec.Zero;
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('element 0 should be 0', '', LStrVec[0]);
  AssertEquals('element 1 should be 0', '', LStrVec[1]);
  AssertEquals('element 2 should be 0', '', LStrVec[2]);
  AssertEquals('element 3 should be 0', '', LStrVec[3]);
  AssertEquals('element 4 should be 0', '', LStrVec[4]);
  AssertEquals('element 5 should be 0', '', LStrVec[5]);
  AssertEquals('element 6 should be 0', '', LStrVec[6]);
  AssertEquals('element 7 should be 0', '', LStrVec[7]);
end;

procedure TTestCase_vec.Test_Swap;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  i:       SizeUInt;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LIntVec := specialize TVec<Integer>.Create([1, 2, 3, 4]);

  { 异常测试: 数量为0 }
  AssertException(
    'Failed to swap: element count is 0',
    exception,
    procedure
    begin
      LIntVec.Swap(0, 3, 0);
    end);

  { 异常测试: 索引相同 }
  AssertException(
    'Failed to swap: same index',
    exception,
    procedure
    begin
      LIntVec.Swap(1, 1, 1);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'Failed to swap: index out of range',
    exception,
    procedure
    begin
      LIntVec.Swap(1, 4, 1);
    end);

  { 异常测试: 范围越界 }
  AssertException(
    'Failed to swap: out of range',
    exception,
    procedure
    begin
      LIntVec.Swap(0, 3, 2);
    end);

  { 异常测试: 正向重叠 }
  AssertException(
    'Failed to swap: overlap',
    exception,
    procedure
    begin
      LIntVec.Swap(0, 1, 2);
    end);

  { 异常测试: 反向重叠 }
  AssertException(
    'Failed to swap: overlap',
    exception,
    procedure
    begin
      LIntVec.Swap(1, 0, 2);
    end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1, 2, 3, 4]);

  { 交换首尾两个元素 }
  LIntVec.Swap(0, 3, 1);
  AssertEquals(4, LIntVec[0]);
  AssertEquals(2, LIntVec[1]);
  AssertEquals(3, LIntVec[2]);
  AssertEquals(1, LIntVec[3]);

  { 反转前两个元素 }
  LIntVec.Swap(0, 2, 2);
  AssertEquals(3, LIntVec[0]);
  AssertEquals(1, LIntVec[1]);
  AssertEquals(4, LIntVec[2]);
  AssertEquals(2, LIntVec[3]);

  { 分块交换 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntVec.Resize(65536);

  for i := 0 to LIntVec.Count - 1 do
    LIntVec[i] := i;

  LIntVec.Swap(0, (LIntVec.Count div 2), LIntVec.Count div 2);

  for i := 0 to (LIntVec.Count div 2) - 1 do
    AssertEquals(Integer((LIntVec.Count div 2) + i), LIntVec[i]);

  for i := (LIntVec.Count div 2) to LIntVec.Count - 1 do
    AssertEquals(Integer(i - (LIntVec.Count div 2)), LIntVec[i]);

  ///
  /// 托管元素
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  LStrVec := specialize TVec<String>.Create(['HELLO', 'WORLD', 'FAFAFA', 'STUDIO']);

  { 异常测试: 数量为0 }
  AssertException(
    'Failed to swap: element count is 0',
    exception,
    procedure
    begin
      LStrVec.Swap(0, 3, 0);
    end);

  { 异常测试: 索引相同 }
  AssertException(
    'Failed to swap: same index',
    exception,
    procedure
    begin
      LStrVec.Swap(1, 1, 1);
    end);

  { 异常测试: 索引越界 }
  AssertException(
    'Failed to swap: index out of range',
    exception,
    procedure
    begin
      LStrVec.Swap(1, 4, 1);
    end);

  { 异常测试: 范围越界 }
  AssertException(
    'Failed to swap: out of range',
    exception,
    procedure
    begin
      LStrVec.Swap(0, 3, 2);
    end);

  { 异常测试: 正向重叠 }
  AssertException(
    'Failed to swap: overlap',
    exception,
    procedure
    begin
      LStrVec.Swap(0, 1, 2);
    end);

  { 异常测试: 反向重叠 }
  AssertException(
    'Failed to swap: overlap',
    exception,
    procedure
    begin
      LStrVec.Swap(1, 0, 2);
    end);
  {$ENDIF}
  
  LStrVec := specialize TVec<String>.Create(['HELLO', 'WORLD', 'FAFAFA', 'STUDIO']);

  { 交换首尾两个元素 }
  LStrVec.Swap(0, 3, 1);
  AssertEquals('STUDIO', LStrVec[0]);
  AssertEquals('WORLD',  LStrVec[1]);
  AssertEquals('FAFAFA', LStrVec[2]);
  AssertEquals('HELLO',  LStrVec[3]);

  { 反转前两个元素 }
  LStrVec.Swap(0, 2, 2);
  AssertEquals('FAFAFA', LStrVec[0]);
  AssertEquals('HELLO',  LStrVec[1]);
  AssertEquals('STUDIO', LStrVec[2]);
  AssertEquals('WORLD',  LStrVec[3]);

  { 分块交换 }
  LStrVec := specialize TVec<String>.Create;
  LStrVec.Resize(65536);

  for i := 0 to LStrVec.Count - 1 do
    LStrVec[i] := IntToStr(i);

  LStrVec.Swap(0, (LStrVec.Count div 2), LStrVec.Count div 2);

  for i := 0 to (LStrVec.Count div 2) - 1 do
    AssertEquals(IntToStr((LStrVec.Count div 2) + i), LStrVec[i]);

  for i := (LStrVec.Count div 2) to LStrVec.Count - 1 do
    AssertEquals(IntToStr(i - (LStrVec.Count div 2)), LStrVec[i]);
end;

procedure TTestCase_vec.Test_Swap2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 索引相同 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4]);
  AssertException(
    'Failed to swap: Same index',
    exception,
    procedure
    begin
      LIntVec.Swap(1, 1);
    end);

  { 异常测试: 索引越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4]);
  AssertException(
  'Failed to swap: index out of range',
  exception,
  procedure begin
    LIntVec.Swap(1, 4);
  end);
  {$ENDIF}

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  { 交换元素 0 和 7 }
  LIntVec.Swap(0,7);
  AssertEquals('element 0 should be 8', 8, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 1', 1, LIntVec[7]);

  { 托管元素测试 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrVec.Swap(0,7);
  AssertEquals('element 0 should be hhhh', 'hhhh', LStrVec[0]);
  AssertEquals('element 1 should be bb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);
  AssertEquals('element 7 should be aaaa', 'aaaa', LStrVec[7]);
end;

procedure TTestCase_vec.Test_Copy;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 索引越界}
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4]);
  AssertException(exception,
  procedure begin
    LIntVec.Copy(1,3,2);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(3,1,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 索引相同 }
  AssertException(exception,
  procedure begin
    LIntVec.Copy(1,1,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 范围越界 }
  AssertException(exception,
  procedure begin
    LIntVec.Copy(0,1,4);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(0,1,5);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(1,2,3);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(2,3,2);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(1,0,4);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(2,1,3);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LIntVec.Copy(3,2,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 元素数量为0 }
  AssertException(exception,
  procedure begin
    LIntVec.Copy(1,3,0);
  end,
  'Failed to copy: out of range');

  {$ENDIF}

  { 正向拷贝 }
  LIntVec := specialize TVec<Integer>.Create([0,1,2,3,4,5,6,7]);
  LIntVec.Copy(0,3,2);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 1', 1, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);
  AssertEquals('element 6 should be 6', 6, LIntVec[6]);
  AssertEquals('element 7 should be 7', 7, LIntVec[7]);

  { 正向边界拷贝 }
  LIntVec.Copy(0,4,4);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 0', 0, LIntVec[4]);
  AssertEquals('element 5 should be 1', 1, LIntVec[5]);
  AssertEquals('element 6 should be 2', 2, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  { 反向拷贝 }
  LIntVec.Copy(3,0,3);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 0', 0, LIntVec[1]);
  AssertEquals('element 2 should be 1', 1, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 1', 0, LIntVec[4]);
  AssertEquals('element 5 should be 2', 1, LIntVec[5]);
  AssertEquals('element 6 should be 0', 2, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  { 反向边界拷贝 }
  LIntVec.Copy(4,0,4);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 0', 0, LIntVec[3]);
  AssertEquals('element 4 should be 0', 0, LIntVec[4]);
  AssertEquals('element 5 should be 1', 1, LIntVec[5]);
  AssertEquals('element 6 should be 2', 2, LIntVec[6]);
  AssertEquals('element 7 should be 0', 0, LIntVec[7]);

  ///
  /// 托管类型
  ///

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 索引越界 }
  LStrVec := specialize TVec<String>.Create(['HELLO', 'WORLD', 'FAFAFA']);
  AssertException(exception,
  procedure begin
    LStrVec.Copy(1,3,2);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(3,1,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 索引相同 }
  AssertException(exception,
  procedure begin
    LStrVec.Copy(1,1,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 范围越界 }
  AssertException(exception,
  procedure begin
    LStrVec.Copy(0,1,4);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(0,1,5);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(1,2,3);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(2,3,2);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(1,0,4);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(2,1,3);
  end,
  'Failed to copy: out of range');

  AssertException(exception,
  procedure begin
    LStrVec.Copy(3,2,2);
  end,
  'Failed to copy: out of range');

  { 异常测试: 元素数量为0 }
  AssertException(exception,
  procedure begin
    LStrVec.Copy(1,3,0);
  end,
  'Failed to copy: out of range');

  {$ENDIF}

  { 正向拷贝 }
  LStrVec := specialize TVec<String>.Create(['str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7']);
  LStrVec.Copy(0,3,2);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str0', 'str0', LStrVec[3]);
  AssertEquals('element 4 should be str1', 'str1', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);
  AssertEquals('element 6 should be str6', 'str6', LStrVec[6]);
  AssertEquals('element 7 should be str7', 'str7', LStrVec[7]);

  { 正向边界拷贝 }
  LStrVec.Copy(0,4,4);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str0', 'str0', LStrVec[3]);
  AssertEquals('element 4 should be str0', 'str0', LStrVec[4]);
  AssertEquals('element 5 should be str1', 'str1', LStrVec[5]);
  AssertEquals('element 6 should be str2', 'str2', LStrVec[6]);
  AssertEquals('element 7 should be str0', 'str0', LStrVec[7]);

  { 反向拷贝 }
  LStrVec.Copy(3,0,3);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str0', 'str0', LStrVec[1]);
  AssertEquals('element 2 should be str1', 'str1', LStrVec[2]);
  AssertEquals('element 3 should be str0', 'str0', LStrVec[3]);
  AssertEquals('element 4 should be str0', 'str0', LStrVec[4]);
  AssertEquals('element 5 should be str1', 'str1', LStrVec[5]);
  AssertEquals('element 6 should be str2', 'str2', LStrVec[6]);
  AssertEquals('element 7 should be str0', 'str0', LStrVec[7]);

  { 反向边界拷贝 }
  LStrVec.Copy(4,0,4);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str0', 'str0', LStrVec[3]);
  AssertEquals('element 4 should be str0', 'str0', LStrVec[4]);
  AssertEquals('element 5 should be str1', 'str1', LStrVec[5]);
  AssertEquals('element 6 should be str2', 'str2', LStrVec[6]);
  AssertEquals('element 7 should be str0', 'str0', LStrVec[7]);

end;

procedure TTestCase_vec.Test_PushFromMemory;
const
  DATA_INT1: array[0..7] of Integer = (0, 1, 2, 3, 4, 5, 6, 7);
  DATA_INT2: array[0..15] of Integer = (8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23);
  DATA_STR: array[0..7] of String = ('str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7');
  DATA_STR2: array[0..15] of String = ('str8', 'str9', 'str10', 'str11', 'str12', 'str13', 'str14', 'str15', 'str16', 'str17', 'str18', 'str19', 'str20', 'str21', 'str22', 'str23');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(2, 2);

  { 失败测试: 压入0个元素 }
  AssertFalse('PushFromMemory should return false', LIntVec.PushFromMemory(@DATA_INT1[0], 0));
  
  { 失败测试: 压入nil }
  AssertFalse('PushFromMemory should return false', LIntVec.PushFromMemory(nil, 8));
  
  LIntVec.PushFromMemory(@DATA_INT1[0], 8);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);
  AssertEquals('element 6 should be 6', 6, LIntVec[6]);
  AssertEquals('element 7 should be 7', 7, LIntVec[7]);

  LIntVec.PushFromMemory(@DATA_INT2[0], 16);
  AssertEquals('New size should be 24',   24, LIntVec.GetCount);
  AssertEquals('element 0 should be 0',   0,  LIntVec[0]);
  AssertEquals('element 1 should be 1',   1,  LIntVec[1]);
  AssertEquals('element 2 should be 2',   2,  LIntVec[2]);
  AssertEquals('element 3 should be 3',   3,  LIntVec[3]);
  AssertEquals('element 4 should be 4',   4,  LIntVec[4]);
  AssertEquals('element 5 should be 5',   5,  LIntVec[5]);
  AssertEquals('element 6 should be 6',   6,  LIntVec[6]);
  AssertEquals('element 7 should be 7',   7,  LIntVec[7]);
  AssertEquals('element 8 should be 8',   8,  LIntVec[8]);
  AssertEquals('element 9 should be 9',   9,  LIntVec[9]);
  AssertEquals('element 10 should be 10', 10, LIntVec[10]);
  AssertEquals('element 11 should be 11', 11, LIntVec[11]);
  AssertEquals('element 12 should be 12', 12, LIntVec[12]);
  AssertEquals('element 13 should be 13', 13, LIntVec[13]);
  AssertEquals('element 14 should be 14', 14, LIntVec[14]);
  AssertEquals('element 15 should be 15', 15, LIntVec[15]);
  AssertEquals('element 16 should be 16', 16, LIntVec[16]);
  AssertEquals('element 17 should be 17', 17, LIntVec[17]);
  AssertEquals('element 18 should be 18', 18, LIntVec[18]);
  AssertEquals('element 19 should be 19', 19, LIntVec[19]);
  AssertEquals('element 20 should be 20', 20, LIntVec[20]);
  AssertEquals('element 21 should be 21', 21, LIntVec[21]);
  AssertEquals('element 22 should be 22', 22, LIntVec[22]);
  AssertEquals('element 23 should be 23', 23, LIntVec[23]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(2, 2);

  { 失败测试: 压入0个元素 }
  AssertFalse('PushFromMemory should return false', LStrVec.PushFromMemory(@DATA_STR[0], 0));
  
  { 失败测试: 压入nil }
  AssertFalse('PushFromMemory should return false', LStrVec.PushFromMemory(nil, 8));

  LStrVec.PushFromMemory(@DATA_STR[0], 8);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);
  AssertEquals('element 6 should be str6', 'str6', LStrVec[6]);
  AssertEquals('element 7 should be str7', 'str7', LStrVec[7]);

  LStrVec.PushFromMemory(@DATA_STR2[0], 16);
  AssertEquals('New size should be 24', 24, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);
  AssertEquals('element 6 should be str6', 'str6', LStrVec[6]);
  AssertEquals('element 7 should be str7', 'str7', LStrVec[7]);
  AssertEquals('element 8 should be str8', 'str8', LStrVec[8]);
  AssertEquals('element 9 should be str9', 'str9', LStrVec[9]);
  AssertEquals('element 10 should be str10', 'str10', LStrVec[10]);
  AssertEquals('element 11 should be str11', 'str11', LStrVec[11]);
  AssertEquals('element 12 should be str12', 'str12', LStrVec[12]);
  AssertEquals('element 13 should be str13', 'str13', LStrVec[13]);
  AssertEquals('element 14 should be str14', 'str14', LStrVec[14]);
  AssertEquals('element 15 should be str15', 'str15', LStrVec[15]);
  AssertEquals('element 16 should be str16', 'str16', LStrVec[16]);
  AssertEquals('element 17 should be str17', 'str17', LStrVec[17]);
  AssertEquals('element 18 should be str18', 'str18', LStrVec[18]);
  AssertEquals('element 19 should be str19', 'str19', LStrVec[19]);
  AssertEquals('element 20 should be str20', 'str20', LStrVec[20]);
  AssertEquals('element 21 should be str21', 'str21', LStrVec[21]);
  AssertEquals('element 22 should be str22', 'str22', LStrVec[22]);
  AssertEquals('element 23 should be str23', 'str23', LStrVec[23]);

end;

procedure TTestCase_vec.Test_PushFromMemory2;
const
  DATA_INT: array[0..7] of Integer = (0, 1, 2, 3, 4, 5, 6, 7);
  DATA_STR: array[0..7] of String = ('str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(2, 2);
  LIntVec.PushFromMemory(@DATA_INT[0]);
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);

  LIntVec.PushFromMemory(@DATA_INT[1]);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);

  LIntVec.PushFromMemory(@DATA_INT[2]);
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);

  LIntVec.PushFromMemory(@DATA_INT[3]);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);

  LIntVec.PushFromMemory(@DATA_INT[4]);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);

  LIntVec.PushFromMemory(@DATA_INT[5]);
  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);

  LIntVec.PushFromMemory(@DATA_INT[6]);
  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);

  LIntVec.PushFromMemory(@DATA_INT[7]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 0', 0, LIntVec[0]);
  AssertEquals('element 1 should be 1', 1, LIntVec[1]);
  AssertEquals('element 2 should be 2', 2, LIntVec[2]);
  AssertEquals('element 3 should be 3', 3, LIntVec[3]);
  AssertEquals('element 4 should be 4', 4, LIntVec[4]);
  AssertEquals('element 5 should be 5', 5, LIntVec[5]);
  AssertEquals('element 6 should be 6', 6, LIntVec[6]);
  AssertEquals('element 7 should be 7', 7, LIntVec[7]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(2, 2);
  LStrVec.PushFromMemory(@DATA_STR[0]);
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);

  LStrVec.PushFromMemory(@DATA_STR[1]);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);

  LStrVec.PushFromMemory(@DATA_STR[2]);
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);

  LStrVec.PushFromMemory(@DATA_STR[3]);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);

  LStrVec.PushFromMemory(@DATA_STR[4]);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);

  LStrVec.PushFromMemory(@DATA_STR[5]);
  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);

  LStrVec.PushFromMemory(@DATA_STR[6]);
  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);

  LStrVec.PushFromMemory(@DATA_STR[7]);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);
  AssertEquals('element 6 should be str6', 'str6', LStrVec[6]);
  AssertEquals('element 7 should be str7', 'str7', LStrVec[7]);

end;

procedure TTestCase_vec.Test_PushFromArray;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(2, 2);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 压入0个元素 }
  AssertException(exception,
  procedure begin
    LIntVec.PushFromArray([]);
  end,
  'Failed to push: empty array');
  {$ENDIF}

  LIntVec.PushFromArray([1, 2, 3, 4, 5, 6, 7, 8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  LIntVec.PushFromArray([9, 10, 11, 12, 13, 14, 15, 16]);
  AssertEquals('New size should be 16', 16, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);
  AssertEquals('element 8 should be 9', 9, LIntVec[8]);
  AssertEquals('element 9 should be 10', 10, LIntVec[9]);
  AssertEquals('element 10 should be 11', 11, LIntVec[10]);
  AssertEquals('element 11 should be 12', 12, LIntVec[11]);
  AssertEquals('element 12 should be 13', 13, LIntVec[12]);
  AssertEquals('element 13 should be 14', 14, LIntVec[13]);
  AssertEquals('element 14 should be 15', 15, LIntVec[14]);
  AssertEquals('element 15 should be 16', 16, LIntVec[15]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(2, 2);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 压入0个元素 }
  AssertException(exception,
  procedure begin
    LStrVec.PushFromArray([]);
  end,
  'Failed to push: empty array');
  {$ENDIF}

  LStrVec.PushFromArray(['aaaa', 'bbbb', 'cccc', 'dddd', 'eeee', 'ffff', 'gggg', 'hhhh']);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrVec[7]);

  LStrVec.PushFromArray(['iiii', 'jjjj', 'kkkk', 'llll', 'mmmm', 'nnnn', 'oooo', 'pppp']);
  AssertEquals('New size should be 16', 16, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrVec[7]);
  AssertEquals('element 8 should be iiii', 'iiii', LStrVec[8]);
  AssertEquals('element 9 should be jjjj', 'jjjj', LStrVec[9]);
  AssertEquals('element 10 should be kkkk', 'kkkk', LStrVec[10]);
  AssertEquals('element 11 should be llll', 'llll', LStrVec[11]);
  AssertEquals('element 12 should be mmmm', 'mmmm', LStrVec[12]);
  AssertEquals('element 13 should be nnnn', 'nnnn', LStrVec[13]);
  AssertEquals('element 14 should be oooo', 'oooo', LStrVec[14]);
  AssertEquals('element 15 should be pppp', 'pppp', LStrVec[15]);
end;

procedure TTestCase_vec.Test_PopToMemory;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LIntArray: array[0..7] of Integer;
  LStrArray: array[0..7] of String;
begin
  Initialize(LIntArray);
  Initialize(LStrArray);

  { 失败测试: 弹出0个元素 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PopToMemory should return false', LIntVec.PopToMemory(@LIntArray[0], 0));

  { 失败测试: 范围越界 }
  AssertFalse('PopToMemory out of range', LIntVec.PopToMemory(@LIntArray[0], 9));

  { 全部弹出 }
  LIntVec.PopToMemory(@LIntArray[0], 8);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntArray[0]);
  AssertEquals('element 1 should be 2', 2, LIntArray[1]);
  AssertEquals('element 2 should be 3', 3, LIntArray[2]);
  AssertEquals('element 3 should be 4', 4, LIntArray[3]);
  AssertEquals('element 4 should be 5', 5, LIntArray[4]);
  AssertEquals('element 5 should be 6', 6, LIntArray[5]);
  AssertEquals('element 6 should be 7', 7, LIntArray[6]);
  AssertEquals('element 7 should be 8', 8, LIntArray[7]);

  { 部分弹出 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.PopToMemory(@LIntArray[0], 4);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element 0 should be 5', 5, LIntArray[0]);
  AssertEquals('element 1 should be 6', 6, LIntArray[1]);
  AssertEquals('element 2 should be 7', 7, LIntArray[2]);
  AssertEquals('element 3 should be 8', 8, LIntArray[3]);

  ///
  /// 托管元素
  ///

  { 失败测试: 弹出0个元素 }
  LStrVec := specialize TVec<String>.Create(['aaaa', 'bbbb', 'cccc', 'dddd', 'eeee', 'ffff', 'gggg', 'hhhh']);
  AssertFalse('PopToMemory should return false', LStrVec.PopToMemory(@LStrArray[0], 0));

  { 失败测试: 范围越界 }
  AssertFalse('PopToMemory out of range', LStrVec.PopToMemory(@LStrArray[0], 9));

  { 全部弹出 }
  LStrVec := specialize TVec<String>.Create(['aaaa', 'bbbb', 'cccc', 'dddd', 'eeee', 'ffff', 'gggg', 'hhhh']);
  LStrVec.PopToMemory(@LStrArray[0], 8);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrArray[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrArray[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrArray[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrArray[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrArray[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrArray[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrArray[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrArray[7]);

  { 部分弹出 }
  LStrVec := specialize TVec<String>.Create(['aaaa', 'bbbb', 'cccc', 'dddd', 'eeee', 'ffff', 'gggg', 'hhhh']);
  LStrVec.PopToMemory(@LStrArray[0], 4);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element 0 should be eeee', 'eeee', LStrArray[0]);
  AssertEquals('element 1 should be ffff', 'ffff', LStrArray[1]);
  AssertEquals('element 2 should be gggg', 'gggg', LStrArray[2]);
  AssertEquals('element 3 should be hhhh', 'hhhh', LStrArray[3]);
end;

procedure TTestCase_vec.Test_PopToMemory2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LIntBuf: Integer;
  LStrBuf: String;
begin
  { 失败测试: 空栈 }
  LIntVec := specialize TVec<Integer>.Create(2, 2);
  AssertFalse('PopToMemory should return false', LIntVec.PopToMemory(@LIntBuf));

  { 失败测试: nil 指针 }
  AssertFalse('PopToMemory nil pointer', LIntVec.PopToMemory(nil));

  { 弹出测试 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertEquals('element should be 8', 8, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertEquals('element should be 7', 7, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('element should be 5', 6, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element should be 4', 5, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('element should be 3', 4, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('element should be 2', 3, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('element should be 1', 2, LIntBuf);

  LIntVec.PopToMemory(@LIntBuf);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('element should be 0', 1, LIntBuf);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  { 失败测试: 空栈 }
  AssertFalse('PopToMemory should return false', LStrVec.PopToMemory(@LStrBuf));

  { 失败测试: nil 指针 }
  AssertFalse('PopToMemory nil pointer', LStrVec.PopToMemory(nil));

  { 弹出测试 }
  LStrVec := specialize TVec<String>.Create(['aaaa', 'bbbb', 'cccc', 'dddd', 'eeee', 'ffff', 'gggg', 'hhhh']);
  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertEquals('element should be hhhh', 'hhhh', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertEquals('element should be gggg', 'gggg', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('element should be ffff', 'ffff', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element should be eeee', 'eeee', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('element should be dddd', 'dddd', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('element should be cccc', 'cccc', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('element should be bbbb', 'bbbb', LStrBuf);

  LStrVec.PopToMemory(@LStrBuf);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('element should be aaaa', 'aaaa', LStrBuf);
end;

procedure TTestCase_vec.Test_Push;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create(2, 2);
  LIntVec.Push(1);
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);

  LIntVec.Push(2);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);

  LIntVec.Push(3);
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);

  LIntVec.Push(4);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);

  LIntVec.Push(5);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(2, 2);
  LStrVec.Push('aaaa');
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);

  LStrVec.Push('bbbb');
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  
  LStrVec.Push('cccc');
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);

  LStrVec.Push('dddd');
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);

  LStrVec.Push('eeee');
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
end;

procedure TTestCase_vec.Test_Pop;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  AssertEquals('Pop element should be 8', 8, LIntVec.Pop);
  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);

  AssertEquals('Pop element should be 7', 7, LIntVec.Pop);
  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);

  AssertEquals('Pop element should be 6', 6, LIntVec.Pop);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  AssertEquals('Pop element should be 5', 5, LIntVec.Pop);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);

  AssertEquals('Pop element should be 4', 4, LIntVec.Pop);
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);

  AssertEquals('Pop element should be 3', 3, LIntVec.Pop);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);

  AssertEquals('Pop element should be 2', 2, LIntVec.Pop);
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);

  AssertEquals('Pop element should be 1', 1, LIntVec.Pop);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);

  AssertTrue('Vector should be empty',LIntVec.IsEmpty);

  { 异常测试: 空栈 }
  AssertException('Vector should be empty',Exception,
    procedure
    begin
      LIntVec.Pop;
    end);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrVec[7]);

  AssertEquals('Peek element should be hhhh', 'hhhh', LStrVec.Pop);
  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);

  AssertEquals('Peek element should be gggg', 'gggg', LStrVec.Pop);
  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);

  AssertEquals('Peek element should be ffff', 'ffff', LStrVec.Pop);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);

  AssertEquals('Peek element should be eeee', 'eeee', LStrVec.Pop);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);

  AssertEquals('Peek element should be dddd', 'dddd', LStrVec.Pop);
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);

  AssertEquals('Peek element should be cccc', 'cccc', LStrVec.Pop);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);

  AssertEquals('Peek element should be bbbb', 'bbbb', LStrVec.Pop);
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);

  AssertEquals('Peek element should be aaaa', 'aaaa', LStrVec.Pop);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertTrue('Vector should be empty',LStrVec.IsEmpty);

  { 异常测试: 空栈 }
  AssertException('Vector should be empty',Exception,
    procedure
    begin
      LStrVec.Pop;
    end);
end;

procedure TTestCase_vec.Test_Pop2;
var
  LVec:        specialize IVec<Integer>;
  LVecStr:     specialize IVec<String>;
  LElement:    Integer;
  LElementStr: String;
begin
  Initialize(LElement);
  Initialize(LElementStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 8', 8, LElement);
  AssertEquals('New size should be 7', 7, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('element 1 should be 2', 2, LVec[1]);
  AssertEquals('element 2 should be 3', 3, LVec[2]);
  AssertEquals('element 3 should be 4', 4, LVec[3]);
  AssertEquals('element 4 should be 5', 5, LVec[4]);
  AssertEquals('element 5 should be 6', 6, LVec[5]);
  AssertEquals('element 6 should be 7', 7, LVec[6]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 7', 7, LElement);
  AssertEquals('New size should be 6', 6, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('element 1 should be 2', 2, LVec[1]);
  AssertEquals('element 2 should be 3', 3, LVec[2]);
  AssertEquals('element 3 should be 4', 4, LVec[3]);
  AssertEquals('element 4 should be 5', 5, LVec[4]);
  AssertEquals('element 5 should be 6', 6, LVec[5]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 6', 6, LElement);
  AssertEquals('New size should be 5', 5, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('element 1 should be 2', 2, LVec[1]);
  AssertEquals('element 2 should be 3', 3, LVec[2]);
  AssertEquals('element 3 should be 4', 4, LVec[3]);
  AssertEquals('element 4 should be 5', 5, LVec[4]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 5', 5, LElement);
  AssertEquals('New size should be 4', 4, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('element 1 should be 2', 2, LVec[1]);
  AssertEquals('element 2 should be 3', 3, LVec[2]);
  AssertEquals('element 3 should be 4', 4, LVec[3]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 4', 4, LElement);
  AssertEquals('New size should be 3', 3, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('element 1 should be 2', 2, LVec[1]);
  AssertEquals('element 2 should be 3', 3, LVec[2]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 3', 3, LElement);
  AssertEquals('element 0 should be 1', 1, LVec[0]);
  AssertEquals('New size should be 2', 2, LVec.GetCount);
  AssertEquals('element 1 should be 2', 2, LVec[1]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 2', 2, LElement);
  AssertEquals('New size should be 1', 1, LVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LVec[0]);

  AssertTrue('Pop 1 element failed',LVec.Pop(LElement));
  AssertEquals('Pop element should be 1', 1, LElement);
  AssertEquals('New size should be 0', 0, LVec.GetCount);

  AssertFalse('Pop 1 element failed',LVec.Pop(LElement));
  AssertTrue('Vector should be empty',LVec.IsEmpty);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be hhhh', 'hhhh', LElementStr);
  AssertEquals('New size should be 7', 7, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LVecStr[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LVecStr[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LVecStr[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LVecStr[6]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be gggg', 'gggg', LElementStr);
  AssertEquals('New size should be 6', 6, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LVecStr[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LVecStr[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LVecStr[5]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be ffff', 'ffff', LElementStr);
  AssertEquals('New size should be 5', 5, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LVecStr[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LVecStr[4]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be eeee', 'eeee', LElementStr);
  AssertEquals('New size should be 4', 4, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LVecStr[3]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be dddd', 'dddd', LElementStr);
  AssertEquals('New size should be 3', 3, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be cccc', 'cccc', LElementStr);
  AssertEquals('New size should be 2', 2, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be bbbb', 'bbbb', LElementStr);
  AssertEquals('New size should be 1', 1, LVecStr.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);

  AssertTrue('Pop 1 element failed',LVecStr.Pop(LElementStr));
  AssertEquals('Pop element should be aaaa', 'aaaa', LElementStr);
  AssertEquals('New size should be 0', 0, LVecStr.GetCount);

  AssertFalse('Vector should be empty',LVecStr.Pop(LElementStr));
  AssertTrue('Vector should be empty',LVecStr.IsEmpty);
end;

procedure TTestCase_vec.Test_PopToArray;
var
  LIntVec:   specialize IVec<Integer>;
  LStrVec:   specialize IVec<String>;
  LIntArray: array of Integer;
  LStrArray: array of String;
begin
  Initialize(LIntArray);
  Initialize(LStrArray);

  { 失败测试: 空栈}
  LIntVec := specialize TVec<Integer>.Create;
  AssertFalse('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 8));

  { 失败测试: 范围越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 9));
  
  { 失败测试: Pop 0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 0));

  { Pop 全部 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 8));
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('element count should be 8', 8, Length(LIntArray));
  AssertEquals('element 0 should be 1', 1, LIntArray[0]);
  AssertEquals('element 1 should be 2', 2, LIntArray[1]);
  AssertEquals('element 2 should be 3', 3, LIntArray[2]);
  AssertEquals('element 3 should be 4', 4, LIntArray[3]);
  AssertEquals('element 4 should be 5', 5, LIntArray[4]);
  AssertEquals('element 5 should be 6', 6, LIntArray[5]);
  AssertEquals('element 6 should be 7', 7, LIntArray[6]);
  AssertEquals('element 7 should be 8', 8, LIntArray[7]);

  { 失败测试: Pop 空栈 }
  AssertFalse('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 8));

  { Pop 一半 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 4));
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('element count should be 4', 4, Length(LIntArray));
  AssertEquals('element 0 should be 5', 5, LIntArray[0]);
  AssertEquals('element 1 should be 6', 6, LIntArray[1]);
  AssertEquals('element 2 should be 7', 7, LIntArray[2]);
  AssertEquals('element 3 should be 8', 8, LIntArray[3]);
  AssertTrue('PopToArray 1 element failed',LIntVec.PopToArray(LIntArray, 4));
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('element count should be 4', 4, Length(LIntArray));
  AssertEquals('element 0 should be 1', 1, LIntArray[0]);
  AssertEquals('element 1 should be 2', 2, LIntArray[1]);
  AssertEquals('element 2 should be 3', 3, LIntArray[2]);
  AssertEquals('element 3 should be 4', 4, LIntArray[3]);

  ///
  /// 托管元素
  ///

  { 失败测试: 空栈 }
  LStrVec := specialize TVec<String>.Create;
  AssertFalse('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 8));

  { 失败测试: 范围越界 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 9));

  { 失败测试: Pop 0 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 0));

  { Pop 全部 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 8));
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('element count should be 8', 8, Length(LStrArray));
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrArray[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrArray[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrArray[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrArray[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrArray[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrArray[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrArray[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrArray[7]);

  { 失败测试: Pop 空栈 }
  AssertFalse('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 8));

  { Pop 一半 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 4));
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('element count should be 4', 4, Length(LStrArray));
  AssertEquals('element 0 should be eeee', 'eeee', LStrArray[0]);
  AssertEquals('element 1 should be ffff', 'ffff', LStrArray[1]);
  AssertEquals('element 2 should be gggg', 'gggg', LStrArray[2]);
  AssertEquals('element 3 should be hhhh', 'hhhh', LStrArray[3]);

  AssertTrue('PopToArray 1 element failed',LStrVec.PopToArray(LStrArray, 4));
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('element count should be 4', 4, Length(LStrArray));
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrArray[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrArray[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrArray[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrArray[3]);
end;

procedure TTestCase_vec.Test_PeekMemory;
var
  LIntVec:   specialize IVec<Integer>;
  LStrVec:   specialize IVec<String>;
  LIntPtr:   PInteger;
  LStrPtr:   PString;
begin
  { 失败测试: PeekMemory 0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntPtr := LIntVec.PeekMemory(0);
  AssertNull('PeekMemory element should be null', LIntPtr);

  { 失败测试: 空栈 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntPtr := LIntVec.PeekMemory(8);
  AssertNull('PeekMemory element should be null', LIntPtr);

  { 失败测试: 范围越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntPtr := LIntVec.PeekMemory(9);
  AssertNull('PeekMemory element should be null', LIntPtr);

  { PeekMemory 1个 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntPtr := LIntVec.PeekMemory(1);
  AssertEquals('PeekMemory element should be 8', 8, LIntPtr^);

  { PeekMemory 全部 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntPtr := LIntVec.PeekMemory(8);
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 8', 8, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 1', 1, LIntPtr[0]);
  AssertEquals('PeekMemory element should be 2', 2, LIntPtr[1]);
  AssertEquals('PeekMemory element should be 3', 3, LIntPtr[2]);
  AssertEquals('PeekMemory element should be 4', 4, LIntPtr[3]);
  AssertEquals('PeekMemory element should be 5', 5, LIntPtr[4]);
  AssertEquals('PeekMemory element should be 6', 6, LIntPtr[5]);
  AssertEquals('PeekMemory element should be 7', 7, LIntPtr[6]);
  AssertEquals('PeekMemory element should be 8', 8, LIntPtr[7]);

  ///
  /// 托管元素
  ///

  { 失败测试: PeekMemory 0 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrPtr := LStrVec.PeekMemory(0);
  AssertNull('PeekMemory element should be null', LStrPtr);

  { 失败测试: 空栈 }
  LStrVec := specialize TVec<String>.Create;
  LStrPtr := LStrVec.PeekMemory(8);
  AssertNull('PeekMemory element should be null', LStrPtr);

  { 失败测试: 范围越界 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrPtr := LStrVec.PeekMemory(9);
  AssertNull('PeekMemory element should be null', LStrPtr);

  { PeekMemory 1个 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrPtr := LStrVec.PeekMemory(1);
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 8', 8, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be hhhh', 'hhhh', LStrPtr^);

  { PeekMemory 全部 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrPtr := LStrVec.PeekMemory(8);
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 8', 8, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be aaaa', 'aaaa', LStrPtr[0]);
  AssertEquals('PeekMemory element should be bbbb', 'bbbb', LStrPtr[1]);
  AssertEquals('PeekMemory element should be cccc', 'cccc', LStrPtr[2]);
  AssertEquals('PeekMemory element should be dddd', 'dddd', LStrPtr[3]);
  AssertEquals('PeekMemory element should be eeee', 'eeee', LStrPtr[4]);
  AssertEquals('PeekMemory element should be ffff', 'ffff', LStrPtr[5]);
  AssertEquals('PeekMemory element should be gggg', 'gggg', LStrPtr[6]);
  AssertEquals('PeekMemory element should be hhhh', 'hhhh', LStrPtr[7]);
end;

procedure TTestCase_vec.Test_PeekMemory2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LIntPtr: PInteger;
  LStrPtr: PString;
begin
  { 失败测试: 空栈 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntPtr := LIntVec.PeekMemory;
  AssertNull('PeekMemory element should be null', LIntPtr);

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 8', 8, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 8', 8, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 7', 7, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 7', 7, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 6', 6, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 6', 6, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 5', 5, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 5', 5, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 4', 4, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 4', 4, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 3', 3, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 3', 3, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 2', 2, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 2', 2, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LIntPtr);
  AssertEquals('PeekMemory element count should be 1', 1, LIntVec.GetCount);
  AssertEquals('PeekMemory element should be 1', 1, LIntPtr^);

  LIntVec.Pop;
  LIntPtr := LIntVec.PeekMemory;
  AssertNull('PeekMemory element should be null', LIntPtr);

  ///
  /// 托管元素
  ///

  { 失败测试: 空栈 }
  LStrVec := specialize TVec<String>.Create;
  LStrPtr := LStrVec.PeekMemory;
  AssertNull('PeekMemory element should be null', LStrPtr);

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 8', 8, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be hhhh', 'hhhh', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 7', 7, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be gggg', 'gggg', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 6', 6, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be ffff', 'ffff', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 5', 5, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be eeee', 'eeee', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 4', 4, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be dddd', 'dddd', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 3', 3, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be cccc', 'cccc', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 2', 2, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be bbbb', 'bbbb', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNotNull('PeekMemory element should not be null', LStrPtr);
  AssertEquals('PeekMemory element count should be 1', 1, LStrVec.GetCount);
  AssertEquals('PeekMemory element should be aaaa', 'aaaa', LStrPtr^);

  LStrVec.Pop;
  LStrPtr := LStrVec.PeekMemory;
  AssertNull('PeekMemory element should be null', LStrPtr);
end;

procedure TTestCase_vec.Test_PeekReadMemory;
var
  LIntVec:   specialize IVec<Integer>;
  LStrVec:   specialize IVec<String>;
  LIntArray: array[0..7] of Integer;
  LStrArray: array[0..7] of String;
begin
  { 失败测试: PeekReadMemory 0 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],0));

  { 失败测试: PeekReadMemory nil }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(nil,1));

  { 失败测试: 范围越界 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],9));

  { 失败测试: 空栈 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertFalse('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],1));

  { PeekReadMemory 1个 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],1));
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 8', 8, LIntArray[0]);

  { PeekReadMemory 2个 }
  AssertTrue('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],2));
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 7', 7, LIntArray[0]);
  AssertEquals('element 1 should be 8', 8, LIntArray[1]);

  { PeekReadMemory 全部 }
  AssertTrue('PeekReadMemory 1 element failed',LIntVec.PeekReadMemory(@LIntArray[0],8));
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntArray[0]);
  AssertEquals('element 1 should be 2', 2, LIntArray[1]);
  AssertEquals('element 2 should be 3', 3, LIntArray[2]);
  AssertEquals('element 3 should be 4', 4, LIntArray[3]);
  AssertEquals('element 4 should be 5', 5, LIntArray[4]);
  AssertEquals('element 5 should be 6', 6, LIntArray[5]);
  AssertEquals('element 6 should be 7', 7, LIntArray[6]);

  ///
  /// 托管元素
  ///

  { 失败测试: PeekReadMemory 0 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],0));

  { 失败测试: PeekReadMemory nil }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(nil,1));

  { 失败测试: 范围越界 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],9));

  { 失败测试: 空栈 }
  LStrVec := specialize TVec<String>.Create;
  AssertFalse('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],1));

  { PeekReadMemory 1个 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],1));
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be hhhh', 'hhhh', LStrArray[0]);

  { PeekReadMemory 2个 }
  AssertTrue('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],2));
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be gggg', 'gggg', LStrArray[0]);
  AssertEquals('element 1 should be hhhh', 'hhhh', LStrArray[1]);

  { PeekReadMemory 全部 }
  AssertTrue('PeekReadMemory 1 element failed',LStrVec.PeekReadMemory(@LStrArray[0],8));
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrArray[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrArray[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrArray[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrArray[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrArray[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrArray[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrArray[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrArray[7]);
end;

procedure TTestCase_vec.Test_PeekReadMemory2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LIntBuf: Integer;
  LStrBuf: String;
begin
  { 失败测试: Peek 空栈 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertFalse('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));

  { 失败测试: Peek nil }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse('Peek 1 element failed',LIntVec.PeekReadMemory(nil));

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('Peek element should be 8', 8, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertEquals('Peek element should be 7', 7, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertEquals('Peek element should be 6', 6, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('Peek element should be 5', 5, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('Peek element should be 4', 4, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('Peek element should be 3', 3, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('Peek element should be 2', 2, LIntBuf);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('Peek element should be 1', 1, LIntBuf);

  LIntVec.Pop;
  AssertFalse('Peek 1 element failed',LIntVec.PeekReadMemory(@LIntBuf));

  ///
  /// 托管元素
  ///

  { 失败测试: Peek 空栈 }
  LStrVec := specialize TVec<String>.Create;
  AssertFalse('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));

  { 失败测试: Peek nil }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertFalse('Peek 1 element failed',LStrVec.PeekReadMemory(nil));

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('Peek element should be hhhh', 'hhhh', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertEquals('Peek element should be gggg', 'gggg', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertEquals('Peek element should be ffff', 'ffff', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('Peek element should be eeee', 'eeee', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('Peek element should be dddd', 'dddd', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('Peek element should be cccc', 'cccc', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('Peek element should be bbbb', 'bbbb', LStrBuf);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('Peek element should be aaaa', 'aaaa', LStrBuf);

  LStrVec.Pop;
  AssertFalse('Peek 1 element failed',LStrVec.PeekReadMemory(@LStrBuf));
end;

procedure TTestCase_vec.Test_Peek;
var
  LIntVec:     specialize IVec<Integer>;
  LStrVec:     specialize IVec<String>;
  LElement:    Integer;
  LElementStr: String;
begin
  Initialize(LElement);
  Initialize(LElementStr);

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);
  AssertEquals('element 5 should be 6', 6, LIntVec[5]);
  AssertEquals('element 6 should be 7', 7, LIntVec[6]);
  AssertEquals('element 7 should be 8', 8, LIntVec[7]);

  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('Peek element should be 8', 8, LIntVec.Peek);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertEquals('Peek element should be 7', 7, LIntVec.Peek);
  AssertEquals('New size should be 7', 7, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertEquals('Peek element should be 6', 6, LIntVec.Peek);
  AssertEquals('New size should be 6', 6, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('Peek element should be 5', 5, LIntVec.Peek);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertEquals('Peek element should be 4', 4, LIntVec.Peek);
  AssertEquals('New size should be 4', 4, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertEquals('Peek element should be 3', 3, LIntVec.Peek);
  AssertEquals('New size should be 3', 3, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('Peek element should be 2', 2, LIntVec.Peek);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertEquals('Peek element should be 1', 1, LIntVec.Peek);
  AssertEquals('New size should be 1', 1, LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.Pop(LElement));

  AssertFalse('Vector should be empty',LIntVec.Peek(LElement));
  AssertTrue('Vector should be empty',LIntVec.IsEmpty);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空栈 }
  AssertException('Vector should be empty',Exception,
    procedure
    begin
      LIntVec.Peek;
    end);
  {$ENDIF}

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('element 0 should be aaaa', 'aaaa', LStrVec[0]);
  AssertEquals('element 1 should be bbbb', 'bbbb', LStrVec[1]);
  AssertEquals('element 2 should be cccc', 'cccc', LStrVec[2]);
  AssertEquals('element 3 should be dddd', 'dddd', LStrVec[3]);
  AssertEquals('element 4 should be eeee', 'eeee', LStrVec[4]);
  AssertEquals('element 5 should be ffff', 'ffff', LStrVec[5]);
  AssertEquals('element 6 should be gggg', 'gggg', LStrVec[6]);
  AssertEquals('element 7 should be hhhh', 'hhhh', LStrVec[7]);

  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('Peek element should be hhhh', 'hhhh', LStrVec.Peek);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertEquals('Peek element should be gggg', 'gggg', LStrVec.Peek);
  AssertEquals('New size should be 7', 7, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertEquals('Peek element should be ffff', 'ffff', LStrVec.Peek);
  AssertEquals('New size should be 6', 6, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('Peek element should be eeee', 'eeee', LStrVec.Peek);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));
  
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertEquals('Peek element should be dddd', 'dddd', LStrVec.Peek);
  AssertEquals('New size should be 4', 4, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertEquals('Peek element should be cccc', 'cccc', LStrVec.Peek);
  AssertEquals('New size should be 3', 3, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('Peek element should be bbbb', 'bbbb', LStrVec.Peek);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertEquals('Peek element should be aaaa', 'aaaa', LStrVec.Peek);
  AssertEquals('New size should be 1', 1, LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.Pop(LElementStr));

  AssertFalse('Vector should be empty',LStrVec.Peek(LElementStr));
  AssertTrue('Vector should be empty',LStrVec.IsEmpty);

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 空栈 }
  AssertException('Vector should be empty',Exception,
    procedure
    begin
      LStrVec.Peek;
    end);
  {$ENDIF}
end;

procedure TTestCase_vec.Test_Peek2;
var
  LIntVec:     specialize IVec<Integer>;
  LStrVec:     specialize IVec<String>;
  LElement:    Integer;
  LElementStr: String;
begin
  Initialize(LElement);

  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 8', 8, LElement);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 7', 7, LElement);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 6', 6, LElement);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 5', 5, LElement);
  
  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 4', 4, LElement);
  
  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 3', 3, LElement);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 2', 2, LElement);

  LIntVec.Pop;
  AssertTrue('Peek 1 element failed', LIntVec.Peek(LElement));
  AssertEquals('Peek element should be 1', 1, LElement);

  { 失败测试: 空栈 }
  LIntVec.Pop;
  AssertFalse('Vector should be empty',LIntVec.Peek(LElement));

  ///
  /// 托管类型
  ///

  Initialize(LElementStr);
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);

  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be hhhh', 'hhhh', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be gggg', 'gggg', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be ffff', 'ffff', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be eeee', 'eeee', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be dddd', 'dddd', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be cccc', 'cccc', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be bbbb', 'bbbb', LElementStr);

  LStrVec.Pop;
  AssertTrue('Peek 1 element failed', LStrVec.Peek(LElementStr));
  AssertEquals('Peek element should be aaaa', 'aaaa', LElementStr);

  { 失败测试: 空栈 }
  LStrVec.Pop;
  AssertFalse('Vector should be empty',LStrVec.Peek(LElementStr));
end;

procedure TTestCase_vec.Test_ResizeExact;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  { 扩大调整 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.ResizeExact(10);
  AssertEquals('New size should be 10', 10, LIntVec.GetCount);
  AssertEquals('capacity should be 10', 10, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  { 缩小调整 }
  LIntVec.ResizeExact(5);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);
  AssertEquals('capacity should be 5', 5, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);
  AssertEquals('element 2 should be 3', 3, LIntVec[2]);
  AssertEquals('element 3 should be 4', 4, LIntVec[3]);
  AssertEquals('element 4 should be 5', 5, LIntVec[4]);

  { 缩小调整 }
  LIntVec.ResizeExact(2);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount);
  AssertEquals('capacity should be 2', 2, LIntVec.GetCapacity);
  AssertEquals('element 0 should be 1', 1, LIntVec[0]);
  AssertEquals('element 1 should be 2', 2, LIntVec[1]);

  { 缩小调整,置零 }
  LIntVec.ResizeExact(0);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('capacity should be 0', 0, LIntVec.GetCapacity);

  { 扩大调整 }
  LIntVec.ResizeExact(4096);
  AssertEquals('New size should be 4096', 4096, LIntVec.GetCount);
  AssertEquals('capacity should be 4096', 4096, LIntVec.GetCapacity);

  ///
  /// 托管类型
  ///

  { 扩大调整 }
  LStrVec := specialize TVec<String>.Create(['str0', 'str1', 'str2', 'str3', 'str4', 'str5', 'str6', 'str7']);
  LStrVec.ResizeExact(10);
  AssertEquals('New size should be 10', 10, LStrVec.GetCount);
  AssertEquals('capacity should be 10', 10, LStrVec.GetCapacity);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);
  AssertEquals('element 5 should be str5', 'str5', LStrVec[5]);
  AssertEquals('element 6 should be str6', 'str6', LStrVec[6]);
  AssertEquals('element 7 should be str7', 'str7', LStrVec[7]);

  { 缩小调整 }
  LStrVec.ResizeExact(5);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
  AssertEquals('capacity should be 5', 5, LStrVec.GetCapacity);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);
  AssertEquals('element 2 should be str2', 'str2', LStrVec[2]);
  AssertEquals('element 3 should be str3', 'str3', LStrVec[3]);
  AssertEquals('element 4 should be str4', 'str4', LStrVec[4]);

  { 缩小调整 }
  LStrVec.ResizeExact(2);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount);
  AssertEquals('capacity should be 2', 2, LStrVec.GetCapacity);
  AssertEquals('element 0 should be str0', 'str0', LStrVec[0]);
  AssertEquals('element 1 should be str1', 'str1', LStrVec[1]);

  { 缩小调整,置零 }
  LStrVec.ResizeExact(0);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('capacity should be 0', 0, LStrVec.GetCapacity);

  { 扩大调整 }
  LStrVec.ResizeExact(4096);
  AssertEquals('New size should be 4096', 4096, LStrVec.GetCount);
  AssertEquals('capacity should be 4096', 4096, LStrVec.GetCapacity);
end;

procedure TTestCase_vec.Test_WriteFromMemoryExact;
begin

end;

procedure TTestCase_vec.Test_WriteFromArrayExact;
begin

end;

procedure TTestCase_vec.Test_WriteFromArrayExact_TArray;
begin

end;

procedure TTestCase_vec.Test_ReadToMemoryExact;
begin

end;

procedure TTestCase_vec.Test_ReadToArrayExact;
begin

end;

procedure TTestCase_vec.Test_Peek_Element;
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
  LElement: Integer;
  LElementStr: String;
begin
  Initialize(LElement);
  Initialize(LElementStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    AssertEquals('New size should be 8', 8, LVec.GetCount);
    AssertEquals('element 0 should be 1', 1, LVec[0]);
    AssertEquals('element 1 should be 2', 2, LVec[1]);
    AssertEquals('element 2 should be 3', 3, LVec[2]);
    AssertEquals('element 3 should be 4', 4, LVec[3]);
    AssertEquals('element 4 should be 5', 5, LVec[4]);
    AssertEquals('element 5 should be 6', 6, LVec[5]);
    AssertEquals('element 6 should be 7', 7, LVec[6]);
    AssertEquals('element 7 should be 8', 8, LVec[7]);

    AssertEquals('New size should be 8', 8, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 8', 8, LElement);
    AssertEquals('New size should be 8', 8, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 7', 7, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 7', 7, LElement);
    AssertEquals('New size should be 7', 7, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 6', 6, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 6', 6, LElement);
    AssertEquals('New size should be 6', 6, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 5', 5, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 5', 5, LElement);
    AssertEquals('New size should be 5', 5, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 4', 4, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 4', 4, LElement);
    AssertEquals('New size should be 4', 4, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 3', 3, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 3', 3, LElement);
    AssertEquals('New size should be 3', 3, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 2', 2, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 2', 2, LElement);
    AssertEquals('New size should be 2', 2, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertEquals('New size should be 1', 1, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Peek(LElement));
    AssertEquals('Peek element should be 1', 1, LElement);
    AssertEquals('New size should be 1', 1, LVec.GetCount);
    AssertTrue('Peek 1 element failed',LVec.Pop(LElement));

    AssertFalse('Vector should be empty',LVec.Peek(LElement));
    AssertTrue('Vector should be empty',LVec.IsEmpty);

  finally
    LVec.Free;
  end;

  { 托管元素测试 }
  LVecStr := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  try
    AssertEquals('New size should be 8', 8, LVecStr.GetCount);
    AssertEquals('element 0 should be aaaa', 'aaaa', LVecStr[0]);
    AssertEquals('element 1 should be bbbb', 'bbbb', LVecStr[1]);
    AssertEquals('element 2 should be cccc', 'cccc', LVecStr[2]);
    AssertEquals('element 3 should be dddd', 'dddd', LVecStr[3]);
    AssertEquals('element 4 should be eeee', 'eeee', LVecStr[4]);
    AssertEquals('element 5 should be ffff', 'ffff', LVecStr[5]);
    AssertEquals('element 6 should be gggg', 'gggg', LVecStr[6]);
    AssertEquals('element 7 should be hhhh', 'hhhh', LVecStr[7]);

    AssertEquals('New size should be 8', 8, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be hhhh', 'hhhh', LElementStr);
    AssertEquals('New size should be 8', 8, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 7', 7, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be gggg', 'gggg', LElementStr);
    AssertEquals('New size should be 7', 7, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 6', 6, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be ffff', 'ffff', LElementStr);
    AssertEquals('New size should be 6', 6, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 5', 5, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be eeee', 'eeee', LElementStr);
    AssertEquals('New size should be 5', 5, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 4', 4, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be dddd', 'dddd', LElementStr);
    AssertEquals('New size should be 4', 4, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 3', 3, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be cccc', 'cccc', LElementStr);
    AssertEquals('New size should be 3', 3, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 2', 2, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be bbbb', 'bbbb', LElementStr);
    AssertEquals('New size should be 2', 2, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertEquals('New size should be 1', 1, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Peek(LElementStr));
    AssertEquals('Peek element should be aaaa', 'aaaa', LElementStr);
    AssertEquals('New size should be 1', 1, LVecStr.GetCount);
    AssertTrue('Peek 1 element failed',LVecStr.Pop(LElementStr));

    AssertFalse('Vector should be empty',LVecStr.Peek(LElementStr));
    AssertTrue('Vector should be empty',LVecStr.IsEmpty);

  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_GetCapacity;
var
  LIntVec:   specialize IVec<Integer>;
  LStrVec:   specialize IVec<String>;
  LIntArray: specialize IArray<Integer>;
  LStrArray: specialize IArray<String>;
begin
  { 根据数组构造的容量为数组长度 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { LoadFromArray 会把容量固定为目标长度 }
  LIntVec.LoadFromArray([1,2,3,4,5,6,7,8,9,10]);
  AssertEquals('New capacity should be 10', 10, LIntVec.GetCapacity);

  LIntVec.LoadFromArray([1,2]);
  AssertEquals('New capacity should be 2', 2, LIntVec.GetCapacity);

  LIntVec.LoadFromArray([]);
  AssertEquals('New capacity should be 0', 0, LIntVec.GetCapacity);

  LIntVec.LoadFromArray([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]);
  AssertEquals('New capacity should be 20', 20, LIntVec.GetCapacity);

  LIntArray := specialize TArray<Integer>.Create([1,2,3,4]);
  LIntVec.LoadFromCollection(LIntArray as specialize TGenericCollection<Integer>);
  AssertEquals('New capacity should be 4', 4, LIntVec.GetCapacity);

  ///
  /// 托管元素测试
  ///

  { 根据数组构造的容量为数组长度 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LStrVec.GetCapacity);

  { LoadFromArray 会把容量固定为目标长度 }
  LStrVec.LoadFromArray(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh','iiii','jjjj','kkkk','llll','mmmm','nnnn','oooo','pppp','qqqq','rrrr','ssss','tttt','uuuu','vvvv','wwww','xxxx','yyyy','zzzz']);
  AssertEquals('New capacity should be 26', 26, LStrVec.GetCapacity);
  
  LStrVec.LoadFromArray(['hello','world']);
  AssertEquals('New capacity should be 2', 2, LStrVec.GetCapacity);

  LStrVec.LoadFromArray([]);
  AssertEquals('New capacity should be 0', 0, LStrVec.GetCapacity);
  
  LStrArray := specialize TArray<String>.Create(['aaaa','bbbb','cccc','dddd']);
  LStrVec.LoadFromCollection(LStrArray as specialize TGenericCollection<String>);
  AssertEquals('New capacity should be 4', 4, LStrVec.GetCapacity);

  { 根据泛型数组构造的容量为泛型数组长度 }
  LIntArray := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec   := specialize TVec<Integer>.Create(LIntArray as specialize TArray<Integer>);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);
end;

procedure TTestCase_vec.Test_SetCapacity;
var
  LVec:    specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New size should be 8', 8, LVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LVec.GetCapacity);

  LVec.SetCapacity(10);
  AssertEquals('New capacity should be 10', 10, LVec.GetCapacity);
  AssertEquals('New size should be 8', 8, LVec.GetCount); // 容量扩大，元素数量不变

  LVec.SetCapacity(2);
  AssertEquals('New capacity should be 2', 2, LVec.GetCapacity);
  AssertEquals('New size should be 2', 2, LVec.GetCount); // 容量缩小，元素数量缩小

  { 设置为0 }
  LVec.SetCapacity(0);
  AssertEquals('New capacity should be 0', 0, LVec.GetCapacity);
  AssertEquals('New size should be 0', 0, LVec.GetCount);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertEquals('New size should be 8', 8, LVecStr.GetCount);
  AssertEquals('New capacity should be 8', 8, LVecStr.GetCapacity);
  
  LVecStr.SetCapacity(10);
  AssertEquals('New capacity should be 10', 10, LVecStr.GetCapacity);
  AssertEquals('New size should be 8', 8, LVecStr.GetCount); // 容量扩大，元素数量不变

  LVecStr.SetCapacity(2);
  AssertEquals('New capacity should be 2', 2, LVecStr.GetCapacity);
  AssertEquals('New size should be 2', 2, LVecStr.GetCount); // 容量缩小，元素数量缩小
  
  LVecStr.SetCapacity(0);
  AssertEquals('New capacity should be 0', 0, LVecStr.GetCapacity);
  AssertEquals('New size should be 0', 0, LVecStr.GetCount);
end;

procedure TTestCase_vec.Test_SetCapacity2;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue(LIntVec.SetCapacity2(10));
  AssertEquals('New capacity should be 10', 10, LIntVec.GetCapacity);
  AssertEquals('New size should be 8', 8, LIntVec.GetCount); // 容量扩大，元素数量不变

  AssertTrue(LIntVec.SetCapacity2(2));
  AssertEquals('New capacity should be 2', 2, LIntVec.GetCapacity);
  AssertEquals('New size should be 2', 2, LIntVec.GetCount); // 容量缩小，元素数量缩小
  
  AssertTrue(LIntVec.SetCapacity2(0));
  AssertEquals('New capacity should be 0', 0, LIntVec.GetCapacity);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue(LStrVec.SetCapacity2(10));
  AssertEquals('New capacity should be 10', 10, LStrVec.GetCapacity);
  AssertEquals('New size should be 8', 8, LStrVec.GetCount); // 容量扩大，元素数量不变

  AssertTrue(LStrVec.SetCapacity2(2));
  AssertEquals('New capacity should be 2', 2, LStrVec.GetCapacity);
  AssertEquals('New size should be 2', 2, LStrVec.GetCount); // 容量缩小，元素数量缩小
  
  AssertTrue(LStrVec.SetCapacity2(0));
  AssertEquals('New capacity should be 0', 0, LStrVec.GetCapacity);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
end;

procedure TTestCase_vec.Test_GetGrowSize;
var
  LVec: specialize IVec<Integer>;
begin
  { 默认增长常数(VEC_DEFAULT_GROW_SIZE) }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New grow size should be 0', VEC_DEFAULT_GROW_SIZE, LVec.GetGrowSize);

  { 指定增长常数 }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8], RtlMemAllocator, 10);
  AssertEquals('New grow size should be 10', 10, LVec.GetGrowSize);
end;

procedure TTestCase_vec.Test_SetGrowSize;
var
  LVec: specialize IVec<Integer>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 设置增长常数 }
  LVec.SetGrowSize(10);
  AssertEquals('New grow size should be 10', 10, LVec.GetGrowSize);

  { 设置为0时，会恢复默认增长常数(VEC_DEFAULT_GROW_SIZE) }
  LVec.SetGrowSize(0);
  AssertEquals('New grow size should be 0', VEC_DEFAULT_GROW_SIZE, LVec.GetGrowSize);
end;

procedure TTestCase_vec.Test_TryReserve;
var
  LIntVec:   specialize IVec<Integer>;
  LCapacity: SizeUint;
  LStrVec:   specialize IVec<String>;
begin
  { 尝试保留0个元素的空间会被直接返回true }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue(LIntVec.TryReserve(0));
  AssertEquals(format('New capacity should be %d', [8]), 8, LIntVec.GetCapacity);

  LIntVec   := specialize TVec<Integer>.Create;
  LCapacity := VEC_DEFAULT_GROW_SIZE;

  { 空容器尝试保留空间,但是默认容量足够 }
  AssertTrue(LIntVec.TryReserve(4));
  AssertEquals(format('New capacity should be %d', [LCapacity]), LCapacity, LIntVec.GetCapacity);

  { 空间并未使用,保留空间未超容量被否决 }
  AssertTrue(LIntVec.TryReserve(8));
  AssertEquals(format('New capacity should be %d', [LCapacity]), LCapacity, LIntVec.GetCapacity);

  { 触发默认增长 }
  LIntVec.Push(1);
  AssertTrue(LIntVec.TryReserve(LCapacity));
  AssertEquals(format('New capacity should be %d', [LCapacity * 2]), LCapacity * 2, LIntVec.GetCapacity);

  { 自定增长常数 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8], RtlMemAllocator, 4);
  AssertTrue(LIntVec.TryReserve(1));
  AssertEquals(format('New capacity should be %d', [8 + 4]), 8 + 4, LIntVec.GetCapacity);

  { 小型容器行为模拟 }
  LIntVec := specialize TVec<Integer>.Create(4,4);
  AssertEquals('New capacity should be 4', 4, LIntVec.GetCapacity);
  AssertEquals('New size should be 0', 0, LIntVec.GetCount);
  AssertEquals('New grow size should be 4', 4, LIntVec.GetGrowSize);

  { 增加5个元素 触发一次增长(4) }
  LIntVec.Push(1);
  LIntVec.Push(2);
  LIntVec.Push(3);
  LIntVec.Push(4);
  LIntVec.Push(5);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);

  { 尝试保留4个元素 触发一次增长(4) }
  AssertTrue(LIntVec.TryReserve(4));
  AssertEquals('New capacity should be 12', 12, LIntVec.GetCapacity);
  AssertEquals('New size should be 5', 5, LIntVec.GetCount);

  ///
  /// 托管元素
  ///

  { 尝试保留0个元素的空间会被直接返回true }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  AssertTrue(LStrVec.TryReserve(0));
  AssertEquals('New capacity should be 8', 8, LStrVec.GetCapacity);

  LStrVec   := specialize TVec<String>.Create;
  LCapacity := VEC_DEFAULT_GROW_SIZE;

  { 空容器尝试保留空间,但是默认容量足够 }
  AssertTrue(LStrVec.TryReserve(4));
  AssertEquals(format('New capacity should be %d', [LCapacity]), LCapacity, LStrVec.GetCapacity);

  { 空间并未使用,保留空间未超容量被否决 }
  AssertTrue(LStrVec.TryReserve(LCapacity));
  AssertEquals(format('New capacity should be %d', [LCapacity]), LCapacity, LStrVec.GetCapacity);

  { 触发默认增长 }
  LStrVec.Push('hello');
  AssertTrue(LStrVec.TryReserve(LCapacity));
  AssertEquals(format('New capacity should be %d', [LCapacity * 2]), LCapacity * 2, LStrVec.GetCapacity);

  { 自定增长常数 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh'], RtlMemAllocator, 4);
  AssertTrue(LStrVec.TryReserve(1));
  AssertEquals(format('New capacity should be %d', [8 + 4]), 8 + 4, LStrVec.GetCapacity);

  { 小型容器行为模拟 }
  LStrVec := specialize TVec<String>.Create(4,4);
  AssertEquals('New capacity should be 4', 4, LStrVec.GetCapacity);
  AssertEquals('New size should be 0', 0, LStrVec.GetCount);
  AssertEquals('New grow size should be 4', 4, LStrVec.GetGrowSize);

  { 增加5个元素 触发一次增长(4) }
  LStrVec.Push('aaaa');
  LStrVec.Push('bbbb');
  LStrVec.Push('cccc');
  LStrVec.Push('dddd');
  LStrVec.Push('eeee');
  AssertEquals('New capacity should be 8', 8, LStrVec.GetCapacity);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);

  { 尝试保留4个元素 触发一次增长(4) }
  AssertTrue(LStrVec.TryReserve(4));
  AssertEquals('New capacity should be 12', 12, LStrVec.GetCapacity);
  AssertEquals('New size should be 5', 5, LStrVec.GetCount);
end;

procedure TTestCase_vec.Test_Reserve;
var
  LVec: specialize IVec<Integer>;
begin
  { 默认容量 8 }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { + 默认增长常数(VEC_DEFAULT_GROW_SIZE) }
  LVec.Reserve(10);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE + 8]), VEC_DEFAULT_GROW_SIZE + 8, LVec.GetCapacity);
end;

procedure TTestCase_vec.Test_TryReserveExact;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  { 默认容量 8 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 精确保留8个元素的空间 }
  AssertTrue(LIntVec.TryReserveExact(8));
  AssertEquals(format('New capacity should be %d', [16]), 16, LIntVec.GetCapacity);

  { 精确保留4个元素的空间,但是容量足够,不会触发增长 }
  AssertTrue(LIntVec.TryReserveExact(4));
  AssertEquals(format('New capacity should be %d', [16]), 16, LIntVec.GetCapacity);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);

  { 精确保留8个元素的空间 }
  AssertTrue(LStrVec.TryReserveExact(8));
  AssertEquals(format('New capacity should be %d', [16]), 16, LStrVec.GetCapacity);

  { 精确保留4个元素的空间,但是容量足够,不会触发增长 }
  AssertTrue(LStrVec.TryReserveExact(4));
  AssertEquals(format('New capacity should be %d', [16]), 16, LStrVec.GetCapacity);
end;

procedure TTestCase_vec.Test_ReserveExact;
var
  LVec: specialize IVec<Integer>;
begin
  { 默认容量 8 }
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 精确保留8个元素的空间 }
  LVec.ReserveExact(8);
  AssertEquals(format('New capacity should be %d', [16]), 16, LVec.GetCapacity);

  { 精确保留4个元素的空间,但是容量足够,不会触发增长 }
  LVec.ReserveExact(4);
  AssertEquals(format('New capacity should be %d', [16]), 16, LVec.GetCapacity);
end;

procedure TTestCase_vec.Test_Shrink;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  { 空容器,容量收缩完容量为0 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntVec.Shrink;
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);
  AssertEquals('New capacity should be 0', 0, LIntVec.GetCapacity);

  { 在无余地的情况下收缩,什么也没发生 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);
  LIntVec.Shrink;
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { 扩大容量再收缩,容量恢复到8 }
  LIntVec.SetCapacity(64);
  AssertEquals('New Count should be 8',8,LIntVec.GetCount);
  AssertEquals('New capacity should be 64',64,LIntVec.GetCapacity);
  LIntVec.Shrink;
  AssertEquals('New Count should be 8',8,LIntVec.GetCount);
  AssertEquals('New capacity should be 8',8,LIntVec.GetCapacity);

  ///
  /// 托管元素
  ///

  { 空容器,容量收缩完容量为0 }
  LStrVec := specialize TVec<String>.Create;
  LStrVec.Shrink;
  AssertEquals('New Count should be 0', 0, LStrVec.GetCount);
  AssertEquals('New capacity should be 0', 0, LStrVec.GetCapacity);

  { 在无余地的情况下收缩,什么也没发生 }
  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);
  LStrVec.Shrink;
  AssertEquals('New Count should be 8', 8, LStrVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LStrVec.GetCapacity);

  { 扩大容量再收缩 }
  LStrVec.SetCapacity(64);
  AssertEquals('New Count should be 8',8,LStrVec.GetCount);
  AssertEquals('New capacity should be 64',64,LStrVec.GetCapacity);
  LStrVec.Shrink;
  AssertEquals('New Count should be 8',8,LStrVec.GetCount);
  AssertEquals('New capacity should be 8',8,LStrVec.GetCapacity);
end;

procedure TTestCase_vec.Test_ShrinkTo;
var
  LIntVec: specialize IVec<Integer>;
begin
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 低于元素数量收缩 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertException('Exception should be raised',exception,
  procedure
  begin
    LIntVec.ShrinkTo(4);
  end);
  {$ENDIF}

  { 空容器,收缩到0 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntVec.ShrinkTo(0);
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);
  AssertEquals('New capacity should be 0', 0, LIntVec.GetCapacity);

  { 收缩容器 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  LIntVec.ShrinkTo(8);
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { 收缩容器,超过当前容量 }
  LIntVec.ShrinkTo(12);
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);
end;

procedure TTestCase_vec.Test_Truncate;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 截断为4个元素,容量不变 }
  LIntVec.Truncate(4);
  AssertEquals('New Count should be 4', 4, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { 截断为2个元素,容量不变 }
  LIntVec.Truncate(2);
  AssertEquals('New Count should be 2', 2, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { 截断为0个元素,容量不变 }
  LIntVec.Truncate(0);
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  { 截断数量超过 count, 什么也不干}
  LIntVec.Truncate(10);
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);
  AssertEquals('New capacity should be 8', 8, LIntVec.GetCapacity);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create(['aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh']);

  { 截断为4个元素,容量不变 }
  LStrVec.Truncate(4);
  AssertEquals('New Count should be 4',4, LStrVec.GetCount); 
  AssertEquals('New capacity should be 8',8, LStrVec.GetCapacity);  

  // 截断为2个元素,容量不变
  LStrVec.Truncate(2);
  AssertEquals('New Count should be 2',2, LStrVec.GetCount);  
  AssertEquals('New capacity should still be 8',8 , LStrVec.GetCapacity );

  { 截断数量超过 count, 什么也不干}
  LStrVec.Truncate(10);
  AssertEquals('New Count should be 2',2, LStrVec.GetCount);  
  AssertEquals('New capacity should still be 8',8 , LStrVec.GetCapacity );

  { 截断为0个元素,容量不变 }
  LStrVec.Truncate(0);
  AssertEquals('New Count should be 0',0, LStrVec.GetCount);  
  AssertEquals('New capacity should still be 8',8 , LStrVec.GetCapacity );
end;

procedure TTestCase_vec.Test_InsertFromMemory;
const
  DATA:      array[0..7] of Integer = (1,2,3,4,5,6,7,8);
  DATA2:     array[0..15] of Integer = (1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16);
  STR_DATA:  array[0..7] of String = ('aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh');
  STR_DATA2: array[0..15] of String = ('aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh','iiii','jjjj','kkkk','llll','mmmm','nnnn','oooo','pppp');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;
  
  { 失败测试: 插入0个元素 }
  AssertFalse(LIntVec.InsertFromMemory(0, @DATA[0], 0));
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);

  { 失败测试: 插入索引越界 }
  AssertFalse(LIntVec.InsertFromMemory(1, @DATA[0], 1));
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);

  { 失败测试: 插入 nil }
  AssertFalse(LIntVec.InsertFromMemory(0, nil, 1));
  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);

  { 插入到空容器(1) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[0], 1));
  AssertEquals('New Count should be 1', 1, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[0], LIntVec[0]);

  { 插入到非空容器(1) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[1], 1));
  AssertEquals('New Count should be 2', 2, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d', [DATA[1]]), DATA[1], LIntVec[0]);
  AssertEquals(format('New item should be %d', [DATA[0]]), DATA[0], LIntVec[1]);

  { 插入到非空容器(2) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[2], 1));
  AssertEquals('New Count should be 3', 3, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d', [DATA[2]]), DATA[2], LIntVec[0]);
  AssertEquals(format('New item should be %d', [DATA[1]]), DATA[1], LIntVec[1]);
  AssertEquals(format('New item should be %d', [DATA[0]]), DATA[0], LIntVec[2]);

  { 插入到非空容器(3) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[3], 1));
  AssertEquals('New Count should be 4', 4, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[3]]), DATA[3], LIntVec[0]);
  AssertEquals(format('New item should be %d',     [DATA[2]]), DATA[2], LIntVec[1]);
  AssertEquals(format('New item should be %d',     [DATA[1]]), DATA[1], LIntVec[2]);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[0], LIntVec[3]);

  { 插入到非空容器(4) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[4], 1));
  AssertEquals('New Count should be 5', 5, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[4]]), DATA[4], LIntVec[0]);
  AssertEquals(format('New item should be %d',     [DATA[3]]), DATA[3], LIntVec[1]);
  AssertEquals(format('New item should be %d',     [DATA[2]]), DATA[2], LIntVec[2]);
  AssertEquals(format('New item should be %d',     [DATA[1]]), DATA[1], LIntVec[3]);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[0], LIntVec[4]);

  { 插入到非空容器(8) }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA[0], 8));
  AssertEquals('New Count should be 13', 13, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[0], LIntVec[0]);
  AssertEquals(format('New item should be %d',     [DATA[1]]), DATA[1], LIntVec[1]);
  AssertEquals(format('New item should be %d',     [DATA[2]]), DATA[2], LIntVec[2]);
  AssertEquals(format('New item should be %d',     [DATA[3]]), DATA[3], LIntVec[3]);
  AssertEquals(format('New item should be %d',     [DATA[4]]), DATA[4], LIntVec[4]);
  AssertEquals(format('New item should be %d',     [DATA[5]]), DATA[5], LIntVec[5]);
  AssertEquals(format('New item should be %d',     [DATA[6]]), DATA[6], LIntVec[6]);
  AssertEquals(format('New item should be %d',     [DATA[7]]), DATA[7], LIntVec[7]);

  { 插入到尾部(1) }
  AssertTrue(LIntVec.InsertFromMemory(13, @DATA[0], 1));
  AssertEquals('New Count should be 14', 14, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[0], LIntVec[13]);

  { 插入到尾部(2) }
  AssertTrue(LIntVec.InsertFromMemory(14, @DATA[1], 2));
  AssertEquals('New Count should be 16', 16, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',    [DATA[1]]), DATA[1], LIntVec[14]);
  AssertEquals(format('New item should be %d',    [DATA[2]]), DATA[2], LIntVec[15]);

  { 插入一个大的内存块 }
  AssertTrue(LIntVec.InsertFromMemory(0, @DATA2[0], 16));
  AssertEquals('New Count should be 32', 32, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',    [DATA2[0]]),  DATA2[0],  LIntVec[0]);
  AssertEquals(format('New item should be %d',    [DATA2[1]]),  DATA2[1],  LIntVec[1]);
  AssertEquals(format('New item should be %d',    [DATA2[2]]),  DATA2[2],  LIntVec[2]);
  AssertEquals(format('New item should be %d',    [DATA2[3]]),  DATA2[3],  LIntVec[3]);
  AssertEquals(format('New item should be %d',    [DATA2[4]]),  DATA2[4],  LIntVec[4]);
  AssertEquals(format('New item should be %d',    [DATA2[5]]),  DATA2[5],  LIntVec[5]);
  AssertEquals(format('New item should be %d',    [DATA2[6]]),  DATA2[6],  LIntVec[6]);
  AssertEquals(format('New item should be %d',    [DATA2[7]]),  DATA2[7],  LIntVec[7]);
  AssertEquals(format('New item should be %d',    [DATA2[8]]),  DATA2[8],  LIntVec[8]);
  AssertEquals(format('New item should be %d',    [DATA2[9]]),  DATA2[9],  LIntVec[9]);
  AssertEquals(format('New item should be %d',    [DATA2[10]]), DATA2[10], LIntVec[10]);
  AssertEquals(format('New item should be %d',    [DATA2[11]]), DATA2[11], LIntVec[11]);
  AssertEquals(format('New item should be %d',    [DATA2[12]]), DATA2[12], LIntVec[12]);
  AssertEquals(format('New item should be %d',    [DATA2[13]]), DATA2[13], LIntVec[13]);
  AssertEquals(format('New item should be %d',    [DATA2[14]]), DATA2[14], LIntVec[14]);
  AssertEquals(format('New item should be %d',    [DATA2[15]]), DATA2[15], LIntVec[15]);
  
  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  { 失败测试: 插入0个元素 }
  AssertFalse(LStrVec.InsertFromMemory(0, @DATA[0], 0));
  AssertEquals('New Count should be 0', 0, LStrVec.GetCount);

  { 失败测试: 插入索引越界 }
  AssertFalse(LStrVec.InsertFromMemory(1, @DATA[0], 1));
  AssertEquals('New Count should be 0', 0, LStrVec.GetCount);

  { 失败测试: 插入 nil }
  AssertFalse(LStrVec.InsertFromMemory(0, nil, 1));
  AssertEquals('New Count should be 0', 0, LStrVec.GetCount);
  
  { 插入到空容器(1) aaaa }
  AssertTrue(LStrVec.InsertFromMemory(0, @STR_DATA[0], 1));
  AssertEquals('New Count should be 1', 1, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s', [STR_DATA[0]]), STR_DATA[0], LStrVec[0]);

  { 插入到非空容器(1) bbbb,aaaa }
  AssertTrue(LStrVec.InsertFromMemory(0, @STR_DATA[1], 1));
  AssertEquals('New Count should be 2', 2, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[1]]), STR_DATA[1], LStrVec[0]);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[0], LStrVec[1]);

  { 插入到非空容器(2) cccc,bbbb,aaaa }
  AssertTrue(LStrVec.InsertFromMemory(0, @STR_DATA[2], 1));
  AssertEquals('New Count should be 3', 3, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',    [STR_DATA[2]]), STR_DATA[2], LStrVec[0]);
  AssertEquals(format('New item should be %s',    [STR_DATA[1]]), STR_DATA[1], LStrVec[1]);
  AssertEquals(format('New item should be %s',    [STR_DATA[0]]), STR_DATA[0], LStrVec[2]);

  { 插入一个大的内存块 }
  AssertTrue(LStrVec.InsertFromMemory(0, @STR_DATA2[0], 16));
  AssertEquals('New Count should be 19', 19, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA2[0]]),  STR_DATA2[0],  LStrVec[0]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[1]]),  STR_DATA2[1],  LStrVec[1]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[2]]),  STR_DATA2[2],  LStrVec[2]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[3]]),  STR_DATA2[3],  LStrVec[3]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[4]]),  STR_DATA2[4],  LStrVec[4]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[5]]),  STR_DATA2[5],  LStrVec[5]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[6]]),  STR_DATA2[6],  LStrVec[6]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[7]]),  STR_DATA2[7],  LStrVec[7]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[8]]),  STR_DATA2[8],  LStrVec[8]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[9]]),  STR_DATA2[9],  LStrVec[9]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[10]]), STR_DATA2[10], LStrVec[10]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[11]]), STR_DATA2[11], LStrVec[11]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[12]]), STR_DATA2[12], LStrVec[12]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[13]]), STR_DATA2[13], LStrVec[13]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[14]]), STR_DATA2[14], LStrVec[14]);
  AssertEquals(format('New item should be %s',     [STR_DATA2[15]]), STR_DATA2[15], LStrVec[15]);

  { 插入到尾部 }
  AssertTrue(LStrVec.InsertFromMemory(19, @STR_DATA[0], 1));
  AssertEquals('New Count should be 20', 20, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[0], LStrVec[19]);
end;

procedure TTestCase_vec.Test_InsertFromMemory2;
const
  DATA:     array[0..7] of Integer = (1,2,3,4,5,6,7,8);
  STR_DATA: array[0..7] of String = ('aaaa','bbbb','cccc','dddd','eeee','ffff','gggg','hhhh');
var
  LVec:    specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
begin
  LVec := specialize TVec<Integer>.Create;

  { 失败测试: 插入nil }
  AssertFalse(LVec.InsertFromMemory(0, nil));
  AssertEquals('New Count should be 0', 0, LVec.GetCount);

  { 失败测试: 插入索引越界 }
  AssertFalse(LVec.InsertFromMemory(1, nil));
  AssertEquals('New Count should be 0', 0, LVec.GetCount);

  { 插入到空容器 }
  AssertTrue(LVec.InsertFromMemory(0, @DATA[0]));
  AssertEquals('New Count should be 1', 1, LVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVec.GetCapacity);
  AssertEquals(format('New item should be %d',    [DATA[0]]), DATA[0], LVec[0]);

  { 插入到非空容器 }
  AssertTrue(LVec.InsertFromMemory(0, @DATA[1]));
  AssertEquals('New Count should be 2', 2, LVec.GetCount);
  AssertEquals(format('New capacity should be %d',[VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVec.GetCapacity);
  AssertEquals(format('New item should be %d',    [DATA[1]]), DATA[1], LVec[0]);
  AssertEquals(format('New item should be %d',    [DATA[0]]), DATA[0], LVec[1]);

  { 插入一个到中间 }
  AssertTrue(LVec.InsertFromMemory(1, @DATA[2]));
  AssertEquals('New Count should be 3', 3, LVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[1]]), DATA[1], LVec[0]);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[2], LVec[1]);
  AssertEquals(format('New item should be %d',     [DATA[2]]), DATA[0], LVec[2]);

  { 插入一个到尾部 }
  AssertTrue(LVec.InsertFromMemory(3, @DATA[3]));
  AssertEquals('New Count should be 4', 4, LVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [DATA[1]]), DATA[1], LVec[0]);
  AssertEquals(format('New item should be %d',     [DATA[0]]), DATA[2], LVec[1]);
  AssertEquals(format('New item should be %d',     [DATA[2]]), DATA[0], LVec[2]);
  AssertEquals(format('New item should be %d',     [DATA[3]]), DATA[3], LVec[3]);

  /// 
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create;

  { 失败测试: 插入空的 }
  AssertFalse(LVecStr.InsertFromMemory(0, nil));
  AssertEquals('New Count should be 0', 0, LVecStr.GetCount);

  { 失败测试: 插入索引越界 }
  AssertFalse(LVecStr.InsertFromMemory(1, nil));
  AssertEquals('New Count should be 0', 0, LVecStr.GetCount);
  
  { 插入到空容器 }
  AssertTrue(LVecStr.InsertFromMemory(0, @STR_DATA[0]));
  AssertEquals('New Count should be 1', 1, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[0], LVecStr[0]);
  
  { 插入到非空容器 }
  AssertTrue(LVecStr.InsertFromMemory(0, @STR_DATA[1]));
  AssertEquals('New Count should be 2', 2, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[1]]), STR_DATA[1], LVecStr[0]);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[0], LVecStr[1]);
  
  { 插入一个到中间 }
  AssertTrue(LVecStr.InsertFromMemory(1, @STR_DATA[2]));
  AssertEquals('New Count should be 3', 3, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[1]]), STR_DATA[1], LVecStr[0]);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[2], LVecStr[1]);
  AssertEquals(format('New item should be %s',     [STR_DATA[2]]), STR_DATA[0], LVecStr[2]);

  { 插入一个到尾部 }
  AssertTrue(LVecStr.InsertFromMemory(3, @STR_DATA[3]));
  AssertEquals('New Count should be 4', 4, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     [STR_DATA[1]]), STR_DATA[1], LVecStr[0]);
  AssertEquals(format('New item should be %s',     [STR_DATA[0]]), STR_DATA[2], LVecStr[1]);
  AssertEquals(format('New item should be %s',     [STR_DATA[2]]), STR_DATA[0], LVecStr[2]);
  AssertEquals(format('New item should be %s',     [STR_DATA[3]]), STR_DATA[3], LVecStr[3]);
end;

procedure TTestCase_vec.Test_Insert;
var
  LIntVec: specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 插入索引越界 }
  AssertException(
  'Index out of bounds',
  Exception,
  procedure
  begin
    LIntVec.Insert(1, 1)
  end);
  {$ENDIF}

  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);

  { 插入到空容器 }
  LIntVec.Insert(0, 1);
  AssertEquals('New Count should be 1', 1, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[0]);

  { 插入到非空容器 }
  LIntVec.Insert(0, 2);
  AssertEquals('New Count should be 2', 2, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[1]);

  { 插入到中间 }
  LIntVec.Insert(1, 3);
  AssertEquals('New Count should be 3', 3, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [3]), 3, LIntVec[1]);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[2]);

    // 插入到尾部
  LIntVec.Insert(3, 4);
  AssertEquals('New Count should be 4', 4, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [3]), 3, LIntVec[1]);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[2]);
  AssertEquals(format('New item should be %d',     [4]), 4, LIntVec[3]);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 插入索引越界 }
  AssertException(
  'Index out of bounds',
  Exception,
  procedure
  begin
    LVecStr.Insert(1, '1');
  end);
  {$ENDIF}

  AssertEquals('New Count should be 0', 0, LVecStr.GetCount);

  { 插入到空容器 }
  LVecStr.Insert(0, '1');
  AssertEquals('New Count should be 1', 1, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     ['1']), '1', LVecStr[0]);

  { 插入到非空容器 }
  LVecStr.Insert(0, '2');
  AssertEquals('New Count should be 2', 2, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     ['2']), '2', LVecStr[0]);
  AssertEquals(format('New item should be %s',     ['1']), '1', LVecStr[1]);

  { 插入到中间 }
  LVecStr.Insert(1, '3');
  AssertEquals('New Count should be 3', 3, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     ['2']), '2', LVecStr[0]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LVecStr[1]);
  AssertEquals(format('New item should be %s',     ['1']), '1', LVecStr[2]);

  { 插入到尾部 }
  LVecStr.Insert(3, '4');
  AssertEquals('New Count should be 4', 4, LVecStr.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LVecStr.GetCapacity);
  AssertEquals(format('New item should be %s',     ['2']), '2', LVecStr[0]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LVecStr[1]);
  AssertEquals(format('New item should be %s',     ['1']), '1', LVecStr[2]);
  AssertEquals(format('New item should be %s',     ['4']), '4', LVecStr[3]);
end;

procedure TTestCase_vec.Test_InsertFromArray;
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 插入索引越界 }
  AssertException('Index out of bounds',Exception,
  procedure
  begin
    LIntVec.InsertFromArray(1, [1,2,3]);
  end);
  {$ENDIF}

  AssertEquals('New Count should be 0', 0, LIntVec.GetCount);

  { 插入到空容器 }
  LIntVec.InsertFromArray(0, [1,2,3]);
  AssertEquals('New Count should be 3', 3, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[1]);
  AssertEquals(format('New item should be %d',     [3]), 3, LIntVec[2]);

  { 插入空数组 }
  LIntVec.InsertFromArray(0, []);
  AssertEquals('New Count should be 3', 3, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[1]);
  AssertEquals(format('New item should be %d',     [3]), 3, LIntVec[2]);

  { 插入到非空容器 }
  LIntVec.InsertFromArray(0, [4,5,6]);
  AssertEquals('New Count should be 6', 6, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d', [4]), 4, LIntVec[0]);
  AssertEquals(format('New item should be %d', [5]), 5, LIntVec[1]);
  AssertEquals(format('New item should be %d', [6]), 6, LIntVec[2]);
  AssertEquals(format('New item should be %d', [1]), 1, LIntVec[3]);
  AssertEquals(format('New item should be %d', [2]), 2, LIntVec[4]);
  AssertEquals(format('New item should be %d', [3]), 3, LIntVec[5]);

  { 插入到中间 }
  LIntVec.InsertFromArray(2, [7,8,9]);
  AssertEquals('New Count should be 9', 9, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [4]), 4, LIntVec[0]);
  AssertEquals(format('New item should be %d',     [5]), 5, LIntVec[1]);
  AssertEquals(format('New item should be %d',     [7]), 7, LIntVec[2]);
  AssertEquals(format('New item should be %d',     [8]), 8, LIntVec[3]);
  AssertEquals(format('New item should be %d',     [9]), 9, LIntVec[4]);
  AssertEquals(format('New item should be %d',     [6]), 6, LIntVec[5]);
  AssertEquals(format('New item should be %d',     [1]), 1, LIntVec[6]);
  AssertEquals(format('New item should be %d',     [2]), 2, LIntVec[7]);
  AssertEquals(format('New item should be %d',     [3]), 3, LIntVec[8]);

  { 插入到尾部 }
  LIntVec.InsertFromArray(9, [10,11,12]);
  AssertEquals('New Count should be 12', 12, LIntVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LIntVec.GetCapacity);
  AssertEquals(format('New item should be %d',     [4]),  4,  LIntVec[0]);
  AssertEquals(format('New item should be %d',     [5]),  5,  LIntVec[1]);
  AssertEquals(format('New item should be %d',     [7]),  7,  LIntVec[2]);
  AssertEquals(format('New item should be %d',     [8]),  8,  LIntVec[3]);
  AssertEquals(format('New item should be %d',     [9]),  9,  LIntVec[4]);
  AssertEquals(format('New item should be %d',     [6]),  6,  LIntVec[5]);
  AssertEquals(format('New item should be %d',     [1]),  1,  LIntVec[6]);
  AssertEquals(format('New item should be %d',     [2]),  2,  LIntVec[7]);
  AssertEquals(format('New item should be %d',     [3]),  3,  LIntVec[8]);
  AssertEquals(format('New item should be %d',     [10]), 10, LIntVec[9]);
  AssertEquals(format('New item should be %d',     [11]), 11, LIntVec[10]);
  AssertEquals(format('New item should be %d',     [12]), 12, LIntVec[11]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  { 异常测试: 插入索引越界 }
  AssertException(
  'Index out of bounds',
  Exception,
  procedure
  begin
    LStrVec.InsertFromArray(1, ['1','2','3']);
  end);
  {$ENDIF}

  AssertEquals('New Count should be 0', 0, LStrVec.GetCount);

  { 插入到空容器 }
  LStrVec.InsertFromArray(0, ['1','2','3']);
  AssertEquals('New Count should be 3', 3, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     ['1']), '1', LStrVec[0]);
  AssertEquals(format('New item should be %s',     ['2']), '2', LStrVec[1]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LStrVec[2]);

  { 插入空数组 }
  LStrVec.InsertFromArray(0, []);
  AssertEquals('New Count should be 3', 3, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     ['1']), '1', LStrVec[0]);
  AssertEquals(format('New item should be %s',     ['2']), '2', LStrVec[1]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LStrVec[2]);

  { 插入到非空容器 }
  LStrVec.InsertFromArray(0, ['4','5','6']);
  AssertEquals('New Count should be 6', 6, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     ['4']), '4', LStrVec[0]);
  AssertEquals(format('New item should be %s',     ['5']), '5', LStrVec[1]);
  AssertEquals(format('New item should be %s',     ['6']), '6', LStrVec[2]);
  AssertEquals(format('New item should be %s',     ['1']), '1', LStrVec[3]);
  AssertEquals(format('New item should be %s',     ['2']), '2', LStrVec[4]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LStrVec[5]);

  { 插入到中间 }
  LStrVec.InsertFromArray(2, ['7','8','9']);
  AssertEquals('New Count should be 9', 9, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     ['4']), '4', LStrVec[0]);
  AssertEquals(format('New item should be %s',     ['5']), '5', LStrVec[1]);
  AssertEquals(format('New item should be %s',     ['7']), '7', LStrVec[2]);
  AssertEquals(format('New item should be %s',     ['8']), '8', LStrVec[3]);
  AssertEquals(format('New item should be %s',     ['9']), '9', LStrVec[4]);
  AssertEquals(format('New item should be %s',     ['6']), '6', LStrVec[5]);
  AssertEquals(format('New item should be %s',     ['1']), '1', LStrVec[6]);
  AssertEquals(format('New item should be %s',     ['2']), '2', LStrVec[7]);
  AssertEquals(format('New item should be %s',     ['3']), '3', LStrVec[8]);

  { 插入到尾部 }
  LStrVec.InsertFromArray(9, ['10','11','12']);
  AssertEquals('New Count should be 12', 12, LStrVec.GetCount);
  AssertEquals(format('New capacity should be %d', [VEC_DEFAULT_GROW_SIZE]), VEC_DEFAULT_GROW_SIZE, LStrVec.GetCapacity);
  AssertEquals(format('New item should be %s',     ['4']),  '4',  LStrVec[0]);
  AssertEquals(format('New item should be %s',     ['5']),  '5',  LStrVec[1]);
  AssertEquals(format('New item should be %s',     ['7']),  '7',  LStrVec[2]);
  AssertEquals(format('New item should be %s',     ['8']),  '8',  LStrVec[3]);
  AssertEquals(format('New item should be %s',     ['9']),  '9',  LStrVec[4]);
  AssertEquals(format('New item should be %s',     ['6']),  '6',  LStrVec[5]);
  AssertEquals(format('New item should be %s',     ['1']),  '1',  LStrVec[6]);
  AssertEquals(format('New item should be %s',     ['2']),  '2',  LStrVec[7]);
  AssertEquals(format('New item should be %s',     ['3']),  '3',  LStrVec[8]);
  AssertEquals(format('New item should be %s',     ['10']), '10', LStrVec[9]);
  AssertEquals(format('New item should be %s',     ['11']), '11', LStrVec[10]);
  AssertEquals(format('New item should be %s',     ['12']), '12', LStrVec[11]);
end;

procedure TTestCase_vec.Test_InsertFromCollection;
var
  LIntVec:   specialize IVec<Integer>;
  LIntArr:   specialize IArray<Integer>;
  LIntVec2:  specialize IVec<Integer>;
  LInt64Arr: specialize IArray<Int64>;
  LStrVec:   specialize IVec<String>;
  LStrArr:   specialize IArray<String>;
  LStrVec2:  specialize IVec<String>;
begin
  { 失败测试: nil 容器 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntArr := nil;
  AssertFalse('Failed to insert: collection is nil', LIntVec.InsertFromCollection(0, LIntArr as specialize TGenericCollection<Integer>, 1));
  
  { 失败测试: 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3]);
  AssertFalse('Failed to insert: index is out of bounds', LIntVec.InsertFromCollection(1, LIntArr as specialize TGenericCollection<Integer>, LIntArr.GetCount));

  { 失败测试: 范围越界 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3]);
  AssertFalse('Failed to insert: count is greater than collection count', LIntVec.InsertFromCollection(0, LIntArr as specialize TGenericCollection<Integer>, LIntArr.GetCount + 1));

  { 插入数组到空Vec尾部 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  AssertTrue('insert from array should succeed', LIntVec.InsertFromCollection(0, LIntArr as specialize TGenericCollection<Integer>, LIntArr.GetCount));
  AssertEquals('New Count should be 4', 4, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]), 0, LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]), 1, LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]), 2, LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]), 3, LIntVec[3]);

  { 再插入一半数组到Vec尾部 }
  LIntArr := specialize TArray<Integer>.Create([4, 5, 6, 7]);
  AssertTrue('insert from array should succeed', LIntVec.InsertFromCollection(LIntVec.GetCount, LIntArr as specialize TGenericCollection<Integer>, 2));
  AssertEquals('New Count should be 6', 6, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]), 0, LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]), 1, LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]), 2, LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]), 3, LIntVec[3]);
  AssertEquals(format('New item should be %d', [4]), 4, LIntVec[4]);
  AssertEquals(format('New item should be %d', [5]), 5, LIntVec[5]);

  { 再插入一个Vec到Vec尾部 }
  LIntVec2 := specialize TVec<Integer>.Create([8, 9, 10, 11]);
  AssertTrue('insert from vec should succeed', LIntVec.InsertFromCollection(LIntVec.GetCount, LIntVec2 as specialize TGenericCollection<Integer>, LIntVec2.GetCount));
  AssertEquals('New Count should be 10', 10, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]),  0,  LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]),  1,  LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]),  2,  LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]),  3,  LIntVec[3]);
  AssertEquals(format('New item should be %d', [4]),  4,  LIntVec[4]);
  AssertEquals(format('New item should be %d', [5]),  5,  LIntVec[5]);
  AssertEquals(format('New item should be %d', [8]),  8,  LIntVec[6]);
  AssertEquals(format('New item should be %d', [9]),  9,  LIntVec[7]);
  AssertEquals(format('New item should be %d', [10]), 10, LIntVec[8]);
  AssertEquals(format('New item should be %d', [11]), 11, LIntVec[9]);

  { 再插入一个Vec到Vec头部 }
  LIntVec2 := specialize TVec<Integer>.Create([12, 13, 14, 15]);
  AssertTrue('insert from vec should succeed', LIntVec.InsertFromCollection(0, LIntVec2 as specialize TGenericCollection<Integer>, LIntVec2.GetCount));
  AssertEquals('New Count should be 14', 14, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [12]), 12, LIntVec[0]);
  AssertEquals(format('New item should be %d', [13]), 13, LIntVec[1]);
  AssertEquals(format('New item should be %d', [14]), 14, LIntVec[2]);
  AssertEquals(format('New item should be %d', [15]), 15, LIntVec[3]);
  AssertEquals(format('New item should be %d', [0]),  0,  LIntVec[4]);
  AssertEquals(format('New item should be %d', [1]),  1,  LIntVec[5]);
  AssertEquals(format('New item should be %d', [2]),  2,  LIntVec[6]);
  AssertEquals(format('New item should be %d', [3]),  3,  LIntVec[7]);
  AssertEquals(format('New item should be %d', [4]),  4,  LIntVec[8]);
  AssertEquals(format('New item should be %d', [5]),  5,  LIntVec[9]);
  AssertEquals(format('New item should be %d', [6]),  8,  LIntVec[10]);
  AssertEquals(format('New item should be %d', [7]),  9,  LIntVec[11]);
  AssertEquals(format('New item should be %d', [8]),  10, LIntVec[12]);
  AssertEquals(format('New item should be %d', [9]),  11, LIntVec[13]);

  //
  /// 托管元素
  ///

  { 失败测试: nil 容器 }
  LStrVec := specialize TVec<String>.Create;
  LStrArr := nil;
  AssertFalse('Failed to insert: collection is nil', LStrVec.InsertFromCollection(0, LStrArr as specialize TGenericCollection<String>, 1));
  
  { 失败测试: 索引越界 }
  LStrArr := specialize TArray<String>.Create(['1','2','3']);
  AssertFalse('Failed to insert: index is out of bounds', LStrVec.InsertFromCollection(1, LStrArr as specialize TGenericCollection<String>, LStrArr.GetCount));

  { 失败测试: 范围越界 }
  LStrArr := specialize TArray<String>.Create(['1','2','3']);
  AssertFalse('Failed to insert: count is greater than collection count', LStrVec.InsertFromCollection(0, LStrArr as specialize TGenericCollection<String>, LStrArr.GetCount + 1));

  { 插入数组到空Vec尾部 }
  LStrVec := specialize TVec<String>.Create;
  LStrArr := specialize TArray<String>.Create(['0','1','2','3']);
  AssertTrue('insert from array should succeed', LStrVec.InsertFromCollection(0, LStrArr as specialize TGenericCollection<String>, LStrArr.GetCount));
  AssertEquals('New Count should be 4', 4, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['0']), '0', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['1']), '1', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['2']), '2', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['3']), '3', LStrVec[3]);

  { 再插入一半数组到Vec尾部 }
  LStrArr := specialize TArray<String>.Create(['4','5','6','7']);
  AssertTrue('insert from array should succeed', LStrVec.InsertFromCollection(LStrVec.GetCount, LStrArr as specialize TGenericCollection<String>, 2));
  AssertEquals('New Count should be 6', 6, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['0']), '0', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['1']), '1', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['2']), '2', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['3']), '3', LStrVec[3]);
  AssertEquals(format('New item should be %s', ['4']), '4', LStrVec[4]);
  AssertEquals(format('New item should be %s', ['5']), '5', LStrVec[5]);

  { 再插入一个Vec到Vec尾部 }
  LStrVec2 := specialize TVec<String>.Create(['8','9','10','11']);
  AssertTrue('insert from vec should succeed', LStrVec.InsertFromCollection(LStrVec.GetCount, LStrVec2 as specialize TGenericCollection<String>, LStrVec2.GetCount));
  AssertEquals('New Count should be 10', 10, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['0']),  '0',  LStrVec[0]);
  AssertEquals(format('New item should be %s', ['1']),  '1',  LStrVec[1]);
  AssertEquals(format('New item should be %s', ['2']),  '2',  LStrVec[2]);
  AssertEquals(format('New item should be %s', ['3']),  '3',  LStrVec[3]);
  AssertEquals(format('New item should be %s', ['4']),  '4',  LStrVec[4]);
  AssertEquals(format('New item should be %s', ['5']),  '5',  LStrVec[5]);
  AssertEquals(format('New item should be %s', ['8']),  '8',  LStrVec[6]);
  AssertEquals(format('New item should be %s', ['9']),  '9',  LStrVec[7]);
  AssertEquals(format('New item should be %s', ['10']), '10', LStrVec[8]);
  AssertEquals(format('New item should be %s', ['11']), '11', LStrVec[9]);

  { 再插入一个Vec到Vec头部 }
  LStrVec2 := specialize TVec<String>.Create(['12','13','14','15']);
  AssertTrue('insert from vec should succeed', LStrVec.InsertFromCollection(0, LStrVec2 as specialize TGenericCollection<String>, LStrVec2.GetCount));
  AssertEquals('New Count should be 14', 14, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['12']), '12', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['13']), '13', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['14']), '14', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['15']), '15', LStrVec[3]);
  AssertEquals(format('New item should be %s', ['0']),  '0',  LStrVec[4]);
  AssertEquals(format('New item should be %s', ['1']),  '1',  LStrVec[5]);
  AssertEquals(format('New item should be %s', ['2']),  '2',  LStrVec[6]);
  AssertEquals(format('New item should be %s', ['3']),  '3',  LStrVec[7]);
  AssertEquals(format('New item should be %s', ['4']),  '4',  LStrVec[8]);
  AssertEquals(format('New item should be %s', ['5']),  '5',  LStrVec[9]);
  AssertEquals(format('New item should be %s', ['8']),  '8',  LStrVec[10]);
  AssertEquals(format('New item should be %s', ['9']),  '9',  LStrVec[11]);
  AssertEquals(format('New item should be %s', ['10']), '10', LStrVec[12]);
  AssertEquals(format('New item should be %s', ['11']), '11', LStrVec[13]);
end;

procedure TTestCase_vec.Test_InsertFromCollection2;
var
  LIntVec:  specialize IVec<Integer>;
  LIntVec2: specialize IVec<Integer>;
  LIntArr:  specialize IArray<Integer>;
  LStrVec:  specialize IVec<String>;
  LStrVec2: specialize IVec<String>;
  LStrArr:  specialize IArray<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;

  { 失败测试: nil 容器 }
  LIntArr := nil;
  AssertFalse('Failed to insert: collection is nil', LIntVec.InsertFromCollection(0, LIntArr as specialize TGenericCollection<Integer>));

  { 失败测试: 索引越界 }
  LIntArr := specialize TArray<Integer>.Create([1,2,3]);
  AssertFalse('Failed to insert: index is out of bounds', LIntVec.InsertFromCollection(1, LIntArr as specialize TGenericCollection<Integer>));


  { 插入数组到空Vec尾部 }
  LIntVec := specialize TVec<Integer>.Create;
  LIntArr := specialize TArray<Integer>.Create([0,1,2,3]);
  AssertTrue('insert from array should succeed', LIntVec.InsertFromCollection(0, LIntArr as specialize TGenericCollection<Integer>));
  AssertEquals('New Count should be 4', 4, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]), 0, LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]), 1, LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]), 2, LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]), 3, LIntVec[3]);

  { 再插入一个Vec到Vec尾部 }
  LIntVec2 := specialize TVec<Integer>.Create([4,5,6,7]);
  AssertTrue('insert from vec should succeed', LIntVec.InsertFromCollection(LIntVec.GetCount, LIntVec2 as specialize TGenericCollection<Integer>));
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]), 0, LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]), 1, LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]), 2, LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]), 3, LIntVec[3]);
  AssertEquals(format('New item should be %d', [4]), 4, LIntVec[4]);
  AssertEquals(format('New item should be %d', [5]), 5, LIntVec[5]);
  AssertEquals(format('New item should be %d', [6]), 6, LIntVec[6]);
  AssertEquals(format('New item should be %d', [7]), 7, LIntVec[7]);

  { 再插入一个Vec到Vec头部 }
  LIntVec2 := specialize TVec<Integer>.Create([8,9,10,11]);
  AssertTrue('insert from vec should succeed', LIntVec.InsertFromCollection(0, LIntVec2 as specialize TGenericCollection<Integer>));
  AssertEquals('New Count should be 12', 12, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [8]),  8, LIntVec[0]);
  AssertEquals(format('New item should be %d', [9]),  9, LIntVec[1]);
  AssertEquals(format('New item should be %d', [10]), 10, LIntVec[2]);
  AssertEquals(format('New item should be %d', [11]), 11, LIntVec[3]);
  AssertEquals(format('New item should be %d', [0]),  0, LIntVec[4]);
  AssertEquals(format('New item should be %d', [1]),  1, LIntVec[5]);
  AssertEquals(format('New item should be %d', [2]),  2, LIntVec[6]);
  AssertEquals(format('New item should be %d', [3]),  3, LIntVec[7]);
  AssertEquals(format('New item should be %d', [4]),  4, LIntVec[8]);
  AssertEquals(format('New item should be %d', [5]),  5, LIntVec[9]);
  AssertEquals(format('New item should be %d', [6]),  6, LIntVec[10]);
  AssertEquals(format('New item should be %d', [7]),  7, LIntVec[11]);

  { 再插入一个数组到Vec 索引4 }
  LIntArr := specialize TArray<Integer>.Create([12,13,14,15,16,17,18,19]);
  AssertTrue('insert from array should succeed', LIntVec.InsertFromCollection(4, LIntArr as specialize TGenericCollection<Integer>));
  AssertEquals('New Count should be 20', 20, LIntVec.GetCount);
  AssertEquals(format('New item should be %d', [0]),  8, LIntVec[0]);
  AssertEquals(format('New item should be %d', [1]),  9, LIntVec[1]);
  AssertEquals(format('New item should be %d', [2]),  10, LIntVec[2]);
  AssertEquals(format('New item should be %d', [3]),  11, LIntVec[3]);
  AssertEquals(format('New item should be %d', [12]), 12, LIntVec[4]);
  AssertEquals(format('New item should be %d', [13]), 13, LIntVec[5]);
  AssertEquals(format('New item should be %d', [14]), 14, LIntVec[6]);
  AssertEquals(format('New item should be %d', [15]), 15, LIntVec[7]);
  AssertEquals(format('New item should be %d', [16]), 16, LIntVec[8]);
  AssertEquals(format('New item should be %d', [17]), 17, LIntVec[9]);
  AssertEquals(format('New item should be %d', [18]), 18, LIntVec[10]);
  AssertEquals(format('New item should be %d', [19]), 19, LIntVec[11]);
  AssertEquals(format('New item should be %d', [0]),  0, LIntVec[12]);
  AssertEquals(format('New item should be %d', [1]),  1, LIntVec[13]);
  AssertEquals(format('New item should be %d', [2]),  2, LIntVec[14]);
  AssertEquals(format('New item should be %d', [3]),  3, LIntVec[15]);
  AssertEquals(format('New item should be %d', [4]),  4, LIntVec[16]);
  AssertEquals(format('New item should be %d', [5]),  5, LIntVec[17]);
  AssertEquals(format('New item should be %d', [6]),  6, LIntVec[18]);
  AssertEquals(format('New item should be %d', [7]),  7, LIntVec[19]);


  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  { 失败测试: nil 容器 }
  LStrArr := nil;
  AssertFalse('Failed to insert: collection is nil', LStrVec.InsertFromCollection(0, LStrArr as specialize TGenericCollection<String>));

  { 失败测试: 索引越界 }
  LStrArr := specialize TArray<String>.Create(['1','2','3']);
  AssertFalse('Failed to insert: index is out of bounds', LStrVec.InsertFromCollection(1, LStrArr as specialize TGenericCollection<String>));

  { 插入数组到空Vec尾部 }
  LStrVec := specialize TVec<String>.Create;
  LStrArr := specialize TArray<String>.Create(['0','1','2','3']);
  AssertTrue('insert from array should succeed', LStrVec.InsertFromCollection(0, LStrArr as specialize TGenericCollection<String>));
  AssertEquals('New Count should be 4', 4, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['0']), '0', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['1']), '1', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['2']), '2', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['3']), '3', LStrVec[3]);

  { 再插入一个Vec到Vec尾部 }
  LStrVec2 := specialize TVec<String>.Create(['4','5','6','7']);
  AssertTrue('insert from vec should succeed', LStrVec.InsertFromCollection(LStrVec.GetCount, LStrVec2 as specialize TGenericCollection<String>));
  AssertEquals('New Count should be 8', 8, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['0']), '0', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['1']), '1', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['2']), '2', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['3']), '3', LStrVec[3]);
  AssertEquals(format('New item should be %s', ['4']), '4', LStrVec[4]);
  AssertEquals(format('New item should be %s', ['5']), '5', LStrVec[5]);
  AssertEquals(format('New item should be %s', ['6']), '6', LStrVec[6]);
  AssertEquals(format('New item should be %s', ['7']), '7', LStrVec[7]);

  { 再插入一个Vec到Vec头部 }
  LStrVec2 := specialize TVec<String>.Create(['8','9','10','11']);
  AssertTrue('insert from vec should succeed', LStrVec.InsertFromCollection(0, LStrVec2 as specialize TGenericCollection<String>));
  AssertEquals('New Count should be 12', 12, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['8']),  '8', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['9']),  '9', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['10']), '10', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['11']), '11', LStrVec[3]);
  AssertEquals(format('New item should be %s', ['0']),  '0', LStrVec[4]);
  AssertEquals(format('New item should be %s', ['1']),  '1', LStrVec[5]);
  AssertEquals(format('New item should be %s', ['2']),  '2', LStrVec[6]);
  AssertEquals(format('New item should be %s', ['3']),  '3', LStrVec[7]);
  AssertEquals(format('New item should be %s', ['4']),  '4', LStrVec[8]);
  AssertEquals(format('New item should be %s', ['5']),  '5', LStrVec[9]);
  AssertEquals(format('New item should be %s', ['6']),  '6', LStrVec[10]);
  AssertEquals(format('New item should be %s', ['7']),  '7', LStrVec[11]);

  { 再插入一个数组到Vec 索引4 }
  LStrArr := specialize TArray<String>.Create(['12','13','14','15','16','17','18','19']);
  AssertTrue('insert from array should succeed', LStrVec.InsertFromCollection(4, LStrArr as specialize TGenericCollection<String>));
  AssertEquals('New Count should be 20', 20, LStrVec.GetCount);
  AssertEquals(format('New item should be %s', ['8']),  '8', LStrVec[0]);
  AssertEquals(format('New item should be %s', ['9']),  '9', LStrVec[1]);
  AssertEquals(format('New item should be %s', ['10']), '10', LStrVec[2]);
  AssertEquals(format('New item should be %s', ['11']), '11', LStrVec[3]);
  AssertEquals(format('New item should be %s', ['12']), '12', LStrVec[4]);
  AssertEquals(format('New item should be %s', ['13']), '13', LStrVec[5]);
  AssertEquals(format('New item should be %s', ['14']), '14', LStrVec[6]);
  AssertEquals(format('New item should be %s', ['15']), '15', LStrVec[7]);
  AssertEquals(format('New item should be %s', ['16']), '16', LStrVec[8]);
  AssertEquals(format('New item should be %s', ['17']), '17', LStrVec[9]);
  AssertEquals(format('New item should be %s', ['18']), '18', LStrVec[10]);
  AssertEquals(format('New item should be %s', ['19']), '19', LStrVec[11]);
  AssertEquals(format('New item should be %s', ['0']),  '0', LStrVec[12]);
  AssertEquals(format('New item should be %s', ['1']),  '1', LStrVec[13]);
  AssertEquals(format('New item should be %s', ['2']),  '2', LStrVec[14]);
  AssertEquals(format('New item should be %s', ['3']),  '3', LStrVec[15]);
  AssertEquals(format('New item should be %s', ['4']),  '4', LStrVec[16]);
  AssertEquals(format('New item should be %s', ['5']),  '5', LStrVec[17]);
  AssertEquals(format('New item should be %s', ['6']),  '6', LStrVec[18]);
  AssertEquals(format('New item should be %s', ['7']),  '7', LStrVec[19]);
end;

procedure TTestCase_vec.Test_PushPtr_ElementCount;
const
  DATA:     array[0..7] of Integer = (1,2,3,4,5,6,7,8);
  DATA_STR: array[0..7] of String = ('1','2','3','4','5','6','7','8');
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    // 压入nil
    AssertFalse(LVec.PushFromMemory(nil, 1));
    AssertEquals('New Count should be 0',0,LVec.GetCount);

    // 压入0个元素
    AssertFalse(LVec.PushFromMemory(@DATA[0], 0));
    AssertEquals('New Count should be 0',0,LVec.GetCount);

    // 压入1个元素
    AssertTrue(LVec.PushFromMemory(@DATA[0], 1));
    AssertEquals('New Count should be 1',1,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);

    // 压入多个元素
    AssertTrue(LVec.PushFromMemory(@DATA[1], 7));
    AssertEquals('New Count should be 8',8,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);
    AssertEquals(format('New item should be %d',[DATA[1]]),DATA[1],LVec[1]);
    AssertEquals(format('New item should be %d',[DATA[2]]),DATA[2],LVec[2]);
    AssertEquals(format('New item should be %d',[DATA[3]]),DATA[3],LVec[3]);
    AssertEquals(format('New item should be %d',[DATA[4]]),DATA[4],LVec[4]);
    AssertEquals(format('New item should be %d',[DATA[5]]),DATA[5],LVec[5]);
    AssertEquals(format('New item should be %d',[DATA[6]]),DATA[6],LVec[6]);
    AssertEquals(format('New item should be %d',[DATA[7]]),DATA[7],LVec[7]);

    // 压入nil
    AssertFalse(LVec.PushFromMemory(nil, 1));
    AssertEquals('New Count should be 8',8,LVec.GetCount);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create;
  try
    // 压入nil
    AssertFalse(LVecStr.PushFromMemory(nil, 1));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);

    // 压入0个元素
    AssertFalse(LVecStr.PushFromMemory(@DATA_STR[0], 0));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);

    // 压入1个元素
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[0], 1));
    AssertEquals('New Count should be 1',1,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);

    // 压入多个元素
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[1], 7));
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);
    AssertEquals(format('New item should be %s',[DATA_STR[1]]),DATA_STR[1],LVecStr[1]);
    AssertEquals(format('New item should be %s',[DATA_STR[2]]),DATA_STR[2],LVecStr[2]);
    AssertEquals(format('New item should be %s',[DATA_STR[3]]),DATA_STR[3],LVecStr[3]);
    AssertEquals(format('New item should be %s',[DATA_STR[4]]),DATA_STR[4],LVecStr[4]);
    AssertEquals(format('New item should be %s',[DATA_STR[5]]),DATA_STR[5],LVecStr[5]);
    AssertEquals(format('New item should be %s',[DATA_STR[6]]),DATA_STR[6],LVecStr[6]);
    AssertEquals(format('New item should be %s',[DATA_STR[7]]),DATA_STR[7],LVecStr[7]);

    // 压入nil
    AssertFalse(LVecStr.PushFromMemory(nil, 1));
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);
    
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PushPtr;
const
  DATA:     array[0..3] of Integer = (1,2,3,4);
  DATA_STR: array[0..3] of String = ('1','2','3','4');
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    // 压入nil
    AssertFalse(LVec.PushFromMemory(nil));
    AssertEquals('New Count should be 0',0,LVec.GetCount);

    // 压入1个元素
    AssertTrue(LVec.PushFromMemory(@DATA[0]));
    AssertEquals('New Count should be 1',1,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);

    // 继续压入
    AssertTrue(LVec.PushFromMemory(@DATA[1]));
    AssertEquals('New Count should be 2',2,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);
    AssertEquals(format('New item should be %d',[DATA[1]]),DATA[1],LVec[1]);

    // 继续压入
    AssertTrue(LVec.PushFromMemory(@DATA[2]));
    AssertEquals('New Count should be 3',3,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);
    AssertEquals(format('New item should be %d',[DATA[1]]),DATA[1],LVec[1]);
    AssertEquals(format('New item should be %d',[DATA[2]]),DATA[2],LVec[2]);

    // 继续压入
    AssertTrue(LVec.PushFromMemory(@DATA[3]));
    AssertEquals('New Count should be 4',4,LVec.GetCount);
    AssertEquals(format('New item should be %d',[DATA[0]]),DATA[0],LVec[0]);
    AssertEquals(format('New item should be %d',[DATA[1]]),DATA[1],LVec[1]);
    AssertEquals(format('New item should be %d',[DATA[2]]),DATA[2],LVec[2]);
    AssertEquals(format('New item should be %d',[DATA[3]]),DATA[3],LVec[3]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create;
  try
    // 压入nil
    AssertFalse(LVecStr.PushFromMemory(nil));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);

    // 压入1个元素
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[0]));
    AssertEquals('New Count should be 1',1,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);

    // 继续压入
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[1]));
    AssertEquals('New Count should be 2',2,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);
    AssertEquals(format('New item should be %s',[DATA_STR[1]]),DATA_STR[1],LVecStr[1]);

    // 继续压入
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[2]));
    AssertEquals('New Count should be 3',3,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);
    AssertEquals(format('New item should be %s',[DATA_STR[1]]),DATA_STR[1],LVecStr[1]);
    AssertEquals(format('New item should be %s',[DATA_STR[2]]),DATA_STR[2],LVecStr[2]);

    // 继续压入
    AssertTrue(LVecStr.PushFromMemory(@DATA_STR[3]));
    AssertEquals('New Count should be 4',4,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',[DATA_STR[0]]),DATA_STR[0],LVecStr[0]);
    AssertEquals(format('New item should be %s',[DATA_STR[1]]),DATA_STR[1],LVecStr[1]);
    AssertEquals(format('New item should be %s',[DATA_STR[2]]),DATA_STR[2],LVecStr[2]);
    AssertEquals(format('New item should be %s',[DATA_STR[3]]),DATA_STR[3],LVecStr[3]);
  finally
    LVecStr.Free;
  end;

end;

procedure TTestCase_vec.Test_Push_Elements;
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create;
  try
    // 压入空数组

    AssertException('Push empty array',Exception,
    procedure
    begin
      LVec.PushFromArray([]);
    end);
    AssertEquals('New Count should be 0',0,LVec.GetCount);

    // 压入1个元素
    LVec.PushFromArray([1]);
    AssertEquals('New Count should be 1',1,LVec.GetCount);
    AssertEquals(format('New item should be %d',[1]),1,LVec[0]);

    // 压入多个元素
    LVec.PushFromArray([2,3,4]);
    AssertEquals('New Count should be 4',4,LVec.GetCount);
    AssertEquals(format('New item should be %d',[1]),1,LVec[0]);
    AssertEquals(format('New item should be %d',[2]),2,LVec[1]);
    AssertEquals(format('New item should be %d',[3]),3,LVec[2]);
    AssertEquals(format('New item should be %d',[4]),4,LVec[3]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create;
  try
    // 压入空数组
    AssertException('Push empty array',Exception,
    procedure
    begin
      LVecStr.PushFromArray([]);
    end);
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);

    // 压入1个元素
    LVecStr.PushFromArray(['1']);
    AssertEquals('New Count should be 1',1,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['1']),'1',LVecStr[0]);

    // 压入多个元素
    LVecStr.PushFromArray(['2','3','4']);
    AssertEquals('New Count should be 4',4,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['1']),'1',LVecStr[0]);
    AssertEquals(format('New item should be %s',['2']),'2',LVecStr[1]);
    AssertEquals(format('New item should be %s',['3']),'3',LVecStr[2]);
    AssertEquals(format('New item should be %s',['4']),'4',LVecStr[3]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PopPtr_ElementCount;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 弹出0个元素
    AssertFalse(LVec.PopToMemory(@LArray[0], 0));
    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 错误测试: 弹出到nil
    AssertFalse(LVec.PopToMemory(nil, 1));
    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 弹出1个元素
    AssertTrue(LVec.PopToMemory(@LArray[0], 1));
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals(format('New item should be %d',[8]),8,LArray[0]);

    // 弹出多个元素
    AssertTrue(LVec.PopToMemory(@LArray[1], 7));
    AssertEquals('New Count should be 0',0,LVec.GetCount);
    AssertEquals(format('New item should be %d',[8]),8,LArray[0]);
    AssertEquals(format('New item should be %d',[1]),1,LArray[1]);
    AssertEquals(format('New item should be %d',[2]),2,LArray[2]);
    AssertEquals(format('New item should be %d',[3]),3,LArray[3]);
    AssertEquals(format('New item should be %d',[4]),4,LArray[4]);
    AssertEquals(format('New item should be %d',[5]),5,LArray[5]);
    AssertEquals(format('New item should be %d',[6]),6,LArray[6]);
    AssertEquals(format('New item should be %d',[7]),7,LArray[7]);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 弹出0个元素
    AssertFalse(LVecStr.PopToMemory(@LArrayStr[0], 0));
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 错误测试: 弹出到nil
    AssertFalse(LVecStr.PopToMemory(nil, 1));
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 弹出1个元素
    AssertTrue(LVecStr.PopToMemory(@LArrayStr[0], 1));
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['8']),'8',LArrayStr[0]);

    // 弹出多个元素
    AssertTrue(LVecStr.PopToMemory(@LArrayStr[1], 7));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['8']),'8',LArrayStr[0]);
    AssertEquals(format('New item should be %s',['1']),'1',LArrayStr[1]);
    AssertEquals(format('New item should be %s',['2']),'2',LArrayStr[2]);
    AssertEquals(format('New item should be %s',['3']),'3',LArrayStr[3]);
    AssertEquals(format('New item should be %s',['4']),'4',LArrayStr[4]);
    AssertEquals(format('New item should be %s',['5']),'5',LArrayStr[5]);
    AssertEquals(format('New item should be %s',['6']),'6',LArrayStr[6]);
    AssertEquals(format('New item should be %s',['7']),'7',LArrayStr[7]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PopPtr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 弹出到nil
    AssertFalse(LVec.PopToMemory(nil));
    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 弹出1个元素
    AssertTrue(LVec.PopToMemory(@LArray[0]));
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals(format('New item should be %d',[8]),8,LArray[0]);

    // 弹出多个元素
    AssertTrue(LVec.PopToMemory(@LArray[1]));
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals(format('New item should be %d',[8]),8,LArray[0]);
    AssertEquals(format('New item should be %d',[7]),7,LArray[1]);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 弹出到nil
    AssertFalse(LVecStr.PopToMemory(nil));
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 弹出1个元素
    AssertTrue(LVecStr.PopToMemory(@LArrayStr[0]));
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['8']),'8',LArrayStr[0]);

    // 弹出多个元素
    AssertTrue(LVecStr.PopToMemory(@LArrayStr[1]));
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals(format('New item should be %s',['8']),'8',LArrayStr[0]);
    AssertEquals(format('New item should be %s',['7']),'7',LArrayStr[1]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Pop_Array;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array of integer;
  LArrayStr: array of String;
begin
  Initialize(LArray);
  Initialize(LArrayStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 弹出0个元素
    AssertFalse(LVec.PopToArray(LArray,0));

    // 弹出1个元素
    AssertTrue('Pop 1 element',LVec.PopToArray(LArray,1));
    AssertEquals('Array length should be 1',1,Length(LArray));
    AssertEquals('Item should be 8',8,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);

    // 弹出多个元素
    AssertTrue(LVec.PopToArray(LArray,7));
    AssertEquals(Length(LArray),7);
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('Item should be 2',2,LArray[1]);
    AssertEquals('Item should be 3',3,LArray[2]);
    AssertEquals('Item should be 4',4,LArray[3]);
    AssertEquals('Item should be 5',5,LArray[4]);
    AssertEquals('Item should be 6',6,LArray[5]);
    AssertEquals('Item should be 7',7,LArray[6]);
    AssertEquals('New Count should be 0',0,LVec.GetCount);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 弹出0个元素
    AssertFalse(LVecStr.PopToArray(LArrayStr,0));

    // 弹出1个元素
    AssertTrue('Pop 1 element',LVecStr.PopToArray(LArrayStr,1));
    AssertEquals('Array length should be 1',1,Length(LArrayStr));
    AssertEquals('Item should be "8"','8',LArrayStr[0]);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);

    // 弹出多个元素
    AssertTrue(LVecStr.PopToArray(LArrayStr,7));
    AssertEquals(Length(LArrayStr),7);
    AssertEquals('Item should be "1"','1',LArrayStr[0]);
    AssertEquals('Item should be "2"','2',LArrayStr[1]);
    AssertEquals('Item should be "3"','3',LArrayStr[2]);
    AssertEquals('Item should be "4"','4',LArrayStr[3]);
    AssertEquals('Item should be "5"','5',LArrayStr[4]);
    AssertEquals('Item should be "6"','6',LArrayStr[5]);
    AssertEquals('Item should be "7"','7',LArrayStr[6]);
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PeekPtr_ElementCount;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LP:        PInteger;
  LPStr:     PString;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 查看0个元素
    AssertTrue('PeekMemory should be nil',nil = LVec.PeekMemory(0));

    // 查看1个元素
    LP := LVec.PeekMemory(1);
    AssertEquals('PeekMemory should be 8',8,LP^);
    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 查看多个元素
    LP := LVec.PeekMemory(8);
    AssertEquals('PeekMemory should be 1',1,LP[0]);
    AssertEquals('PeekMemory should be 2',2,LP[1]);
    AssertEquals('PeekMemory should be 3',3,LP[2]);
    AssertEquals('PeekMemory should be 4',4,LP[3]);
    AssertEquals('PeekMemory should be 5',5,LP[4]);
    AssertEquals('PeekMemory should be 6',6,LP[5]);
    AssertEquals('PeekMemory should be 7',7,LP[6]);
    AssertEquals('PeekMemory should be 8',8,LP[7]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 查看0个元素
    AssertTrue('PeekMemory should be nil',nil = LVecStr.PeekMemory(0));

    // 查看1个元素
    LPStr := LVecStr.PeekMemory(1);
    AssertEquals('PeekMemory should be "8"','8',LPStr^);
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 查看多个元素
    LPStr := LVecStr.PeekMemory(8);
    AssertEquals('PeekMemory should be "1"','1',LPStr[0]);
    AssertEquals('PeekMemory should be "2"','2',LPStr[1]);
    AssertEquals('PeekMemory should be "3"','3',LPStr[2]);
    AssertEquals('PeekMemory should be "4"','4',LPStr[3]);
    AssertEquals('PeekMemory should be "5"','5',LPStr[4]);
    AssertEquals('PeekMemory should be "6"','6',LPStr[5]);
    AssertEquals('PeekMemory should be "7"','7',LPStr[6]);
    AssertEquals('PeekMemory should be "8"','8',LPStr[7]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PeekPtr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LP:        PInteger;
  LPStr:     PString;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    LP := LVec.PeekMemory;
    AssertEquals('PeekMemory should be 8',8,LP^);
    AssertEquals('New Count should be 8',8,LVec.GetCount);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    LPStr := LVecStr.PeekMemory;
    AssertEquals('PeekMemory should be "8"','8',LPStr^);
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Peek_Read_ptr_ElementCount;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  Initialize(LArray);
  Initialize(LArrayStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 0个元素
    AssertFalse(LVec.PeekReadMemory(@LArray[0],0));

    // 查看1个元素
    AssertTrue(LVec.PeekReadMemory(@LArray[0],1));
    AssertEquals('PeekReadMemory should be 8',8,LArray[0]);
    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 查看多个元素
    AssertTrue(LVec.PeekReadMemory(@LArray[0],8));
    AssertEquals('PeekReadMemory should be 1',1,LArray[0]);
    AssertEquals('PeekReadMemory should be 2',2,LArray[1]);
    AssertEquals('PeekReadMemory should be 3',3,LArray[2]);
    AssertEquals('PeekReadMemory should be 4',4,LArray[3]);
    AssertEquals('PeekReadMemory should be 5',5,LArray[4]);
    AssertEquals('PeekReadMemory should be 6',6,LArray[5]);
    AssertEquals('PeekReadMemory should be 7',7,LArray[6]);
    AssertEquals('PeekReadMemory should be 8',8,LArray[7]);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 0个元素
    AssertFalse(LVecStr.PeekReadMemory(@LArrayStr[0],0));

    // 查看1个元素
    AssertTrue(LVecStr.PeekReadMemory(@LArrayStr[0],1));
    AssertEquals('PeekReadMemory should be "8"','8',LArrayStr[0]);
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 查看多个元素
    AssertTrue(LVecStr.PeekReadMemory(@LArrayStr[0],8));
    AssertEquals('PeekReadMemory should be "1"','1',LArrayStr[0]);
    AssertEquals('PeekReadMemory should be "2"','2',LArrayStr[1]);
    AssertEquals('PeekReadMemory should be "3"','3',LArrayStr[2]);
    AssertEquals('PeekReadMemory should be "4"','4',LArrayStr[3]);
    AssertEquals('PeekReadMemory should be "5"','5',LArrayStr[4]);
    AssertEquals('PeekReadMemory should be "6"','6',LArrayStr[5]);
    AssertEquals('PeekReadMemory should be "7"','7',LArrayStr[6]);
    AssertEquals('PeekReadMemory should be "8"','8',LArrayStr[7]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Peek_Read_ptr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LBuf:      Integer;
  LBufStr:   String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 0个元素
    AssertFalse(LVec.PeekReadMemory(@LBuf,0));

    // 错误测试: nil
    AssertFalse(LVec.PeekReadMemory(nil,1));

    // 查看1个元素
    AssertTrue(LVec.PeekReadMemory(@LBuf,1));
    AssertEquals('PeekReadMemory should be 8',8,LBuf);
    AssertEquals('New Count should be 8',8,LVec.GetCount);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 0个元素
    AssertFalse(LVecStr.PeekReadMemory(@LBufStr,0));

    // 查看1个元素
    AssertTrue(LVecStr.PeekReadMemory(@LBufStr,1));
    AssertEquals('PeekReadMemory should be "8"','8',LBufStr);
    AssertEquals('New Count should be 8',8,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_PeekToArray;
var
  LIntVec:   specialize IVec<Integer>;
  LStrVec:   specialize IVec<String>;
  LIntArray: array of Integer;
  LStrArray: array of String;
begin
  Initialize(LIntArray);
  Initialize(LStrArray);
  
  { 错误测试,0个元素 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertFalse(LIntVec.PeekToArray(LIntArray,0));

  { 错误测试,空栈 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertFalse(LIntVec.PeekToArray(LIntArray,1));

  { Peek 1个元素 }
  LIntVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  AssertTrue(LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 8',8,LIntArray[0]);
  AssertEquals('New Count should be 8',8,LIntVec.GetCount);

  { Peek 全部 }
  AssertTrue(LIntVec.PeekToArray(LIntArray,8));
  AssertEquals('Array length should be 8',8,Length(LIntArray));
  AssertEquals('PeekToArray should be 1',1,LIntArray[0]);
  AssertEquals('PeekToArray should be 2',2,LIntArray[1]);
  AssertEquals('PeekToArray should be 3',3,LIntArray[2]);
  AssertEquals('PeekToArray should be 4',4,LIntArray[3]);
  AssertEquals('PeekToArray should be 5',5,LIntArray[4]);
  AssertEquals('PeekToArray should be 6',6,LIntArray[5]);
  AssertEquals('PeekToArray should be 7',7,LIntArray[6]);
  AssertEquals('PeekToArray should be 8',8,LIntArray[7]);

  { Pop + Peek }
  LIntVec.Pop;
  AssertEquals('New Count should be 7',7,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 7',7,LIntArray[0]);
  
  LIntVec.Pop;
  AssertEquals('New Count should be 6',6,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 6',6,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 5',5,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 5',5,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 4',4,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 4',4,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 3',3,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 3',3,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 2',2,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 2',2,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 1',1,LIntVec.GetCount);
  AssertTrue('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));
  AssertEquals('Array length should be 1',1,Length(LIntArray));
  AssertEquals('PeekToArray should be 1',1,LIntArray[0]);

  LIntVec.Pop;
  AssertEquals('New Count should be 0',0,LIntVec.GetCount);
  AssertFalse('Peek 1 element failed',LIntVec.PeekToArray(LIntArray,1));

  ///
  /// 托管元素
  ///

  { 错误测试,0个元素 }
  LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  AssertFalse(LStrVec.PeekToArray(LStrArray,0));

  { 错误测试,空栈 }
  LStrVec := specialize TVec<String>.Create;
  AssertFalse(LStrVec.PeekToArray(LStrArray,1));

  { Peek 1个元素 }
  LStrVec := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  AssertTrue(LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "8"','8',LStrArray[0]);
  AssertEquals('New Count should be 8',8,LStrVec.GetCount);

  { Peek 全部 }
  AssertTrue(LStrVec.PeekToArray(LStrArray,8));
  AssertEquals('Array length should be 8',8,Length(LStrArray));
  AssertEquals('PeekToArray should be "1"','1',LStrArray[0]);
  AssertEquals('PeekToArray should be "2"','2',LStrArray[1]);
  AssertEquals('PeekToArray should be "3"','3',LStrArray[2]);
  AssertEquals('PeekToArray should be "4"','4',LStrArray[3]);
  AssertEquals('PeekToArray should be "5"','5',LStrArray[4]);
  AssertEquals('PeekToArray should be "6"','6',LStrArray[5]);
  AssertEquals('PeekToArray should be "7"','7',LStrArray[6]);
  AssertEquals('PeekToArray should be "8"','8',LStrArray[7]);

  { Pop + Peek }
  LStrVec.Pop;
  AssertEquals('New Count should be 7',7,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "7"','7',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 6',6,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "6"','6',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 5',5,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "5"','5',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 4',4,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "4"','4',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 3',3,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "3"','3',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 2',2,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "2"','2',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 1',1,LStrVec.GetCount);
  AssertTrue('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
  AssertEquals('Array length should be 1',1,Length(LStrArray));
  AssertEquals('PeekToArray should be "1"','1',LStrArray[0]);

  LStrVec.Pop;
  AssertEquals('New Count should be 0',0,LStrVec.GetCount);
  AssertFalse('Peek 1 element failed',LStrVec.PeekToArray(LStrArray,1));
end;

procedure TTestCase_vec.Test_Delete_Index_ElementCount;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVec.Delete(0,0);
    end);

    // 删除1个元素
    LVec.Delete(0,1);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);

    // 删除最后一个元素
    LVec.Delete(6,1);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);

    // 删除剩余元素
    LVec.Delete(0,6);
    AssertEquals('New Count should be 0',0,LVec.GetCount);

  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVecStr.Delete(0,0);
    end);
    
    // 删除1个元素
    LVecStr.Delete(0,1);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "2"','2',LVecStr[0]);
    AssertEquals('Item should be "3"','3',LVecStr[1]);
    AssertEquals('Item should be "4"','4',LVecStr[2]);
    AssertEquals('Item should be "5"','5',LVecStr[3]);
    AssertEquals('Item should be "6"','6',LVecStr[4]);
    AssertEquals('Item should be "7"','7',LVecStr[5]);
    AssertEquals('Item should be "8"','8',LVecStr[6]);

    // 删除最后一个元素
    LVecStr.Delete(6,1);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "2"','2',LVecStr[0]);
    AssertEquals('Item should be "3"','3',LVecStr[1]);
    AssertEquals('Item should be "4"','4',LVecStr[2]);
    AssertEquals('Item should be "5"','5',LVecStr[3]);
    AssertEquals('Item should be "6"','6',LVecStr[4]);
    AssertEquals('Item should be "7"','7',LVecStr[5]);

    // 删除剩余元素
    LVecStr.Delete(0,6);
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);

  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Delete_Index;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try

    // 删除1个元素
    LVec.Delete(0);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);

    // 删除最后一个元素
    LVec.Delete(6);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);

    // 随便删除一个
    LVec.Delete(3);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 6',6,LVec[3]);
    AssertEquals('Item should be 7',7,LVec[4]);

  finally
    LVec.Free;
  end;

  // 字符串测试
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 删除1个元素
    LVecStr.Delete(0);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "2"','2',LVecStr[0]);
    AssertEquals('Item should be "3"','3',LVecStr[1]);
    AssertEquals('Item should be "4"','4',LVecStr[2]);
    AssertEquals('Item should be "5"','5',LVecStr[3]);
    AssertEquals('Item should be "6"','6',LVecStr[4]);
    AssertEquals('Item should be "7"','7',LVecStr[5]);
    AssertEquals('Item should be "8"','8',LVecStr[6]);

    // 删除最后一个元素
    LVecStr.Delete(6);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "2"','2',LVecStr[0]);
    AssertEquals('Item should be "3"','3',LVecStr[1]);
    AssertEquals('Item should be "4"','4',LVecStr[2]);
    AssertEquals('Item should be "5"','5',LVecStr[3]);
    AssertEquals('Item should be "6"','6',LVecStr[4]);
    AssertEquals('Item should be "7"','7',LVecStr[5]);

    // 随便删除一个
    LVecStr.Delete(3);
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "2"','2',LVecStr[0]);
    AssertEquals('Item should be "3"','3',LVecStr[1]);
    AssertEquals('Item should be "4"','4',LVecStr[2]);
    AssertEquals('Item should be "6"','6',LVecStr[3]);
    
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_DeleteSwap_Index_ElementCount;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVec.DeleteSwap(0,0);
    end);

    // 删除1个元素
    LVec.DeleteSwap(0,1);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 删除前两个元素
    LVec.DeleteSwap(0,2);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 6',6,LVec[0]);
    AssertEquals('Item should be 7',7,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);

    // 删除后两个个元素
    LVec.DeleteSwap(3,2);
    AssertEquals('New Count should be 3',3,LVec.GetCount);
    AssertEquals('Item should be 6',6,LVec[0]);
    AssertEquals('Item should be 7',7,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);

    // 删除最后一个元素
    LVec.DeleteSwap(2,1);
    AssertEquals('New Count should be 2',2,LVec.GetCount);
    AssertEquals('Item should be 6',6,LVec[0]);
    AssertEquals('Item should be 7',7,LVec[1]);

    // 删除所有元素
    LVec.DeleteSwap(0,2);
    AssertEquals('New Count should be 0',0,LVec.GetCount);
  finally
    LVec.Free;
  end;
  
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVecStr.DeleteSwap(0,0);
    end);

    // 删除1个元素
    LVecStr.DeleteSwap(0,1);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"','8',LVecStr[0]);
    AssertEquals('Item should be "2"','2',LVecStr[1]);
    AssertEquals('Item should be "3"','3',LVecStr[2]);

    // 删除前两个元素
    LVecStr.DeleteSwap(0,2);
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "6"','6',LVecStr[0]);
    AssertEquals('Item should be "7"','7',LVecStr[1]);
    AssertEquals('Item should be "3"','3',LVecStr[2]);

    // 删除后两个个元素
    LVecStr.DeleteSwap(3,2);
    AssertEquals('New Count should be 3',3,LVecStr.GetCount);
    AssertEquals('Item should be "6"','6',LVecStr[0]);
    AssertEquals('Item should be "7"','7',LVecStr[1]);
    AssertEquals('Item should be "3"','3',LVecStr[2]);

    // 删除最后一个元素
    LVecStr.DeleteSwap(2,1);
    AssertEquals('New Count should be 2',2,LVecStr.GetCount);
    AssertEquals('Item should be "6"','6',LVecStr[0]);
    AssertEquals('Item should be "7"','7',LVecStr[1]);
    
    // 删除所有元素
    LVecStr.DeleteSwap(0,2);
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_DeleteSwap_Index;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVec.DeleteSwap(0,0);
    end);

    // 删除1个元素
    LVec.DeleteSwap(0);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 删除最后一个元素
    LVec.DeleteSwap(6);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 删除0个元素
    AssertException('delete 0 element should raise exception',Exception,
    procedure
    begin
      LVecStr.DeleteSwap(0,0);
    end);

    // 删除1个元素
    LVecStr.DeleteSwap(0);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"','8',LVecStr[0]);
    AssertEquals('Item should be "2"','2',LVecStr[1]);
    AssertEquals('Item should be "3"','3',LVecStr[2]);

    // 删除最后一个元素
    LVecStr.DeleteSwap(6);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"','8',LVecStr[0]);
    AssertEquals('Item should be "2"','2',LVecStr[1]);
    AssertEquals('Item should be "3"','3',LVecStr[2]);
    
    
    
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemovePtr_Index_ElementCount_Ptr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVec.RemoveMemory(0,0,@LArray[0]));

    // 错误测试: nil
    AssertFalse('remove nil should return false',LVec.RemoveMemory(0,1,nil));

    // 移除1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveMemory(0,1,@LArray[0]));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveMemory(6,1,@LArray[0]));
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LArray[0]);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);

    // 移除前两个元素
    AssertTrue('remove 2 elements should return true',LVec.RemoveMemory(0,2,@LArray[0]));
    AssertEquals('Item should be 2',2,LArray[0]);
    AssertEquals('Item should be 3',3,LArray[1]);
    AssertEquals('New Count should be 4',4,LVec.GetCount);
    AssertEquals('Item should be 4',4,LVec[0]);
    AssertEquals('Item should be 5',5,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);
    AssertEquals('Item should be 7',7,LVec[3]);

    // 移除后两个元素
    AssertTrue('remove 2 elements should return true',LVec.RemoveMemory(2,2,@LArray[0]));
    AssertEquals('New Count should be 2',2,LVec.GetCount);
    AssertEquals('Item should be 6',6,LArray[0]);
    AssertEquals('Item should be 7',7,LArray[1]);
    AssertEquals('Item should be 4',4,LVec[0]);
    AssertEquals('Item should be 5',5,LVec[1]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVec.RemoveMemory(0,2,@LArray[0]));
    AssertEquals('New Count should be 0',0,LVec.GetCount);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVecStr.RemoveMemory(0,0,@LArrayStr[0]));

    // 错误测试: nil
    AssertFalse('remove nil should return false',LVecStr.RemoveMemory(0,1,nil));

    // 移除1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemory(0,1,@LArrayStr[0]));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7', 7, LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveMemory(6,1,@LArrayStr[0]));
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LArrayStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 移除前两个元素
    AssertTrue('remove 2 elements should return true',LVecStr.RemoveMemory(0,2,@LArrayStr[0]));
    AssertEquals('Item should be "2"', '2', LArrayStr[0]);
    AssertEquals('Item should be "3"', '3', LArrayStr[1]);
    AssertEquals('New Count should be 4',4,LVecStr.GetCount);

    // 移除后两个元素
    AssertTrue('remove 2 elements should return true',LVecStr.RemoveMemory(2,2,@LArrayStr[0]));
    AssertEquals('New Count should be 2',2,LVecStr.GetCount);
    AssertEquals('Item should be "6"', '6', LArrayStr[0]);
    AssertEquals('Item should be "7"', '7', LArrayStr[1]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVecStr.RemoveMemory(0,2,@LArrayStr[0]));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemovePtr_Index_Ptr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: nil
    AssertFalse('remove nil should return false',LVec.RemoveMemory(0,1,nil));
    
    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveMemory(0,@LArray[0]));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);
    
    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveMemory(6,@LArray[0]));
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LArray[0]);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    
    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVec.RemoveMemory(2,@LArray[0]));
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 4',4,LArray[0]);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 5',5,LVec[2]);
    AssertEquals('Item should be 6',6,LVec[3]);
    AssertEquals('Item should be 7',7,LVec[4]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: nil
    AssertFalse('remove nil should return false',LVecStr.RemoveMemory(0, nil));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemory(0,@LArrayStr[0]));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7', 7, LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveMemory(6,@LArrayStr[0]));
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LArrayStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemory(2,@LArrayStr[0]));
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "4"', '4', LArrayStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemovePtrSwap_Index_ElementCount_Ptr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVec.RemoveMemorySwap(0,0,@LArray[0]));

    // 错误测试: nil
    AssertFalse('remove nil should return false',LVec.RemoveMemorySwap(0,1,nil));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveMemorySwap(0,@LArray[0]));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveMemorySwap(6,@LArray[0]));
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 7',7,LArray[0]);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);

    // 随便移除三个
    AssertTrue('remove 3 elements should return true',LVec.RemoveMemorySwap(2,3,@LArray[0]));
    AssertEquals('New Count should be 3',3,LVec.GetCount);
    AssertEquals('Item should be 3',3,LArray[0]);
    AssertEquals('Item should be 4',4,LArray[1]);
    AssertEquals('Item should be 5',5,LArray[2]);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVec.RemoveMemorySwap(0,3,@LArray[0]));
    AssertEquals('New Count should be 0',0,LVec.GetCount);
    AssertEquals('Item should be 8',8,LArray[0]);
    AssertEquals('Item should be 2',2,LArray[1]);
    AssertEquals('Item should be 6',6,LArray[2]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: nil
    AssertFalse('remove nil should return false',LVecStr.RemoveMemorySwap(0, nil));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemorySwap(0,@LArrayStr[0]));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);
    AssertEquals('Item should be "7"', '7', LVecStr[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveMemorySwap(6,@LArrayStr[0]));
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "7"', '7', LArrayStr[0]);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);

    // 随便移除三个
    AssertTrue('remove 3 elements should return true',LVecStr.RemoveMemorySwap(2,3,@LArrayStr[0]));
    AssertEquals('New Count should be 3',3,LVecStr.GetCount);
    AssertEquals('Item should be "3"', '3', LArrayStr[0]);
    AssertEquals('Item should be "4"', '4', LArrayStr[1]);
    AssertEquals('Item should be "5"', '5', LArrayStr[2]);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "6"', '6', LVecStr[2]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVecStr.RemoveMemorySwap(0,3,@LArrayStr[0]));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LArrayStr[0]);
    AssertEquals('Item should be "2"', '2', LArrayStr[1]);
    AssertEquals('Item should be "6"', '6', LArrayStr[2]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemovePtrSwap_Index_Ptr;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array[0..7] of Integer;
  LArrayStr: array[0..7] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: nil
    AssertFalse('remove nil should return false',LVec.RemoveMemorySwap(0,nil));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveMemorySwap(0,@LArray[0]));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveMemorySwap(6,@LArray[0]));
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 7',7,LArray[0]);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVec.RemoveMemorySwap(2,@LArray[0]));
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 3',3,LArray[0]);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: nil
    AssertFalse('remove nil should return false',LVecStr.RemoveMemorySwap(0, nil));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemorySwap(0,@LArrayStr[0]));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveMemorySwap(6,@LArrayStr[0]));
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "7"', '7', LArrayStr[0]);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVecStr.RemoveMemorySwap(2,@LArrayStr[0]));
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "3"', '3', LArrayStr[0]);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Remove_Index_ElementCount_Elements;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array of Integer;
  LArrayStr: array of String;
begin
  Initialize(LArray);
  Initialize(LArrayStr);
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVec.RemoveArray(0,0,LArray));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveArray(0,1,LArray));
    AssertEquals('removed element count should be 1',1,Length(LArray));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveArray(6,1,LArray));
    AssertEquals('removed element count should be 1',1,Length(LArray));
    AssertEquals('Item should be 8',8,LArray[0]);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);

    // 随便移除3个
    AssertTrue('remove 3 elements should return true',LVec.RemoveArray(2,3,LArray));
    AssertEquals('removed element count should be 3',3,Length(LArray));
    AssertEquals('Item should be 4',4,LArray[0]);
    AssertEquals('Item should be 5',5,LArray[1]);
    AssertEquals('Item should be 6',6,LArray[2]);
    AssertEquals('New Count should be 3',3,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 7',7,LVec[2]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVec.RemoveArray(0,3,LArray));
    AssertEquals('removed element count should be 3',3,Length(LArray));
    AssertEquals('Item should be 2',2,LArray[0]);
    AssertEquals('Item should be 3',3,LArray[1]);
    AssertEquals('Item should be 7',7,LArray[2]);
    AssertEquals('New Count should be 0',0,LVec.GetCount);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVecStr.RemoveArray(0,0,LArrayStr));

     // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveArray(0,1,LArrayStr));
    AssertEquals('removed element count should be 1',1,Length(LArrayStr));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
    AssertEquals('Item should be "4"', '4', LVecStr[2]);
    AssertEquals('Item should be "5"', '5', LVecStr[3]);
    AssertEquals('Item should be "6"', '6', LVecStr[4]);
    AssertEquals('Item should be "7"', '7', LVecStr[5]);
    AssertEquals('Item should be "8"', '8', LVecStr[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveArray(6,1,LArrayStr));
    AssertEquals('removed element count should be 1',1,Length(LArrayStr));
    AssertEquals('Item should be "8"', '8', LArrayStr[0]);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
    AssertEquals('Item should be "4"', '4', LVecStr[2]);
    AssertEquals('Item should be "5"', '5', LVecStr[3]);
    AssertEquals('Item should be "6"', '6', LVecStr[4]);
    AssertEquals('Item should be "7"', '7', LVecStr[5]);

    // 随便移除3个
    AssertTrue('remove 3 elements should return true',LVecStr.RemoveArray(2,3,LArrayStr));
    AssertEquals('removed element count should be 3',3,Length(LArrayStr));
    AssertEquals('Item should be "4"', '4', LArrayStr[0]);
    AssertEquals('Item should be "5"', '5', LArrayStr[1]);
    AssertEquals('Item should be "6"', '6', LArrayStr[2]);
    AssertEquals('New Count should be 3',3,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
    AssertEquals('Item should be "7"', '7', LVecStr[2]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVecStr.RemoveArray(0,3,LArrayStr));
    AssertEquals('removed element count should be 3',3,Length(LArrayStr));
    AssertEquals('Item should be "2"', '2', LArrayStr[0]);
    AssertEquals('Item should be "3"', '3', LArrayStr[1]);
    AssertEquals('Item should be "7"', '7', LArrayStr[2]);
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Remove_Index_Element;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LBuf:      Integer;
  LBufStr:   String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.Remove(0,LBuf));
    AssertEquals('Item should be 1',1,LBuf);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);
    AssertEquals('Item should be 8',8,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.Remove(6,LBuf));
    AssertEquals('Item should be 8',8,LBuf);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 4',4,LVec[2]);
    AssertEquals('Item should be 5',5,LVec[3]);
    AssertEquals('Item should be 6',6,LVec[4]);
    AssertEquals('Item should be 7',7,LVec[5]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVec.Remove(2,LBuf));
    AssertEquals('Item should be 4',4,LBuf);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 5',5,LVec[2]);
    AssertEquals('Item should be 6',6,LVec[3]);
    AssertEquals('Item should be 7',7,LVec[4]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.Remove(0,LBufStr));
    AssertEquals('Item should be "1"', '1', LBufStr);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.Remove(6,LBufStr));
    AssertEquals('Item should be "8"', '8', LBufStr);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVecStr.Remove(2,LBufStr));
    AssertEquals('Item should be "4"', '4', LBufStr);
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_Remove_Index;
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
  LBuf:    Integer;
  LBufStr: String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 越界
    AssertException('remove out of bounds should raise EArgumentOutOfRangeException',Exception,
      procedure
      begin
        LBuf := LVec.Remove(8);
      end);

    AssertEquals('New Count should be 8',8,LVec.GetCount);

    // 移除第1个元素
    LBuf := LVec.Remove(0);
    AssertEquals('Item should be 1',1,LBuf);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);

    // 移除最后一个元素
    LBuf := LVec.Remove(6);
    AssertEquals('Item should be 8',8,LBuf);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);

    // 随便移除一个
    LBuf := LVec.Remove(2);
    AssertEquals('Item should be 4',4,LBuf);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 2',2,LVec[0]);
    AssertEquals('Item should be 3',3,LVec[1]);
    AssertEquals('Item should be 5',5,LVec[2]);
    AssertEquals('Item should be 6',6,LVec[3]);
    AssertEquals('Item should be 7',7,LVec[4]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 越界
    AssertException('remove out of bounds should raise EArgumentOutOfRangeException',Exception,
      procedure
      begin
        LBufStr := LVecStr.Remove(8);
      end);

    AssertEquals('New Count should be 8',8,LVecStr.GetCount);

    // 移除第1个元素
    LBufStr := LVecStr.Remove(0);
    AssertEquals('Item should be "1"', '1', LBufStr);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);

    // 移除最后一个元素
    LBufStr := LVecStr.Remove(6);
    AssertEquals('Item should be "8"', '8', LBufStr);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);

    // 随便移除一个
    LBufStr := LVecStr.Remove(2);
    AssertEquals('Item should be "4"', '4', LBufStr);
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "2"', '2', LVecStr[0]);
    AssertEquals('Item should be "3"', '3', LVecStr[1]);
    AssertEquals('Item should be "5"', '5', LVecStr[2]);
    AssertEquals('Item should be "6"', '6', LVecStr[3]);
    AssertEquals('Item should be "7"', '7', LVecStr[4]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemoveSwap_Index_ElementCount_Elements;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LArray:    array of Integer;
  LArrayStr: array of String;
begin
  Initialize(LArray);
  Initialize(LArrayStr);
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 删除0个元素
    AssertFalse('remove 0 element should return false',LVec.RemoveArraySwap(0,0,LArray));

    // 错误测试: 越界
    AssertFalse('remove out of bounds should return false',LVec.RemoveArraySwap(8,1,LArray));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveArraySwap(0,1,LArray));
    AssertEquals('removed element count should be 1',1,Length(LArray));
    AssertEquals('Item should be 1',1,LArray[0]);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveArraySwap(6,1,LArray));
    AssertEquals('removed element count should be 1',1,Length(LArray));
    AssertEquals('Item should be 7',7,LArray[0]);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);

    // 随便移除两个
    AssertTrue('remove 2 elements should return true',LVec.RemoveArraySwap(1,2,LArray));
    AssertEquals('removed element count should be 2',2,Length(LArray));
    AssertEquals('Item should be 2',2,LArray[0]);
    AssertEquals('Item should be 3',3,LArray[1]);
    AssertEquals('New Count should be 4',4,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 5',5,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVec.RemoveArraySwap(0,4,LArray));
    AssertEquals('removed element count should be 4',4,Length(LArray));
    AssertEquals('New Count should be 0',0,LVec.GetCount);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 0个元素
    AssertFalse('remove 0 element should return false',LVecStr.RemoveArraySwap(0,0,LArrayStr));

    // 错误测试: 越界
    AssertFalse('remove out of bounds should return false',LVecStr.RemoveArraySwap(8,1,LArrayStr));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveArraySwap(0,1,LArrayStr));
    AssertEquals('removed element count should be 1',1,Length(LArrayStr));
    AssertEquals('Item should be "1"', '1', LArrayStr[0]);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);
    AssertEquals('Item should be "7"', '7', LVecStr[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveArraySwap(6,1,LArrayStr));
    AssertEquals('removed element count should be 1',1,Length(LArrayStr));
    AssertEquals('Item should be "7"', '7', LArrayStr[0]);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);

    // 随便移除两个
    AssertTrue('remove 2 elements should return true',LVecStr.RemoveArraySwap(1,2,LArrayStr));
    AssertEquals('removed element count should be 2',2,Length(LArrayStr));
    AssertEquals('Item should be "2"', '2', LArrayStr[0]);
    AssertEquals('Item should be "3"', '3', LArrayStr[1]);
    AssertEquals('New Count should be 4',4,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "5"', '5', LVecStr[1]);
    AssertEquals('Item should be "6"', '6', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);

    // 移除所有元素
    AssertTrue('remove all elements should return true',LVecStr.RemoveArraySwap(0,4,LArrayStr));
    AssertEquals('removed element count should be 4',4,Length(LArrayStr));
    AssertEquals('New Count should be 0',0,LVecStr.GetCount);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemoveSwap_Index_Element;
var
  LVec:      specialize TVec<Integer>;
  LVecStr:   specialize TVec<String>;
  LBuf:      Integer;
  LBufStr:   String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 越界
    AssertFalse('remove out of bounds should return false',LVec.RemoveSwap(8,LBuf));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVec.RemoveSwap(0,LBuf));
    AssertEquals('Item should be 1',1,LBuf);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);
    AssertEquals('Item should be 7',7,LVec[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVec.RemoveSwap(6,LBuf));
    AssertEquals('Item should be 7',7,LBuf);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVec.RemoveSwap(2,LBuf));
    AssertEquals('Item should be 3',3,LBuf);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
  finally
    LVec.Free;
  end;

  // 托管元素
  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 越界
    AssertFalse('remove out of bounds should return false',LVecStr.RemoveSwap(8,LBufStr));

    // 移除第1个元素
    AssertTrue('remove 1 element should return true',LVecStr.RemoveSwap(0,LBufStr));
    AssertEquals('Item should be "1"', '1', LBufStr);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);
    AssertEquals('Item should be "7"', '7', LVecStr[6]);

    // 移除最后一个元素
    AssertTrue('remove last element should return true',LVecStr.RemoveSwap(6,LBufStr));
    AssertEquals('Item should be "7"', '7', LBufStr);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);

    // 随便移除一个
    AssertTrue('remove 1 element should return true',LVecStr.RemoveSwap(2,LBufStr));
    AssertEquals('Item should be "3"', '3', LBufStr);
    AssertEquals('New Count should be 5',5,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "6"', '6', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_RemoveSwap_Index;
var
  LVec:    specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
  LBuf:    Integer;
  LBufStr: String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    // 错误测试: 越界
    AssertException('remove out of bounds should raise EArgumentOutOfRangeException',Exception,
      procedure
      begin
        LBuf := LVec.RemoveSwap(8);
      end);

    // 移除第1个元素
    LBuf := LVec.RemoveSwap(0);
    AssertEquals('Item should be 1',1,LBuf);
    AssertEquals('New Count should be 7',7,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);

    // 移除最后一个元素
    LBuf := LVec.RemoveSwap(6);
    AssertEquals('Item should be 7',7,LBuf);
    AssertEquals('New Count should be 6',6,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 3',3,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
    AssertEquals('Item should be 6',6,LVec[5]);

    // 随便移除一个
    LBuf := LVec.RemoveSwap(2);
    AssertEquals('Item should be 3',3,LBuf);
    AssertEquals('New Count should be 5',5,LVec.GetCount);
    AssertEquals('Item should be 8',8,LVec[0]);
    AssertEquals('Item should be 2',2,LVec[1]);
    AssertEquals('Item should be 6',6,LVec[2]);
    AssertEquals('Item should be 4',4,LVec[3]);
    AssertEquals('Item should be 5',5,LVec[4]);
  finally
    LVec.Free;
  end;

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    // 错误测试: 越界
    AssertException('remove out of bounds should raise EArgumentOutOfRangeException',Exception,
      procedure
      begin
        LBufStr := LVecStr.RemoveSwap(8);
      end);

    // 移除第1个元素
    LBufStr := LVecStr.RemoveSwap(0);
    AssertEquals('Item should be "1"', '1', LBufStr);
    AssertEquals('New Count should be 7',7,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);
    AssertEquals('Item should be "7"', '7', LVecStr[6]);

    // 移除最后一个元素
    LBufStr := LVecStr.RemoveSwap(6);
    AssertEquals('Item should be "7"', '7', LBufStr);
    AssertEquals('New Count should be 6',6,LVecStr.GetCount);
    AssertEquals('Item should be "8"', '8', LVecStr[0]);
    AssertEquals('Item should be "2"', '2', LVecStr[1]);
    AssertEquals('Item should be "3"', '3', LVecStr[2]);
    AssertEquals('Item should be "4"', '4', LVecStr[3]);
    AssertEquals('Item should be "5"', '5', LVecStr[4]);
    AssertEquals('Item should be "6"', '6', LVecStr[5]);
  finally
    LVecStr.Free;
  end;
end;

procedure TTestCase_vec.Test_FromTArray;
var
  LVec:   specialize TVec<Integer>;
  LVecStr: specialize TVec<String>;
  LArray: specialize TArray<Integer>;
  LArrayStr: specialize TArray<String>;
begin
  LArray := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8]);
  try
    LVec := specialize TVec<Integer>.Create;
    try
      LVec.LoadFromCollection(LArray as specialize TGenericCollection<Integer>);
      AssertEquals('New Count should be 8',8,LVec.GetCount);
      AssertEquals('Item should be 1',1,LVec[0]);
      AssertEquals('Item should be 2',2,LVec[1]);
      AssertEquals('Item should be 3',3,LVec[2]);
      AssertEquals('Item should be 4',4,LVec[3]);
      AssertEquals('Item should be 5',5,LVec[4]);
      AssertEquals('Item should be 6',6,LVec[5]);
      AssertEquals('Item should be 7',7,LVec[6]);
      AssertEquals('Item should be 8',8,LVec[7]);
    finally
      LVec.Free;
    end;
  finally
    LArray.Free;
  end;

  // 托管元素
  LArrayStr := specialize TArray<String>.Create(['1','2','3','4','5','6','7','8']);
  try
    LVecStr := specialize TVec<String>.Create;
    try
      LVecStr.LoadFromCollection(LArrayStr as specialize TGenericCollection<String>);
      AssertEquals('New Count should be 8',8,LVecStr.GetCount);
      AssertEquals('Item should be "1"', '1', LVecStr[0]);
      AssertEquals('Item should be "2"', '2', LVecStr[1]);
      AssertEquals('Item should be "3"', '3', LVecStr[2]);
      AssertEquals('Item should be "4"', '4', LVecStr[3]);
      AssertEquals('Item should be "5"', '5', LVecStr[4]);
      AssertEquals('Item should be "6"', '6', LVecStr[5]);
      AssertEquals('Item should be "7"', '7', LVecStr[6]);
      AssertEquals('Item should be "8"', '8', LVecStr[7]);
    finally
      LVecStr.Free;
    end;
  finally
    LArrayStr.Free;
  end;
end;

procedure TTestCase_vec.Test_WriteFromMemory;
const
  DATA_INT: array[0..7] of Integer = (1,2,3,4,5,6,7,8);
  DATA_STR: array[0..7] of String = ('1','2','3','4','5','6','7','8');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
begin
  LIntVec := specialize TVec<Integer>.Create;

  { 失败测试: 写入 nil }
  AssertFalse('WriteFromMemory should return false', LIntVec.WriteFromMemory(0, nil, 8));

  { 失败测试: 0 长度 }
  AssertFalse('WriteFromMemory should return false', LIntVec.WriteFromMemory(0, @DATA_INT[0], 0));

  { 失败测试: 越界 }
  AssertFalse('WriteFromMemory should return false', LIntVec.WriteFromMemory(1, @DATA_INT[0], 8));
  
  { 从 0 开始 }
  LIntVec := specialize TVec<Integer>.Create;
  AssertTrue('WriteFromMemory should return true', LIntVec.WriteFromMemory(0, @DATA_INT[0], 8));
  AssertEquals('New Count should be 8', 8, LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 2', 2, LIntVec[1]);
  AssertEquals('Item should be 3', 3, LIntVec[2]);
  AssertEquals('Item should be 4', 4, LIntVec[3]);
  AssertEquals('Item should be 5', 5, LIntVec[4]);
  AssertEquals('Item should be 6', 6, LIntVec[5]);
  AssertEquals('Item should be 7', 7, LIntVec[6]);
  AssertEquals('Item should be 8', 8, LIntVec[7]);

  { 从 1 开始 }
  AssertTrue('WriteFromMemory should return true', LIntVec.WriteFromMemory(1, @DATA_INT[0], 8));
  AssertEquals('New Count should be 9', 9, LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 1', 1, LIntVec[1]);
  AssertEquals('Item should be 2', 2, LIntVec[2]);
  AssertEquals('Item should be 3', 3, LIntVec[3]);
  AssertEquals('Item should be 4', 4, LIntVec[4]);
  AssertEquals('Item should be 5', 5, LIntVec[5]);
  AssertEquals('Item should be 6', 6, LIntVec[6]);
  AssertEquals('Item should be 7', 7, LIntVec[7]);
  AssertEquals('Item should be 8', 8, LIntVec[8]);

  { 从3写入两个 }
  AssertTrue('WriteFromMemory should return true', LIntVec.WriteFromMemory(3, @DATA_INT[0], 2));
  AssertEquals('New Count should be 9', 9, LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 1', 1, LIntVec[1]);
  AssertEquals('Item should be 2', 2, LIntVec[2]);
  AssertEquals('Item should be 1', 1, LIntVec[3]);
  AssertEquals('Item should be 2', 2, LIntVec[4]);
  AssertEquals('Item should be 5', 5, LIntVec[5]);
  AssertEquals('Item should be 6', 6, LIntVec[6]);
  AssertEquals('Item should be 7', 7, LIntVec[7]);
  AssertEquals('Item should be 8', 8, LIntVec[8]);

  { 从尾部写入 }
  AssertTrue('WriteFromMemory should return true', LIntVec.WriteFromMemory(LIntVec.Count, @DATA_INT[0], Length(DATA_INT)));
  AssertEquals('New Count should be 11', 9 + Length(DATA_INT), LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 1', 1, LIntVec[1]);
  AssertEquals('Item should be 2', 2, LIntVec[2]);
  AssertEquals('Item should be 1', 1, LIntVec[3]);
  AssertEquals('Item should be 2', 2, LIntVec[4]);
  AssertEquals('Item should be 5', 5, LIntVec[5]);
  AssertEquals('Item should be 6', 6, LIntVec[6]);
  AssertEquals('Item should be 7', 7, LIntVec[7]);
  AssertEquals('Item should be 8', 8, LIntVec[8]);
  AssertEquals('Item should be 1', 1, LIntVec[9]);
  AssertEquals('Item should be 2', 2, LIntVec[10]);
  AssertEquals('Item should be 3', 3, LIntVec[11]);
  AssertEquals('Item should be 4', 4, LIntVec[12]);
  AssertEquals('Item should be 5', 5, LIntVec[13]);
  AssertEquals('Item should be 6', 6, LIntVec[14]);
  AssertEquals('Item should be 7', 7, LIntVec[15]);
  AssertEquals('Item should be 8', 8, LIntVec[16]);

  { 扩容触发 }
  LIntVec := specialize TVec<Integer>.Create(2, 2);
  LIntVec.WriteFromMemory(0, @DATA_INT[0], 8);
  AssertEquals('New Count should be 8',8, LIntVec.GetCount);
  AssertEquals('Capacity should be 8',8, LIntVec.GetCapacity);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 2', 2, LIntVec[1]);
  AssertEquals('Item should be 3', 3, LIntVec[2]);
  AssertEquals('Item should be 4', 4, LIntVec[3]);
  AssertEquals('Item should be 5', 5, LIntVec[4]);
  AssertEquals('Item should be 6', 6, LIntVec[5]);
  AssertEquals('Item should be 7', 7, LIntVec[6]);
  AssertEquals('Item should be 8', 8, LIntVec[7]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  { 失败测试: nil }
  AssertFalse('WriteFromMemory should return false',LStrVec.WriteFromMemory(0,nil,8));

  { 失败测试: 0 长度 }
  AssertFalse('WriteFromMemory should return false',LStrVec.WriteFromMemory(0,@DATA_STR[0],0));
  
  { 失败测试: 越界 }
  AssertFalse('WriteFromMemory should return false',LStrVec.WriteFromMemory(1,@DATA_STR[0],8));

  { 从 0 开始 }
  AssertTrue('WriteFromMemory should return true',LStrVec.WriteFromMemory(0,@DATA_STR[0],8));
  AssertEquals('New Count should be 8',8,LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "2"', '2', LStrVec[1]);
  AssertEquals('Item should be "3"', '3', LStrVec[2]);
  AssertEquals('Item should be "4"', '4', LStrVec[3]);
  AssertEquals('Item should be "5"', '5', LStrVec[4]);
  AssertEquals('Item should be "6"', '6', LStrVec[5]);
  AssertEquals('Item should be "7"', '7', LStrVec[6]);
  AssertEquals('Item should be "8"', '8', LStrVec[7]);

  { 从 1 开始 }
  AssertTrue('WriteFromMemory should return true',LStrVec.WriteFromMemory(1,@DATA_STR[0],8));
  AssertEquals('New Count should be 9',9,LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "1"', '1', LStrVec[1]);
  AssertEquals('Item should be "2"', '2', LStrVec[2]);
  AssertEquals('Item should be "3"', '3', LStrVec[3]);
  AssertEquals('Item should be "4"', '4', LStrVec[4]);
  AssertEquals('Item should be "5"', '5', LStrVec[5]);
  AssertEquals('Item should be "6"', '6', LStrVec[6]);
  AssertEquals('Item should be "7"', '7', LStrVec[7]);
  AssertEquals('Item should be "8"', '8', LStrVec[8]);

  { 从3写入两个 }
  AssertTrue('WriteFromMemory should return true',LStrVec.WriteFromMemory(3,@DATA_STR[0],2));
  AssertEquals('New Count should be 9',9,LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "1"', '1', LStrVec[1]);
  AssertEquals('Item should be "2"', '2', LStrVec[2]);
  AssertEquals('Item should be "1"', '1', LStrVec[3]);
  AssertEquals('Item should be "2"', '2', LStrVec[4]);
  AssertEquals('Item should be "5"', '5', LStrVec[5]);
  AssertEquals('Item should be "6"', '6', LStrVec[6]);
  AssertEquals('Item should be "7"', '7', LStrVec[7]);
  AssertEquals('Item should be "8"', '8', LStrVec[8]);

  { 从尾部写入 }
  AssertTrue('WriteFromMemory should return true',LStrVec.WriteFromMemory(LStrVec.Count,@DATA_STR[0],Length(DATA_STR)));
  AssertEquals('New Count should be 11',9+Length(DATA_STR),LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "1"', '1', LStrVec[1]);
  AssertEquals('Item should be "2"', '2', LStrVec[2]);
  AssertEquals('Item should be "1"', '1', LStrVec[3]);
  AssertEquals('Item should be "2"', '2', LStrVec[4]);
  AssertEquals('Item should be "5"', '5', LStrVec[5]);
  AssertEquals('Item should be "6"', '6', LStrVec[6]);
  AssertEquals('Item should be "7"', '7', LStrVec[7]);
  AssertEquals('Item should be "8"', '8', LStrVec[8]);
  AssertEquals('Item should be "1"', '1', LStrVec[9]);
  AssertEquals('Item should be "2"', '2', LStrVec[10]);
  AssertEquals('Item should be "3"', '3', LStrVec[11]);
  AssertEquals('Item should be "4"', '4', LStrVec[12]);
  AssertEquals('Item should be "5"', '5', LStrVec[13]);
  AssertEquals('Item should be "6"', '6', LStrVec[14]);
  AssertEquals('Item should be "7"', '7', LStrVec[15]);
  AssertEquals('Item should be "8"', '8', LStrVec[16]);

  { 扩容触发 }
  LStrVec := specialize TVec<String>.Create(2,2);
  LStrVec.WriteFromMemory(0,@DATA_STR[0],8);
  AssertEquals('New Count should be 8',8,LStrVec.GetCount);
  AssertEquals('Capacity should be 8',8,LStrVec.GetCapacity);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "2"', '2', LStrVec[1]);
  AssertEquals('Item should be "3"', '3', LStrVec[2]);
  AssertEquals('Item should be "4"', '4', LStrVec[3]);
  AssertEquals('Item should be "5"', '5', LStrVec[4]);
  AssertEquals('Item should be "6"', '6', LStrVec[5]);
  AssertEquals('Item should be "7"', '7', LStrVec[6]);
  AssertEquals('Item should be "8"', '8', LStrVec[7]);

end;

procedure TTestCase_vec.Test_WriteFromArray;
const
  DATA_INT: array[0..7] of Integer = (1,2,3,4,5,6,7,8);
  DATA_STR: array[0..7] of String = ('1','2','3','4','5','6','7','8');
var
  LIntVec: specialize IVec<Integer>;
  LStrVec: specialize IVec<String>;
  LLen:    SizeUInt;
begin
  LIntVec := specialize TVec<Integer>.Create;

  { 失败测试: 0 长度 }
  AssertFalse('WriteFromArray should return false', LIntVec.WriteFromArray(0, []));
  
  { 失败测试: 越界 }
  AssertFalse('WriteFromArray should return false', LIntVec.WriteFromArray(1, DATA_INT));

  { 写入到 0 }
  AssertTrue('WriteFromArray should return true', LIntVec.WriteFromArray(0, DATA_INT));
  LLen := SizeUInt(Length(DATA_INT));
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 2', 2, LIntVec[1]);
  AssertEquals('Item should be 3', 3, LIntVec[2]);
  AssertEquals('Item should be 4', 4, LIntVec[3]);
  AssertEquals('Item should be 5', 5, LIntVec[4]);
  AssertEquals('Item should be 6', 6, LIntVec[5]);
  AssertEquals('Item should be 7', 7, LIntVec[6]);
  AssertEquals('Item should be 8', 8, LIntVec[7]);

  { 写入到 1 }
  AssertTrue('WriteFromArray should return true', LIntVec.WriteFromArray(1, DATA_INT));
  LLen := SizeUInt(Length(DATA_INT)) + 1;
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 1', 1, LIntVec[1]);
  AssertEquals('Item should be 2', 2, LIntVec[2]);
  AssertEquals('Item should be 3', 3, LIntVec[3]);
  AssertEquals('Item should be 4', 4, LIntVec[4]);
  AssertEquals('Item should be 5', 5, LIntVec[5]);
  AssertEquals('Item should be 6', 6, LIntVec[6]);
  AssertEquals('Item should be 7', 7, LIntVec[7]);
  AssertEquals('Item should be 8', 8, LIntVec[8]);

  { 写入到 2 }
  AssertTrue('WriteFromArray should return true', LIntVec.WriteFromArray(2, [100, 101, 102]));
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LIntVec.GetCount);
  AssertEquals('Item should be 1',   1,   LIntVec[0]);
  AssertEquals('Item should be 1',   1,   LIntVec[1]);
  AssertEquals('Item should be 100', 100, LIntVec[2]);
  AssertEquals('Item should be 101', 101, LIntVec[3]);
  AssertEquals('Item should be 102', 102, LIntVec[4]);
  AssertEquals('Item should be 5',   5,   LIntVec[5]);
  AssertEquals('Item should be 6',   6,   LIntVec[6]);
  AssertEquals('Item should be 7',   7,   LIntVec[7]);
  AssertEquals('Item should be 8',   8,   LIntVec[8]);

  { 写入到尾部 }
  AssertTrue('WriteFromArray should return true', LIntVec.WriteFromArray(LLen, [103, 104, 105]));
  LLen := LLen + 3;
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LIntVec.GetCount);
  AssertEquals('Item should be 1',   1,   LIntVec[0]);
  AssertEquals('Item should be 1',   1,   LIntVec[1]);
  AssertEquals('Item should be 100', 100, LIntVec[2]);
  AssertEquals('Item should be 101', 101, LIntVec[3]);
  AssertEquals('Item should be 102', 102, LIntVec[4]);
  AssertEquals('Item should be 5',   5,   LIntVec[5]);
  AssertEquals('Item should be 6',   6,   LIntVec[6]);
  AssertEquals('Item should be 7',   7,   LIntVec[7]);
  AssertEquals('Item should be 8',   8,   LIntVec[8]);
  AssertEquals('Item should be 103', 103, LIntVec[9]);
  AssertEquals('Item should be 104', 104, LIntVec[10]);
  AssertEquals('Item should be 105', 105, LIntVec[11]);

  { 扩容触发 }
  LIntVec := specialize TVec<Integer>.Create(2,2);
  LIntVec.WriteFromArray(0,DATA_INT);
  AssertEquals('New Count should be '+ IntToStr(Length(DATA_INT)), SizeUInt(Length(DATA_INT)), LIntVec.GetCount);
  AssertEquals('Item should be 1', 1, LIntVec[0]);
  AssertEquals('Item should be 2', 2, LIntVec[1]);
  AssertEquals('Item should be 3', 3, LIntVec[2]);
  AssertEquals('Item should be 4', 4, LIntVec[3]);
  AssertEquals('Item should be 5', 5, LIntVec[4]);
  AssertEquals('Item should be 6', 6, LIntVec[5]);
  AssertEquals('Item should be 7', 7, LIntVec[6]);
  AssertEquals('Item should be 8', 8, LIntVec[7]);

  ///
  /// 托管元素
  ///

  LStrVec := specialize TVec<String>.Create;

  { 失败测试: 0 长度 }
  AssertFalse('WriteFromArray should return false', LStrVec.WriteFromArray(0, []));

  { 失败测试: 越界 }
  AssertFalse('WriteFromArray should return false', LStrVec.WriteFromArray(1, DATA_STR));
    
  { 写入到 0 }
  AssertTrue('WriteFromArray should return true', LStrVec.WriteFromArray(0, DATA_STR));
  LLen := Length(DATA_STR);
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "2"', '2', LStrVec[1]);
  AssertEquals('Item should be "3"', '3', LStrVec[2]);
  AssertEquals('Item should be "4"', '4', LStrVec[3]);
  AssertEquals('Item should be "5"', '5', LStrVec[4]);
  AssertEquals('Item should be "6"', '6', LStrVec[5]);
  AssertEquals('Item should be "7"', '7', LStrVec[6]);
  AssertEquals('Item should be "8"', '8', LStrVec[7]);

  { 写入到 1 }
  AssertTrue('WriteFromArray should return true',LStrVec.WriteFromArray(1, DATA_STR));
  LLen := Length(DATA_STR) + 1;
  AssertEquals('New Count should be '+IntToStr(LLen),LLen,LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "1"', '1', LStrVec[1]);
  AssertEquals('Item should be "2"', '2', LStrVec[2]);
  AssertEquals('Item should be "3"', '3', LStrVec[3]);
  AssertEquals('Item should be "4"', '4', LStrVec[4]);
  AssertEquals('Item should be "5"', '5', LStrVec[5]);
  AssertEquals('Item should be "6"', '6', LStrVec[6]);
  AssertEquals('Item should be "7"', '7', LStrVec[7]);
  AssertEquals('Item should be "8"', '8', LStrVec[8]);

  { 写入到 2 }
  AssertTrue('WriteFromArray should return true', LStrVec.WriteFromArray(2, ['100','101','102']));
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LStrVec.GetCount);
  AssertEquals('Item should be "1"',   '1',   LStrVec[0]);
  AssertEquals('Item should be "1"',   '1',   LStrVec[1]);
  AssertEquals('Item should be "100"', '100', LStrVec[2]);
  AssertEquals('Item should be "101"', '101', LStrVec[3]);
  AssertEquals('Item should be "102"', '102', LStrVec[4]);
  AssertEquals('Item should be "5"',   '5',   LStrVec[5]);
  AssertEquals('Item should be "6"',   '6',   LStrVec[6]);
  AssertEquals('Item should be "7"',   '7',   LStrVec[7]);
  AssertEquals('Item should be "8"',   '8',   LStrVec[8]);

  { 写入到尾部 }
  AssertTrue('WriteFromArray should return true', LStrVec.WriteFromArray(LLen, ['103','104','105']));
  LLen := LLen + 3;
  AssertEquals('New Count should be '+ IntToStr(LLen), LLen, LStrVec.GetCount);
  AssertEquals('Item should be "1"',   '1',   LStrVec[0]);
  AssertEquals('Item should be "1"',   '1',   LStrVec[1]);
  AssertEquals('Item should be "100"', '100', LStrVec[2]);
  AssertEquals('Item should be "101"', '101', LStrVec[3]);
  AssertEquals('Item should be "102"', '102', LStrVec[4]);
  AssertEquals('Item should be "5"',   '5',   LStrVec[5]);
  AssertEquals('Item should be "6"',   '6',   LStrVec[6]);
  AssertEquals('Item should be "7"',   '7',   LStrVec[7]);
  AssertEquals('Item should be "8"',   '8',   LStrVec[8]);
  AssertEquals('Item should be "103"', '103', LStrVec[9]);
  AssertEquals('Item should be "104"', '104', LStrVec[10]);
  AssertEquals('Item should be "105"', '105', LStrVec[11]);

  { 扩容触发 }
  LStrVec := specialize TVec<String>.Create(2,2);
  LStrVec.WriteFromArray(0,DATA_STR);
  AssertEquals('New Count should be '+ IntToStr(Length(DATA_STR)), SizeUInt(Length(DATA_STR)), LStrVec.GetCount);
  AssertEquals('Item should be "1"', '1', LStrVec[0]);
  AssertEquals('Item should be "2"', '2', LStrVec[1]);
  AssertEquals('Item should be "3"', '3', LStrVec[2]);
  AssertEquals('Item should be "4"', '4', LStrVec[3]);
  AssertEquals('Item should be "5"', '5', LStrVec[4]);
  AssertEquals('Item should be "6"', '6', LStrVec[5]);
  AssertEquals('Item should be "7"', '7', LStrVec[6]);
  AssertEquals('Item should be "8"', '8', LStrVec[7]);
end;

procedure TTestCase_vec.Test_WriteCollection;
var
  LArray:    specialize TArray<Integer>;
  LVec:      specialize TVec<Integer>;
  LArrayStr: specialize TArray<String>;
  LVecStr:   specialize TVec<String>;
begin
  // LArray := specialize TArray<Integer>.Create([1,2,3,4,5,6,7,8,9,10]);
  // try
  //   LVec := specialize TVec<Integer>.Create;
  //   try
  //     // 错误测试: nil
  //     AssertFalse('WriteFromArray should return false',LVec.WriteFromArray(0,nil));

  //     // 错误测试: 越界
  //     AssertFalse('WriteFromArray should return false',LVec.WriteFromArray(1,LArray));

  //     // 错误测试: 0长度
  //     LArray.Clear();
  //     AssertFalse('WriteFromArray should return false',LVec.WriteFromArray(0,LArray));

  //     LArray.LoadFromArray([1,2,3,4,5,6,7,8,9,10]);

  //     // 写入到 0
  //     AssertTrue('WriteFromArray should return true',LVec.WriteFromArray(0,LArray));
  //     AssertEquals('New Count should be 10',10,LVec.GetCount);
  //     AssertEquals('Item should be 1', 1,LVec[0]);
  //     AssertEquals('Item should be 2', 2,LVec[1]);
  //     AssertEquals('Item should be 3', 3,LVec[2]);
  //     AssertEquals('Item should be 4', 4,LVec[3]);
  //     AssertEquals('Item should be 5', 5,LVec[4]);
  //     AssertEquals('Item should be 6', 6,LVec[5]);
  //     AssertEquals('Item should be 7', 7,LVec[6]);
  //     AssertEquals('Item should be 8', 8,LVec[7]);
  //     AssertEquals('Item should be 9', 9,LVec[8]);
  //     AssertEquals('Item should be 10',10,LVec[9]);

  //     // 写入到 1
  //     AssertTrue('WriteFromArray should return true',LVec.WriteFromArray(1,LArray));
  //     AssertEquals('New Count should be 11',11,LVec.GetCount);
  //     AssertEquals('Item should be 1', 1,LVec[0]);
  //     AssertEquals('Item should be 1', 1,LVec[1]);
  //     AssertEquals('Item should be 2', 2,LVec[2]);
  //     AssertEquals('Item should be 3', 3,LVec[3]);
  //     AssertEquals('Item should be 4', 4,LVec[4]);
  //     AssertEquals('Item should be 5', 5,LVec[5]);
  //     AssertEquals('Item should be 6', 6,LVec[6]);
  //     AssertEquals('Item should be 7', 7,LVec[7]);
  //     AssertEquals('Item should be 8', 8,LVec[8]);
  //     AssertEquals('Item should be 9', 9,LVec[9]);
  //     AssertEquals('Item should be 10',10,LVec[10]);

  //     LArray.LoadFromArray([888,999,1000]);

  //     // 写入到最后
  //     AssertTrue('WriteFromArray should return true',LVec.WriteFromArray(LVec.GetCount,LArray));
  //     AssertEquals('New Count should be 14',14,LVec.GetCount);
  //     AssertEquals('Item should be 1',   1,    LVec[0]);
  //     AssertEquals('Item should be 1',   1,    LVec[1]);
  //     AssertEquals('Item should be 2',   2,    LVec[2]);
  //     AssertEquals('Item should be 3',   3,    LVec[3]);
  //     AssertEquals('Item should be 4',   4,    LVec[4]);
  //     AssertEquals('Item should be 5',   5,    LVec[5]);
  //     AssertEquals('Item should be 6',   6,    LVec[6]);
  //     AssertEquals('Item should be 7',   7,    LVec[7]);
  //     AssertEquals('Item should be 8',   8,    LVec[8]);
  //     AssertEquals('Item should be 9',   9,    LVec[9]);
  //     AssertEquals('Item should be 10',  10,   LVec[10]);
  //     AssertEquals('Item should be 888', 888,  LVec[11]);
  //     AssertEquals('Item should be 999', 999,  LVec[12]);
  //     AssertEquals('Item should be 1000',1000, LVec[13]);

  //   finally
  //     LVec.Free;
  //   end;
  // finally
  //   LArray.Free;
  // end;

  // // 托管元素
  // LArrayStr := specialize TArray<String>.Create(['1','2','3','4','5','6','7','8','9','10']);
  // try
  // LVecStr := specialize TVec<String>.Create;
  //   try
  //     // 错误测试: nil
  //     AssertFalse('WriteFromArray should return false',LVecStr.WriteFromArray(0,nil));

  //     // 错误测试: 越界
  //     AssertFalse('WriteFromArray should return false',LVecStr.WriteFromArray(1,LArrayStr));

  //     // 错误测试: 0长度
  //     LArrayStr.Clear();
  //     AssertFalse('writeTest_WriteFromArray2',LVecStr.WriteFromArray(0,[]));

  //     LArrayStr.LoadFromArray(['1','2','3','4','5','6','7','8','9','10']);

  //     // 写入到 0
  //     AssertTrue('WriteFromArray should return true',LVecStr.WriteFromArray(0,LArrayStr));
  //     AssertEquals('New Count should be 10',10, LVecStr.GetCount);
  //     AssertEquals('Item should be "1"', '1',   LVecStr[0]);
  //     AssertEquals('Item should be "2"', '2',   LVecStr[1]);
  //     AssertEquals('Item should be "3"', '3',   LVecStr[2]);
  //     AssertEquals('Item should be "4"', '4',   LVecStr[3]);
  //     AssertEquals('Item should be "5"', '5',   LVecStr[4]);
  //     AssertEquals('Item should be "6"', '6',   LVecStr[5]);
  //     AssertEquals('Item should be "7"', '7',   LVecStr[6]);
  //     AssertEquals('Item should be "8"', '8',   LVecStr[7]);
  //     AssertEquals('Item should be "9"', '9',   LVecStr[8]);
  //     AssertEquals('Item should be "10"', '10', LVecStr[9]);
      
  //     // 写入到 1
  //     AssertTrue('WriteFromArray should return true',LVecStr.WriteFromArray(1,LArrayStr));
  //     AssertEquals('New Count should be 11',11, LVecStr.GetCount);
  //     AssertEquals('Item should be "1"', '1',   LVecStr[0]);
  //     AssertEquals('Item should be "1"', '1',   LVecStr[1]);
  //     AssertEquals('Item should be "2"', '2',   LVecStr[2]);
  //     AssertEquals('Item should be "3"', '3',   LVecStr[3]);
  //     AssertEquals('Item should be "4"', '4',   LVecStr[4]);
  //     AssertEquals('Item should be "5"', '5',   LVecStr[5]);
  //     AssertEquals('Item should be "6"', '6',   LVecStr[6]);
  //     AssertEquals('Item should be "7"', '7',   LVecStr[7]);
  //     AssertEquals('Item should be "8"', '8',   LVecStr[8]);
  //     AssertEquals('Item should be "9"', '9',   LVecStr[9]);
  //     AssertEquals('Item should be "10"', '10', LVecStr[10]);

  //     LArrayStr.LoadFromArray(['888','999','1000']);

  //     // 写入到最后
  //     AssertTrue('WriteFromArray should return true',   LVecStr.WriteFromArray(LVecStr.GetCount,LArrayStr));
  //     AssertEquals('New Count should be 14',14,     LVecStr.GetCount);
  //     AssertEquals('Item should be "1"', '1',       LVecStr[0]);
  //     AssertEquals('Item should be "1"', '1',       LVecStr[1]);
  //     AssertEquals('Item should be "2"', '2',       LVecStr[2]);
  //     AssertEquals('Item should be "3"', '3',       LVecStr[3]);
  //     AssertEquals('Item should be "4"', '4',       LVecStr[4]);
  //     AssertEquals('Item should be "5"', '5',       LVecStr[5]);
  //     AssertEquals('Item should be "6"', '6',       LVecStr[6]);
  //     AssertEquals('Item should be "7"', '7',       LVecStr[7]);
  //     AssertEquals('Item should be "8"', '8',       LVecStr[8]);
  //     AssertEquals('Item should be "9"', '9',       LVecStr[9]);
  //     AssertEquals('Item should be "10"', '10',     LVecStr[10]);
  //     AssertEquals('Item should be "888"', '888',   LVecStr[11]);
  //     AssertEquals('Item should be "999"', '999',   LVecStr[12]);
  //     AssertEquals('Item should be "1000"', '1000', LVecStr[13]);
  //   finally
  //     LVecStr.Free;
  //   end;
  // finally
  //   LArrayStr.Free;
  // end;
end;

procedure TTestCase_vec.Test_ReadToMemory;
var
  LVec:    specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
  LBuf:    array[0..8] of Integer;
  LBufStr: array[0..8] of String;
begin
  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 失败测试: nil }
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(0, nil, 1));
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(0, @LBuf[0], 0));

  { 失败测试: 越界 }
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(LVec.GetCount, @LBuf[0], 1));
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(0, @LBuf[0], 9));
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(1, @LBuf[0], 8));

  { 失败测试: 0长度 }
  AssertFalse('ReadToMemory should return false', LVec.ReadToMemory(0, @LBuf[0], 0));

  { 从0读取两个元素 }
  AssertTrue('ReadToMemory should return true', LVec.ReadToMemory(0, @LBuf[0], 2));
  AssertEquals('Item should be 1', 1, LBuf[0]);
  AssertEquals('Item should be 2', 2, LBuf[1]);

  { 从1读取两个元素 }
  AssertTrue('ReadToMemory should return true', LVec.ReadToMemory(1, @LBuf[0], 2));
  AssertEquals('Item should be 2', 2, LBuf[0]);
  AssertEquals('Item should be 3', 3, LBuf[1]);

  { 从0读取8个元素 }
  AssertTrue('ReadToMemory should return true', LVec.ReadToMemory(0, @LBuf[0], 8));
  AssertEquals('Item should be 1', 1, LBuf[0]);
  AssertEquals('Item should be 2', 2, LBuf[1]);
  AssertEquals('Item should be 3', 3, LBuf[2]);
  AssertEquals('Item should be 4', 4, LBuf[3]);
  AssertEquals('Item should be 5', 5, LBuf[4]);
  AssertEquals('Item should be 6', 6, LBuf[5]);
  AssertEquals('Item should be 7', 7, LBuf[6]);
  AssertEquals('Item should be 8', 8, LBuf[7]);

  { 从1读取7个元素 }
  AssertTrue('ReadToMemory should return true', LVec.ReadToMemory(1, @LBuf[0], 7));
  AssertEquals('Item should be 2', 2, LBuf[0]);
  AssertEquals('Item should be 3', 3, LBuf[1]);
  AssertEquals('Item should be 4', 4, LBuf[2]);
  AssertEquals('Item should be 5', 5, LBuf[3]);
  AssertEquals('Item should be 6', 6, LBuf[4]);
  AssertEquals('Item should be 7', 7, LBuf[5]);
  AssertEquals('Item should be 8', 8, LBuf[6]);

  { 读取最后一个元素 }
  AssertTrue('ReadToMemory should return true', LVec.ReadToMemory(LVec.GetCount - 1, @LBuf[0], 1));
  AssertEquals('Item should be 8', 8, LBuf[0]);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);
  { 失败测试: nil }
  AssertFalse('ReadToMemory should return false', LVecStr.ReadToMemory(0, nil, 1));

  { 失败测试: 越界 }
  AssertFalse('ReadToMemory should return false', LVecStr.ReadToMemory(LVecStr.GetCount, nil, 1));
  AssertFalse('ReadToMemory should return false', LVecStr.ReadToMemory(0, @LBufStr[0], 9));
  AssertFalse('ReadToMemory should return false', LVecStr.ReadToMemory(1, @LBufStr[0], 8));

  { 失败测试: 0长度 }
  AssertFalse('ReadToMemory should return false', LVecStr.ReadToMemory(0, nil, 0));

  { 从0读取两个元素 }
  AssertTrue('ReadToMemory should return true', LVecStr.ReadToMemory(0, @LBufStr[0], 2));
  AssertEquals('Item should be "1"', '1', LBufStr[0]);
  AssertEquals('Item should be "2"', '2', LBufStr[1]);

  { 从1读取两个元素 }
  AssertTrue('ReadToMemory should return true', LVecStr.ReadToMemory(1, @LBufStr[0], 2));
  AssertEquals('Item should be "2"', '2', LBufStr[0]);
  AssertEquals('Item should be "3"', '3', LBufStr[1]);
  
  { 从0读取8个元素 }
  AssertTrue('ReadToMemory should return true', LVecStr.ReadToMemory(0, @LBufStr[0], 8));
  AssertEquals('Item should be "1"', '1', LBufStr[0]);
  AssertEquals('Item should be "2"', '2', LBufStr[1]);
  AssertEquals('Item should be "3"', '3', LBufStr[2]);
  AssertEquals('Item should be "4"', '4', LBufStr[3]);
  AssertEquals('Item should be "5"', '5', LBufStr[4]);
  AssertEquals('Item should be "6"', '6', LBufStr[5]);
  AssertEquals('Item should be "7"', '7', LBufStr[6]);
  AssertEquals('Item should be "8"', '8', LBufStr[7]);

  { 从1读取7个元素 }
  AssertTrue('ReadToMemory should return true', LVecStr.ReadToMemory(1, @LBufStr[0], 7));
  AssertEquals('Item should be "2"', '2', LBufStr[0]);
  AssertEquals('Item should be "3"', '3', LBufStr[1]);
  AssertEquals('Item should be "4"', '4', LBufStr[2]);
  AssertEquals('Item should be "5"', '5', LBufStr[3]);
  AssertEquals('Item should be "6"', '6', LBufStr[4]);
  AssertEquals('Item should be "7"', '7', LBufStr[5]);
  AssertEquals('Item should be "8"', '8', LBufStr[6]);

  { 读取最后一个元素 }
  AssertTrue('ReadToMemory should return true', LVecStr.ReadToMemory(LVecStr.GetCount - 1, @LBufStr[0], 1));
  AssertEquals('Item should be "8"', '8', LBufStr[0]);
end;

procedure TTestCase_vec.Test_ReadToArray;
var
  LVec:    specialize IVec<Integer>;
  LVecStr: specialize IVec<String>;
  LBuf:    array of Integer;
  LBufStr: array of String;
begin
  Initialize(LBuf);
  Initialize(LBufStr);

  LVec := specialize TVec<Integer>.Create([1,2,3,4,5,6,7,8]);

  { 失败测试: 越界 }
  AssertFalse('ReadToArray should return false', LVec.ReadToArray(LVec.GetCount, LBuf, 1));
  AssertFalse('ReadToArray should return false', LVec.ReadToArray(0, LBuf, 9));
  AssertFalse('ReadToArray should return false', LVec.ReadToArray(4, LBuf, 5));

  { 从0读取 }
  AssertTrue('ReadToArray should return true', LVec.ReadToArray(0, LBuf, 1));
  AssertEquals('Count should be 1', 1, Length(LBuf));
  AssertEquals('Item should be 1', 1, LBuf[0]);

  { 从1读取 }
  AssertTrue('ReadToArray should return true', LVec.ReadToArray(1, LBuf, 1));
  AssertEquals('Count should be 1', 1, Length(LBuf));
  AssertEquals('Item should be 2', 2, LBuf[0]);

  { 从0读取8个元素 }
  AssertTrue('ReadToArray should return true', LVec.ReadToArray(0, LBuf, 8));
  AssertEquals('Count should be 8', 8, Length(LBuf));
  AssertEquals('Item should be 1',  1, LBuf[0]);
  AssertEquals('Item should be 2',  2, LBuf[1]);
  AssertEquals('Item should be 3',  3, LBuf[2]);
  AssertEquals('Item should be 4',  4, LBuf[3]);
  AssertEquals('Item should be 5',  5, LBuf[4]);
  AssertEquals('Item should be 6',  6, LBuf[5]);
  AssertEquals('Item should be 7',  7, LBuf[6]);
  AssertEquals('Item should be 8',  8, LBuf[7]);

  ///
  /// 托管元素
  ///

  LVecStr := specialize TVec<String>.Create(['1','2','3','4','5','6','7','8']);

  { 失败测试: 越界 }
  AssertFalse('ReadToArray should return false', LVecStr.ReadToArray(LVecStr.GetCount, LBufStr, 1));
  AssertFalse('ReadToArray should return false', LVecStr.ReadToArray(0, LBufStr, 9));
  AssertFalse('ReadToArray should return false', LVecStr.ReadToArray(4, LBufStr, 5));

  { 从0读取 }
  AssertTrue('ReadToArray should return true', LVecStr.ReadToArray(0, LBufStr, 1));
  AssertEquals('Count should be 1', 1, Length(LBufStr));
  AssertEquals('Item should be "1"', '1', LBufStr[0]);

  { 从1读取 }
  AssertTrue('ReadToArray should return true', LVecStr.ReadToArray(1, LBufStr, 1));
  AssertEquals('Count should be 1', 1, Length(LBufStr));
  AssertEquals('Item should be "2"', '2', LBufStr[0]);

  { 从0读取8个元素 }
  AssertTrue('ReadToArray should return true', LVecStr.ReadToArray(0, LBufStr, 8));
  AssertEquals('Count should be 8', 8, Length(LBufStr));
  AssertEquals('Item should be "1"', '1', LBufStr[0]);
  AssertEquals('Item should be "2"', '2', LBufStr[1]);
  AssertEquals('Item should be "3"', '3', LBufStr[2]);
  AssertEquals('Item should be "4"', '4', LBufStr[3]);
  AssertEquals('Item should be "5"', '5', LBufStr[4]);
  AssertEquals('Item should be "6"', '6', LBufStr[5]);
  AssertEquals('Item should be "7"', '7', LBufStr[6]);
  AssertEquals('Item should be "8"', '8', LBufStr[7]);
end;


initialization

  RegisterTest(TTestCase_vec);
end.

